#!/bin/bash

# 测试构建脚本
# 使用方法: ./test-build.sh [staging|production]

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

ENVIRONMENT=${1:-staging}

log_info "🧪 测试 Turbo Monorepo 构建流程..."
echo "   环境: $ENVIRONMENT"

# 检查必要工具
log_info "检查必要工具..."
if ! command -v turbo &> /dev/null; then
    log_error "Turbo 未安装"
    exit 1
fi

if ! command -v docker &> /dev/null; then
    log_error "Docker 未安装"
    exit 1
fi

log_success "✅ 工具检查通过"

# 检查项目结构
log_info "检查项目结构..."
if [ ! -f "turbo.json" ]; then
    log_error "turbo.json 不存在"
    exit 1
fi

if [ ! -f "pnpm-workspace.yaml" ]; then
    log_error "pnpm-workspace.yaml 不存在"
    exit 1
fi

if [ ! -f "packages/nuxt-app/package.json" ]; then
    log_error "packages/nuxt-app/package.json 不存在"
    exit 1
fi

log_success "✅ 项目结构检查通过"

# 检查环境文件
ENV_FILE=".env.$ENVIRONMENT"
if [ ! -f "$ENV_FILE" ]; then
    log_warning "⚠️  环境文件 $ENV_FILE 不存在"
else
    log_success "✅ 环境文件检查通过"
fi

# 测试 Turbo 构建
log_info "测试 Turbo 构建..."
if turbo run build --filter=nuxt-app --dry-run > /dev/null 2>&1; then
    log_success "✅ Turbo 构建配置正确"
else
    log_error "❌ Turbo 构建配置有问题"
    exit 1
fi

# 测试 Docker 构建（不实际构建）
log_info "测试 Docker 构建配置..."
if docker build -f packages/nuxt-app/Dockerfile --dry-run . > /dev/null 2>&1; then
    log_success "✅ Docker 构建配置正确"
else
    log_warning "⚠️  Docker 构建配置可能有问题（或 Docker 版本不支持 --dry-run）"
fi

# 检查部署脚本
log_info "检查部署脚本..."
if [ -x "deploy-nuxt-only.sh" ]; then
    log_success "✅ deploy-nuxt-only.sh 可执行"
else
    log_warning "⚠️  deploy-nuxt-only.sh 不可执行"
fi

if [ -x "deploy-turbo.sh" ]; then
    log_success "✅ deploy-turbo.sh 可执行"
else
    log_warning "⚠️  deploy-turbo.sh 不可执行"
fi

log_success "🎉 测试完成！"
echo ""
echo "📋 部署建议："
echo "   1. 使用 ./deploy-nuxt-only.sh $ENVIRONMENT 进行快速部署"
echo "   2. 使用 ./deploy-turbo.sh $ENVIRONMENT nuxt 进行完整部署"
echo "   3. 查看 DEPLOYMENT-TURBO.md 了解详细说明"
