import type { PaymentResult, PriceItem } from '../types/index'

/**
 * 格式化价格显示
 */
export function formatPrice(amount: number, currency = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2
  }).format(amount / 100) // amount 是分为单位
}

/**
 * 格式化虚拟币数量
 */
export function formatCoins(coins: number): string {
  if (coins >= 1000000) {
    return `${(coins / 1000000).toFixed(1)}M`
  }
  if (coins >= 1000) {
    return `${(coins / 1000).toFixed(1)}K`
  }
  return coins.toString()
}

/**
 * 计算折扣后的价格
 */
export function calculateDiscountPrice(originalAmount: number, discountPercent: number): number {
  return Math.round(originalAmount * (1 - discountPercent / 100))
}

/**
 * 获取最优惠的价格项
 */
export function getBestValuePrice(priceList: PriceItem[]): PriceItem | null {
  if (priceList.length === 0) return null

  return priceList.reduce((best, current) => {
    const currentValue = current.coins / current.amount
    const bestValue = best.coins / best.amount

    return currentValue > bestValue ? current : best
  })
}

/**
 * 检查支付结果是否成功
 */
export function isPaymentSuccessful(result: PaymentResult): boolean {
  return result.success && !!result.redirectUrl
}

/**
 * 生成支付成功回调URL
 */
export function generateCallbackUrl(
  baseUrl: string,
  provider: 'stripe' | 'onlypay',
  additionalParams: Record<string, string> = {}
): string {
  const url = new URL(baseUrl)

  // 根据支付提供商设置不同的路径
  if (provider === 'stripe') {
    url.pathname = '/payment/stripe-callback'
  } else {
    url.pathname = '/recharge-success'
  }

  // 添加额外参数
  Object.entries(additionalParams).forEach(([key, value]) => {
    url.searchParams.set(key, value)
  })

  return url.toString()
}

/**
 * 解析支付回调URL参数
 */
export function parseCallbackParams(url: string): Record<string, string> {
  const urlObj = new URL(url)
  const params: Record<string, string> = {}

  urlObj.searchParams.forEach((value, key) => {
    params[key] = value
  })

  return params
}

/**
 * 验证价格ID格式
 */
export function isValidPriceId(priceId: string): boolean {
  // UUID格式验证
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(priceId)
}

/**
 * 生成支付会话ID（用于跟踪）
 */
export function generatePaymentSessionId(): string {
  return `pay_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 支付错误处理
 */
export class PaymentError extends Error {
  constructor(
    message: string,
    public provider: 'stripe' | 'onlypay',
    public code?: string,
    public details?: any
  ) {
    super(message)
    this.name = 'PaymentError'
  }
}

/**
 * 创建支付错误
 */
export function createPaymentError(
  message: string,
  provider: 'stripe' | 'onlypay',
  code?: string,
  details?: any
): PaymentError {
  return new PaymentError(message, provider, code, details)
}

/**
 * 检查是否为支付错误
 */
export function isPaymentError(error: any): error is PaymentError {
  return error instanceof PaymentError
}
