import type {
  PaymentConfig,
  PaymentProvider,
  ApiResponse,
} from '../types/index'
import { getProjectConfig } from '../config/project'

/**
 * 支付提供商抽象基类
 */
export abstract class BasePaymentProvider {
  abstract name: PaymentProvider['name']
  abstract displayName: string

  protected config: PaymentConfig
  protected authTokenGetter?: () => string | null

  constructor(config: PaymentConfig, authTokenGetter?: () => string | null) {
    this.config = config
    this.authTokenGetter = authTokenGetter
  }

  abstract createPayment(params: any): Promise<any>
  abstract isAvailable(): boolean

  protected getAuthHeader(): string {
    const token = this.authTokenGetter?.() || this.getTokenFromStorage()
    return token ? `Bearer ${token}` : ''
  }

  private getTokenFromStorage(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('token')
    }
    return null
  }
}

/**
 * Stripe 支付提供商
 */
export class StripeProvider extends BasePaymentProvider {
  name: PaymentProvider['name'] = 'stripe'
  displayName = 'Stripe'

  async createPayment(params: {
    priceId: string
    successUrl: string
    cancelUrl: string
  }) {
    const response = await fetch(
      `${this.config.apiBase}/api/v1/checkout.create`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': this.getAuthHeader(),
        },
        body: JSON.stringify({
          price_id: params.priceId,
          success_url: params.successUrl,
          cancel_url: params.cancelUrl,
        }),
      },
    )

    const result: ApiResponse<{
      session_id: string
      client_secret?: string
    }> = await response.json()

    if (result.code === '0') {
      return {
        success: true,
        provider: this.name,
        sessionId: result.data.session_id,
        useStripeSDK: true,
      }
    }

    throw new Error(result.message || 'Stripe payment creation failed')
  }

  isAvailable(): boolean {
    // 如果使用 stripe.redirectToCheckout() 需要公钥
    // 如果使用直接 URL 重定向则不需要公钥
    // 当前 shared-payment 使用直接重定向，所以不需要公钥
    // 但为了兼容性，如果提供了公钥就检查，没提供就默认可用
    return this.config.publicKey ? !!this.config.publicKey : true
  }
}

/**
 * Onlypay 支付提供商
 */
export class OnlypayProvider extends BasePaymentProvider {
  name: PaymentProvider['name'] = 'onlypay'
  displayName = 'OnlyPay'

  async createPayment(params: { priceId: string; redirectUrl: string }) {
    // 使用传入的 priceId 而不是固定值
    const response = await fetch(
      `${this.config.apiBase}/api/v1/onlypay/order.create`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': this.getAuthHeader(),
        },
        body: JSON.stringify({
          price_id: params.priceId,
          redirect_url: params.redirectUrl,
        }),
      },
    )

    const result: ApiResponse<{ pay_url: string; order_id?: string }> =
      await response.json()

    if (result.code === '0') {
      return {
        success: true,
        provider: this.name,
        orderId: result.data.order_id,
        redirectUrl: result.data.pay_url,
      }
    }

    throw new Error(result.message || 'Onlypay payment creation failed')
  }

  isAvailable(): boolean {
    return true // Onlypay 不需要公钥配置
  }
}

/**
 * 支付提供商工厂
 */
export class PaymentProviderFactory {
  static create(
    config: PaymentConfig,
    authTokenGetter?: () => string | null,
  ): BasePaymentProvider {
    switch (config.provider) {
      case 'stripe':
        return new StripeProvider(config, authTokenGetter)
      case 'onlypay':
        return new OnlypayProvider(config, authTokenGetter)
      default:
        throw new Error(`Unsupported payment provider: ${config.provider}`)
    }
  }

  static createForCurrentProject(
    apiBase: string,
    authTokenGetter?: () => string | null,
    options: {
      stripePublicKey?: string
      successUrl?: string
      cancelUrl?: string
    } = {},
  ): BasePaymentProvider {
    const projectConfig = getProjectConfig()

    const config: PaymentConfig = {
      provider: projectConfig.paymentProvider,
      publicKey: options.stripePublicKey,
      apiBase,
      successUrl:
        options.successUrl ||
        (typeof window !== 'undefined' ? window.location.href : ''),
      cancelUrl:
        options.cancelUrl ||
        (typeof window !== 'undefined' ? window.location.href : ''),
    }

    return this.create(config, authTokenGetter)
  }
}
