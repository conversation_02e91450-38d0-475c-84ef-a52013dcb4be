// 导出所有类型
export * from './types/index'

// 导出配置相关
export * from './config/project'

// 导出API
export * from './api/index'

// 导出支付提供商
export * from './providers/index'

// 导出工具函数
export * from './utils/index'

// 导出 Embedded Checkout
export * from './embedded/index'

// 默认导出便捷函数
export { createPaymentAPI } from './api/index'
export { PaymentProviderFactory } from './providers/index'
export {
  detectProjectType,
  detectProjectTypeWithConfig,
  getProjectConfig,
  isPlayshot,
  isReelplay,
} from './config/project'
