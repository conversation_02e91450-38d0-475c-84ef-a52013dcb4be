<template>
  <div class="stripe-embedded-checkout">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p class="loading-text">{{ loadingText }}</p>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-container">
      <div class="error-icon">⚠️</div>
      <p class="error-message">{{ error }}</p>
      <button @click="retry" class="retry-button">重试</button>
    </div>

    <!-- Checkout 容器 -->
    <div
      :id="elementId"
      class="checkout-container"
      :class="{ hidden: loading || error }"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { EmbeddedCheckoutManager } from '../embedded'

interface Props {
  publicKey: string
  clientSecret: string
  elementId?: string
  appearance?: {
    theme?: 'stripe' | 'night' | 'flat'
    variables?: Record<string, string>
  }
  loadingText?: string
}

interface Emits {
  (e: 'complete'): void
  (e: 'error', error: Error): void
  (e: 'ready'): void
}

const props = withDefaults(defineProps<Props>(), {
  elementId: 'stripe-embedded-checkout',
  loadingText: 'Loading payment form...',
})

const emit = defineEmits<Emits>()

// 状态
const loading = ref(true)
const error = ref<string | null>(null)
const manager = ref<EmbeddedCheckoutManager | null>(null)

// 初始化 Checkout
const initializeCheckout = async () => {
  if (!props.publicKey || !props.clientSecret) {
    error.value = 'Missing required payment configuration'
    loading.value = false
    return
  }

  try {
    loading.value = true
    error.value = null

    // 创建管理器
    manager.value = new EmbeddedCheckoutManager(props.publicKey)

    // 创建 Embedded Checkout
    const result = await manager.value.createEmbeddedCheckout({
      publicKey: props.publicKey,
      clientSecret: props.clientSecret,
      appearance: props.appearance,
      onComplete: () => {
        emit('complete')
      },
      onError: (err: Error) => {
        error.value = err.message
        emit('error', err)
      },
    })

    if (!result.success) {
      throw new Error(result.error || 'Failed to create payment form')
    }

    // 挂载到 DOM
    await manager.value.mount(props.elementId)

    loading.value = false
    emit('ready')
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : '未知错误'
    error.value = errorMessage
    loading.value = false
    emit('error', new Error(errorMessage))
  }
}

// 重试
const retry = () => {
  initializeCheckout()
}

// 监听 props 变化
watch(
  [() => props.publicKey, () => props.clientSecret],
  () => {
    if (props.publicKey && props.clientSecret) {
      initializeCheckout()
    }
  },
  { immediate: false },
)

// 生命周期
onMounted(() => {
  initializeCheckout()
})

onUnmounted(() => {
  if (manager.value) {
    manager.value.destroy()
  }
})
</script>

<style scoped>
.stripe-embedded-checkout {
  width: 100%;
  min-height: 400px;
  position: relative;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #635bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
  text-align: center;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}

.error-message {
  color: #dc3545;
  font-size: 16px;
  margin-bottom: 1.5rem;
  max-width: 400px;
}

.retry-button {
  background: #635bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: #5a52e8;
}

.checkout-container {
  width: 100%;
  min-height: 400px;
}

.checkout-container.hidden {
  display: none;
}
</style>
