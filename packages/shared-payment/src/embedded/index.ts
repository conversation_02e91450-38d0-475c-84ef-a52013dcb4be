import { loadStripe, Stripe, StripeEmbeddedCheckout } from '@stripe/stripe-js'

export interface EmbeddedCheckoutOptions {
  publicKey: string
  clientSecret: string
  onComplete?: () => void
  onError?: (error: Error) => void
  onLoadError?: (error: Error) => void
  appearance?: {
    theme?: 'stripe' | 'night' | 'flat'
    variables?: Record<string, string>
  }
}

export interface EmbeddedCheckoutResult {
  success: boolean
  checkout?: StripeEmbeddedCheckout
  error?: string
}

/**
 * Stripe Embedded Checkout 管理器
 * 提供现代化的嵌入式支付体验，无需页面跳转
 */
export class EmbeddedCheckoutManager {
  private stripe: Stripe | null = null
  private checkout: StripeEmbeddedCheckout | null = null
  private publicKey: string
  private isLoading = false

  constructor(publicKey: string) {
    this.publicKey = publicKey
  }

  /**
   * 初始化 Stripe SDK
   */
  private async initializeStripe(): Promise<Stripe> {
    if (this.stripe) {
      return this.stripe
    }

    try {
      this.stripe = await loadStripe(this.publicKey)
      if (!this.stripe) {
        throw new Error('Failed to load Stripe SDK')
      }
      return this.stripe
    } catch (error) {
      throw new Error(`Stripe initialization failed: ${error}`)
    }
  }

  /**
   * 创建嵌入式结账
   */
  async createEmbeddedCheckout(
    options: EmbeddedCheckoutOptions,
  ): Promise<EmbeddedCheckoutResult> {
    if (this.isLoading) {
      return { success: false, error: 'Checkout is already being created' }
    }

    this.isLoading = true

    try {
      const stripe = await this.initializeStripe()

      // 销毁现有的 checkout
      if (this.checkout) {
        this.checkout.destroy()
        this.checkout = null
      }

      // 创建新的 embedded checkout
      this.checkout = await stripe.initEmbeddedCheckout({
        clientSecret: options.clientSecret,
        onComplete: options.onComplete,
      })

      return {
        success: true,
        checkout: this.checkout,
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error'
      options.onError?.(new Error(errorMessage))

      return {
        success: false,
        error: errorMessage,
      }
    } finally {
      this.isLoading = false
    }
  }

  /**
   * 挂载到 DOM 元素
   */
  async mount(elementId: string): Promise<void> {
    if (!this.checkout) {
      throw new Error(
        'Checkout not initialized. Call createEmbeddedCheckout first.',
      )
    }

    const element = document.getElementById(elementId)
    if (!element) {
      throw new Error(`Element with id "${elementId}" not found`)
    }

    try {
      this.checkout.mount(element)
    } catch (error) {
      throw new Error(`Failed to mount checkout: ${error}`)
    }
  }

  /**
   * 卸载 checkout
   */
  unmount(): void {
    if (this.checkout) {
      this.checkout.unmount()
    }
  }

  /**
   * 销毁 checkout
   */
  destroy(): void {
    if (this.checkout) {
      this.checkout.destroy()
      this.checkout = null
    }
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      isInitialized: !!this.stripe,
      hasCheckout: !!this.checkout,
      isLoading: this.isLoading,
    }
  }
}

/**
 * 创建 Embedded Checkout 管理器的工厂函数
 */
export function createEmbeddedCheckoutManager(
  publicKey: string,
): EmbeddedCheckoutManager {
  return new EmbeddedCheckoutManager(publicKey)
}

/**
 * 便捷函数：一步创建并挂载 Embedded Checkout
 */
export async function createAndMountEmbeddedCheckout(
  publicKey: string,
  clientSecret: string,
  elementId: string,
  options?: Partial<EmbeddedCheckoutOptions>,
): Promise<EmbeddedCheckoutResult> {
  const manager = createEmbeddedCheckoutManager(publicKey)

  const result = await manager.createEmbeddedCheckout({
    publicKey,
    clientSecret,
    ...options,
  })

  if (result.success && result.checkout) {
    try {
      await manager.mount(elementId)
    } catch (error) {
      return {
        success: false,
        error: `Mount failed: ${error}`,
      }
    }
  }

  return result
}
