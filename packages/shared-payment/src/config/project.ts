import type {
  ProjectType,
  ProjectConfig,
  PaymentProvider,
} from '../types/index'

/**
 * 检测当前项目类型
 */
export function detectProjectType(): ProjectType {
  // 优先检查调试环境变量
  try {
    if (typeof globalThis !== 'undefined' && 'process' in globalThis) {
      const process = (globalThis as any).process
      if (process?.env) {
        const debugPaymentProvider =
          process.env.PAYMENT_PROVIDER ||
          process.env.VITE_PAYMENT_PROVIDER ||
          process.env.NUXT_PUBLIC_PAYMENT_PROVIDER
        if (debugPaymentProvider) {
          if (debugPaymentProvider === 'stripe') {
            return 'reelplay'
          }
          if (debugPaymentProvider === 'onlypay') {
            return 'playshot'
          }
        }
      }
    }
  } catch (error) {
    console.debug('Failed to access debug environment:', error)
  }

  // 检查 Nuxt runtime config
  if (typeof window !== 'undefined') {
    try {
      // 尝试获取 Nuxt 的 runtime config
      const nuxtApp = (window as any).$nuxt || (globalThis as any).$nuxt
      if (
        nuxtApp?.ssrContext?.runtimeConfig?.public?.paymentProvider ||
        nuxtApp?.$config?.public?.paymentProvider
      ) {
        const paymentProvider =
          nuxtApp.ssrContext?.runtimeConfig?.public?.paymentProvider ||
          nuxtApp.$config.public.paymentProvider
        if (paymentProvider === 'stripe') {
          return 'reelplay'
        }
        if (paymentProvider === 'onlypay') {
          return 'playshot'
        }
      }
    } catch (error) {
      console.debug('Failed to access Nuxt runtime config:', error)
    }

    // 检查 Vite 注入的环境变量
    const vitePaymentProvider =
      (window as any).__VITE_PAYMENT_PROVIDER__ ||
      (globalThis as any).__VITE_PAYMENT_PROVIDER__
    if (vitePaymentProvider) {
      if (vitePaymentProvider === 'stripe') {
        return 'reelplay'
      }
      if (vitePaymentProvider === 'onlypay') {
        return 'playshot'
      }
    }

    // 浏览器环境
    const hostname = window.location.hostname
    const title = document.title

    // 根据域名检测
    if (hostname.includes('playshot')) {
      return 'playshot'
    }
    if (hostname.includes('reelplay')) {
      return 'reelplay'
    }

    // 根据页面标题检测
    if (title.toLowerCase().includes('playshot')) {
      return 'playshot'
    }
    if (title.toLowerCase().includes('reelplay')) {
      return 'reelplay'
    }
  }

  // 服务器端环境 - 尝试访问环境变量
  try {
    // 检查是否有 process 对象可用
    if (typeof globalThis !== 'undefined' && 'process' in globalThis) {
      const process = (globalThis as any).process
      if (process?.env) {
        const appName =
          process.env.NUXT_PUBLIC_APP_NAME || process.env.VITE_WEBSITE_TITLE
        if (appName?.toLowerCase().includes('playshot')) {
          return 'playshot'
        }
        if (appName?.toLowerCase().includes('reelplay')) {
          return 'reelplay'
        }
      }
    }
  } catch (error) {
    // 忽略错误，继续执行
    console.debug('Failed to access process environment:', error)
  }

  // 默认返回 reelplay（保持向后兼容）
  return 'reelplay'
}

/**
 * 接受外部传入的项目类型参数
 */
export function detectProjectTypeWithConfig(
  debugPaymentProvider?: string,
): ProjectType {
  if (debugPaymentProvider) {
    if (debugPaymentProvider === 'stripe') {
      return 'reelplay'
    }
    if (debugPaymentProvider === 'onlypay') {
      return 'playshot'
    }
  }

  return detectProjectType()
}

/**
 * 获取项目配置
 */
export function getProjectConfig(): ProjectConfig {
  const projectType = detectProjectType()

  const configs: Record<ProjectType, ProjectConfig> = {
    playshot: {
      type: 'playshot',
      paymentProvider: 'onlypay',
      branding: {
        name: 'Playshot',
      },
    },
    reelplay: {
      type: 'reelplay',
      paymentProvider: 'stripe',
      branding: {
        name: 'ReelPlay',
      },
    },
  }

  return configs[projectType]
}

/**
 * 获取支付提供商配置
 */
export function getPaymentProvider(): PaymentProvider {
  const config = getProjectConfig()

  const providers: Record<PaymentProvider['name'], PaymentProvider> = {
    stripe: {
      name: 'stripe',
      displayName: 'Stripe',
      isAvailable: true,
    },
    onlypay: {
      name: 'onlypay',
      displayName: 'OnlyPay',
      isAvailable: true,
    },
  }

  return providers[config.paymentProvider]
}

/**
 * 检查是否为特定项目类型
 */
export function isProjectType(type: ProjectType): boolean {
  return detectProjectType() === type
}

/**
 * 检查是否为 Playshot
 */
export function isPlayshot(): boolean {
  return isProjectType('playshot')
}

/**
 * 检查是否为 ReelPlay
 */
export function isReelplay(): boolean {
  return isProjectType('reelplay')
}
