// 支付系统基础类型定义
export interface PaymentProvider {
  name: 'stripe' | 'onlypay'
  displayName: string
  isAvailable: boolean
}

export interface PriceItem {
  id: string
  name: string
  amount: number // 金额（分）
  coins: number // 虚拟币数量
  period: string
  extra: {
    background_url: string
    discount_percent: number
  }
}

export interface PaymentConfig {
  provider: PaymentProvider['name']
  publicKey?: string
  apiBase: string
  successUrl: string
  cancelUrl: string
}

export interface CreatePaymentParams {
  priceId: string
  redirectUrl: string
}

export interface PaymentResponse<T = any> {
  code: string
  message: string
  data: T
}

// Stripe 相关类型
export interface StripeCheckoutParams extends CreatePaymentParams {
  successUrl: string
  cancelUrl: string
}

export interface StripeCheckoutResponse {
  session_id: string
}

// Onlypay 相关类型
export interface OnlypayOrderParams extends CreatePaymentParams {
  priceId: string
  redirectUrl: string
}

export interface OnlypayOrderResponse {
  pay_url: string
  order_id?: string
}

// 项目类型检测
export type ProjectType = 'playshot' | 'reelplay'

export interface ProjectConfig {
  type: ProjectType
  paymentProvider: PaymentProvider['name']
  branding: {
    name: string
    logo?: string
  }
}

// API 响应通用格式
export interface ApiResponse<T = any> {
  code: string
  message: string
  data: T
}

// 支付状态
export interface PaymentState {
  isLoading: boolean
  error: string | null
  provider: PaymentProvider['name'] | null
  priceList: PriceItem[]
}

// 支付模式
export type PaymentMode = 'redirect' | 'embedded'

// 支付结果
export interface PaymentResult {
  success: boolean
  provider: PaymentProvider['name']
  mode?: PaymentMode // 支付模式
  redirectUrl?: string
  sessionId?: string
  clientSecret?: string // Embedded Checkout 需要的 client secret
  orderId?: string
  error?: string
  useStripeSDK?: boolean // 是否需要使用 Stripe SDK 进行重定向
}
