{"name": "shared-payment", "version": "1.0.0", "description": "Shared payment system for Magic Partner monorepo", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint src --ext .ts,.js", "type:check": "tsc --noEmit"}, "keywords": ["payment", "stripe", "onlypay", "shared"], "author": "Magic Partner Team", "license": "MIT", "dependencies": {"@stripe/stripe-js": "^2.4.0"}, "devDependencies": {"typescript": "5.9.0-beta", "tsup": "^7.0.0", "eslint": "^8.0.0"}, "peerDependencies": {"typescript": "5.9.0-beta"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"]}