{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": ["env.d.ts", "src/**/*", "src/**/*.vue"],
  "exclude": ["node_modules", "dist", "src/**/__tests__/*"],

  "compilerOptions": {
    /* 项目结构 */
    "composite": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    },

    /* 编译目标与库 */
    "target": "ES2022", // 更现代的 JS 特性
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "moduleResolution": "Bundler", // 更好支持 Vite 的打包模式
    "types": ["vite/client", "node"],

    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "noImplicitThis": true,
    "alwaysStrict": true,

    /* Vue / JSX 支持 */
    "jsx": "preserve", // 保留 JSX，交给插件处理
    "allowJs": true, // 支持 JS 文件
    "checkJs": false, // 不检查 JS 文件

    /* 导入与模块 */
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "verbatimModuleSyntax": false,
    "resolveJsonModule": true,
    "isolatedModules": true,

    /* 输出优化 */
    "skipLibCheck": true, // 跳过依赖检查，加快编译
    "forceConsistentCasingInFileNames": true
  },

  /* Vue 编译器选项 */
  "vueCompilerOptions": {
    "globalTypesPath": "./src/env.d.ts"
  }
}
