# CSR应用 Dockerfile
FROM node:18-alpine AS base

# 安装pnpm
RUN npm install -g pnpm

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package.json pnpm-lock.yaml ./

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建应用
ARG NODE_ENV=production
ARG VITE_DEPLOYMENT_ENV=production
ENV NODE_ENV=$NODE_ENV
ENV VITE_DEPLOYMENT_ENV=$VITE_DEPLOYMENT_ENV

RUN pnpm build

# 生产阶段
FROM nginx:alpine AS production

# 复制构建产物
COPY --from=base /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
