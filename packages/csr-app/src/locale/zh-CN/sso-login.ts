export default {
  'sso-login.authCodeLogin': '验证码登录',
  'sso-login.passwordLogin': '密码登录',
  'sso-login.phoneNumberOrMailbox': '手机号或邮箱',
  'sso-login.phoneNumber': '手机号',
  'sso-login.password': '密码',
  'sso-login.noteAuthCode': '短信验证码',
  'sso-login.sendAuthCode': '发送验证码',
  'sso-login.resendSecond': '秒后重新发送',
  'sso-login.userNickname': '用户昵称',
  'sso-login.confirmPassword': '确认密码',
  'sso-login.phoneAreaCode': '+86',
  'sso-login.inviteCode': '邀请码（选填）',
  'sso-login.retrievePassword': '找回密码',
  'sso-login.authCode': '验证码',
  'sso-login.modifyPassword': '修改密码',
  'sso-login.readAndAgree': '我已阅读并同意',
  'sso-login.userAgreement': '《用户协议》',
  'sso-login.and': '与',
  'sso-login.privacyPolicy': '《隐私政策》',
  'sso-login.login': '登录',
  'sso-login.signin': '注册',
  'sso-login.nextStep': '下一步',
  'sso-login.haveAccount': '已有账号？',
  'sso-login.loginNow': '立即登录',
  'sso-login.noAccount': '还没有账号？',
  'sso-login.signinNow': '立即注册',
  'sso-login.passwordFormatErrorTip': '密码格式不正确, 必须要有字母和数字，密码长度8～30位',
  'sso-login.twoDifferentPassword': '两次密码不相同',
  'sso-login.phoneNumberFormatError': '手机号码格式不正确',
  'sso-login.notExit': '不存在',
  'sso-login.userNotExit': ' 用户不存在',
  'sso-login.authCodeSendSuccess': '验证码发送成功',
  'sso-login.getUserInfoFail': '获取用户信息失败',
  'sso-login.loginSuccess': '登录成功',
  'sso-login.inputPhoneOrMailbox': '请输入手机号或邮箱',
  'sso-login.inputPhoneOrMailboxNotNickname': '请输入正确的手机号或邮箱，而不是用户昵称',
  'sso-login.inputPassword': '请输入密码',
  'sso-login.inputAuthCode': '请输入验证码',
  'sso-login.guideAgree': '请同意《用户协议》与《隐私政策》',
  'sso-login.writePhoneLoginTip': ' 用户不存在，请填写手机号码登录',
  'sso-login.inputUserNickname': '请输入用户昵称',
  'sso-login.inputConfirmPassword': '请输入确认密码',
  'sso-login.notInvite': '未邀请',
  'sso-login.jumpLoginPage': '注册成功，为你跳转到登录页面',
  'sso-login.inputUserName': '请输入用户名'
}
