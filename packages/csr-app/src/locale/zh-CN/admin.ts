export default {
  // index-keep-alive.vue
  'admin.worksLibrary': '作品库',
  'admin.actorLibrary': '形象库',
  'admin.user': '用户',
  // project-list.vue
  'admin.workID': '作品ID',
  'admin.inputWorkID': '请输入作品ID',
  'admin.workName': '作品名称',
  'admin.inputWorkName': '请输入作品名称',
  'admin.userName': '用户名称',
  'admin.inputUserName': '请输入用户名称',
  'admin.userId': '用户id',
  'admin.inputUserId': '请输入用户id',
  'admin.workStyle': '作品风格',
  'admin.publishStatus': '发布状态',
  'admin.category': '分类',
  'admin.activity': '活动',
  'admin.sortRule': '排序规则',
  'admin.ratioRule': '横竖屏',
  'admin.search': '搜索',
  'admin.noWorkData': '~ 暂无作品数据 ～',
  'admin.pinnedValue': '置顶值',
  'admin.tipText': '提示：2000 以上影响热门榜单，1000～2000影响首页重磅，1000 以下影响活动专区',
  'admin.inputNumber': '请输入数字',
  'admin.chosenValue': '精选值',
  'admin.influenceFactorAnnotation': '该值影响推荐精选',
  'admin.likeNumber': '点赞数',
  'admin.workCategory': '作品分类',
  'admin.selectCategory': '请选择分类',
  'admin.workJoinActivity': '作品参与活动',
  'admin.selectActivity': '请选择活动',
  'admin.confirmDeleteTip': '确定要删除该作品吗',
  'admin.minusCreditTip': '原作品对应获得的{credit}积分将被扣除',
  'admin.hotRank': '热门榜单',
  'admin.heavyRecommend': '重磅推荐',
  'admin.hottest': '最热',
  'admin.publishTime': '发布时间',
  'admin.createTime': '创建时间',
  'admin.public': '公开',
  'admin.private': '私有',
  'admin.draft': '草稿',
  'admin.deleteSuccess': '删除成功',
  'admin.updateSuccess': '更新成功',
  // role-list.vue
  'admin.gender': '性别：',
  'admin.age': '年龄：',
  'admin.style': '风格：',
  'admin.typeAll': '类型：全部',
  'admin.type': '类型：',
  'admin.bottomTip': '～ 已经到底了 ～',
  'admin.noRoleData': '～ 暂无形象数据 ～',
  'admin.child': '儿童',
  'admin.teenager1': '少年',
  'admin.teenager2': '青年',
  'admin.middleAge': '中年',
  'admin.elderly': '老年',
  // user-list.vue
  'admin.userID': '用户ID',
  'admin.inputUserID': '请输入用户ID',
  'admin.selfIntroduction': '自我介绍',
  'admin.inputUserSelfIntroduction': '请输入用户自我介绍',
  'admin.userGender': '用户性别',
  'admin.phoneNumber': '手机号',
  'admin.inputUserPhoneNumber': '请输入用户手机号',
  'admin.userMailbox': '用户邮箱',
  'admin.inputUserMailbox': '请输入用户邮箱',
  'admin.addCredit': '添加积分',
  'admin.addPayCredit': '添加梦币',
  'admin.selectAll': '全选',
  'admin.noUserData': '~ 暂无用户数据 ~',
  'admin.addCreditForUsers': '为以下用户添加积分',
  'admin.userNameLabel': '用户名称: ',
  'admin.userIDLabel': '用户ID: ',
  'admin.creditTitle': '积分标题',
  'admin.inputCredit': '请输入积分',
  'admin.credit': '积分',
  'admin.selectUser': '请选择用户',
  'admin.inputRightCredit': '请输入正确积分',
  'admin.maxCreditTip': '用户最大积分值为20万'
}
