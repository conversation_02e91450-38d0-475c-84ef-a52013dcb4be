export default {
  // index.vue
  'user-center.myWork': '我的作品 ',
  'user-center.loading': '(加载中)',
  'user-center.myRoleLibrary': '我的形象库 ',
  // my-project.vue
  'user-center.searchMyWork': '搜索我的作品',
  'user-center.createNewWork': '创建新作品',
  'user-center.guideTryCreateWork': '暂无作品内容，试试创建作品',
  'user-center.modifyProjectName': '修改项目名称',
  'user-center.confirmDeleteWorkOr': '确定要删除该作品吗',
  'user-center.minusCreditTip': '原作品对应获得的{credit}积分将被扣除',
  'user-center.deleteSuccess': '删除成功',
  'user-center.emptyProjectNameTip': '项目名称不能为空',
  'user-center.modifySuccess': '修改成功',
  // my-roles.vue
  'user-center.searchMyRole': '搜索我的形象',
  'user-center.createActor': '创建形象',
  'user-center.noRoleData': '～ 暂无形象数据 ～',
  // process-modal.vue
  'user-center.doneSoonTip': '马上就好，不要走开',
  'user-center.gotIt': '知道了'
}
