export default {
  // activity-card
  'components.activity-card.xiaobai': '小白',
  'components.activity-card.vote': '票数 {count} ·',
  'components.activity-card.workNum': '作品 {count}',
  'components.activity-card.invite': '邀请',
  'components.activity-card.newFriendNum': '位新朋友',
  'components.activity-card.view': '查看',

  // activity-header
  'components.activity-header.activityName': '2024年度AIGC整活大赛',

  // box-select
  'components.box-select.selectLeast': '至少选择{count}个',
  'components.box-select.selectMost': '最多选择{count}个',

  // comment
  'components.comment.noUserId': '该用户不存在',
  'components.comment.comment': '评论',
  'components.comment.writeToo': '有趣的评论千千万，不如你也来一条？',
  'components.comment.submit': '发布',
  'components.comment.reply': '回复',
  'components.comment.deleteTheComment': '删除该评论',
  'components.comment.replyUser': '回复{user}',
  'components.comment.authorThinkLike': '作者觉得很赞',
  'components.comment.me': '我',
  'components.comment.author': '作者',
  'components.comment.packUp': '收起',
  'components.comment.replyNum': '共{count}条回复,',
  'components.comment.clickToView': '点击查看',
  'components.comment.loginUnlockAllContent': '登录解锁所有内容',
  'components.comment.afterLoginTip': '登录后可以看到全部回复和参与互动哦～',
  'components.comment.login': '登录',
  'components.comment.getReplyFail': '获取回复失败',
  'components.comment.inputCommentContent': '请输入评论内容',
  'components.comment.addCredit': '评论发布成功，积分+{credit}',
  'components.comment.commentSubmitSuccess': '评论发布成功',
  'components.comment.commentSubmitFail': '评论发布失败',
  'components.comment.getCommentFail': '获取评论失败',
  'components.comment.deleteComment': '删除评论',
  'components.comment.confirmDeleteCommentOr': '删除评论后后不可恢复，是否确定删除该评论？',
  'components.comment.deleteSuccess': '删除成功',
  'components.comment.deleteFail': '删除失败',
  'components.comment.likeFail': '点赞失败',
  'components.comment.cancelLikeFail': '取消点赞失败',
  'components.comment.stepOnFail': '点踩失败',
  'components.comment.cancelStepOnFail': '取消点踩失败',
  'components.comment.likeSuccess': '点赞成功',
  'components.comment.cancelLikeSuccess': '取消点赞成功',
  'components.comment.stepOnSuccess': '点踩成功',
  'components.comment.cancelStepOnSuccess': '取消点踩成功',

  // creator-card
  'components.creator-card.voteCount': '{count}票',
  'components.creator-card.workCount': '{count}个作品',

  // empty
  'components.empty.noContent': '暂无内容',

  // error-tip
  'components.error-tip.errorRefreshRetry': '出现错误，请刷新重试',

  // footer
  'components.footer.companyName': '沈阳霏航网络科技有限公司',

  // img-card
  'components.img-card.noPhoto': '暂无图片',

  // loading-state
  'components.loading-state.schedule': '当前进度 {count}% 马上就好，不要走开～',
  'components.loading-state.generatingStoryboard': '正在生成分镜',
  'components.loading-state.soon': '马上就好',
  'components.loading-state.generationTime': '生成时长',
  'components.loading-state.secondNum': '{count}秒',
  'components.loading-state.minuteNum': '{count}分钟',
  'components.loading-state.estimatedTime': '{prefix}预计{desc}，马上就好',

  // my-card
  'components.my-card.estimatedSecond': '预计{count}秒后完成',
  'components.my-card.finishSoon': '即将完成',
  'components.my-card.pinned': '置顶',
  'components.my-card.winnow': '精选',
  'components.my-card.notClassify': '未分类',
  'components.my-card.editIn': '编辑于 {time}',
  'components.my-card.viewNum': '浏览量',
  'components.my-card.transpondNum': '转发量',
  'components.my-card.workInGen': '作品正在生成中，请稍后再试',

  // notify-modal
  'components.notify-modal.createFinish': '创建完成',
  'components.notify-modal.workCreateFinish': '您的作品{work}已创建完成。快去查看吧~',
  'components.notify-modal.ok': '好的',
  'components.notify-modal.createNow': '立即去创作',
  'components.notify-modal.workVideoGenDone': '您的作品{video}视频已生成完成',
  'components.notify-modal.submitOkThenDownload': '成功发布后可以下载您的作品哦',
  'components.notify-modal.preview': '预览',
  'components.notify-modal.toSubmit': '去发布',
  'components.notify-modal.genDone': '生成完毕',
  'components.notify-modal.roleGenDone': '您的形象{name}已生成完毕，快去查看吧~',
  'components.notify-modal.toView': '去查看',
  'components.notify-modal.trainFail': '训练失败',
  'components.notify-modal.roleTrainFail': '您的形象{name}，训练失败，具体原因请点击查看',

  // rank-card
  'components.rank-card.portray': '饰演{name} ',

  // reward-button
  'components.reward-button.addCreditNum': '(加{count}积分)',

  // role-card
  'components.role-card.viewMagnumOpus': '查看代表作',
  'components.role-card.recentAppearance': '近期出演 {count}',
  'components.role-card.creatorName': '创作者：{name}',

  // top-back
  'components.top-back.top': '顶部'
}
