export default {
  // index.vue
  'new-editor.queueWaiting': '队列等待中',
  'new-editor.storyAnalyzing': '剧情解析中',
  'new-editor.storyboardGenerating': '分镜生成中',
  'new-editor.projectSaveTip': '编辑中项目已经保存在「我的作品」',
  // intro.ts
  'new-editor.jump': '跳过',
  'new-editor.gotIt': '知道了',
  'new-editor.setImageForRole': '为角色设置形象',
  'new-editor.recognizedRoleTip': '我们已经从文本识别到角色，点击左侧为你的角色设置形象吧。',
  'new-editor.editRole': '编辑角色',
  'new-editor.dismantledRoleTip': '已经为你拆解出所有角色，选择该角色可进入【角色设定】',
  'new-editor.adjustRoleSettings': '调整角色设定',
  'new-editor.modifySettingsHere': '这里可以修改角色名称、性别和角色配音。',
  'new-editor.selectCharacterImage': '选择人物形象',
  'new-editor.selectFavoriteActorGuide':
    '确定人物档案后，在右侧形象池中选择你最心仪的角色，别忘了保存当前角色形象哦！',
  'new-editor.createActor': '创建角色',
  'new-editor.quickCreateActorImage':
    '如果角色库中没有你需要的角色形象，你可以在此快速创建你需要的角色形象！',
  'new-editor.intoEditor': '进入编辑器',
  'new-editor.intoEditorTip':
    '以此类推，完成全部未编辑的角色设定后，点击【下一步】进入编辑器。后续也可以点击左侧角色，进行角色形象调整。',
  'new-editor.nextStep': '下一步>',
  'new-editor.editParagraph': '编辑段落',
  'new-editor.editParagraphTip':
    '一个段落对应一个画面，每个画面可以在右侧编辑修改字幕、出镜角色、分镜等。',
  'new-editor.paragraphAddMinus': '段落增加/删减',
  'new-editor.paragraphAddMinusTip': '点击对应段落右上角可在段落后【新增段落】与【删除段落】',
  'new-editor.deleteAddRole': '删除/新增当前段落出镜角色',
  'new-editor.deleteAddRoleTip': '点击【编辑】可新增/删除出镜角色',
  'new-editor.graphicsStoryboardSelect': '画面分镜-选择',
  'new-editor.graphicsStoryboardSelectTip': '点击可以选择对应分镜。',
  'new-editor.graphicsStoryboardRefresh': '画面分镜-刷新',
  'new-editor.graphicsStoryboardRefreshTip':
    '若段落内出镜角色发生变动，需确认无误后点击【重新生成分镜】',
  'new-editor.generateImage': '生成图片',
  'new-editor.generateImageTip': '确定全部段落分镜后，点击开始生成你的画面吧！',
  'new-editor.modifySubtitles': '修改字幕',
  'new-editor.modifySubtitlesTip': '仅展示字幕文案变动，不影响当前画面生成。',
  'new-editor.replaceGraphicsImage': '替换画面图片',
  'new-editor.replaceGraphicsImageTip': '选中当前段落画面，选择替换不同的分镜画面',
  'new-editor.generateNewGraphics': '生成新画面',
  'new-editor.generateNewGraphicsTip': '如果没有满意的画面，可以点击这里生成新画面',
  'new-editor.modifyImageAddMinusRole': '编辑图片-新增/删减角色',
  'new-editor.modifyImageAddMinusRoleTip':
    '点击【修改角色】对当前画面的角色进行新增/删减，角色变动后需刷新下方分镜列表哦！',
  'new-editor.refreshStoryboardList': '刷新分镜列表',
  'new-editor.refreshStoryboardListTip':
    '若对生成的分镜不满意，可点击【重新生成】重新刷新分镜。注意，旧的分镜无法保存哦！',
  'new-editor.editImageGenNewImage': '编辑图片-生成新的图片',
  'new-editor.editImageGenNewImageTip':
    '选择一个你最心仪的分镜，点击【生成图片】开始生成（生成后将替换原替换图片哦~）',
  // audio-select.vue
  'new-editor.pleaseChoose': '请选择',
  'new-editor.tryListen': '试听',
  'new-editor.noChooseDubTip': '该人物没有选择配音，无法试听',
  'new-editor.previewAudioFail': '预览音频失败',
  // captcha-modal.vue
  'new-editor.inputAuthCode': '请输入验证码',
  // caption-modal.vue
  'new-editor.image2videoSound': '朗读字幕',
  // animation-prompt-modal.vue
  'new-editor.animationPrompt': '请结合画面，描述你想要的动态效果',
  'new-editor.animationPromptDefaultText':
    '示例: 在落日的余晖中，人们在公园里悠闲地打着太极。如提示词为空，则系统自动优化生成动态视频',
  'new-editor.animationPromptEmpty': '请输入提示词',
  // card-state.vue
  'new-editor.generating': '生成中 ',
  'new-editor.noGraphics': '暂无画面',
  'new-editor.generateFail': '生成失败',
  'new-editor.generatingStoryboard': '正在生成分镜',
  // error-page.vue
  'new-editor.noProjectPrivilege': '没有项目权限',
  'new-editor.projectNotFound': '项目不存在，请检查链接或确定项目是否已删除',
  'new-editor.pullProjectDataError': '拉取项目数据出现错误',
  'new-editor.refreshRetry': '刷新重试',
  'new-editor.backHome': '返回首页',
  // grid-select.vue
  'new-editor.nowSeleted': '当前选中',
  'new-editor.atLeastSelect': '至少选择{count}个',
  'new-editor.atMostSelect': '最多选择{count}个',
  // header.vue
  'new-editor.warmTip': '温馨提示',
  'new-editor.banEditTip': '生成视频将会耗时2分钟，\n生成中不可编辑',
  'new-editor.generateVideo': '生成视频',
  'new-editor.giveUpPublishWorkOr': '是否放弃发布作品?',
  'new-editor.confirmGenerateImageOr': '确定生成图片吗',
  'new-editor.consumeLotsCredit': '此次生成将消耗大量积分',
  'new-editor.creditInsufficient': '积分不足，可减少画面数',
  'new-editor.creditUsePay': '积分不足，剩余 {credit} 积分', // TODO:en
  'new-editor.creditUsePayDesc':
    '生成图片共消耗 {creditTotal} 积分，额外还需 {creditNeed} 积分或梦币', // TODO:en
  'new-editor.image2VideoCreditUsePayTitle': '梦币不足，剩余{credit}梦币', // TODO:en
  'new-editor.image2VideoCreditUsePayDesc': '转动态Pro需消耗 200 梦币，额外还需 {creditNeed} 梦币', // TODO:en
  'new-editor.creditNotEnoughTitle': '梦币及积分不足，剩余{credit}积分及{payCredit}梦币',
  'new-editor.creditNotEnoughDesc':
    '生成图片共消耗 {creditTotal} 积分，额外还需 {creditNeed} 积分或梦币', // TODO:en
  'new-editor.usePayCredit': '其余用梦币生成', // TODO: en
  'new-editor.toPayCredit': '购买梦币', // TODO: en
  'new-editor.earnCreditResumeGen': '去赚积分继续生成',
  'new-editor.toEarnCredit': '去赚积分',
  'new-editor.back': '返回',
  'new-editor.nextStep2': '下一步',
  'new-editor.regenImage': '重新生图',
  'new-editor.backLastStep': '重新编辑',
  'new-editor.publish': '发布',
  'new-editor.publishReward': '(奖励{credit}积分)',
  'new-editor.partImageGenBlock': '部分图片生成可能会卡顿，点击重新生成',
  'new-editor.regenBlockImage': '重新生成卡顿图片',
  'new-editor.regenAllImage': '重新生成全部图片',
  'new-editor.pleaseSetImageForRole': '请为角色设置形象',
  'new-editor.videoCompositingTip': '视频正在合成中，请耐心等待',
  'new-editor.emptyWorkNameTip': '作品名称不能为空',
  'new-editor.download': '下载',
  'new-editor.downloadVideo': '下载视频',
  'new-editor.downloadAllPhoto': '下载全部图片 ({count}张)',
  'new-editor.downloadFailed': '下载失败，请检查网络情况',
  'new-editor.genVideoRatio': '生成视频设置',
  'new-editor.genVideoRatioLabel': '视频比例',
  'new-editor.genVideoCaptureLabel': '旁白字幕',
  'new-editor.genVideoCaptureShow': '显示',
  'new-editor.genVideoCaptureHide': '隐藏',
  'new-editor.pleaseSelectVideoRatio': '请选择生成视频比例',
  // image-score.vue
  'new-editor.expectYourFeedback': '期待您的反馈',
  'new-editor.thanksForFeedback': '您的反馈可以帮我们变得更好，谢谢！',
  'new-editor.others': '其他',
  'new-editor.addAdditionalFeedback': '请在这里补充其它反馈...',
  'new-editor.submit': '提交',
  'new-editor.satisfaction': '满意度',
  'new-editor.badOption1': '画面生成瑕疵',
  'new-editor.badOption2': '人物不一致',
  'new-editor.badOption3': '生成的动作不相关',
  'new-editor.badOption4': '生成的场景不相关',
  'new-editor.goodOption1': '画面效果好',
  'new-editor.goodOption2': '生成速度快',
  'new-editor.goodOption3': '人物一致',
  'new-editor.goodOption4': '生成场景一致',
  // image-selects.vue
  'new-editor.generatingImage': '正在生成图片',
  'new-editor.historyImage': '历史图片 ({count})',
  // layout-item.vue
  'new-editor.uploadFromLocal': '从本地上传',
  // layout-modal.vue
  'new-editor.editStoryboard': '编辑分镜',
  'new-editor.storyboardRegenerating': '分镜重新生成中',
  'new-editor.dataErrorRefreshWeb': '数据错误，请刷新网页',
  // layout-selects.vue
  'new-editor.nowParagraphStoryboard': '当前段落分镜 ({count})',
  'new-editor.storyboardGenUnfinish': '分镜还没生成完，不能选择',
  // layout-template-modal.vue
  'new-editor.genStoryboard': '生成分镜',
  'new-editor.framing': '分镜构图',
  'new-editor.storyboardTag': '分镜标签(使用逗号分隔)',
  'new-editor.inputStoryboardTag': '请输入分镜标签',
  'new-editor.uploadFraming': '请上传分镜构图',
  'new-editor.writeStoryboardTag': '请填写分镜标签',
  // loading-state.vue
  'new-editor.GenFailClickRetry': '生成失败, 点击重试',
  // lora-image-card.vue
  'new-editor.author': '作者：',
  'new-editor.recentAppear': '出演 {count} 次',
  'new-editor.withProjectStyle': '与项目风格',
  'new-editor.styleUnmatch': '风格不匹配',
  // lora-select-list.vue
  'new-editor.noMore': ' 没有更多了~ ',
  'new-editor.createRole': '创建新形象',
  'new-editor.createRoleTip': '没有找到满意的形象？点击',
  'new-editor.thisImageSelectedOr': '该形象已经被选中，是否确定？',
  // main-panel.vue
  'new-editor.editAttributeGuide': '可在右侧面板编辑属性',
  'new-editor.addSubtitlesText': '添加字幕文案',
  'new-editor.imageChangingDynamic': '图片转动态中',
  'new-editor.keepGenerating': '退出或返回静态AI会继续生成',
  'new-editor.imageInGenQueue': '图片生成队列中',
  'new-editor.imageInChangeQueue': '图片转动态队列中',
  'new-editor.imageInGen': '图片生成中',
  'new-editor.exitKeepGen': '退出后 AI 会继续生成',
  'new-editor.inRegenStoryboard': '重新生成分镜中',
  // paragraph.vue
  'new-editor.copyParagraph': '复制段落',
  'new-editor.deleteParagraph': '删除段落',
  'new-editor.paragraphInGen': '该段落正在生成中, 无法删除',
  'new-editor.onlyOneParagraph': '该项目只有一个段落，不能删除',
  // previewer.vue
  'new-editor.chooseCover': '选择封面',
  'new-editor.videoInGen': '视频生成中',
  'new-editor.videoInGenSubtitle': '退出AI会继续生成',
  'new-editor.videoPreview': '视频预览',
  'new-editor.imageTextPreview': '图文预览',
  // progress-bar.vue
  'new-editor.writeStory': '编写故事',
  'new-editor.roleSetting': '角色设定',
  'new-editor.chooseStoryboard': '选择分镜',
  'new-editor.editImage': '编辑图片',
  'new-editor.previewVideo': '预览视频',
  // props-panel.vue
  'new-editor.cameraRole': '出镜角色',
  'new-editor.modifyCameraRole': '修改出镜角色',
  'new-editor.nowStoryboard': '当前分镜',
  'new-editor.modifyStoryboard': '修改分镜',
  'new-editor.graphicsTipWord': '画面提示词',
  'new-editor.clickViewSkill': '💡点这里查看技巧',
  'new-editor.mentionPlaceholder':
    "示例:一个人站在一家现代电影院前，电影院有着光滑的玻璃外墙和一个展示当前放映影片的大型广告牌，周围摆放着各种零食。 {'@'}小明 抱着零食，表情兴奋",
  'new-editor.backStatic': '返回静态',
  'new-editor.dynamicMouth': '动态口型',
  'new-editor.changeDynamic': '转动态',
  'new-editor.changeDynamicAgain': '重新转动态',
  'new-editor.changeDynamicPro': '转动态Pro',
  'new-editor.redrawNowGraphics': '重绘画面',
  'new-editor.retryTask': '重试任务',
  'new-editor.backEdit': '返回编辑',
  'new-editor.feedbackTitle': '您觉得当前画面有瑕疵吗?',
  'new-editor.animationPromptConfig1': '根据画面智能生成',
  'new-editor.animationPromptConfig2': '自定义提示词生成',
  'new-editor.feedbackYes': '有',
  'new-editor.feedbackNo': '没有',
  // publish-modal.vue
  'new-editor.confirmWorkOr': '确定发布作品？',
  'new-editor.AfterPublishTip': '发布后用户可以浏览、评论、点赞您的作品',
  'new-editor.publishFail': '发布失败',
  'new-editor.publishSuccessAddCredit': '作品发布成功,积分+100',
  'new-editor.workPublishSuccess': '作品发布成功',
  'new-editor.publishSuccessToDetail': '发布成功，正在跳转到详情页',
  // role-editor.vue
  'new-editor.save': '保存',
  'new-editor.editRoleSetting': '编辑角色设定',
  'new-editor.addRoleSetting': '新增角色设定',
  'new-editor.nowRole': '当前角色',
  'new-editor.setRoleImage': '请设置角色形象',
  'new-editor.roleName': '角色名',
  'new-editor.waitName': '待命名',
  'new-editor.roleAttribute': '性别',
  'new-editor.roleDub': '配音',
  'new-editor.searchImage': '搜索形象',
  'new-editor.gender': '性别：',
  'new-editor.age': '年龄：',
  'new-editor.useRecently': '最近使用',
  'new-editor.myCreation': '我创建的',
  'new-editor.noLimit': '不限',
  'new-editor.title': '形象库',
  // role-list.vue
  'new-editor.roleInGraphicsLocation': '角色在画面中的位置',
  'new-editor.left': '左',
  'new-editor.right': '右',
  'new-editor.exchange': '互换',
  'new-editor.modifyRole': '修改角色',
  'new-editor.setImage': '设置形象',
  'new-editor.edit': '编辑',
  'new-editor.addRole': '新增角色',
  'new-editor.banChangeCharacter': '图片生成中，不允许更改人物',
  // role-modal.vue
  'new-editor.modifyRoleSetting': '修改角色设定',
  'new-editor.finish': '完成',
  'new-editor.guideConnectAssistant': '数据异常，请联系小助手',
  'new-editor.cameraRoleCountChange': '出镜角色数量变化，帮您重新生成分镜',
  'new-editor.saveStoryboard': '正在为您保存分镜',
  // role-select-list.vue
  'new-editor.appendRole': '添加角色',
  // search-layout.vue
  'new-editor.inputDesiredStoryboard': '请输入你想要的分镜',
  'new-editor.searchStoryboard': '搜分镜',
  'new-editor.swapABatch': '换一批',
  'new-editor.searchResult': '搜索结果({count})',
  'new-editor.inputSearchKeyword': '请输入搜索关键词',
  // side-bar.vue
  'new-editor.voiceOver': '旁白',
  'new-editor.voiceSpeed': '语速',
  'new-editor.bgm': '音乐',
  'new-editor.paragraph': '自动识别{count}个段落',
  'new-editor.confirmCopyParagraph': '确定复制该段落？',
  'new-editor.confirmDeleteParagraph': '确定删除该段落？',
  // starter.vue
  'new-editor.writeAStoryBySelf': '自己写一个故事',
  'new-editor.style': '风格',
  'new-editor.storyTitle': '故事标题',
  'new-editor.takeATitle': '给你的故事起个标题吧',
  'new-editor.storyMainBody': '故事正文',
  'new-editor.aspectRatio': '画面比例',
  'new-editor.portrait': '竖屏',
  'new-editor.landscape': '横屏',
  'new-editor.descriptionReference':
    '参考示例：\n在一座阴森的古堡里，德古拉公爵坐在丝绒沙发上闭目养神，他的爱宠是一只德牧，正趴在他的脚下。突然，窗户处传来了响声，德牧警惕地站了起来，而德古拉公爵只是抬了抬眼。',
  'new-editor.descriptionTip': '按句号/问号/感叹号可划分句子，每一句生成一幅画面',
  'new-editor.deconstructionStoryboard': '拆解分镜',
  'new-editor.boardCount': '已智能生成{count}个分镜',
  'new-editor.boardTip':
    '💡每个分镜会生成1张图片。无文本分镜则自动合并。{maxLength}字的文案建议分镜数在120个以内。按回车键在光标处拆分新分镜，点击右边箭头向上合并分镜。',
  'new-editor.mergeUp': '向上合并分镜',
  'new-editor.deleteConfrim': '确定删除该段落？',
  // step2.vue
  'new-editor.setImageForYourRole': '请为你的角色设置形象',
  'new-editor.setImageGuide': '已根据故事为你识别到 {count} 个角色，请点击角色卡片设置形象',
  'new-editor.autoSelectRole': '一键选形象',
  // template-modal.vue
  'new-editor.pulishProjectAsTemplate': '发布该项目为模板',
  'new-editor.templateName': '模版名称',
  'new-editor.templateDescription': '模版描述',
  'new-editor.relatedActivity': '关联活动',
  'new-editor.haveNot': '无',
  'new-editor.carryImageOr': '是否携带图片',
  'new-editor.publishSuccess': '发布成功',
  'new-editor.pullActivityListFail': '拉取活动列表失败',
  // voice-modal.vue
  'new-editor.speakRole': '发言角色',
  'new-editor.changeRoleTimbre': '更改角色音色',
  'new-editor.tryListenDialogue': '试听对白',
  'new-editor.stopTryListen': '停止试听',
  'new-editor.updateDubFail': '更新配音失败'
}
