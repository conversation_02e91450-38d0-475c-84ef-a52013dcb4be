export default {
  // index.vue
  'role-detail.dataLoading': '数据加载中',
  'role-detail.author': '作者',
  'role-detail.introduction': '介绍',
  'role-detail.category': '类型',
  'role-detail.public': '公开',
  'role-detail.private': '私密',
  'role-detail.turnToPublic': '转为公开',
  'role-detail.editProfile': '编辑资料',
  'role-detail.deleteRole': '删除形象',
  'role-detail.participatingWork': '参演作品',
  'role-detail.noParticipatingWork': '~ 暂无出演作品 ~',
  'role-detail.otherActor': '作者的其他形象',
  'role-detail.infoListLabel1': '形象',
  'role-detail.infoListContent1': '形象的内容',
  'role-detail.infoListLabel2': '性别',
  'role-detail.infoListLabel3': '年龄',
  'role-detail.infoListLabel4': '风格',
  'role-detail.infoListLabel5': '介绍',
  'role-detail.infoListContent5': '介绍的内容',
  'role-detail.publicSuccess': '公开成功',
  // artifact-card.vue
  'role-detail.act': '饰演',
  // delete-role.vue
  'role-detail.confirmDeleteRoleOr': '确定要删除该形象吗？',
  'role-detail.deleteThenReomve': '删除后将从形象库中移除',
  'role-detail.deleteSuccess': '删除成功',
  // edit-info.vue
  'role-detail.save': '保存',
  'role-detail.modifyInfo': '修改信息',
  'role-detail.coverSetting': '封面设置',
  'role-detail.currentCover': '当前封面',
  'role-detail.nickName': '昵称',
  'role-detail.inputNickname': '请输入昵称',
  'role-detail.inputIntroduction': '请输入介绍',
  'role-detail.selectCover': '请选择封面',
  'role-detail.modifySuccess': '修改成功'
}
