import axios from 'axios'

export interface Tag {
  name: string
  color: string
  bg_color: string
}

export interface Actor {
  id: string
  name: string
  subtitle: string
  avatar_url: string
  preview_url: string
  hot: number
  tags: Tag[]
}

export interface ActorListResponse {
  code: string
  message: string
  data: {
    actors: Actor[]
  }
}

/**
 * 获取角色列表
 */
export async function fetchActorList(): Promise<ActorListResponse> {
  const { data } = await axios.get<ActorListResponse>('/api/v1/actor.list')
  return data
}

/**
 * 格式化数字（如将1000转换为1k）
 */
export function formatNumber(num: number): string {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}
