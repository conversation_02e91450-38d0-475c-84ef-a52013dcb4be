import axios from 'axios'
import type { ApiResponse } from '@/types/api'

export interface PromptVariable {
  default: string
  description: string
  key: string
  name: string
  type: string
}

export interface PromptTemplate {
  id: string
  name: string
  scope: string
  description: string
  prompt: string
  variables: PromptVariable[]
}

export interface PromptTemplateListResponse {
  prompt_templates: PromptTemplate[]
}

/**
 * Fetches the list of prompt templates from the API
 * @returns Promise with the list of prompt templates
 */
export const getPromptTemplates = () => {
  return axios.get<ApiResponse<PromptTemplateListResponse>>('/api/v1/prompt/template.list')
}
