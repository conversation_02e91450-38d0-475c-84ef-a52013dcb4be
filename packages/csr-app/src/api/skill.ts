import type { SkillInfoResponse, SkillInfo } from '@/interface/skill'
import type { ApiResponse } from '@/types/api'
import axios from 'axios'

interface SkillDetailResponse {
  code: number
  data: {
    skill: SkillInfo
  }
  message: string
}

export const getAllSkillInfo = () => {
  return axios.get<SkillInfoResponse>('/api/v1/talent/skill-info.list')
}

export const getSkillInfo = (storyId: string) => {
  return axios.post<SkillInfoResponse>('/api/v1/talent/skill-from-story.list', {
    story_id: storyId
  })
}

export const fetchSkillDetail = (story_id: string, actor_id: string) => {
  return axios.post<SkillDetailResponse>(`/api/v1/talent/skill-from-state.get`, {
    story_id,
    actor_id
  })
}

export const createSkill = (skill: SkillInfo) => {
  return axios.post<ApiResponse<SkillDetailResponse>>('/api/v1/talent/skill.create', skill)
}

export const updateSkill = (skill: SkillInfo) => {
  return axios.post<ApiResponse<SkillDetailResponse>>('/api/v1/talent/skill.update', skill)
}

export const deleteSkill = (skill_id: string) => {
  return axios.post<ApiResponse<SkillDetailResponse>>('/api/v1/talent/skill.delete', {
    id: skill_id
  })
}
