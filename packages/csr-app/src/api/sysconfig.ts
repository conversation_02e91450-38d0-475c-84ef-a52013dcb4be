import axios from 'axios'
import type { ResponseData } from '@/interface'

export interface CheckInCoinsPerDay {
  day_one: number
  day_two: number
  day_three: number
  day_four: number
  day_five: number
  day_six: number
  day_seven: number
}

export interface SysConfigListResponse {
  checkin: {
    check_in_coins_per_day: CheckInCoinsPerDay
  }
  user_hobby_collection: Record<string, object>
}

export const getSysConfigList = () => {
  return axios.get<ResponseData<SysConfigListResponse>>('/api/v1/sysconfig.list')
}
