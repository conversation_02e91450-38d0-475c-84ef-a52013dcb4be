import axios from 'axios'
import type { Actor } from './stories'
import type { ApiResponse } from '@/types/api'
export interface StoryInfo {
  project_id: string
  actor_id: string
  name: string
  description: string
  preview_url: string
  bgm_url: string
  preview_video: string
  status: string
  story_text: string
  story_content: string
  is_active_skill?: boolean
  skill_ids?: string[]
}

export interface StoryListResponse {
  project_infos: StoryInfo[] | null
}

export const getStoryList = () => {
  return axios.get<ApiResponse<StoryListResponse>>('/api/v1/editor/story.list')
}

export interface ActorEditorResponse {
  actors: Actor[]
}

export const getActorList = () => {
  return axios.post<ApiResponse<ActorEditorResponse>>('/api/v1/actor-editor.list', {
    page: 1,
    page_size: 100
  })
}

export interface CreateStoryResponse {
  project_info: StoryInfo
}

export interface UpdateStoryRequest {
  project_id: string
  actor_id?: string
  project_name: string
  description: string
  preview_url: string
  preview_video: string
  story_text: string
  bgm_url: string
  story_content: string
  is_active_skill?: boolean
  skill_ids?: string[]
}

export const createStory = (name: string) => {
  return axios.post<ApiResponse<CreateStoryResponse>>('/api/v1/editor/story.create', { name })
}

export const updateStory = (data: UpdateStoryRequest) => {
  return axios.post<ApiResponse<void>>('/api/v1/editor/story.update', data)
}

export interface StoryDetailResponse {
  project_info: StoryInfo
}

export const getStoryDetail = (project_id: string) => {
  return axios.post<ApiResponse<StoryDetailResponse>>('/api/v1/editor/story.get', { project_id })
}

export const publishStory = (project_id: string) => {
  return axios.post<ApiResponse<void>>('/api/v1/editor/story.publish', { project_id })
}

export const deleteStory = (project_id: string) => {
  return axios.post<ApiResponse<void>>('/api/v1/editor/story.delete', { project_id })
}

export interface GenerateStoryResourcesRequest {
  project_id: string
  brm_user_id: string
}

export const generateStoryResources = (data: GenerateStoryResourcesRequest) => {
  return axios.post<ApiResponse<void>>('/api/v1/editor/story-resources.generate', data)
}

export interface StoryStatusResponse {
  is_resources_generated: boolean
}

export const getStoryStatus = (project_id: string) => {
  return axios.post<ApiResponse<StoryStatusResponse>>('/api/v1/editor/story-resources.get-status', {
    project_id
  })
}

export const updateActor = (actor: Actor) => {
  return axios.post<ApiResponse<Actor>>(`/api/v1/actor-editor.update`, actor)
}
