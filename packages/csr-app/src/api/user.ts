import axios from 'axios'
import type { CommonQuery, ResponseData, ListResult } from '@/interface'
import type { Story } from '@/api/stories'
import { useReferralStore } from '@/store/referral'

export interface GuestSignUpResponse {
  auth: {
    access_token: string
    token_type: string
    expires: number
    refresh_token: string
  }
  user: {
    uuid: string
    name: string
    email: string
    avatar_url: string
    status: string
    plan: string
    gender: string
    coins: number
    role: string
  }
}

export interface UserInfoResponse {
  user: {
    uuid: string
    name: string
    email: string
    avatar_url: string
    status: string
    plan: string
    coins: number
    role: string
    create_time: string
  }
  auth?: {
    access_token: string
    token_type: string
    expires: number
    refresh_token: string
  }
  is_first_login?: boolean
}

export interface PlayedStoriesResponse {
  stories: Story[]
}

export interface UserConvertParams {
  user_id: string
  email: string
  name: string
  password: string
  gender: string
}

export interface UserInfo {
  name: string
  gender: string
  avatar_url?: string
  email?: string
  uuid?: string
}

export interface UpdateUserInfoParams {
  name?: string
  gender?: string
  avatar_url?: string
}

export interface SendVerificationCodeParams {
  email: string
  code_type: 'register' | 'login'
}

export interface LoginWithCodeParams {
  email: string
  code: string
  code_type: 'register' | 'login'
  gender: 'male' | 'female' | 'unknown'
  user_id: string
  source_code?: string
}

export function updateUserInfo(data: UpdateUserInfoParams) {
  return axios.post<ResponseData<UserInfoResponse>>('/api/v1/user-info.update', data)
}

export const guestSignUp = (device_id: string, gender: string) => {
  // const referralStore = useReferralStore()
  return axios.post<ResponseData<GuestSignUpResponse>>('/api/v1/user-guest.sign-up', {
    device_id,
    gender
    // source_code: referralStore.getSourceCode(),
    // invite_code: referralStore.getInviteCode()
  })
}

export const convertGuestToUser = (data: UserConvertParams) => {
  const referralStore = useReferralStore()
  return axios.post<ResponseData<GuestSignUpResponse>>('/api/v1/user-guest.convert', {
    ...data,
    source_code: referralStore.getSourceCode(),
    invite_code: referralStore.getInviteCode()
  })
}

export const whoami = () => {
  return axios.get<ResponseData<UserInfoResponse>>('/api/v1/user.whoami')
}

export const userPlayedStories = () => {
  return axios.get<ResponseData<PlayedStoriesResponse>>('/api/v1/game-history.list')
}

export const userLikedStories = () => {
  return axios.get<ResponseData<PlayedStoriesResponse>>('/api/v1/favorite.list')
}

export const sendVerificationCode = (data: SendVerificationCodeParams) => {
  return axios.post<ResponseData>('/api/v1/verify-code.send', data)
}

export const loginWithCode = (data: LoginWithCodeParams) => {
  const referralStore = useReferralStore()
  // Only add source_code and invite_code if they're not already provided in the data
  const requestData = {
    ...data,
    source_code: data.source_code || referralStore.getSourceCode(),
    invite_code: referralStore.getInviteCode()
  }
  return axios.post<ResponseData<UserInfoResponse>>('/api/v1/user.auth', requestData)
}
