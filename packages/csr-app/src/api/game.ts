import axios from 'axios'
import type { ApiResponse } from '@/types/api'

/**
 * 获取当前关卡耗时的响应接口
 */
export interface CurrentPlaytimeResponse {
  duration: number // 当前关卡耗时，单位秒
  last_reset_time: number // 上次重置时间的时间戳
}

/**
 * 重置当前关卡耗时的响应接口
 */
export interface ResetPlaytimeResponse {
  success: boolean
  message: string
}

/**
 * 获取当前关卡耗时的请求参数
 */
export interface PlaytimeParams {
  story_id: string // 故事ID（必传）
  actor_id?: string // 角色ID（可选）
}

/**
 * 获取当前关卡耗时
 * @param params 请求参数，包含必传的story_id和可选的actor_id
 * @returns Promise with the current playtime information
 */
export async function getCurrentPlaytime(
  params: PlaytimeParams
): Promise<ApiResponse<CurrentPlaytimeResponse>> {
  const response = await axios.post('/api/v1/game/cost-time.get', params)
  return response.data
}

/**
 * 重置当前关卡耗时
 * @param params 请求参数，包含必传的story_id和可选的actor_id
 * @returns Promise with the reset result
 */
export async function resetPlaytime(params: PlaytimeParams): Promise<ApiResponse> {
  const response = await axios.post<ApiResponse>('/api/v1/game/current-playtime.reset', params)
  return response.data
}

/**
 * 购买好感度的请求参数
 */
export interface BuyHeartValueParams {
  story_id: string // 故事ID（必传）
  actor_id: string // 角色ID（必传）
  coins: number // 消耗的钻石数量（必传）
}

/**
 * 购买好感度的响应接口
 */
export interface BuyHeartValueResponse {
  success: boolean
  message: string
  heart_value?: number // 购买后的好感度值
  remaining_coins?: number // 剩余钻石数量
}

/**
 * 购买好感度
 * @param params 请求参数，包含story_id、actor_id和coins
 * @returns Promise with the purchase result
 */
export async function buyHeartValue(
  params: BuyHeartValueParams
): Promise<ApiResponse<BuyHeartValueResponse>> {
  const response = await axios.post<ApiResponse<BuyHeartValueResponse>>(
    '/api/v1/game/heart-value.buy',
    params
  )
  return response.data
}
