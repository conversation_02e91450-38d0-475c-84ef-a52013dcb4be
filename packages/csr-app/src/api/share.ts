import axios, { type AxiosResponse } from 'axios'

export interface UserInfo {
  uuid?: string
  name?: string
  email?: string
  avatar_url?: string
  status?: 'guest' | 'normal'
  plan?: 'free' | 'basic'
  coins?: number
  gender?: 'male' | 'female' | 'unknown'
  role?: 'guest' | 'normal' | 'admin'
}

interface SharePosterResponse {
  poster_url: string
}

interface ShareInfoResponse {
  is_rewarded: boolean
  coins: number
}

interface CreateShareResponse {
  message: string
  user: UserInfo
}

/**
 * Get share poster image URL
 */
export const getSharePoster = (story_id: string, actor_id: string) => {
  return axios.post<AxiosResponse<SharePosterResponse>>('/api/v1/user-share-poster.create', {
    story_id,
    actor_id
  })
}

/**
 * Record successful share
 */
export const createShare = (story_id: string, actor_id: string) => {
  return axios.post<AxiosResponse<CreateShareResponse>>('/api/v1/user-share.create', {
    story_id,
    actor_id
  })
}

/**
 * Get user share info
 */
export const getShareInfo = (story_id: string, actor_id: string) => {
  return axios.post<AxiosResponse<ShareInfoResponse>>('/api/v1/user-share.get-info', {
    story_id,
    actor_id
  })
}

/**
 * Get ending share poster
 */
export const getEndingSharePoster = (story_id: string, actor_id: string) => {
  return axios.post<AxiosResponse<SharePosterResponse>>('/api/v1/story-poster.create', {
    story_id,
    actor_id
  })
}
