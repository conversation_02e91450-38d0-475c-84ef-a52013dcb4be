import axios from 'axios'
import type { TasksResponse, CompleteTaskResponse, InviteCodeResponse } from '@/interface/tasks'

/**
 * 获取任务列表
 * @returns Promise with the list of tasks
 */
export async function getTasksList(): Promise<TasksResponse> {
  const response = await axios.get('/api/v1/daily-tasks.list')
  return response.data
}

/**
 * 完成播放游戏任务
 * @returns Promise with the completed task response
 */
export async function completePlayGameTask(): Promise<CompleteTaskResponse> {
  const response = await axios.post('/api/v1/daily-tasks.play-game')
  return response.data
}

/**
 * 完成分享游戏任务
 * @returns Promise with the completed task response
 */
export async function completeShareGameTask(): Promise<CompleteTaskResponse> {
  const response = await axios.post('/api/v1/daily-tasks.share-game')
  return response.data
}

/**
 * 完成创建故事任务
 * @returns Promise with the completed task response
 */
export async function completeCreateStoryTask(): Promise<CompleteTaskResponse> {
  const response = await axios.post('/api/v1/daily-tasks.create-story')
  return response.data
}

/**
 * 完成邀请好友任务
 * @returns Promise with the completed task response
 */
export async function completeInviteFriendTask(): Promise<CompleteTaskResponse> {
  const response = await axios.post('/api/v1/daily-tasks.invite-friend')
  return response.data
}

/**
 * 获取用户专属邀请码
 * @returns Promise with the invite code
 */
export async function getInviteCode(): Promise<InviteCodeResponse> {
  const response = await axios.get('/api/v1/invite-code.create')
  return response.data
}
