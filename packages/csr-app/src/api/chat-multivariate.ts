import axios from 'axios'

// 礼物相关接口类型定义
export interface Present {
  id: string
  title: string
  image_url: string
  coins: number
  heart_value: number
}

export interface PresentsListResponse {
  code: number
  message: string
  data: {
    present_list: Present[]
  }
}

export interface SendPresentRequest {
  actorId: string
  presentId: string
  quantity?: number
}

export interface SendPresentResponse {
  code: number
  message: string
  data: {
    success: boolean
    remainingCoins?: number
    remainingDiamonds?: number
  }
}

// Chat4 聊天记录相关接口
export interface Chat4HistoryRequest {
  story_id: string
  actor_id: string
  location?: string // 场景位置参数
}

export interface Chat4Message {
  id: string
  content: string
  sender: {
    id: string
    name: string
    type: 'user' | 'actor'
  }
  timestamp: number
  location?: string
}

export interface Chat4HistoryResponse {
  code: string
  message: string
  data: {
    history: Chat4Message[] | null
  }
}

/**
 * 获取礼物列表
 */
export const getPresentsListAPI = () => {
  return axios.get<PresentsListResponse>('api/v1/presents.list')
}

/**
 * 发送礼物
 */
export const sendPresentAPI = (data: SendPresentRequest) => {
  return axios.post<SendPresentResponse>('/api/v1/actor/chat/multivariate/present.send', data)
}

/**
 * 获取礼物历史记录
 */
export const getPresentHistoryAPI = (actorId: string) => {
  // return axios.get<any>('/actor/chat/multivariate/presents.history', {
  //   params: { actorId }
  // })
}

/**
 * 获取 Chat4 聊天记录
 */
export const getChat4HistoryAPI = (data: Chat4HistoryRequest) => {
  return axios.post<Chat4HistoryResponse>('/api/v1/actor/chat/multivariate/game.history', data)
}
