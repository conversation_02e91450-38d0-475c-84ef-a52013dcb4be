import axios from 'axios'
import { ResponseData } from '@/interface'
import { omitBy, isEmpty } from 'lodash-es'
import { RatingForm, RatingHistory, ChatHistoryResponse, ChatHistory } from '@/types/chat'

// 添加 TTS 生成接口
export function generateTTS(text: string, actorId: string, voice_id?: string, provider?: string) {
  const filteredParams = omitBy(
    {
      text,
      ...(voice_id || provider ? { voice_config: omitBy({ voice_id, provider }, isEmpty) } : {})
    },
    isEmpty
  )
  return axios.post(`/api/v1/actor/${actorId}/tts.generate`, filteredParams)
}

// 提交评分表单
export function submitRatingForm(formData: RatingForm) {
  return axios.post<ResponseData>('/api/v1/story-rating.create', formData)
}

// 获取评分历史
export function getRatingHistory(storyId?: string, actorId?: string) {
  return axios.post<ResponseData<RatingHistory>>('/api/v1/story-rating.get', {
    story_id: storyId,
    actor_id: actorId
  })
}

// 获取用户聊天记录
export function getUserChatHistory(storyId: string, actorId: string, version?: string) {
  const apiUrl =
    version === '3'
      ? '/api/v1/actor/chat/reasoning/game.history'
      : '/api/v1/actor/chat/game.history'
  const params = version === '3' ? { story_id: storyId } : { story_id: storyId, actor_id: actorId }
  return axios.post<ResponseData<ChatHistoryResponse>>(apiUrl, params)
}

// 获取用户事件记录
export function getUserEventHistory(storyId: string, actorId: string) {
  return axios.post<ResponseData<ChatHistory>>('/api/v1/actor/event/game.history', {
    story_id: storyId,
    actor_id: actorId
  })
}

// 获取用户游戏配置状态
export function getUserGameConfigState(storyId: string, actorId: string) {
  return axios.post<ResponseData<ChatHistory>>(`/api/v1/actor/${actorId}/game.state`, {
    story_id: storyId,
    actor_id: actorId
  })
}
