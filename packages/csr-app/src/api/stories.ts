import axios, { type AxiosResponse } from 'axios'
import type { ApiResponse } from '@/types/api'
import { pickBy, isEqual } from 'lodash-es'
import type { UserState } from '@/store/user'
import { useCache } from '@/composables/useCache'
export interface Tag {
  name: string
  color: string
  bg_color: string
}

export interface Actor {
  id: string
  name: string
  subtitle: string
  avatar_url: string
  preview_url: string
  coins: number
  is_purchased: boolean
  extra: Record<string, string>
  version: string
  chat_config?: Record<string, string>
}

export interface Story {
  id: string
  title: string
  description: string
  preview_url: string
  bgm_url: string
  actors: Actor[]
  hot: number
  categories: string[]
  coins: number
  is_fav: boolean
  is_purchased: boolean
  badge?: string
  preview_video_url?: string
  is_subscribed: boolean
  status: 'preparing' | 'normal'
  is_active_skill: boolean
  is_support_face_swap: boolean
  version?: string
  author?: string // 添加作者字段
  tags?: string[]
  carousel_image_url?: string[] // 轮播图片/视频URL数组
}

export interface Category {
  id: string
  name: string
  parent_id: string
  level: number
  subcategories: SubCategory[]
}

export interface SubCategory {
  id: string
  name: string
  parent_id: string
  level: number
}

export interface StoriesResponse {
  stories: Story[]
}
export interface PreloadResource {
  url: string
  type: 'video' | 'image'
  isPreloaded: boolean // 添加标志位表示是否已预加载
}

export interface StoreDetailResponse {
  story: Story
  is_discount_user: boolean
}

export interface PreloadResourcesResponse {
  preloads: PreloadResource[]
}

interface FavoriteResponse {
  is_favorited: boolean
}

export interface StoryCategoriesResponse {
  category: Category[]
}

// 创建一个缓存实例，用于存储故事列表
const storyListCache = useCache({
  maxSize: 20, // 最多缓存20个不同的请求
  ttl: 1 * 60 * 1000, // 缓存1分钟
  persistKey: 'story-list-cache' // 持久化到localStorage
})

export const fetchStoryList = async (categoryIds?: string[], sort?: string) => {
  const params = pickBy(
    {
      category_ids: categoryIds,
      sort: sort
    },
    (value) => {
      if (Array.isArray(value)) return value.length > 0
      return value !== '' && value !== undefined && value !== null
    }
  )

  // 生成缓存键
  const cacheKey = `story-list-${sort || 'default'}-${JSON.stringify(categoryIds || [])}`

  // 尝试从缓存获取数据
  const cachedData = storyListCache.get(cacheKey)
  if (cachedData) {
    // 安全地解析缓存数据
    console.log('从缓存中获取故事列表:', cachedData)
    try {
      const parsedData = JSON.parse(cachedData)
      return { data: parsedData } as AxiosResponse<ApiResponse<StoriesResponse>>
    } catch {
      // 解析失败时静默移除缓存，继续执行网络请求
      storyListCache.remove(cacheKey)
    }
  }

  // 发起网络请求
  const response = await axios.post<ApiResponse<StoriesResponse>>('/api/v1/story.list', params)

  // 缓存成功的响应
  if (response.data.code === '0') {
    storyListCache.set(cacheKey, JSON.stringify(response.data))
  }

  return response
}

export const getPreloadResources = async (
  actorId: string,
  storyId: string
): Promise<AxiosResponse<ApiResponse<PreloadResourcesResponse>>> => {
  return axios.post<ApiResponse<PreloadResourcesResponse>>(
    `/api/v1/actor/${storyId}/${actorId}/game.preload`,
    {
      story_id: storyId
    }
  )
}

// Add favorite story
export const addFavorite = (storyId: string) => {
  return axios.post<ApiResponse<FavoriteResponse>>('/api/v1/favorite.create', {
    story_id: storyId
  })
}

// Remove favorite story
export const removeFavorite = (storyId: string) => {
  return axios.post<ApiResponse<FavoriteResponse>>('/api/v1/favorite.delete', {
    story_id: storyId
  })
}

// Get favorite status
export const getFavoriteStatus = (storyId: string) => {
  return axios.get<ApiResponse<FavoriteResponse>>(`/api/v1/favorite.status?story_id=${storyId}`)
}

export const getStoryDetail = (storyId: string) => {
  return axios.get<ApiResponse<StoreDetailResponse>>(`/api/v1/story.get?id=${storyId}`)
}

export const getStoryCategories = () => {
  return axios.get<ApiResponse<StoryCategoriesResponse>>('/api/v1/category.list')
}

export const getStoryListByCategory = (categoryId: string) => {
  return axios.post<ApiResponse<StoriesResponse>>(`/api/v1/category-story.list`, {
    category_id: categoryId
  })
}

// Subscribe to a story
export const subscribeStory = (storyId: string, userEmail?: string) => {
  const params = pickBy({
    story_id: storyId,
    email: userEmail
  })
  return axios.post<ApiResponse<{ is_subscribed: boolean }>>(
    '/api/v1/user-subscribe.create',
    params
  )
}

// Unsubscribe from a story
export const unsubscribeStory = (storyId: string) => {
  return axios.post<ApiResponse<{ is_subscribed: boolean }>>('/api/v1/user-subscribe.cancel', {
    story_id: storyId
  })
}

// Get story subscribe status
export const getStorySubscribeStatus = (storyId: string) => {
  return axios.post<ApiResponse<{ is_subscribed: boolean }>>('/api/v1/user-subscribe.get-status', {
    story_id: storyId
  })
}

interface HobbyCollectionStatusResponse {
  code: string
  message: string
  data: {
    is_collected_hobby: boolean
  }
}

interface HobbyCollectionCreateResponse {
  code: string
  message: string
  data: {
    user: UserState['userInfo']
  }
}

export const getHobbyCollectionStatus = () => {
  return axios.get<HobbyCollectionStatusResponse>('/api/v1/hobby/collection-status.get')
}

export const createHobbyCollection = (data: Record<string, string[]>) => {
  return axios.post<HobbyCollectionCreateResponse>('/api/v1/hobby/collection.create', data)
}
