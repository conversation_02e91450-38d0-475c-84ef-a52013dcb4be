import axios from 'axios'
import type { CheckinResponse, ClaimCheckinResponse } from '@/interface/checkin'

export async function getCheckinInfo(): Promise<CheckinResponse> {
  const response = await axios.get('/api/v1/user-checkin.get')
  return response.data
}

export async function claimCheckinReward(): Promise<ClaimCheckinResponse> {
  const response = await axios.post('/api/v1/user-checkin.create')
  return response.data
}
