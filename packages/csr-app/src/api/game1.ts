import axios from 'axios'
import { <PERSON><PERSON>uery, ResponseD<PERSON>, ListResult } from '@/interface'

export interface Scene {
  uuid: string
  sort: number
  type: 'image' | 'video' | 'interaction'
  input_type?: InputType
  preview_url: string
  caption: string
  sound_url?: string
  status?: string
  ambient_url?: string
  bgm_url?: string
  inputPosition?: {
    x: number
    y: number
  }
  nextBtnText?: string
}

interface StoryTemplateResponse {
  templates: Scene[]
}

export interface AudioController {
  playVoice: () => void
  playAmbient: (index?: number, loop?: boolean) => void
  playBackgroundMusic: () => void
  stopVoice: () => void
  stopAmbient: () => void
  stopBackgroundMusic: () => void
  stopAll: () => void
  playAll: () => void
}

export interface UserInputs {
  name: string
  pose: string
  time: string
  clothes: string
  [key: string]: string
}

export type InputType = 'name' | 'pose' | 'time' | 'clothes'

// API 响应类型
export interface ApiResponse<T> {
  code: number
  data: T
  message?: string
}

export interface ImageTask {
  uuid: string
  template_id: string
  status: string
  image_url?: string
}

export interface ImageTasksResponse {
  images: ImageTask[]
}

export interface ImageGenerationResponse {
  image_ids: string[]
}

export interface ImagePostSceneImageTasksResponse {
  image_ids: string[]
}

export interface VoiceResponse {
  audio_urls: string[]
}

export interface Template {
  uuid: string
  sort: number
  type: string
  input_type?: InputType
  preview_url: string
  caption: string
}

export interface TemplateResponse {
  templates: Template[]
}

export const getStoryTemplate = () =>
  axios.get<ResponseData<StoryTemplateResponse>>('/api/v1/story-template.list')

export const batchTextToSpeech = (texts: string[]) =>
  axios.post<ResponseData<ListResult<string>>>('/api/v1/tts.generate-batch', texts)

export const postScenceImageTasks = (images) =>
  axios.post<ResponseData<ImagePostSceneImageTasksResponse>>('/api/v1/story-image.generate-batch', {
    images
  })

export const getScenceImageTasks = (image_ids: string[]) =>
  axios.post<ResponseData<ImageTasksResponse>>('/api/v1/story-image.list', {
    image_ids
  })
