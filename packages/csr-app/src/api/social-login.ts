import axios from 'axios'
import type { ResponseData } from '@/interface'
import { useReferralStore } from '@/store/referral'

export type SocialLoginType = 'google' | 'discord' | 'facebook'

export interface SocialLoginUrlResponse {
  url: string
}

export interface SocialLoginExchangeResponse {
  auth: {
    access_token: string
    token_type: string
    refresh_token: string
    expires: number
  }
  is_first_login: boolean // 是否是第一次登录
  user: {
    uuid: string
    name: string
    email: string
    avatar_url: string
    status: string
    plan: string
  }
}

export interface exchangeSocialLoginParams {
  login_type: SocialLoginType
  code: string
  user_id: string
  source_code?: string
}

// Get social login URL
export const getSocialLoginUrl = (login_type: SocialLoginType, redirect_url: string) => {
  return axios.post<ResponseData<SocialLoginUrlResponse>>('/api/v1/social-login.get-url', {
    login_type,
    redirect_url
  })
}

// Exchange social login code for user data
export const exchangeSocialLogin = (login_type: SocialLoginType, code: string, user_id: string) => {
  const referralStore = useReferralStore()
  return axios.post<ResponseData<SocialLoginExchangeResponse>>('/api/v1/social-login.exchange', {
    login_type,
    code,
    user_id,
    source_code: referralStore.getSourceCode(),
    invite_code: referralStore.getInviteCode()
  })
}
