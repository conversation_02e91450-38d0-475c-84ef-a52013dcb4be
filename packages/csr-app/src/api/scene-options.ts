/**
 * 场景选项相关API接口
 * 用于版本5角色的场景跳转付费功能
 */
import axios from 'axios'
import type { ApiResponse } from '@/types/api'

// 场景选项接口定义
export interface SceneOption {
  option_id: string
  scene_id: string
  is_purchased: boolean
  coins_required: number
  is_first_access: boolean
}

// 获取场景选项列表请求参数
export interface GetSceneOptionsRequest {
  actor_id: string
}

// 获取场景选项列表响应
export interface GetSceneOptionsResponse {
  options: string[] // 实际返回的是场景ID数组
}

// 购买场景选项请求参数
export interface PurchaseSceneOptionRequest {
  actor_id: string
  option_id: string
}

// 购买场景选项响应
export interface PurchaseSceneOptionResponse {
  remaining_coins?: number // 可选，如果有扣费才返回
}

/**
 * 获取场景选项列表
 * 查询用户对各个场景的付费状态
 */
export const getSceneOptions = async (
  storyId: string,
  params: GetSceneOptionsRequest,
): Promise<ApiResponse<GetSceneOptionsResponse>> => {
  try {
    const response = await axios.post<ApiResponse<GetSceneOptionsResponse>>(
      `/api/v1/story/${storyId}/option.list`,
      params,
    )
    return response.data
  } catch (error) {
    console.error('Failed to get scene options:', error)
    throw error
  }
}

/**
 * 购买场景选项
 * 为指定场景付费以获得访问权限
 */
export const purchaseSceneOption = async (
  storyId: string,
  params: PurchaseSceneOptionRequest,
): Promise<ApiResponse<PurchaseSceneOptionResponse>> => {
  try {
    const response = await axios.post<ApiResponse<PurchaseSceneOptionResponse>>(
      `/api/v1/story/${storyId}/option.purchase`,
      params,
    )
    return response.data
  } catch (error) {
    console.error('Failed to purchase scene option:', error)
    throw error
  }
}
