import type { Directive, DirectiveBinding } from 'vue'

/**
 * 文字描边指令
 * 使用方法：
 * v-text-stroke - 使用默认配置（黑色描边，1px宽度）
 * v-text-stroke="'#000'" - 指定描边颜色
 * v-text-stroke="{ color: '#000', width: 2 }" - 指定描边颜色和宽度
 */
export const textStroke: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    applyTextStroke(el, binding.value)
  },
  updated(el: HTMLElement, binding: DirectiveBinding) {
    applyTextStroke(el, binding.value)
  }
}

/**
 * 应用文字描边效果
 * @param el 目标元素
 * @param value 指令值
 */
function applyTextStroke(el: HTMLElement, value: any) {
  // 默认配置
  let color = '#000'
  let width = 1

  // 处理指令值
  if (typeof value === 'string') {
    color = value
  } else if (typeof value === 'object' && value !== null) {
    color = value.color || color
    width = value.width || width
  }

  // 根据宽度生成阴影配置
  const shadows = []
  
  // 生成八个方向的阴影
  for (let x = -width; x <= width; x++) {
    for (let y = -width; y <= width; y++) {
      // 跳过中心点
      if (x === 0 && y === 0) continue
      
      // 只添加边缘点以减少阴影数量
      if (Math.abs(x) === width || Math.abs(y) === width) {
        shadows.push(`${x}px ${y}px 0 ${color}`)
      }
    }
  }

  // 添加四个主要方向的中间点
  shadows.push(`0 ${-width}px 0 ${color}`) // 上
  shadows.push(`${width}px 0 0 ${color}`)  // 右
  shadows.push(`0 ${width}px 0 ${color}`)  // 下
  shadows.push(`${-width}px 0 0 ${color}`) // 左

  // 应用文字阴影
  el.style.textShadow = shadows.join(', ')
}
