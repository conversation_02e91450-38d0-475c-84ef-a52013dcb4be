<template>
  <div class="duration-picker">
    <div class="input-wrapper">
      <input
        type="number"
        class="custom-input"
        :value="duration"
        :min="0"
        :step="0.1"
        @input="handleInput"
      />
      <span class="unit">秒</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
  modelValue: number
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: number): void
}>()

const duration = ref(props.modelValue || 0)

watch(
  () => props.modelValue,
  (newVal) => {
    duration.value = newVal || 0
  }
)

const handleInput = (e: Event) => {
  const value = parseFloat((e.target as HTMLInputElement).value)
  if (!isNaN(value)) {
    emit('update:modelValue', value)
  }
}
</script>

<style lang="less" scoped>
.duration-picker {
  .input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
  }

  .custom-input {
    width: 100%;
    height: 42px;
    padding: 0 16px;
    border: 1px solid rgba(184, 196, 255, 0.1);
    background: rgba(204, 213, 255, 0.05);
    color: #fff;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;
    padding-right: 40px; // 为单位留出空间

    &:hover,
    &:focus {
      border-color: #ca93f2;
      background: rgba(204, 213, 255, 0.08);
    }

    // 移除数字输入框的上下箭头
    &::-webkit-inner-spin-button,
    &::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
    &[type='number'] {
      -moz-appearance: textfield;
    }
  }

  .unit {
    position: absolute;
    right: 16px;
    color: rgba(255, 255, 255, 0.5);
    font-size: 14px;
    pointer-events: none;
  }
}
</style>
