<template>
  <div class="media-selector">
    <a-upload
      :accept="acceptTypes"
      :custom-request="handleUpload"
      :show-upload-list="false"
      :disabled="uploading || !!previewUrl"
    >
      <template #upload-button>
        <a-button class="upload-button" :loading="uploading" :disabled="!!previewUrl">
          <template #icon>
            <icon-upload />
          </template>
          {{ buttonText }}
        </a-button>
      </template>
    </a-upload>

    <div v-if="previewUrl" class="preview">
      <img v-if="type === 'image'" :src="previewUrl" class="preview-media" />
      <video v-else-if="type === 'video'" :src="previewUrl" controls class="preview-media" />
      <audio v-else-if="type === 'audio'" :src="previewUrl" controls class="preview-media" />
      <a-button class="delete-button" type="text" status="danger" @click="handleClear">
        <template #icon>
          <icon-delete />
        </template>
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { UploadRequest, RequestOption } from '@arco-design/web-vue'
import { Message } from '@/mobile/components/Message'
import axios from 'axios'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface/report'

interface Props {
  type: 'image' | 'video' | 'audio'
}

const props = defineProps<Props>()
const previewUrl = defineModel('preview', { default: '' })
const fileUri = defineModel('modelValue', { default: '' })

const uploading = ref(false)

const acceptTypes = computed(() => {
  switch (props.type) {
    case 'image':
      return '.jpg,.jpeg,.png,.gif,.webp'
    case 'video':
      return '.mp4,.webm'
    case 'audio':
      return '.mp3,.wav'
    default:
      return ''
  }
})

const buttonText = computed(() => {
  if (previewUrl.value) {
    switch (props.type) {
      case 'image':
        return '图片已上传'
      case 'video':
        return '视频已上传'
      case 'audio':
        return '音频已上传'
      default:
        return '文件已上传'
    }
  }

  switch (props.type) {
    case 'image':
      return '上传图片'
    case 'video':
      return '上传视频'
    case 'audio':
      return '上传音频'
    default:
      return '上传文件'
  }
})

const handleUpload = (options: RequestOption): UploadRequest => {
  if (!options.fileItem?.file) return { abort: () => {} }

  uploading.value = true
  uploadFile(options.fileItem.file)
    .then(({ preview, uri }) => {
      previewUrl.value = preview
      fileUri.value = uri
      options.onSuccess?.()
      Message.success('上传成功')
      reportEvent(ReportEvent.UploadImageSuccess, {
        type: props.type,
        url: preview
      })
    })
    .catch((error) => {
      options.onError?.(error)
      Message.error('上传失败')
      reportEvent(ReportEvent.UploadImageFailed, {
        type: props.type,
        error: error instanceof Error ? error.message : 'Upload failed'
      })
    })
    .finally(() => {
      uploading.value = false
    })

  return {
    abort: () => {
      uploading.value = false
    }
  }
}

const handleClear = () => {
  previewUrl.value = ''
  fileUri.value = ''
}

async function uploadFile(file: File): Promise<{ preview: string; uri: string }> {
  try {
    // 1. 获取上传URL
    const response = await axios.post('/api/v1/editor/story-media.upload', {
      file_type: props.type
    })
    const { data } = response
    const { upload_url, download_url, file_uri } = data.data

    // 检查 URL 是否为空
    if (!upload_url || !download_url) {
      throw new Error('Failed to get upload URL')
    }

    // 2. 上传文件
    const uploadResponse = await axios.put(upload_url, file, {
      headers: {
        'Content-Type': file.type
      }
    })
    // 3. 将file_uri转换为https地址
    const convertResponse = await axios.post('/api/v1/tos/http.convert', {
      tos_path: file_uri
    })
    const httpPath = convertResponse.data.data.http_path
    // 如果没有转换成功，使用原始的download_url
    const finalUrl = httpPath || download_url

    // 检查上传响应状态
    if (uploadResponse.status !== 200) {
      throw new Error('File upload failed')
    }

    return {
      preview: finalUrl,
      uri: finalUrl
    }
  } catch (error) {
    console.error('Upload error:', error)
    throw new Error(error instanceof Error ? error.message : 'Upload failed')
  }
}
</script>

<style lang="less" scoped>
.media-selector {
  .upload-button {
    width: 100%;
    height: 42px;
    border-radius: 40px;
    background: rgba(204, 213, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    &:hover:not(:disabled),
    &:focus:not(:disabled) {
      border-color: #ca93f2;
      background: rgba(204, 213, 255, 0.08);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background: rgba(204, 213, 255, 0.02);
      border-color: rgba(255, 255, 255, 0.05);
    }

    .arco-icon {
      font-size: 20px;
      color: #ca93f2;
    }
  }

  .preview {
    margin-top: 12px;
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    background: rgba(204, 213, 255, 0.05);

    .preview-media {
      width: 100%;
      max-height: 200px;
      object-fit: contain;
    }

    .delete-button {
      position: absolute;
      top: 8px;
      right: 8px;
      width: 32px;
      height: 32px;
      border-radius: 16px;
      background: rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(4px);
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: rgba(255, 77, 79, 0.8);
      }

      .arco-icon {
        font-size: 16px;
      }
    }

    audio {
      width: 100%;
      height: 42px;
      margin: 8px 0;
    }
  }
}
</style>
