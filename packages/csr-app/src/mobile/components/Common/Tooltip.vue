<template>
  <div class="tooltip-container" @click="toggleTooltip">
    <slot></slot>
    <Teleport to="body">
      <div
        v-if="isVisible"
        ref="tooltipRef"
        class="tooltip-content"
        :class="{ visible: isReady }"
        :style="tooltipStyle"
      >
        <slot name="content"></slot>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { useEventListener } from '@vueuse/core'
import { tooltipState } from './tooltipState'

const tooltipId = Math.random().toString(36).substring(2)
const isVisible = ref(false)
const isReady = ref(false)
const tooltipRef = ref<HTMLElement>()
const tooltipStyle = ref({
  top: '0px',
  left: '0px'
})

const updatePosition = (event: MouseEvent) => {
  if (!tooltipRef.value) return

  const trigger = event.currentTarget as HTMLElement
  const tooltip = tooltipRef.value
  const rect = trigger.getBoundingClientRect()
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  const tooltipWidth = tooltip.offsetWidth
  const tooltipHeight = tooltip.offsetHeight

  let left
  if (rect.left + rect.width / 2 > viewportWidth / 2) {
    left = Math.min(rect.right - tooltipWidth, viewportWidth - tooltipWidth - 16)
  } else {
    left = Math.max(rect.left, 16)
  }

  let top = rect.bottom + 8
  if (top + tooltipHeight > viewportHeight - 16) {
    top = rect.top - tooltipHeight - 8
  }

  tooltipStyle.value = {
    top: `${top}px`,
    left: `${left}px`
  }
}

const closeTooltip = () => {
  isReady.value = false
  setTimeout(() => {
    isVisible.value = false
    if (tooltipState.activeId === tooltipId) {
      tooltipState.activeTooltip = null
      tooltipState.activeId = null
      tooltipState.close = null
    }
  }, 200)
}

const toggleTooltip = async (event: MouseEvent) => {
  event.stopPropagation()

  // 如果点击的是当前激活的 tooltip，则关闭它
  if (tooltipState.activeId === tooltipId) {
    closeTooltip()
    return
  }

  // 关闭其他打开的 tooltip
  if (tooltipState.activeId && tooltipState.close) {
    tooltipState.close()
  }

  // 显示当前 tooltip
  isVisible.value = true
  tooltipState.activeTooltip = tooltipRef.value || null
  tooltipState.activeId = tooltipId
  tooltipState.close = closeTooltip

  await nextTick()
  updatePosition(event)
  await nextTick()
  isReady.value = true
}

const handleOutsideClick = (event: MouseEvent) => {
  if (
    isVisible.value &&
    tooltipRef.value &&
    !tooltipRef.value.contains(event.target as Node) &&
    !(event.target as HTMLElement).closest('.tooltip-container')
  ) {
    closeTooltip()
  }
}

const handleViewportChange = () => {
  if (isVisible.value && tooltipRef.value) {
    const container = tooltipRef.value.closest('.tooltip-container') as HTMLElement
    if (container) {
      const event = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window
      })
      updatePosition(Object.assign(event, { currentTarget: container }))
    }
  }
}

onMounted(() => {
  useEventListener(document, 'click', handleOutsideClick)
  useEventListener(window, 'resize', handleViewportChange)
  useEventListener(window, 'scroll', handleViewportChange, true)
})

onBeforeUnmount(() => {
  if (tooltipState.activeId === tooltipId) {
    tooltipState.activeTooltip = null
    tooltipState.activeId = null
    tooltipState.close = null
  }
  isVisible.value = false
  isReady.value = false
})
</script>

<style lang="less" scoped>
.tooltip-container {
  display: inline-block;
  position: relative;
}

.tooltip-content {
  position: fixed;
  z-index: 1000;
  min-width: 120px;
  max-width: calc(100vw - 32px);
  padding: 8px 0;
  background: #2a0049;
  border: 1px solid rgba(184, 196, 255, 0.1);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transform: translateY(0);
  transition: opacity 0.2s ease;
  pointer-events: none;
  overflow-x: hidden;
  overflow-y: auto;
  max-height: calc(100vh - 32px);

  &.visible {
    opacity: 1;
    pointer-events: auto;
  }
}
</style>
