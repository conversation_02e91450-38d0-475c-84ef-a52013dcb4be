<template>
  <button
    class="custom-button"
    :class="{
      'is-loading': loading,
      'is-disabled': disabled
    }"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <span v-if="loading" class="loading-icon">
      <svg width="16" height="16" viewBox="0 0 16 16">
        <circle
          cx="8"
          cy="8"
          r="7"
          stroke="currentColor"
          stroke-width="2"
          fill="none"
          stroke-dasharray="32"
          stroke-dashoffset="32"
        >
          <animateTransform
            attributeName="transform"
            type="rotate"
            from="0 8 8"
            to="360 8 8"
            dur="1s"
            repeatCount="indefinite"
          />
          <animate
            attributeName="stroke-dashoffset"
            values="32;0"
            dur="1s"
            repeatCount="indefinite"
          />
        </circle>
      </svg>
    </span>
    <span class="button-content" :class="{ 'with-loading': loading }">
      <slot></slot>
    </span>
  </button>
</template>

<script setup lang="ts">
const props = defineProps<{
  loading?: boolean
  disabled?: boolean
}>()

const emit = defineEmits<{
  (e: 'click', event: MouseEvent): void
}>()

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style lang="less" scoped>
.custom-button {
  position: relative;
  width: 100%;
  height: 42px;
  border-radius: 40px;
  border: none;
  background: #ca93f2;
  color: #241d49;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24px;

  &:active:not(:disabled) {
    transform: translateY(1px);
    opacity: 0.9;
  }

  &.is-disabled {
    background: rgba(255, 255, 255, 0.2);
    cursor: not-allowed;
    color: rgba(255, 255, 255, 0.4);
  }

  &.is-loading {
    cursor: wait;
  }
}

.loading-icon {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: currentColor;
}

.button-content {
  transition: opacity 0.2s ease;

  &.with-loading {
    opacity: 0;
  }
}
</style>
