<template>
  <BaseDrawer v-model:visible="isVisible" height="auto">
    <div class="skill-allocation">
      <div class="header">
        <h2>Attribute Points</h2>
        <div class="total-points">
          Available Points: {{ userPoints - skillStore.usedUserPoints }}/{{ userPoints }}
        </div>
      </div>

      <div class="attributes-list">
        <div v-for="(value, attr) in skillStore.totalAttributes" :key="attr" class="attribute-item">
          <div class="attribute-info">
            <span class="attribute-name">{{ attr }}</span>
            <div class="attribute-values">
              <span class="initial-value">(+{{ skillStore.initialAttributes[attr] }})</span>
              <span class="attribute-value">{{ value }}/{{ maxAttributePoints }}</span>
            </div>
          </div>
          <div class="attribute-controls">
            <button
              class="control-btn minus"
              :class="{ disabled: skillStore.userAllocatedPoints[attr] <= 0 }"
              @click="skillStore.decreaseAttribute(attr)"
            >
              -
            </button>
            <div class="point-boxes">
              <div
                v-for="i in maxAttributePoints"
                :key="i"
                class="point-box"
                :class="{
                  initial: i <= skillStore.initialAttributes[attr],
                  user: i > skillStore.initialAttributes[attr] && i <= value,
                  empty: i > value
                }"
              ></div>
            </div>
            <button
              class="control-btn plus"
              :class="{
                disabled: skillStore.usedUserPoints >= userPoints || value >= maxAttributePoints
              }"
              @click="skillStore.increaseAttribute(attr)"
            >
              +
            </button>
          </div>
        </div>
      </div>

      <button
        class="confirm-btn"
        :disabled="skillStore.usedUserPoints < userPoints"
        @click="handleConfirm"
      >
        Confirm
      </button>
    </div>
  </BaseDrawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useSkillStore } from '@/store/skill'
import BaseDrawer from './BaseDrawer.vue'
import { IconPlus, IconMinus } from '@arco-design/web-vue/es/icon'
import { useChatStore } from '@/store/chat'
import { useStoryStore } from '@/store/story'
import { useRouter } from 'vue-router'
import { ReportEvent } from '@/interface'
import { reportEvent } from '@/utils'

const isVisible = defineModel<boolean>('visible')
const skillStore = useSkillStore()
const maxAttributePoints = 10 // 每个属性的最大值
const userPoints = 20 // 用户可分配的点数
const router = useRouter()

const emit = defineEmits<{
  (e: 'confirm'): void
}>()

const handleConfirm = () => {
  const chatStore = useChatStore()
  const storyStore = useStoryStore()
  const storyId =
    storyStore.currentStory?.id ||
    (router.currentRoute.value.params.storyId as string) ||
    'stepsister1'
  const actorId = storyStore.currentActor?.id

  if (actorId) {
    reportEvent(ReportEvent.UserAllocateAttribute, {
      storyId: storyId,
      actorId: actorId,
      gameConfig: {
        skill_id: skillStore.selectedSkills[0]?.id,
        numerical_values: skillStore.userAllocatedPoints
      }
    })
  }

  emit('confirm')
}
</script>

<style lang="less" scoped>
.skill-allocation {
  color: #fff;
  padding: 0 16px 24px;
}

.header {
  text-align: center;
  margin-bottom: 32px;

  h2 {
    font-size: 20px;
    font-weight: 700;
    margin: 0 0 8px;
  }

  .total-points {
    font-size: 15px;
    color: rgba(255, 255, 255, 0.8);
  }
}

.attributes-list {
  margin-bottom: 32px;
}

.attribute-item {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.attribute-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 15px;
}

.attribute-name {
  font-weight: 700;
  text-transform: capitalize;
}

.attribute-values {
  display: flex;
  align-items: center;
  gap: 8px;
}

.initial-value {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
}

.attribute-value {
  color: #daff96;
}

.attribute-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-btn {
  width: 24px;
  height: 24px;
  border-radius: 40px;
  border-top: 1px solid #1f0038;
  border-right: 1px solid #1f0038;
  border-bottom: 2px solid #1f0038;
  border-left: 1px solid #1f0038;
  background: linear-gradient(180deg, #d6cafe 0%, #ca93f2 100%);
  box-shadow: 0px 2px 5px 0px rgba(176, 152, 255, 0.26);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  &:active {
    transform: scale(0.95);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.point-boxes {
  flex: 1;
  flex-shrink: 0;
  display: flex;
  gap: 4px;
  align-items: center;
  border-radius: 39px;
  background: rgba(202, 147, 242, 0.2);
  padding: 6px 12px;
}

.point-box {
  flex: 1;
  height: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  transition: all 0.3s ease;

  &.initial {
    background: rgba(218, 255, 150, 0.3);
  }

  &.user {
    background: #daff96;
  }

  &.empty {
    background: rgba(255, 255, 255, 0.1);
  }
}

.confirm-btn {
  width: 100%;
  height: 42px;
  border-radius: 40px;
  border: none;
  border-radius: 40px;
  background: #ca93f2;
  color: #241d49;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}
</style>
