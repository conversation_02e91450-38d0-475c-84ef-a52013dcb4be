<template>
  <div class="story-card" @click="handleCardClick">
    <div class="story-image" :class="{ 'image-loaded': imageLoaded }">
      <div class="image-placeholder" :style="generatePlaceholderColor()" />
      <img
        v-img-compress
        loading="lazy"
        :src="imageUrl"
        :alt="story.title"
        @load="onImageLoad"
        @error="onImageError"
        decoding="async"
        fetchpriority="high"
      />
      <div v-if="story.badge" class="story-badge" :style="{ opacity: imageLoaded ? 1 : 0 }">
        {{ story.badge }}
      </div>
      <div
        v-if="story.status === 'preparing'"
        class="story-badge coming-soon"
        :style="{ opacity: imageLoaded ? 1 : 0 }"
      >
        Coming Soon
      </div>
      <div
        v-if="!['normal', 'preparing'].includes(story.status) && isAdmin"
        class="story-badge admin-only"
        :style="{ opacity: imageLoaded ? 1 : 0 }"
      >
        <span class="admin-icon">👑</span>
        <span>Admin Only</span>
      </div>

      <div class="story-overlay" :style="{ opacity: imageLoaded ? 1 : 0 }">
        <div class="story-name">{{ story?.title || 'Untitled' }}</div>
        <div class="story-count">💘 {{ story?.hot || 0 }}</div>
        <div class="story-button" v-if="story.status === 'normal'">
          <img src="https://cdn.magiclight.ai/assets/playshot/play.png" alt="Play" />
        </div>
      </div>
      <div
        class="subscription-status"
        v-if="story.status === 'preparing'"
        :style="{ opacity: imageLoaded ? 1 : 0 }"
      >
        <button
          class="subscription-button"
          :class="{ subscribed: story.is_subscribed, loading: isSubscribing }"
          :disabled="isSubscribing"
        >
          <div class="subscription-button-icon">
            <div v-if="isSubscribing" class="loading-spinner"></div>
            <template v-else>
              <CheckGrayIcon v-if="story.is_subscribed" />
              <AlarmBlackIcon v-else />
            </template>
          </div>
          <div class="subscription-button-text">
            {{ isSubscribing ? 'Loading...' : story.is_subscribed ? 'Subscribed' : 'Subscribe' }}
          </div>
        </button>
      </div>
    </div>
  </div>
  <ConfirmDialog
    v-model:visible="showEmailDialog"
    title="Subscribe"
    :title-style="{ color: '#fff' }"
    :content-style="{ color: '#CA93F2', fontWeight: '600' }"
    confirm-text="Subscribe"
    cancel-text="Cancel"
    :show-icon="false"
    :close-on-click-overlay="false"
    :onBeforeConfirm="validateEmailInput"
    @confirm="handleEmailConfirm"
  >
    <template #content> Enter your email for updates. </template>
    <template #input>
      <input
        v-model="emailInput"
        type="email"
        placeholder="Enter your email"
        @keyup.enter="validateAndConfirm"
      />
      <div v-if="emailError" class="error-message">{{ emailError }}</div>
    </template>
  </ConfirmDialog>
</template>

<script setup lang="ts">
import type { Story } from '@/api/stories'
import { subscribeStory, unsubscribeStory, getStorySubscribeStatus } from '@/api/stories'
import { computed, ref, onMounted, defineEmits, watch } from 'vue'
import { Message } from '@/mobile/components/Message'
import { useDebounceFn } from '@vueuse/core'
import { useUserStore } from '@/store'
import AlarmBlackIcon from '@/assets/icon/alarm-black.svg'
import CheckGrayIcon from '@/assets/icon/check-gray.svg'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { animate } from 'motion'
import ConfirmDialog from '@/components/ConfirmDialog.vue'

const props = defineProps<{
  story: Story
}>()

const userStore = useUserStore()
const showEmailDialog = ref(false)
const emailInput = ref('')
const emailError = ref('')

const emit = defineEmits<{
  (e: 'click', story: Story): void
  (e: 'image-loaded'): void
  (e: 'image-error'): void
  (e: 'subscription-change', story: Story): void
  (e: 'need-login'): void
}>()

const imageLoaded = ref(false)
const hasError = ref(false)
const isSubscribing = ref(false)

const imageUrl = computed(
  () =>
    props.story.preview_url || 'https://static.playshot.ai/static/images/story/default-preview.png'
)

const generatePlaceholderColor = () => {
  return {
    background: '#140a29',
    backgroundImage: 'linear-gradient(234deg, #1f0038 0%, #140a29 100%)',
    animation: !imageLoaded.value ? 'placeholderShimmer 2s infinite linear' : 'none'
  }
}

const onImageLoad = () => {
  setTimeout(() => {
    imageLoaded.value = true
    emit('image-loaded')
  }, 50)
}

const onImageError = () => {
  hasError.value = true
  emit('image-error')
}

const handleCardClick = (event: MouseEvent) => {
  // 如果故事状态为准备中，点击订阅按钮，则触发订阅状态扭转，点击其他地方，则提示故事coming soon
  const subscriptionButton = (event.target as HTMLElement)?.closest('.subscription-button')
  if (props.story.status === 'preparing') {
    if (subscriptionButton) {
      handleSubscriptionToggle()
    } else {
      // 获取当前卡片元素
      const card = event.currentTarget as HTMLElement
      // 添加更轻柔的摇头动画
      animate(
        card,
        {
          x: [0, -4, 4, -4, 4, -2, 2, 0],
          rotate: [0, -0.5, 0.5, -0.5, 0.5, -0.3, 0.3, 0]
        },
        {
          duration: 1.2,
          easing: [0.26, 0.09, 0.18, 0.93]
        }
      )
      Message.info('The Story is coming soon, stay tuned')
    }
  } else {
    emit('click', props.story)
  }
}

// 监听弹窗显示状态，当弹窗打开时，自动填入上次使用的邮箱
watch(showEmailDialog, (newVal) => {
  if (newVal) {
    // 优先使用上次成功订阅的邮箱
    const lastUsedEmail = localStorage.getItem('lastSubscriptionEmail')
    emailInput.value = lastUsedEmail || ''
  }
})

const validateEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const validateEmailInput = () => {
  if (!validateEmail(emailInput.value)) {
    emailError.value = 'Please enter a valid email address'
    return false
  }
  emailError.value = ''
  return true
}

const validateAndConfirm = () => {
  if (validateEmailInput()) {
    handleEmailConfirm()
  }
}

const handleEmailConfirm = async () => {
  // 保存成功使用的邮箱
  localStorage.setItem('lastSubscriptionEmail', emailInput.value)
  await handleSubscriptionToggle(true)
}

// 检查是否需要手动输入邮箱
const checkNeedManualEmail = (bypassEmailCheck: boolean) => {
  return (
    !props.story.is_subscribed &&
    !bypassEmailCheck &&
    (!userStore.userInfo?.email || !validateEmail(userStore.userInfo.email))
  )
}

// 获取用于订阅的邮箱
const getSubscriptionEmail = () => {
  return validateEmail(userStore.userInfo?.email || '')
    ? userStore.userInfo?.email
    : emailInput.value
}

// 上报订阅事件
const reportSubscriptionEvent = () => {
  reportEvent(ReportEvent.StoryCardSubscriptionClick, {
    story_id: props.story.id,
    is_subscribed: props.story.is_subscribed,
    is_guest: userStore.userInfo?.role === 'guest',
    user_email: userStore.userInfo?.email
  })
}

// 检查服务器端订阅状态
const checkServerSubscriptionStatus = async () => {
  const { data } = await getStorySubscribeStatus(props.story.id)
  const story = data.data as unknown as Story
  if (story.is_subscribed !== props.story.is_subscribed) {
    emit('subscription-change', { ...props.story, is_subscribed: story.is_subscribed })
    return true
  }
  return false
}

// 执行订阅/取消订阅操作
const executeSubscriptionAction = async () => {
  if (props.story.is_subscribed) {
    await unsubscribeStory(props.story.id)
    Message.success('Unsubscribed successfully')
  } else {
    await subscribeStory(props.story.id, getSubscriptionEmail())
    Message.success("We'll email you when it's live")
  }
  emit('subscription-change', { ...props.story, is_subscribed: !props.story.is_subscribed })
}

// 确保最小loading时间
const ensureMinLoadingTime = async (startTime: number) => {
  const endTime = Date.now()
  const loadingTime = endTime - startTime
  if (loadingTime < 300) {
    await new Promise((resolve) => setTimeout(resolve, 300 - loadingTime))
  }
}

// 检查登录状态
const checkLoginStatus = () => {
  if (!userStore.isAuthenticated || userStore.isGuest) {
    emit('need-login')
    return true
  }
  return false
}

const handleSubscriptionToggle = useDebounceFn(async (bypassEmailCheck = false) => {
  if (isSubscribing.value) return

  try {
    // 1. 所有操作都需要先登录
    if (checkLoginStatus()) {
      return
    }

    // 2. 检查是否需要手动输入邮箱（已登录但邮箱格式不正确时）
    if (checkNeedManualEmail(bypassEmailCheck)) {
      showEmailDialog.value = true
      return
    }

    const startTime = Date.now()
    isSubscribing.value = true

    // 3. 上报事件
    reportSubscriptionEvent()

    // 4. 检查服务器端状态
    const statusMismatch = await checkServerSubscriptionStatus()
    if (statusMismatch) return

    // 5. 执行订阅/取消订阅操作
    await executeSubscriptionAction()

    // 6. 确保最小loading时间
    await ensureMinLoadingTime(startTime)
  } catch (error) {
    Message.error('Failed to update subscription')
  } finally {
    isSubscribing.value = false
  }
}, 300)

// 预加载图片
// onMounted(() => {
//   if (imageUrl.value) {
//     const img = new Image()
//     img.src = imageUrl.value
//     img.onload = onImageLoad
//     img.onerror = onImageError
//   }
// })

// Computed property to check if current user is an admin
const isAdmin = computed(() => {
  return userStore.userInfo?.role === 'admin'
})
</script>

<style lang="less" scoped>
@keyframes placeholderShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes breathing {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.breathing-animation {
  animation: breathing 2s ease-in-out infinite;
}

.story-card {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  background: #1f0038;
  width: 100%;
  aspect-ratio: 167/294;
  transform: translateZ(0); // 强制硬件加速
  will-change: transform; // 提示浏览器该元素会有变化

  // iPad
  @media screen and (min-width: 768px) and (max-width: 1024px) {
    width: 100%;
    aspect-ratio: 167/294;
  }

  // Desktop
  @media screen and (min-width: 1025px) {
    width: 100%;
    aspect-ratio: 167/294;
  }

  .story-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    &.loading {
      .story-badge,
      .story-overlay {
        opacity: 0;
        visibility: hidden;
      }
    }

    .image-placeholder {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      transition: opacity 0.3s ease;
      background-size: 200% 100%;
      z-index: 1;
      pointer-events: none;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      opacity: 0;
      transition: opacity 0.3s ease;
      transform: translateZ(0);
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
      z-index: 2;
    }

    &.image-loaded {
      .image-placeholder {
        opacity: 0;
      }

      img {
        opacity: 1;
      }

      .story-badge,
      .story-overlay {
        opacity: 1;
        visibility: visible;
        transition:
          opacity 0.3s ease,
          visibility 0.3s ease;
      }
    }

    .story-badge {
      position: absolute;
      top: 0;
      left: 0;
      border-radius: 0px 0px 10px 0px;
      background: #ca93f2;
      display: inline-flex;
      padding: 2px 10px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      color: #fff;
      font-size: 11px;
      font-weight: 600;
      z-index: 3;
      transition: opacity 0.5s ease;
      will-change: opacity;

      &.coming-soon {
        background: #daff96;
        color: #1f0038;
      }

      &.admin-only {
        left: auto;
        right: 0;
        top: 0;
        background: #ff6b6b;
        border-radius: 0px 0px 0px 10px;
        padding: 2px 8px;
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 10px;

        .admin-icon {
          font-size: 12px;
        }
      }
    }

    .story-overlay {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      gap: 8px;
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 16px;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
      color: white;
      z-index: 3;
      transition: opacity 0.5s ease;
      will-change: opacity;

      .story-name {
        font-size: 12px;
        font-weight: 600;
        margin-bottom: 14px;
      }

      .story-subtitle {
        font-size: 15px;
        font-weight: 600;
        margin-bottom: 12px;
      }

      .story-count {
        font-size: 11px;
        font-weight: 400;
      }

      .story-button {
        display: inline-flex;
        align-items: center;
        position: absolute;
        right: 4px;
        bottom: 10px;
        img {
          width: 40px;
          height: 40px;
        }

        &:not(:disabled) {
          .breathing-animation();
        }
      }
    }
  }
  .subscription-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    width: 80%;
    gap: 12px;
    z-index: 10;
    pointer-events: auto;
    transition: opacity 0.5s ease;
    will-change: opacity;

    .status-text {
      font-size: 14px;
      font-weight: 600;
      color: white;
      margin-bottom: 8px;
      transition: all 0.3s ease;
    }

    .subscription-button {
      width: fit-content;
      min-width: 120px;
      height: 30px;
      border-radius: 18px;
      font-weight: 600;
      cursor: pointer;
      color: #1f0038;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      border: 1px solid #daff96;
      font-size: 12px;
      background: #daff96;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      padding: 8px 12px;
      -webkit-tap-highlight-color: transparent;
      &.subscribed {
        background: rgba(0, 0, 0, 0.5);
        border-color: transparent;
        color: rgba(255, 255, 255, 0.8);
      }
      &.loading {
        cursor: not-allowed;
        opacity: 0.7;
      }
      &-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        flex-shrink: 0;
        :deep(svg) {
          width: 14px;
          height: 14px;
        }
      }
      &-text {
        flex: 1;
        line-height: 1;
        text-align: left;
        padding-right: 8px;
        white-space: nowrap;
      }
      .loading-spinner {
        width: 14px;
        height: 14px;
        border: 2px solid transparent;
        border-top-color: currentColor;
        border-radius: 50%;
        animation: spin 0.8s linear infinite;
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
