<template>
  <div class="video-player" v-if="videoUrl && chatResourcesStore.isPlayingVideo">
    <!-- Loading Overlay -->
    <transition name="fade">
      <LoadingOverlay
        v-if="(isLoading && !chatResourcesStore.isFirstVideo) || forceLoading"
        :progress="loadingProgress"
        :show-progress="true"
        :background-image="loadingBackground"
      />
    </transition>

    <!-- Play Button Overlay -->
    <transition name="fade-scale">
      <div
        v-if="needsPlayButton || chatResourcesStore.isFirstVideo || (needsPlayButton && isPlaying)"
        class="play-button-overlay"
      >
        <button class="play-button" @click="handlePlayClick">
          <icon-play-circle-fill class="play-icon" />
          <span> Click to Play</span>
        </button>
      </div>
    </transition>

    <!-- Video Canvas -->
    <transition name="fade">
      <canvas v-if="isPlaying" ref="canvasRef" class="background-video" />
    </transition>

    <!-- Hidden Video Element -->
    <video
      ref="videoRef"
      class="hidden-video"
      @ended="onVideoEnded"
      @loadedmetadata="onVideoLoaded"
      @canplay="onVideoCanPlay"
      webkit-playsinline="true"
      playsinline="true"
      :muted="muted"
      x5-playsinline="true"
      x5-video-player-type="h5"
      x5-video-player-fullscreen="true"
      autoplay="true"
    />
    <!-- todo: above preload will cause autoplay to fail, need to investigate -->
    <!-- Video Controls -->
    <transition name="fade-slide-up">
      <div v-if="isPlaying" class="video-controls">
        <button v-if="!isLoading" class="volume-button" @click="toggleSound">
          <icon-volume-notice v-if="!muted" />
          <icon-volume-mute v-else />
        </button>
        <button v-if="isShowSkipButton" class="skip-button" @click="skipVideo">Skip</button>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeUnmount, computed, nextTick, onMounted } from 'vue'
import { animate } from 'animejs'
import IconVolumeNotice from '@/assets/icon/volume-notice.svg'
import IconVolumeMute from '@/assets/icon/volume-mute.svg'
import IconPlayCircleFill from '@/assets/icon/play-circle-fill.svg'
import LoadingOverlay from './LoadingOverlay.vue'
import { useVideoCanvas } from '@/mobile/composables/useVideoCanvas'
import { professionalVideoCache } from '@/utils/professionalVideoCache'
import { useVideoStreaming } from '@/mobile/composables/useVideoStreaming'
import { useStoryStore } from '@/store'
import { useChatResourcesStore } from '@/store/chat-resources'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { useLocalStorage } from '@vueuse/core'

const chatResourcesStore = useChatResourcesStore()
const storyStore = useStoryStore()
const videoStreaming = useVideoStreaming()

const props = defineProps<{
  videoUrl: string | null
  isPlaying: boolean
  muted: boolean
  storyId: string
  actorId: string
  loadingBackground?: string
  forceLoading?: boolean
}>()

const isShowSkipButton = ref(false)
const videoStartTime = ref<number>(0)

// Timer to check if minimum watch duration has passed
const checkMinWatchDuration = () => {
  if (!chatResourcesStore.videoMinWatchDuration || chatResourcesStore.videoMinWatchDuration <= 0) {
    isShowSkipButton.value = true
    return
  }

  const currentTime = Date.now()
  const elapsedTime = (currentTime - videoStartTime.value) / 1000 // Convert to seconds

  if (elapsedTime >= chatResourcesStore.videoMinWatchDuration) {
    isShowSkipButton.value = true
  } else {
    // Check again after the remaining time
    const remainingTime = (chatResourcesStore.videoMinWatchDuration - elapsedTime) * 1000
    setTimeout(
      () => {
        isShowSkipButton.value = true
      },
      Math.max(0, remainingTime)
    )
  }
}

const emit = defineEmits<{
  'update:isPlaying': [value: boolean]
  'update:muted': [value: boolean]
  'video-ended': []
  'video-loaded': []
  'video-skipped': []
}>()

const {
  canvasRef,
  videoRef,
  clearVideo,
  startVideo,
  stopVideo,
  needsPlayButton,
  resumeAnimation,
  pauseAnimation
} = useVideoCanvas()

const isLoading = ref(false)
const loadingProgress = ref(0)
const playedVideos = useLocalStorage<Set<string>>('played_videos', new Set())
const skipRequestedDuringLoading = ref(false)
const loadingStartTime = ref<number>(0)
const currentXHR = ref<XMLHttpRequest | null>(null)
const loadingBackground = computed(() => props.loadingBackground)
const useStreamingMode = ref(true) // 是否使用分片加载模式

// 公共函数：获取视频名称
const getVideoName = (url: string | null): string => {
  if (!url) return 'unknown'
  return url.split('/').pop() || 'unknown'
}

// 恢复：创建淡入淡出动画，保持视频最后一帧的淡出效果
const createFadeAnimation = (
  element: HTMLCanvasElement | null,
  options: { fadeIn?: boolean; onComplete?: () => void }
) => {
  if (!element) {
    if (options.onComplete) options.onComplete()
    return
  }

  const defaultOptions = options.fadeIn
    ? {
        opacity: [0, 1],
        scale: [0.98, 1], // 轻微的缩放效果，增强视觉体验
        duration: 600,
        ease: 'easeOutCubic'
      }
    : {
        opacity: [1, 0],
        scale: [1, 1.02], // 视频结束时轻微放大淡出，营造无缝衔接感
        duration: 800, // 较慢的淡出，给背景切换留时间
        ease: 'easeOutQuart'
      }

  animate(element, {
    ...defaultOptions,
    onComplete: options.onComplete
  })
}

// Video Event Handlers
const onVideoEnded = () => {
  console.log('onVideoEnded')
  isLoading.value = false

  const video = videoRef.value
  if (!video || !props.videoUrl) return

  const videoName = getVideoName(props.videoUrl)
  const isFirstPlay = !playedVideos.value.has(videoName)

  // 记录已播放视频
  playedVideos.value.add(videoName)

  // 报告视频完成事件
  reportEvent(ReportEvent.VideoCompleted, {
    videoName,
    duration: video.duration ? `${video.duration.toFixed(2)}s` : 'unknown',
    playedTime: video.currentTime ? `${video.currentTime.toFixed(2)}s` : '0s',
    isFirstPlay,
    storyId: props.storyId,
    actorId: props.actorId
  })

  // 先发出视频结束事件，让背景开始切换
  emit('video-ended')

  // 延迟执行淡出动画，给背景切换一些时间
  setTimeout(() => {
    // 创建视频结束时的淡出动画，与背景切换同步
    createFadeAnimation(canvasRef.value, {
      fadeIn: false,
      onComplete: () => {
        // 动画完成后才更新播放状态和清理视频
        emit('update:isPlaying', false)
        clearVideo()
      }
    })
  }, 100) // 给背景切换100ms的准备时间
}

const onVideoLoaded = () => {
  const video = videoRef.value
  if (!video) return

  video.muted = props.muted
  if (video.readyState < 1) return

  // 延迟检查自动播放
  setTimeout(async () => {
    try {
      await video.play()
      needsPlayButton.value = false
      emit('video-loaded')
    } catch (error) {
      console.log('Autoplay prevented:', error)
      // 只有在自动播放被阻止时才显示播放按钮
      const isAutoplayBlocked = ['NotAllowedError', 'NotSupportedError'].includes(error.name)
      needsPlayButton.value = isAutoplayBlocked
    }
  }, 100)
}

const onVideoCanPlay = () => {
  // 创建视频开始播放时的淡入动画
  createFadeAnimation(canvasRef.value, { fadeIn: true })

  emit('update:isPlaying', true)

  // 重置跳过按钮可见性并开始计时
  isShowSkipButton.value = false
  videoStartTime.value = Date.now()
  checkMinWatchDuration()
}

// Control Functions
const toggleSound = () => {
  if (!videoRef.value) return
  reportEvent(ReportEvent.ClickVolume, {
    storyId: props.storyId,
    actorId: props.actorId,
    isMuted: props.muted
  })
  const newMutedState = !props.muted
  videoRef.value.muted = newMutedState
  emit('update:muted', newMutedState)
}

// 提取公共函数：清理视频资源和状态
const cleanupVideoResources = (video: HTMLVideoElement) => {
  // 中止当前请求
  if (currentXHR.value) {
    currentXHR.value.abort()
    currentXHR.value = null
  }

  // 中止流式加载
  if (videoStreaming.isLoading.value) {
    videoStreaming.abortLoading()
  }

  // 暂停视频
  try {
    video.pause()
    video.currentTime = 0
    video.removeAttribute('src')
    video.load()
  } catch (e) {
    console.warn('Error cleaning up video:', e)
  }

  // 停止和清理视频
  stopVideo()
  clearVideo()

  // 重置所有状态
  emit('update:isPlaying', false)
  needsPlayButton.value = false
  chatResourcesStore.isFirstVideo = false
  isLoading.value = false
  loadingProgress.value = 0
  currentVideoUrl.value = null
  isShowSkipButton.value = false
}

// 公共函数：报告视频跳过事件
const reportVideoSkipped = (
  videoName: string,
  isFirstPlay: boolean,
  playedTime = '0s',
  duration = 'unknown'
) => {
  reportEvent(ReportEvent.VideoSkipped, {
    videoName,
    duration,
    playedTime,
    isFirstPlay,
    storyId: props.storyId,
    actorId: props.actorId
  })
}

const skipVideo = async () => {
  // 获取视频元素
  const video = videoRef.value
  if (!video) {
    console.warn('Video element not found')
    emit('video-skipped')
    return
  }

  // 获取视频名称
  const videoName = props.videoUrl ? getVideoName(props.videoUrl) : getVideoName(video.src)

  // 记录播放状态
  const isFirstPlay = !playedVideos.value.has(videoName)
  playedVideos.value.add(videoName)

  // 处理加载状态下的跳过
  if (isLoading.value) {
    skipRequestedDuringLoading.value = true
    cleanupVideoResources(video)
    emit('video-skipped')
    reportVideoSkipped(videoName, isFirstPlay)
    return
  }

  // 处理正常状态下的跳过
  const playedTime = video.currentTime ? `${video.currentTime.toFixed(2)}s` : '0s'
  const duration = video.duration ? `${video.duration.toFixed(2)}s` : 'unknown'
  reportVideoSkipped(videoName, isFirstPlay, playedTime, duration)

  // 清理资源
  cleanupVideoResources(video)
  emit('video-skipped')
}

// 播放按钮点击处理
const handlePlayClick = async () => {
  reportEvent(ReportEvent.ClickToPlay, {
    storyId: props.storyId,
    actorId: props.actorId
  })

  const video = videoRef.value
  if (!video) return

  // 创建点击播放按钮后的动画
  animate('.play-button-overlay', {
    opacity: [1, 0],
    scale: [1, 0.9],
    duration: 400,
    ease: 'easeOutQuad'
  })

  // 更新状态
  needsPlayButton.value = false
  chatResourcesStore.isFirstVideo = false

  // 尝试播放视频
  try {
    await video.play()
    emit('update:isPlaying', true)

    // 重置跳过按钮可见性并开始计时
    isShowSkipButton.value = false
    videoStartTime.value = Date.now()
    checkMinWatchDuration()
  } catch (error) {
    console.error('Play failed:', error)
  }
}

// 监听键盘事件
const handleKeyboardUp = () => {
  // 键盘弹起时，不停止动画，让useVideoCanvas中的检测机制处理
  console.log('Keyboard up detected in VideoPlayer')
}

const handleKeyboardDown = () => {
  // 键盘收起时，确保动画恢复
  console.log('Keyboard down detected in VideoPlayer')
  resumeAnimation()
}

// 添加键盘事件监听
onMounted(() => {
  document.addEventListener('KeyboardUp', handleKeyboardUp)
  document.addEventListener('KeyboardDown', handleKeyboardDown)
})

// 清理事件监听
onBeforeUnmount(() => {
  document.removeEventListener('KeyboardUp', handleKeyboardUp)
  document.removeEventListener('KeyboardDown', handleKeyboardDown)
})

// Watch videoUrl changes
const currentVideoUrl = ref<string | null>(null)

// 提取公共函数：创建视频加载报告
const reportVideoLoaded = (
  url: string,
  loadingStartTime: number,
  options: { fromCache?: boolean; streamingMode?: boolean } = {}
) => {
  const loadingDuration = ((Date.now() - loadingStartTime) / 1000).toFixed(2)
  reportEvent(ReportEvent.VideoLoaded, {
    videoName: getVideoName(url),
    loadingDuration: `${loadingDuration}s`,
    storyId: props.storyId,
    actorId: props.actorId,
    ...options
  })
}

// 提取公共函数：报告视频加载失败
const reportVideoLoadFailed = (url: string, loadingStartTime: number) => {
  const loadingDuration = ((Date.now() - loadingStartTime) / 1000).toFixed(2)
  reportEvent(ReportEvent.VideoLoadFailed, {
    videoName: getVideoName(url),
    loadingDuration: `${loadingDuration}s`,
    storyId: props.storyId,
    actorId: props.actorId
  })
}

// 提取公共函数：从Blob创建DataURL并开始播放视频
const startVideoFromBlob = async (
  blob: Blob,
  url: string,
  options: { fromCache?: boolean; streamingMode?: boolean } = {}
) => {
  return new Promise<void>((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = async () => {
      try {
        const dataUrl = reader.result as string
        startVideo(dataUrl)
        isLoading.value = false

        // 报告视频加载
        reportVideoLoaded(url, loadingStartTime.value, options)
        resolve()
      } catch (error) {
        console.error('Failed to start video with blob data:', error)
        reject(error)
      }
    }

    reader.onerror = () => {
      console.error('Failed to read blob data')
      reject(new Error('Failed to read blob data'))
    }

    reader.readAsDataURL(blob)
  })
}

// 提取公共函数：等待视频缓冲
const waitForVideoBuffer = async (video: HTMLVideoElement, dataUrl: string): Promise<void> => {
  try {
    return new Promise<void>((resolve, reject) => {
      // 设置超时以防止无限等待
      const timeoutId = setTimeout(() => {
        reject(new Error('Video buffer timeout'))
      }, 30000) // 30秒超时

      // 监听视频缓冲进度
      const checkBuffer = () => {
        if (!video.buffered.length) return false
        const bufferedEnd = video.buffered.end(0)
        const minBufferTime = Math.min(5, video.duration)
        return bufferedEnd >= minBufferTime
      }

      video.src = dataUrl
      video.load()

      const bufferCheck = setInterval(() => {
        if (checkBuffer()) {
          clearInterval(bufferCheck)
          clearTimeout(timeoutId)
          resolve()
        }
      }, 100)

      video.oncanplaythrough = () => {
        clearInterval(bufferCheck)
        clearTimeout(timeoutId)
        resolve()
      }

      video.onerror = () => {
        clearInterval(bufferCheck)
        clearTimeout(timeoutId)
        reject(new Error(`Video error: ${video.error?.message || 'unknown error'}`))
      }
    })
  } catch (error) {
    console.warn('Buffer loading error, continuing anyway:', error)
  }
}

// 提取公共函数：尝试直接URL播放（作为后备方案）
const tryDirectUrlFallback = (url: string) => {
  try {
    startVideo(url)
    isLoading.value = false
    return true
  } catch (error) {
    console.error('Direct URL fallback failed:', error)
    isLoading.value = false
    return false
  }
}

// 提取公共函数：清理旧视频
const cleanupOldVideo = (video: HTMLVideoElement | null) => {
  if (!video) return

  try {
    video.pause()
    video.currentTime = 0
    stopVideo()
    clearVideo()
    video.removeAttribute('src')
    video.load()
    currentVideoUrl.value = null
  } catch (e) {
    console.warn('Error cleaning up old video:', e)
  }
}

// 提取公共函数：加载视频的主要逻辑
const loadVideo = async (url: string) => {
  // 确保视频元素存在
  if (!videoRef.value) {
    console.error('Video element not found')
    isLoading.value = false
    return
  }

  // 中止当前请求
  if (currentXHR.value) {
    currentXHR.value.abort()
  }

  // 中止流式加载
  if (videoStreaming.isLoading.value) {
    videoStreaming.abortLoading()
  }

  isLoading.value = true
  loadingProgress.value = 0

  try {
    // 1. 尝试从专业缓存加载
    const cachedBlob = await professionalVideoCache.getCachedVideo(url)
    if (cachedBlob && !skipRequestedDuringLoading.value) {
      await startVideoFromBlob(cachedBlob, url, { fromCache: true })
      return
    }

    // 2. 尝试从预加载的Blob加载
    const preloadedBlob = storyStore.getPreloadedBlob(url)
    if (preloadedBlob && !skipRequestedDuringLoading.value) {
      await startVideoFromBlob(preloadedBlob, url, { fromCache: false })

      // 尝试缓存预加载的blob到专业缓存
      professionalVideoCache.cacheVideo(url, preloadedBlob).catch((error) => {
        console.warn('Failed to cache preloaded video:', error)
      })
      return
    }

    // 3. 尝试使用流式加载
    const canUseStreaming = videoStreaming.isSupported.value && useStreamingMode.value
    if (canUseStreaming) {
      try {
        console.log('Using streaming mode for video loading')

        await videoStreaming.startStreaming(videoRef.value, url, (progress) => {
          loadingProgress.value = progress
        })

        isLoading.value = false
        reportVideoLoaded(url, loadingStartTime.value, { streamingMode: true })
        return
      } catch (error) {
        console.error('Streaming mode failed, falling back to traditional loading:', error)
      }
    }

    // 4. 使用传统XHR加载
    const xhr = new XMLHttpRequest()
    currentXHR.value = xhr
    xhr.open('GET', url, true)
    xhr.responseType = 'blob'

    xhr.onprogress = (event) => {
      if (event.lengthComputable) {
        loadingProgress.value = Math.round((event.loaded / event.total) * 100)
      }
    }

    xhr.onload = async () => {
      currentXHR.value = null

      if (xhr.status !== 200) {
        isLoading.value = false
        reportVideoLoadFailed(url, loadingStartTime.value)
        return
      }

      // 如果在加载过程中请求了跳过
      if (skipRequestedDuringLoading.value) {
        skipRequestedDuringLoading.value = false
        isLoading.value = false
        emit('video-skipped')
        return
      }

      const videoBlob = xhr.response

      try {
        // 创建DataURL
        const reader = new FileReader()
        reader.onload = async () => {
          const dataUrl = reader.result as string

          // 确认视频元素仍然存在
          if (!videoRef.value) {
            console.error('Video element not found after XHR completed')
            isLoading.value = false
            return
          }

          // 等待视频缓冲
          await waitForVideoBuffer(videoRef.value, dataUrl)

          // 开始播放视频
          startVideo(dataUrl)
          isLoading.value = false

          // 报告视频加载完成
          reportVideoLoaded(url, loadingStartTime.value, { streamingMode: false })

          // 缓存视频到专业缓存
          professionalVideoCache.cacheVideo(url, videoBlob).catch((error) => {
            console.warn('Failed to cache video:', error)
          })
        }

        reader.onerror = () => {
          console.error('Failed to create data URL from fetched video')
          tryDirectUrlFallback(url)
        }

        reader.readAsDataURL(videoBlob)
      } catch (error) {
        console.error('Error processing video blob:', error)
        tryDirectUrlFallback(url)
      }
    }

    xhr.onerror = () => {
      currentXHR.value = null
      console.error('Video loading failed')
      isLoading.value = false
      reportVideoLoadFailed(url, loadingStartTime.value)
    }

    xhr.onabort = () => {
      console.log('Video loading aborted')
      currentXHR.value = null
      isLoading.value = false
    }

    xhr.send()
  } catch (error) {
    currentXHR.value = null
    console.error('Error in video loading process:', error)
    isLoading.value = false
  }
}

// 监听videoUrl变化
watch(
  () => props.videoUrl,
  async (newUrl, oldUrl) => {
    // 更新当前URL
    if (newUrl) {
      currentVideoUrl.value = newUrl
    }

    // 清理旧视频
    if (oldUrl) {
      cleanupOldVideo(videoRef.value)
    }

    // 加载新视频
    if (newUrl) {
      skipRequestedDuringLoading.value = false
      loadingStartTime.value = Date.now()
      isShowSkipButton.value = false

      // 等待DOM更新以确保视频元素存在
      await nextTick()

      // 加载视频
      await loadVideo(newUrl)
    }
  }
)

// Cleanup
onBeforeUnmount(() => {
  // 中止所有正在进行的请求
  if (currentXHR.value) {
    currentXHR.value.abort()
    currentXHR.value = null
  }

  // 中止流式加载
  if (videoStreaming.isLoading.value) {
    videoStreaming.abortLoading()
  }

  // 停止视频播放
  stopVideo()
})
</script>

<style lang="less" scoped>
.video-player {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 200;
}

.background-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
  background: #000;
  will-change: transform, opacity;
}

.hidden-video {
  position: absolute;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

// 淡入淡出过渡效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 缩放淡入淡出过渡效果
.fade-scale-enter-active,
.fade-scale-leave-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-scale-enter-from,
.fade-scale-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

// 上滑淡入淡出过渡效果
.fade-slide-up-enter-active,
.fade-slide-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-slide-up-enter-from,
.fade-slide-up-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.video-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1001;
  display: flex;
  gap: 12px;
  align-items: center;
}

.volume-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
  border: none;
}

.skip-button {
  height: 50px;
  padding: 0 20px;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  color: #1f0038;
  font-size: 14px;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
  border: none;
}

.play-button-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  backdrop-filter: blur(2px);
}

.play-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
  border-radius: 24px;
  background: transparent;
  border: none;
  cursor: pointer;
  color: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);

  &:hover {
    transform: scale(1.02);
  }

  &:active {
    transform: scale(0.98);
  }

  .play-icon {
    width: 64px;
    height: 64px;
    opacity: 0.9;
    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.2));
  }

  span {
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}
</style>
