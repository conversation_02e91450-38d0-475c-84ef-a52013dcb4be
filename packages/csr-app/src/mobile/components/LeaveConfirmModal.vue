<template>
  <Teleport to="body">
    <div v-if="visible" class="modal-container" @click.self="handleCancel">
      <div class="modal-wrapper" :class="{ visible }">
        <div class="modal-content">
          <!-- <img
            class="warning-icon"
            src="https://cdn.magiclight.ai/assets/playshot/warning.png"
            alt="warning"
          /> -->
          <div class="modal-title">Leave the game?</div>
          <div class="modal-text">
            Your game progress will be saved, and you can continue it next time.
          </div>
          <div class="modal-buttons">
            <button class="cancel-button" @click="handleCancel">Keep Gaming</button>
            <button class="confirm-button" @click="handleConfirm">Leave Game</button>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface/report'
import { useStoryStore } from '@/store/story'

const storyStore = useStoryStore()

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'confirm'): void
  (e: 'cancel'): void
}>()

// 监听可见性变化，添加/移除 body 滚动锁定
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }
  }
)

const handleConfirm = () => {
  emit('confirm')
  emit('update:visible', false)
  reportEvent(ReportEvent.ClickLeaveChat, {
    storyId: storyStore.currentStory?.id,
    actorId: storyStore.currentActor
  })
}

const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false)
  reportEvent(ReportEvent.ClickKeepChatting, {
    storyId: storyStore.currentStory?.id,
    actorId: storyStore.currentActor
  })
}
</script>

<style lang="less" scoped>
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

.modal-wrapper {
  width: 300px;
  padding: 0 16px;
  opacity: 0;
  transform: scale(0.9) translateY(20px);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  &.visible {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-content {
  width: 100%;
  padding: 24px;
  background: rgba(31, 0, 56, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0px 20px 40px rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.warning-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 4px;
}

.modal-title {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
  line-height: 1.4;
}

.modal-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 15px;
  line-height: 1.6;
  text-align: center;
  margin-bottom: 8px;
  padding: 0 8px;
  word-break: break-word;
  white-space: pre-line;
  letter-spacing: 0.2px;
}

.modal-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  margin-top: 8px;

  button {
    width: 100%;
    height: 48px;
    border-radius: 24px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: transform 0.15s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-tap-highlight-color: transparent;

    &:active {
      transform: scale(0.98);
    }
  }

  .confirm-button {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;

    &:active {
      background: rgba(255, 255, 255, 0.15);
    }
  }

  .cancel-button {
    background: #7c5cff;
    color: #fff;

    &:active {
      background: #6a4deb;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
