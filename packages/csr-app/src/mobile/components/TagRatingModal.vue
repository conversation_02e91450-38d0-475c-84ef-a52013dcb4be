<template>
  <BaseDrawer
    v-model:visible="isVisible"
    height="auto"
    background="#1f0038"
    border-radius="24px 24px 0 0"
    padding="0"
  >
    <div class="drawer-container">
      <div class="fixed-header">
        <div class="title">Select at least three favorite tags</div>
        <button class="close-button" @click="handleClose">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
              d="M18 6L6 18M6 6L18 18"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>

      <div class="scrollable-content">
        <div class="tag-sections">
          <div v-for="(category, key) in hobbyCategories" :key="key" class="tag-section">
            <div class="section-header">
              <img :src="category.icon" class="icon" alt="category icon" />
              <span class="text">{{ formatCategoryName(key) }}</span>
            </div>
            <div class="tag-group">
              <button
                v-for="tag in category.tags"
                :key="tag"
                :class="['tag-button', { selected: selectedTags.includes(tag) }]"
                @click="toggleTag(tag)"
              >
                {{ tag }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="fixed-bottom">
        <button class="submit-button" :disabled="selectedTags.length < 3" @click="handleSubmit">
          <span class="submit-text">Submit and get</span>
          <span class="diamond">
            <span class="credit-amount">10</span>
            <img src="https://cdn.magiclight.ai/assets/mobile/diamond.png" alt="credits" />
          </span>
        </button>
      </div>
    </div>
  </BaseDrawer>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { animate } from 'motion'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { useSysConfigStore } from '@/store/sysconfig'
import { useStoryStore } from '@/store/story'
import BaseDrawer from './BaseDrawer.vue'
import { Message } from '@/mobile/components/Message'

interface Props {
  visible: boolean
  onClose: () => void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const isVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const sysConfigStore = useSysConfigStore()
const storyStore = useStoryStore()
const selectedTags = ref<string[]>([])

interface HobbyCategory {
  icon: string
  tags: string[]
}

type UserHobbyCollection = Record<string, HobbyCategory>

onMounted(async () => {
  if (!sysConfigStore.userHobbyCollection) {
    await sysConfigStore.fetchConfigs()
  }
})

const hobbyCategories = computed<UserHobbyCollection>(() => {
  const categories = (sysConfigStore.userHobbyCollection as UserHobbyCollection) ?? {}
  // Convert to array of entries, shuffle, and convert back to object
  const entries = Object.entries(categories)
  for (let i = entries.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[entries[i], entries[j]] = [entries[j], entries[i]]
  }
  return Object.fromEntries(entries)
})

// Format category name for display
const formatCategoryName = (key: string): string => {
  return key
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

// Watch for visibility changes
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      reportEvent(ReportEvent.RatingModalExposure, {})
    }
  }
)

const toggleTag = (tag: string) => {
  const index = selectedTags.value.indexOf(tag)
  if (index === -1) {
    selectedTags.value.push(tag)
  } else {
    selectedTags.value.splice(index, 1)
  }
}

const handleSubmit = () => {
  if (selectedTags.value.length < 3) {
    Message.warning('Please select at least three tags')
    return
  }

  // Organize selected tags by category
  const tagsByCategory: Record<string, string[]> = {}
  Object.entries(hobbyCategories.value).forEach(([key, category]) => {
    const categoryTags = selectedTags.value.filter((tag) => category.tags.includes(tag))
    if (categoryTags.length > 0) {
      tagsByCategory[key] = categoryTags
    }
  })

  setTimeout(async () => {
    try {
      await storyStore.submitHobbyCollection(tagsByCategory)
      reportEvent(ReportEvent.RatingModalSubmit, {
        selectedTags: selectedTags.value
      })
      selectedTags.value = []
      isVisible.value = false
      Message.success('Thank you for your feedback!')
    } catch (error) {
      console.error('Failed to submit hobby collection:', error)
      Message.error('Failed to submit feedback')
    }
  }, 600)
}

const handleClose = () => {
  isVisible.value = false
  emit('update:visible', false)
  reportEvent(ReportEvent.TagRatingModalClose, {
    storyId: storyStore.currentStory?.id,
    actorId: storyStore.currentActor?.id,
    storyName: storyStore.currentStory?.title,
    actorName: storyStore.currentActor?.name
  })
}
</script>

<style lang="less" scoped>
.drawer-container {
  display: flex;
  flex-direction: column;
  height: 90vh;
  position: relative;
}

.fixed-header {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: #1f0038;
  padding: 24px 24px 16px;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close-button {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    color: rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.1);
  }

  &:active {
    transform: scale(0.95);
  }
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px 24px 0;
  -webkit-overflow-scrolling: touch;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
  }
}

.fixed-bottom {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background: #1f0038;
  padding: 16px 24px 32px;
  z-index: 1;
  display: flex;
  justify-content: center;
}

.title {
  color: #fff;
  font-size: 17px;
  font-weight: 600;
  line-height: 22px;
}

.tag-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding-bottom: 24px;
}

.tag-section {
  .section-header {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 12px;

    .icon {
      width: 24px;
      height: 24px;
      object-fit: contain;
    }

    .text {
      color: rgba(255, 255, 255, 0.5);
      font-size: 13px;
      font-weight: 500;
    }
  }
}

.tag-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-button {
  padding: 6px 10px;
  border-radius: 100px;
  // border: 1px solid #ca93f2;
  border: 1px solid transparent;
  background: rgba(204, 213, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  font-weight: 500;
  line-height: normal;
  &.selected {
    border: 1px solid #ca93f2;
    font-weight: 600;
    color: #fff;
    background: rgba(202, 147, 242, 0.5);
  }
}

.submit-button {
  width: 80%;
  height: 42px;
  background: #ca93f2;
  border: none;
  border-radius: 40px;
  color: #241d49;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  box-shadow: 0 4px 12px rgba(182, 109, 255, 0.2);

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(182, 109, 255, 0.3);
  }

  &:active:not(:disabled) {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(182, 109, 255, 0.2);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: linear-gradient(135deg, #8450bb 0%, #7339bb 100%);
  }

  .submit-text {
    font-weight: 600;
  }

  .diamond {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 8px;

    .credit-amount {
      color: #daff96;
      font-size: 15px;
      font-weight: 600;
    }

    img {
      width: 16px;
      height: 16px;
      object-fit: contain;
    }
  }
}
</style>
