<template>
  <div class="upload-image">
    <div class="preview-container" @click="handleContainerClick">
      <template v-if="uploadedImage">
        <div class="image-wrapper" :class="{ loading: imageLoading }">
          <div v-show="imageLoading" class="loading-animation">
            <div class="spinner">
              <div class="double-bounce1"></div>
              <div class="double-bounce2"></div>
            </div>
          </div>
          <img
            :src="uploadedImage"
            alt="Uploaded"
            class="preview-image"
            @load="handleImageLoad"
            @error="handleImageError"
          />
        </div>
        <IconDelete @click.stop="handleClearUploadImage" class="delete-icon" />
      </template>
      <template v-else>
        <div class="demo-placeholder" :class="{ loading: demoImageLoading }">
          <div class="demo-placeholder-tag">
            <div class="demo-placeholder-tag-text">Demo</div>
          </div>
          <img
            src="https://cdn.magiclight.ai/assets/playshot/upload-img-demo-v3.gif"
            alt="Demo"
            @load="demoImageLoading = false"
            @error="handleDemoImageError"
          />
        </div>
      </template>
    </div>
    <button class="select-button" @click="handleSelectButton">{{ props.buttonText }}</button>
    <button class="skip-button" @click="handleSkipButtonClick">Skip upload</button>
    <!-- Face Requirements Modal -->
    <Teleport to="body">
      <Transition name="drawer">
        <div v-if="showFaceModal" class="drawer-container">
          <div
            class="drawer-mask"
            :class="{ active: showFaceModal }"
            @click="showFaceModal = false"
          ></div>
          <div class="drawer-content" :class="{ active: showFaceModal }">
            <div class="face-requirements">
              <h3>Face</h3>
              <div class="requirements-container">
                <div class="requirement-image">
                  <img
                    src="https://cdn.magiclight.ai/assets/playshot/upload-face-demo.png"
                    alt="Face Example"
                  />
                </div>
                <div class="requirements-list">
                  <div class="requirement-item">
                    <IconCorrect class="correct-icon" />
                    <span>Simple background</span>
                  </div>
                  <div class="requirement-item">
                    <IconCorrect class="correct-icon" />
                    <span>Frontal headshot</span>
                  </div>

                  <div class="requirement-item">
                    <IconCorrect class="correct-icon" />
                    <span>No hand gestures</span>
                  </div>
                  <div class="requirement-item">
                    <IconCorrect class="correct-icon" />
                    <span>No accessories</span>
                  </div>
                </div>
              </div>
              <button class="select-button" @click="triggerFileUpload">Select from album</button>
            </div>
          </div>
        </div>
      </Transition>

      <!-- Loading Modal -->
      <Transition name="drawer">
        <div v-if="showLoadingModal" class="drawer-container">
          <div class="drawer-mask" :class="{ active: showLoadingModal }"></div>
          <div class="drawer-content loading-drawer" :class="{ active: showLoadingModal }">
            <div class="loading-content">
              <a-spin :spinning="true">
                <span>{{ loadingText }}</span>
              </a-spin>
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>

    <!-- Hidden file input -->
    <input
      type="file"
      ref="fileInput"
      style="display: none"
      accept="image/jpeg,image/png"
      @change="handleFileSelected"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Message } from '@/mobile/components/Message'
import axios from 'axios'
import { useStoryStore } from '@/store/story'
import IconCorrect from '@/assets/icon/correct.svg'
import IconDelete from '@/assets/icon/delete.svg'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface/report'

const props = withDefaults(
  defineProps<{
    faceDetect: boolean
    buttonText?: string
  }>(),
  {
    buttonText: 'Select from album'
  }
)

const emit = defineEmits<{
  (e: 'upload-success', url: string): void
  (e: 'upload-error', error: any): void
  (e: 'update:clear'): void
  (e: 'update:send-image'): void
  (e: 'update:skip-upload'): void
}>()

const fileInput = ref<HTMLInputElement | null>(null)
const showFaceModal = ref(false)
const showLoadingModal = ref(false)
const loadingText = ref('Uploading...')
const uploadedImage = ref<string>('')
const imageLoading = ref(true)
const demoImageLoading = ref(true)

const handleSkipButtonClick = () => {
  showFaceModal.value = false
  emit('update:skip-upload')
  reportEvent(ReportEvent.ClickSkipUpload)
}
const handleSelectButton = () => {
  if (uploadedImage.value) {
    emit('update:send-image')
    reportEvent(ReportEvent.ClickSendImage, {
      image_url: uploadedImage.value
    })
  } else {
    showFaceModal.value = true
    reportEvent(ReportEvent.ShowFaceRequirements)
  }
}
// 处理容器点击
const handleContainerClick = () => {
  if (uploadedImage.value) {
    // 如果已经上传过图片，直接触发文件选择
    triggerFileUpload()
  } else {
    // 如果是首次上传，显示要求弹窗
    showFaceModal.value = true
  }
}

// 触发文件选择
const triggerFileUpload = () => {
  reportEvent(ReportEvent.ClickSelectFromAlbum)
  fileInput.value?.click()
}
const handleClearUploadImage = () => {
  uploadedImage.value = ''
  imageLoading.value = true
  emit('update:clear')
  reportEvent(ReportEvent.ClickClearUploadImage)
}

// 处理文件选择
const handleFileSelected = async (event: Event) => {
  const input = event.target as HTMLInputElement
  if (!input.files?.length) return

  const file = input.files[0]
  showFaceModal.value = false
  showLoadingModal.value = true

  try {
    const storyStore = useStoryStore()
    // 1. 获取上传URL
    loadingText.value = 'Getting upload URL...'
    const response = await axios.get('/api/v1/chat-video.upload')
    const { data } = response
    const { upload_url, file_uri, download_url } = data.data

    // 检查 URL 是否为空
    if (!upload_url || !download_url) {
      throw new Error('Failed to get upload URL')
    }

    // 2. 上传文件
    loadingText.value = 'Uploading image...'
    try {
      const uploadResponse = await axios.put(upload_url, file, {
        headers: {
          'Content-Type': file.type
        }
      })

      // 检查上传响应状态
      if (uploadResponse.status !== 200) {
        throw new Error('File upload failed')
      }
    } catch (uploadError) {
      throw new Error('Failed to upload image')
    }

    // 3. 人脸检测
    if (props.faceDetect) {
      loadingText.value = 'Detecting face...'
      const detectResponse = await axios.post('/api/v1/chat-video.detect-face', {
        url: download_url
      })
      const detectData = detectResponse.data
      if (!detectData || !detectData?.data?.success) {
        throw new Error('Face detection failed')
      }
    }

    // 更新预览图时重置加载状态
    imageLoading.value = true
    uploadedImage.value = download_url
    emit('upload-success', download_url)
    reportEvent(ReportEvent.UploadImageSuccess)
  } catch (error) {
    reportEvent(ReportEvent.UploadImageFailed, {
      error: error instanceof Error ? error.message : 'Upload failed. Please try again.'
    })
    Message.error(error instanceof Error ? error.message : 'Upload failed. Please try again.')
    emit('upload-error', error)
  } finally {
    showLoadingModal.value = false
    if (input) input.value = '' // 清除input以允许重新上传相同文件
  }
}

// 处理图片加载错误
const handleImageError = () => {
  imageLoading.value = false
  Message.error('Failed to load image')
}

// 修改图片加载处理函数
const handleImageLoad = () => {
  setTimeout(() => {
    imageLoading.value = false
  }, 300) // 添加短暂延迟确保过渡动画流畅
}

// 处理demo图片加载错误
const handleDemoImageError = () => {
  demoImageLoading.value = false
  Message.error('Failed to load demo image')
}
</script>

<style lang="less" scoped>
.upload-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #200238;
  border-radius: 12px;

  .preview-container {
    width: 200px;
    height: 200px;
    border-radius: 8px;
    overflow: hidden;
    background: #f5f5f5;
    cursor: pointer;
    position: relative;

    svg {
      position: absolute;
      top: 4px;
      right: 4px;
      width: 30px;
      height: 30px;
      fill: #fff;
    }

    .preview-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .demo-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #1f0038;
      color: #666;
      position: relative;
      overflow: hidden;

      &.loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: -150%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(202, 147, 242, 0.15), transparent);
        animation: shine 2s infinite;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .demo-placeholder-tag {
        position: absolute;
        top: 0px;
        left: 0px;
        padding: 4px 8px;
        color: #fff;
        border-radius: 8px 0px;
        background: rgba(77, 53, 96, 0.2);
        font-size: 14px;
        font-weight: 600;
      }
    }

    @keyframes shine {
      0% {
        transform: translateX(0);
      }
      100% {
        transform: translateX(250%);
      }
    }
  }

  .select-button {
    width: 100%;
    padding: 12px;
    border-radius: 40px;
    background: #ca93f2;
    color: #241d49;
    border: none;
    cursor: pointer;
    font-size: 15px;
    font-weight: 600;
    &:hover {
      opacity: 0.9;
    }
  }

  .skip-button {
    color: #CA93F2;
    text-align: center;
    font-size: 13px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    background: none;
    border: none;
  }
}

.drawer-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  pointer-events: auto;

  .drawer-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.2);
    z-index: 1;
    cursor: pointer;
    backdrop-filter: blur(2px);
    opacity: 0;
    transition: opacity 0.3s ease;
    &.active {
      opacity: 1;
    }
  }

  .drawer-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #200238;
    border-radius: 16px 16px 0 0;
    padding: 20px;
    transform: translateY(100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    max-height: 90vh;
    overflow-y: auto;
    z-index: 2;

    &.active {
      transform: translateY(0);
    }

    &.loading-drawer {
      height: auto;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// 添加 Vue 的过渡动画
.drawer-enter-active,
.drawer-leave-active {
  transition: opacity 0.3s ease;
  .drawer-content {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.drawer-enter-from,
.drawer-leave-to {
  opacity: 0;
  .drawer-content {
    transform: translateY(100%);
  }
}

.drawer-enter-to,
.drawer-leave-from {
  opacity: 1;
  .drawer-content {
    transform: translateY(0);
  }
}

.face-requirements {
  color: white;

  h3 {
    margin-bottom: 16px;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
  }

  .requirements-container {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 16px;

    .requirement-image {
      flex: 1;
      img {
        max-width: 200px;
        max-height: 200px;
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
      }
    }

    .requirements-list {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 12px;

      .requirement-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        svg {
          width: 20px;
          height: 20px;
        }
        span {
          white-space: nowrap;
        }
      }
    }
  }

  .select-button {
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    background: #ca93f2;
    color: #241d49;
    border: none;
    font-size: 16px;
    font-weight: 600;
    margin-top: 16px;
  }
}

.loading-content {
  text-align: center;
  padding: 24px;
  color: white;
}

.image-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  &.loading {
    background: rgba(0, 0, 0, 0.05);

    .preview-image {
      opacity: 0.7;
    }
  }

  .loading-animation {
    position: absolute;
    z-index: 1;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .spinner {
    width: 40px;
    height: 40px;
    position: relative;
  }

  .double-bounce1,
  .double-bounce2 {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: rgba(202, 147, 242, 0.8); // 使用主题紫色
    opacity: 0.6;
    position: absolute;
    top: 0;
    left: 0;
    animation: sk-bounce 2s infinite ease-in-out;
  }

  .double-bounce2 {
    animation-delay: -1s;
  }

  @keyframes sk-bounce {
    0%,
    100% {
      transform: scale(0);
    }
    50% {
      transform: scale(1);
    }
  }

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
  }
}
</style>
