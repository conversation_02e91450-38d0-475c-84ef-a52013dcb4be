<template>
  <Teleport to="body">
    <TransitionGroup name="message" tag="div" class="message-container">
      <div v-for="msg in messages" :key="msg.id" class="message" :class="msg.type">
        <div class="message-content">
          <icon-check-circle-fill v-if="msg.type === 'success'" />
          <icon-exclamation-circle-fill v-if="msg.type === 'error'" />
          <icon-info-circle-fill v-if="msg.type === 'info'" />
          <icon-exclamation-circle-fill v-if="msg.type === 'warning'" />
          <icon-loading v-if="msg.type === 'loading'" />
          <icon-info-circle-fill v-if="msg.type === 'normal'" />
          <span>{{ msg.content }}</span>
        </div>
      </div>
    </TransitionGroup>
  </Teleport>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  IconCheckCircleFill,
  IconExclamationCircleFill,
  IconInfoCircleFill,
  IconLoading
} from '@arco-design/web-vue/es/icon'

export interface MessageItem {
  id: number
  content: string
  type: 'success' | 'error' | 'info' | 'warning' | 'loading' | 'normal'
  duration: number
}

const messages = ref<MessageItem[]>([])
let messageId = 0

const success = (content: string, duration = 3000) => {
  const id = messageId++
  const message: MessageItem = {
    id,
    content,
    type: 'success',
    duration
  }
  messages.value.push(message)

  setTimeout(() => {
    const index = messages.value.findIndex((msg) => msg.id === id)
    if (index !== -1) {
      messages.value.splice(index, 1)
    }
  }, duration)
}

const error = (content: string, duration = 3000) => {
  const id = messageId++
  const message: MessageItem = {
    id,
    content,
    type: 'error',
    duration
  }
  messages.value.push(message)

  setTimeout(() => {
    const index = messages.value.findIndex((msg) => msg.id === id)
    if (index !== -1) {
      messages.value.splice(index, 1)
    }
  }, duration)
}

const info = (content: string, duration = 3000) => {
  const id = messageId++
  const message: MessageItem = {
    id,
    content,
    type: 'info',
    duration
  }
  messages.value.push(message)

  setTimeout(() => {
    const index = messages.value.findIndex((msg) => msg.id === id)
    if (index !== -1) {
      messages.value.splice(index, 1)
    }
  }, duration)
}

const warning = (content: string, duration = 3000) => {
  const id = messageId++
  const message: MessageItem = {
    id,
    content,
    type: 'warning',
    duration
  }
  messages.value.push(message)

  setTimeout(() => {
    const index = messages.value.findIndex((msg) => msg.id === id)
    if (index !== -1) {
      messages.value.splice(index, 1)
    }
  }, duration)
}

const loading = (content: string, duration = 3000) => {
  const id = messageId++
  const message: MessageItem = {
    id,
    content,
    type: 'loading',
    duration
  }
  messages.value.push(message)

  setTimeout(() => {
    const index = messages.value.findIndex((msg) => msg.id === id)
    if (index !== -1) {
      messages.value.splice(index, 1)
    }
  }, duration)
}

const normal = (content: string, duration = 3000) => {
  const id = messageId++
  const message: MessageItem = {
    id,
    content,
    type: 'normal',
    duration
  }
  messages.value.push(message)

  setTimeout(() => {
    const index = messages.value.findIndex((msg) => msg.id === id)
    if (index !== -1) {
      messages.value.splice(index, 1)
    }
  }, duration)
}

const clear = () => {
  messages.value = []
}

defineExpose({
  success,
  error,
  info,
  warning,
  loading,
  normal,
  clear
})
</script>

<style lang="less" scoped>
.message-container {
  position: fixed;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  width: calc(100% - 32px);
  max-width: 400px;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.message {
  background: rgba(0, 0, 0, 0.85);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(8px);
  border-radius: 12px;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.95);
  font-size: 15px;
  font-weight: 500;
  line-height: 1.4;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
  transform: translateY(0);
  text-align: center;
  min-width: 200px;
  border: 1px solid rgba(255, 255, 255, 0.1);

  .message-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    :deep(svg) {
      position: relative;
      top: -1.25px;
      width: 18px;
      height: 18px;
      flex-shrink: 0;
    }
  }

  &.success {
    background: rgba(0, 0, 0, 0.85);
    color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(82, 196, 26, 0.3);
    :deep(svg) {
      color: #52c41a;
    }
  }

  &.error {
    background: rgba(0, 0, 0, 0.85);
    color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 77, 79, 0.3);
    :deep(svg) {
      color: #ff4d4f;
    }
  }

  &.info {
    background: rgba(0, 0, 0, 0.85);
    color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(24, 144, 255, 0.3);
    :deep(svg) {
      color: #1890ff;
    }
  }

  &.warning {
    background: rgba(0, 0, 0, 0.85);
    color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(250, 173, 20, 0.3);
    :deep(svg) {
      color: #faad14;
    }
  }

  &.loading {
    background: rgba(0, 0, 0, 0.85);
    color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(24, 144, 255, 0.3);
    :deep(svg) {
      color: #1890ff;
      animation: loading-rotate 1s linear infinite;
    }
  }

  &.normal {
    background: rgba(0, 0, 0, 0.85);
    color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    :deep(svg) {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

@keyframes loading-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.message-enter-active,
.message-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.message-enter-from {
  opacity: 0;
  transform: translateY(-20px);
}

.message-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
</style>
