<template>
  <button class="mobile-theme-toggle" @click="toggleTheme" :title="themeTitle">
    <div class="toggle-track" :class="{ active: !isDarkTheme }">
      <div class="toggle-thumb" :class="{ active: !isDarkTheme }">
        <SunIcon v-if="!isDarkTheme" />
        <MoonIcon v-else />
      </div>
    </div>
  </button>
</template>

<script setup lang="ts">
import { computed, defineComponent, h } from 'vue'
import { useThemeStore } from '@/store/theme'

const themeStore = useThemeStore()
const isDarkTheme = computed(() => themeStore.isDarkTheme)
const themeTitle = computed(() =>
  isDarkTheme.value ? 'Switch to Light Mode' : 'Switch to Dark Mode'
)

const toggleTheme = () => {
  themeStore.toggleTheme()
}

// 太阳图标组件
const SunIcon = defineComponent({
  name: 'SunIcon',
  render() {
    return h(
      'svg',
      {
        viewBox: '0 0 24 24',
        fill: 'none',
        stroke: 'currentColor',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        class: 'theme-icon'
      },
      [
        h('circle', { cx: '12', cy: '12', r: '5' }),
        h('path', { d: 'M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42' })
      ]
    )
  }
})

// 月亮图标组件
const MoonIcon = defineComponent({
  name: 'MoonIcon',
  render() {
    return h(
      'svg',
      {
        viewBox: '0 0 24 24',
        fill: 'none',
        stroke: 'currentColor',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        class: 'theme-icon'
      },
      [h('path', { d: 'M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z' })]
    )
  }
})
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.mobile-theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 20px;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }

  .toggle-track {
    width: 48px;
    height: 24px;
    background: var(--bg-tertiary);
    border-radius: 12px;
    position: relative;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);

    &.active {
      background: var(--accent-bg);
      border-color: var(--accent-color);
    }

    .toggle-thumb {
      width: 20px;
      height: 20px;
      background: var(--text-primary);
      border-radius: 50%;
      position: absolute;
      top: 1px;
      left: 1px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 4px var(--shadow-color);

      &.active {
        transform: translateX(24px);
        background: var(--accent-color);
      }

      .theme-icon {
        width: 12px;
        height: 12px;
        color: var(--bg-primary);
      }
    }
  }

  // 亮色主题下的特殊样式
  body.light-theme & {
    .toggle-track {
      background: rgba(0, 0, 0, 0.1);
      border-color: rgba(0, 0, 0, 0.2);

      &.active {
        background: rgba(202, 147, 242, 0.2);
        border-color: #ca93f2;
      }

      .toggle-thumb {
        background: #ffffff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

        &.active {
          background: #ca93f2;
        }

        .theme-icon {
          color: #333333;
        }
      }
    }
  }

  // 暗色主题下的特殊样式
  body.dark-theme & {
    .toggle-track {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.2);

      &.active {
        background: rgba(202, 147, 242, 0.2);
        border-color: #ca93f2;
      }

      .toggle-thumb {
        background: #ffffff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

        &.active {
          background: #ca93f2;
        }

        .theme-icon {
          color: #180430;
        }
      }
    }
  }
}
</style>
