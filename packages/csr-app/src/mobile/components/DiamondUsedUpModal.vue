<template>
  <div class="diamond-used-up-modal" v-if="visible">
    <div class="modal-content">
      <div class="modal-body">
        <div class="message" v-text-stroke>
          Your diamonds have been used up. Click Recharge to continue playing, or click Exit the
          game
        </div>
        <div class="buttons">
          <button class="leave-button" @click="handleLeave">Leave</button>
          <button class="topup-button" @click="handleTopUp">Top Up</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useRechargeStore } from '@/store/recharge'
import { useChatUIStore } from '@/store/chat-ui'
import { useChatEventsStore } from '@/store/chat-events'

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'leave'): void
}>()

const router = useRouter()
const rechargeStore = useRechargeStore()
const chatUIStore = useChatUIStore()
const chatEventsStore = useChatEventsStore()

// 处理关闭模态框
const handleClose = () => {
  emit('update:visible', false)
}

// 处理离开按钮点击
const handleLeave = () => {
  // 设置已确认离开标记，避免二次确认
  chatEventsStore.isConfirmedLeave = true
  emit('update:visible', false)
  emit('leave')
}

// 处理充值按钮点击
const handleTopUp = () => {
  // emit('update:visible', false)
  // 显示充值弹窗
  rechargeStore.visible = true
  // 锁定聊天输入框
  chatUIStore.setDisableInput(true)
}
</script>

<style lang="less" scoped>
.diamond-used-up-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  width: 85%;
  max-width: 320px;
  background: rgba(30, 10, 40, 0.9);
  border-radius: 16px;
  padding: 24px;
  // box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
  // border: 1px solid rgba(255, 255, 255, 0.1);
  animation: scaleIn 0.3s ease;
}

.modal-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.message {
  color: white;
  font-size: 15px;
  text-align: center;
  line-height: 1.5;
  font-weight: 700;
}

.buttons {
  display: flex;
  gap: 16px;
  width: 100%;
}

.leave-button,
.topup-button {
  flex: 1;
  padding: 12px 0;
  border-radius: 24px;
  color: #241d49;
  font-size: 15px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition:
    transform 0.2s ease,
    opacity 0.2s ease;
}

.leave-button {
  border-radius: 26px;
  border-top: 2px solid #1f0038;
  border-right: 2px solid #1f0038;
  border-bottom: 6px solid #1f0038;
  border-left: 2px solid #1f0038;
  background: linear-gradient(180deg, #d6cafe 0%, #ca93f2 100%);
  box-shadow: 0px 2px 12px 0px #b098ff;

  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
}

.topup-button {
  border-radius: 26px;
  border-top: 2px solid #1f0038;
  border-right: 2px solid #1f0038;
  border-bottom: 6px solid #1f0038;
  border-left: 2px solid #1f0038;
  background: linear-gradient(180deg, #f5ffe2 0%, #daff96 100%);
  box-shadow: 0px 1.855px 11.13px 0px #daff96;
  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
