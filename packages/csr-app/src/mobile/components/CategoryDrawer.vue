<template>
  <BaseDrawer
    :visible="visible"
    @update:visible="handleVisibilityChange"
    height="auto"
    border-radius="24px 24px 0 0"
  >
    <template v-if="title" #title>
      <div class="drawer-header">
        <span>{{ title }}</span>
        <button class="close-btn" @click="handleClose(false)">
          <CloseIcon />
        </button>
      </div>
    </template>

    <!-- For Popular Filter Drawer -->
    <div v-if="type === 'popular'" class="filter-options">
      <div
        v-for="option in popularOptions"
        :key="option.value"
        class="filter-option"
        :class="{ active: selectedPopular === option.value }"
        @click="handlePopularSelect(option.value)"
      >
        {{ option.label }}
      </div>
      <div class="filter-option cancel" @click="handleClose(false)">Cancel</div>
    </div>

    <!-- For Gender Filter Drawer -->
    <div v-else-if="type === 'gender'" class="filter-options">
      <div
        v-for="category in genderCategories"
        :key="category.id"
        class="filter-option"
        :class="{ active: selectedGender === category.id }"
        @click="handleGenderSelect(category.id)"
      >
        {{ category.name }}
      </div>
      <div class="filter-option cancel" @click="handleClose(true)">Cancel</div>
    </div>

    <!-- For Tags Filter Drawer -->
    <div v-else-if="type === 'tags'" class="tags-container">
      <div class="tags-grid">
        <div
          v-for="tag in availableTags"
          :key="tag.id"
          class="tag-item"
          :class="{ active: selectedTags.includes(tag.id) }"
          @click="handleTagToggle(tag.id)"
        >
          {{ tag.name }}
        </div>
      </div>
      <div class="actions-row">
        <button class="back-btn" @click="handleClose(false)">Back</button>
        <button class="confirm-btn" @click="handleTagsConfirm">Confirm</button>
      </div>
    </div>
  </BaseDrawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseDrawer from './BaseDrawer.vue'
import { useStoryStore } from '@/store'
import CloseIcon from '@/assets/icon/close.svg'
// Note: Category and Subcategory interfaces are available from the store

const props = defineProps<{
  visible: boolean
  type: 'popular' | 'gender' | 'tags'
  initialPopular?: string
  initialGender?: string
  initialTags?: string[]
  title?: string
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'popular-change', value: string): void
  (e: 'gender-change', value: string): void
  (e: 'tags-change', value: string[]): void
}>()

const storyStore = useStoryStore()

// Popular filter options
const popularOptions = [
  { label: 'Most popular', value: 'popular' },
  { label: 'Newest', value: 'newest' }
  // { label: 'Most liked', value: 'liked' }
]

const selectedPopular = ref(props.initialPopular || 'liked')
const selectedGender = ref(props.initialGender || '')
const selectedTags = ref<string[]>(props.initialTags || [])

// Computed properties for categories and tags
const genderCategories = computed(() => {
  const tags: { id: string; name: string }[] = []
  // Cast storyCategories to our local Category type
  const categories = storyStore.storyCategories.filter(
    (category) => category?.name?.toUpperCase() === 'GENDER'
  )
  categories.forEach((category) => {
    if (category.subcategories) {
      category.subcategories.forEach((subcategory) => {
        // Check if this tag is already in the list to avoid duplicates
        if (!tags.some((tag) => tag.id === subcategory.id)) {
          tags.push({
            id: subcategory.id,
            name: subcategory.name
          })
        }
      })
    }
  })
  return tags
})

const availableTags = computed(() => {
  // Get all subcategories from the story categories
  const tags: { id: string; name: string }[] = []
  if (!storyStore.storyCategories.length) return []
  // Cast storyCategories to our local Category type
  const categories = storyStore.storyCategories.filter(
    (category) => category?.name?.toUpperCase() === 'HOBBY'
  )

  categories.forEach((category) => {
    if (category.subcategories) {
      category.subcategories.forEach((subcategory) => {
        // Check if this tag is already in the list to avoid duplicates
        if (!tags.some((tag) => tag.id === subcategory.id)) {
          tags.push({
            id: subcategory.id,
            name: subcategory.name
          })
        }
      })
    }
  })
  return tags
})

// Handlers
const handleVisibilityChange = (value: boolean) => {
  emit('update:visible', value)
}

const handleClose = (isCancel?: boolean) => {
  if (props.type === 'gender' && isCancel) {
    emit('gender-change', '')
  }
  emit('update:visible', false)
}

const handlePopularSelect = (value: string) => {
  selectedPopular.value = value
  emit('popular-change', value)
  handleClose()
}

const handleGenderSelect = (value: string) => {
  selectedGender.value = value
  emit('gender-change', value)
  handleClose()
}

const handleTagToggle = (id: string) => {
  const index = selectedTags.value.indexOf(id)
  if (index === -1) {
    selectedTags.value.push(id)
  } else {
    selectedTags.value.splice(index, 1)
  }
}

const handleTagsConfirm = () => {
  emit('tags-change', selectedTags.value)
  handleClose()
}

// Reset selected values when drawer visibility changes or initial values change
watch(
  [
    () => props.visible,
    () => props.initialPopular,
    () => props.initialGender,
    () => props.initialTags
  ],
  ([newVisible, newPopular, newGender, newTags]) => {
    if (newVisible) {
      // When drawer opens, set to initial values if provided
      if (props.type === 'popular' && newPopular) {
        selectedPopular.value = newPopular
      }
      if (props.type === 'gender' && newGender) {
        selectedGender.value = newGender
      }
      if (props.type === 'tags' && newTags) {
        selectedTags.value = [...newTags]
      }
    }
  }
)
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.drawer-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 16px 0;
  width: 100%;

  span {
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
  }

  .close-btn {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    color: var(--text-primary);
    font-size: 24px;
    cursor: pointer;
    border-radius: 15px;
    background: var(--bg-tertiary);
    transition: background 0.3s ease;

    &:hover {
      background: var(--bg-hover);
    }
  }
}

.filter-options {
  display: flex;
  flex-direction: column;
  width: 100%;

  .filter-option {
    padding: 16px 0;
    text-align: center;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 500;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: var(--bg-hover);
    }

    &.active {
      color: var(--accent-color);
    }

    &.cancel {
      color: var(--text-primary);
      margin-top: 8px;
      border-bottom: none;
    }
  }
}

.tags-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 16px 0;

  .tags-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    padding: 0 16px 24px;

    .tag-item {
      padding: 6px 10px;
      text-align: center;
      border-radius: 100px;
      background: var(--mobile-input-bg);
      border: 1px solid var(--mobile-input-border);
      color: var(--text-secondary);
      font-size: 13px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      width: fit-content;

      &:hover {
        background: var(--bg-hover);
      }

      &.active {
        background: var(--accent-bg);
        color: var(--text-primary);
        border: 1px solid var(--accent-color);
      }
    }
  }

  .actions-row {
    display: flex;
    // justify-content: space-between;
    padding: 16px;
    // border-top: 1px solid rgba(255, 255, 255, 0.1);
    // margin-top: 16px;
    width: 100%;
    button {
      padding: 12px 0;
      border-radius: 24px;
      font-size: 15px;
      font-weight: 600;
      cursor: pointer;
      border: none;
      transition: all 0.3s ease;
      height: 42px;
      border: 1px solid var(--accent-color);

      &:active {
        transform: scale(0.98);
      }
    }

    .back-btn {
      background: transparent;
      margin-right: 12px;
      border-radius: 40px;
      color: var(--accent-color);
      width: 96px;

      &:hover {
        background: var(--accent-bg);
      }
    }

    .confirm-btn {
      background: var(--accent-color);
      color: var(--bg-primary);
      border-radius: 40px;
      flex: 1;

      &:hover {
        background: var(--accent-hover);
      }
    }
  }
}
</style>
