<template>
  <div class="select-drawer-container" v-bind="$attrs">
    <div class="select-field" @click="showDrawer = true">
      <div class="select-value">{{ selectedLabel }}</div>
      <div class="select-arrow">
        <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
          <path
            d="M1 1.5L6 6.5L11 1.5"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
          />
        </svg>
      </div>
    </div>

    <BaseDrawer v-model:visible="showDrawer" height="auto">
      <div class="select-drawer">
        <div
          v-for="option in options"
          :key="option.value"
          class="option-item"
          :class="{ active: modelValue === option.value }"
          @click="selectOption(option.value)"
        >
          {{ option.label }}
        </div>
        <div class="option-item cancel" @click="showDrawer = false">Cancel</div>
      </div>
    </BaseDrawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import BaseDrawer from './BaseDrawer.vue'

defineOptions({
  inheritAttrs: true
})

interface Option {
  label: string
  value: string
}

const props = defineProps<{
  modelValue: string
  options: Option[]
  placeholder?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

const showDrawer = ref(false)

const selectedLabel = computed(() => {
  const selected = props.options.find((opt) => opt.value === props.modelValue)
  return selected?.label || props.placeholder || 'Select option'
})

const selectOption = (value: string) => {
  emit('update:modelValue', value)
  showDrawer.value = false
}
</script>

<style lang="less" scoped>
.select-field {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  height: 48px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  color: white;
  font-size: 15px;
  line-height: 22px;
  transition: background-color 0.2s ease;
  flex: 1;

  &:active {
    background: rgba(255, 255, 255, 0.12);
  }

  .select-value {
    opacity: 0.9;
  }

  .select-arrow {
    color: rgba(255, 255, 255, 0.5);
  }
}

.select-drawer {
  padding: 0;
  // background: #1a0030;

  .option-item {
    padding: 18px 0;
    text-align: center;
    color: rgba(255, 255, 255, 0.9);
    font-size: 15px;
    line-height: 22px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    background: transparent;
    transition: background-color 0.2s ease;

    &:active {
      background: rgba(255, 255, 255, 0.05);
    }

    &.active {
      color: #ca93f2;
    }

    &.cancel {
      color: rgba(255, 255, 255, 0.6);
      border-bottom: none;
      margin-top: 0;
      padding: 18px 0;
      background: transparent;
    }
  }
}
</style>
