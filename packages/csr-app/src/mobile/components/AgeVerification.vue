<template>
  <div v-if="ageVerificationStore.showAgeVerificationModal" class="age-verification-overlay">
    <div class="age-verification-modal">
      <div class="age-verification-content">
        <div class="icon-wrapper">🔞</div>
        <h2>Confirm your age</h2>
        <p>You must be 18 years or older to enter this site. Please confirm your age.</p>
        <div class="button-group">
          <button class="confirm-button" @click="handleConfirm">I am over 18, Continue</button>
          <button class="reject-button" @click="handleReject">No</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAgeVerificationStore } from '@/store/ageVerification'
import { useRouter } from 'vue-router'

const router = useRouter()
const ageVerificationStore = useAgeVerificationStore()

const handleConfirm = () => {
  ageVerificationStore.setAgeVerified()
  ageVerificationStore.hideModal()

  if (ageVerificationStore.targetRoute) {
    router.push(ageVerificationStore.targetRoute)
  }
}

const handleReject = () => {
  console.log('handleReject')
  // ageVerificationStore.hideModal()
  // if (window.history.length > 1) {
  //   router.go(-1)
  // } else {
  //   window.location.href = ''
  // }
  window.location.href = ''
}
</script>

<style lang="less" scoped>
.age-verification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.age-verification-modal {
  background-color: #1c1c1e;
  width: 300px;
  border-radius: 16px;
  padding: 24px;
}

.age-verification-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .icon-wrapper {
    margin-bottom: 16px;
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: #ff3b30;
  }

  h2 {
    margin-bottom: 12px;
    font-size: 20px;
    color: #ffffff;
    font-weight: 600;
  }

  p {
    margin-bottom: 24px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.4;
  }

  .button-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }

  .confirm-button {
    color: white;
    border: none;
    padding: 12px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
    border-radius: 6px;
    border: 1px solid #ffd0ed;
    background: linear-gradient(315deg, #ffb0ee 0%, #ff77cf 100%);
    box-shadow: 0px 0px 11.13px 0px #ffa3e7;
    &:hover {
      background-color: darken(#e85b9d, 5%);
    }
  }

  .reject-button {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 12px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(255, 255, 255, 0.15);
    }
  }
}
</style>
