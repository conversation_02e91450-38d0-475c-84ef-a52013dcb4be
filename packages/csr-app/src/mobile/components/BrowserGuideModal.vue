<template>
  <Teleport to="body">
    <div v-if="visible" class="browser-guide-modal" @click.self="handleClose">
      <div class="modal-content">
        <div class="modal-header">
          <div class="icon-wrapper">
            <GoogleIcon />
          </div>
        </div>
        <div class="modal-body">
          <div class="title">Open in Browser</div>
          <div class="description">
            Please copy the link below and open it in your browser to continue with Google login.
          </div>
          <div class="url-box" @click="handleCopy">
            <div class="url-text">{{ currentUrl }}</div>
            <div class="copy-icon">
              <span v-if="!copied">📋</span>
              <span v-else>✓</span>
            </div>
          </div>
          <div class="steps">
            <div class="step">1. Copy the link above</div>
            <div class="step">2. Open your browser</div>
            <div class="step">3. Paste and visit the link</div>
          </div>
          <div class="skip-text" @click="handleClose">Close</div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getCurrentUrl, copyText } from '@/utils'
import GoogleIcon from '@/assets/icon/google.svg'
import { Message } from '@/mobile/components/Message'
const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
}>()

const currentUrl = ref(getCurrentUrl())
const copied = ref(false)

const handleClose = () => {
  emit('update:visible', false)
}

const handleCopy = async () => {
  try {
    await copyText(currentUrl.value)
    copied.value = true
    setTimeout(() => {
      copied.value = false
    }, 2000)
    Message.success('Copied to clipboard')
  } catch (err) {
    Message.error('Failed to copy: ' + err)
  }
}
</script>

<style lang="less" scoped>
.browser-guide-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(31, 0, 56, 0.75);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  width: 100%;
  max-width: 330px;
  background: #1f0038;
  border: 1px solid rgba(202, 147, 242, 0.1);
  border-radius: 24px;
  overflow: hidden;
  animation: slideUp 0.3s ease;
  box-shadow: 0 8px 32px rgba(31, 0, 56, 0.4);
}

.modal-header {
  background: linear-gradient(135deg, rgba(202, 147, 242, 0.15), rgba(218, 255, 150, 0.05));
  padding: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(202, 147, 242, 0.2), transparent);
  }
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(202, 147, 242, 0.2);
}

.modal-body {
  padding: 24px;
  text-align: center;
}

.title {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 12px;
  background: linear-gradient(90deg, #ca93f2, #daff96);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 8px rgba(202, 147, 242, 0.3);
}

.description {
  font-size: 14px;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20px;
}

.url-box {
  background: rgba(202, 147, 242, 0.1);
  border: 1px solid rgba(202, 147, 242, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(202, 147, 242, 0.15);
  }

  &:active {
    transform: scale(0.98);
  }

  .url-text {
    flex: 1;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.9);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
  }

  .copy-icon {
    font-size: 16px;
    color: #ca93f2;
    flex-shrink: 0;
  }
}

.steps {
  text-align: left;
  margin-bottom: 20px;

  .step {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;

    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 6px;
      height: 6px;
      background: #ca93f2;
      border-radius: 50%;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.skip-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  cursor: pointer;
  transition: color 0.2s ease;

  &:hover {
    color: #ca93f2;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
