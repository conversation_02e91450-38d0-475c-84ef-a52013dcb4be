<template>
  <Teleport to="body">
    <Transition name="drawer">
      <div v-if="visible" class="drawer-container">
        <div class="drawer-mask" :class="{ active: visible }" @click="handleMaskClick"></div>
        <div class="drawer-content" :class="{ active: visible }">
          <div class="face-requirements">
            <h3>Face</h3>
            <div v-if="!loading" class="requirements-container">
              <div class="requirement-image">
                <img
                  src="https://cdn.magiclight.ai/assets/playshot/upload-face-demo.png"
                  alt="Face Example"
                />
              </div>
              <div class="requirements-list">
                <div class="requirement-item">
                  <IconCorrect class="correct-icon" />
                  <span>Simple background</span>
                </div>
                <div class="requirement-item">
                  <IconCorrect class="correct-icon" />
                  <span>Frontal headshot</span>
                </div>
                <div class="requirement-item">
                  <IconCorrect class="correct-icon" />
                  <span>No hand gestures</span>
                </div>
                <div class="requirement-item">
                  <IconCorrect class="correct-icon" />
                  <span>No accessories</span>
                </div>
              </div>
            </div>
            <div v-else class="loading-container">
              <div class="loading-spinner"></div>
              <div class="loading-text">{{ loadingText }}</div>
            </div>
            <button v-if="!loading" class="select-button" @click="emit('select')">
              Select from album
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import IconCorrect from '@/assets/icon/correct.svg'

const props = defineProps<{
  visible: boolean
  loading?: boolean
  loadingText?: string
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'select'): void
}>()

const handleMaskClick = () => {
  if (props.loading) return
  emit('update:visible', false)
}
</script>

<style lang="less" scoped>
.drawer-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  pointer-events: auto;

  .drawer-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.2);
    z-index: 1;
    cursor: pointer;
    backdrop-filter: blur(2px);
    opacity: 0;
    transition: opacity 0.3s ease;
    &.active {
      opacity: 1;
    }
  }

  .drawer-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #200238;
    border-radius: 16px 16px 0 0;
    padding: 20px;
    transform: translateY(100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    max-height: 90vh;
    overflow-y: auto;
    z-index: 2;

    &.active {
      transform: translateY(0);
    }
  }
}

.face-requirements {
  color: white;

  h3 {
    margin-bottom: 16px;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
  }

  .requirements-container {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 16px;

    .requirement-image {
      flex: 1;
      img {
        max-width: 200px;
        max-height: 200px;
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
      }
    }

    .requirements-list {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 12px;

      .requirement-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        svg {
          width: 20px;
          height: 20px;
        }
        span {
          white-space: nowrap;
        }
      }
    }
  }

  .select-button {
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    background: #ca93f2;
    color: #241d49;
    border: none;
    font-size: 16px;
    font-weight: 600;
    margin-top: 16px;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  gap: 16px;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top-color: #ca93f2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    text-align: center;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Drawer transition animations
.drawer-enter-active,
.drawer-leave-active {
  transition: opacity 0.3s ease;
  .drawer-content {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.drawer-enter-from,
.drawer-leave-to {
  opacity: 0;
  .drawer-content {
    transform: translateY(100%);
  }
}

.drawer-enter-to,
.drawer-leave-from {
  opacity: 1;
  .drawer-content {
    transform: translateY(0);
  }
}
</style>
