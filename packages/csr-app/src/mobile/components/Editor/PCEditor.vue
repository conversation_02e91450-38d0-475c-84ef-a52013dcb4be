<template>
  <Splitpanes class="pc-editor">
    <!-- 左侧全局配置面板 -->
    <Pane min-size="15" max-size="30" size="20">
      <div class="editor-panel global-config">
        <div class="panel-header">
          <h2>全局配置</h2>
        </div>
        <div class="panel-content">
          <GlobalConfig />
        </div>
      </div>
    </Pane>

    <!-- 中间故事流程配置 -->
    <Pane min-size="30" size="50">
      <div class="editor-panel story-flow">
        <div class="panel-header">
          <h2>故事流程</h2>
        </div>
        <div class="panel-content">
          <StoryEditor />
        </div>
        <div class="panel-footer">
          <ActionBar />
        </div>
      </div>
    </Pane>

    <!-- 右侧预览面板 -->
    <Pane min-size="20" max-size="35" size="30">
      <div class="editor-panel preview">
        <div class="panel-header">
          <h2>预览</h2>
        </div>
        <div class="panel-content">
          <div class="preview-container">
            <div class="preview-frame">
              <StoryPreview />
            </div>
          </div>
        </div>
      </div>
    </Pane>
  </Splitpanes>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
import StoryEditor from './StoryEditor.vue'
import GlobalConfig from './GlobalConfig.vue'
import StoryPreview from './StoryPreview.vue'
import ActionBar from './ActionBar.vue'
</script>

<style lang="less" scoped>
.pc-editor {
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.1);

  :deep(.splitpanes__pane) {
    background-color: transparent;
  }

  :deep(.splitpanes__splitter) {
    background-color: rgba(255, 255, 255, 0.1);
    position: relative;
    margin: 0 -1px;
    width: 2px !important;
    transition: background-color 0.2s;

    &:hover {
      background-color: #ca93f2;
    }

    &::before,
    &::after {
      display: none;
    }
  }

  .editor-panel {
    background: #1f0038;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;

    .panel-header {
      padding: 16px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: rgba(0, 0, 0, 0.2);
      user-select: none;

      h2 {
        margin: 0;
        font-size: 16px;
        color: #fff;
        font-weight: 500;
      }
    }

    .panel-content {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
    }

    .panel-footer {
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      background: rgba(0, 0, 0, 0.2);
      padding: 8px;

      :deep(.action-bar) {
        border-radius: 8px;
        overflow: hidden;
      }
    }
  }

  .story-flow {
    .panel-content {
      padding: 0;

      :deep(.story-editor) {
        padding: 16px;
      }
    }
  }

  .preview {
    .preview-controls {
      display: flex;
      gap: 8px;
    }

    .preview-container {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24px;
      overflow: hidden;
    }

    .preview-frame {
      // width: min(100%, calc((100vh - 120px) * 9 / 16));
      // height: calc(min(100%, calc((100vh - 120px) * 9 / 16)) * 16 / 9);
      max-height: calc(100vh - 120px);
      aspect-ratio: 9/16;
      margin: 0 auto;
      // background: #fff;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40%;
        height: 24px;
        background: #000;
        border-bottom-left-radius: 16px;
        border-bottom-right-radius: 16px;
        z-index: 1;
      }

      :deep(> *) {
        height: 100%;
        // padding-top: 24px;
        // background: #fff;
      }
    }
  }

  :deep(.story-editor) {
    padding: 0;
  }
}
</style>
