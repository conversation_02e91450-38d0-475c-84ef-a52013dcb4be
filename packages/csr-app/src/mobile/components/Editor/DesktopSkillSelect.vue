<template>
  <div class="desktop-skill-select" v-if="visible">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Select Skills</h2>
        <button class="close-button" @click="handleClose">
          <icon-close />
        </button>
      </div>

      <div class="modal-body">
        <div class="skill-grid">
          <div class="skill-card create-card" @click="handleCreateSkill">
            <div class="card-content">
              <div class="create-icon">
                <!-- <icon-plus /> -->
                <icon-add />
              </div>
              <div class="create-text">Create New Skill</div>
            </div>
          </div>

          <div
            v-for="skill in skillList"
            :key="skill.id"
            class="skill-card"
            :class="{ 'is-selected': isSkillSelected(skill.id) }"
            @click="toggleSkill(skill)"
          >
            <div class="card-content">
              <div class="card-actions">
                <button class="edit-button" @click.stop="handleEditSkill(skill)">Edit</button>
                <button class="delete-button" @click.stop="handleDeleteSkill(skill)">Delete</button>
              </div>
              <img :src="skill.image_url" :alt="skill.name" class="preview-url" />
              <div class="skill-info">
                <div class="skill-name">{{ skill.name }}</div>
                <div class="skill-description">{{ skill.description }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="selected-skills-panel">
          <div class="panel-header">
            <h3>Selected Skills ({{ selectedSkills.length }})</h3>
          </div>
          <div class="selected-skills-list">
            <div v-for="skill in selectedSkills" :key="skill.id" class="selected-skill-item">
              <img :src="skill.icon_url" :alt="skill.name" class="skill-avatar" />
              <div class="skill-info">
                <div class="skill-name">{{ skill.name }}</div>
                <div class="skill-effects">
                  <div
                    v-for="(effect, index) in skill.skill_configs?.effect"
                    :key="index"
                    class="effect-item"
                  >
                    {{ effect.attr }}: {{ effect.value }}
                  </div>
                </div>
              </div>
              <button class="remove-button" @click.stop="removeSkill(skill)">
                <icon-close />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <div class="skill-count">Selected: {{ selectedSkills.length }} skills</div>
        <button class="confirm-button" @click="handleConfirm">Confirm</button>
      </div>
    </div>
  </div>

  <SkillEditor v-model:visible="editorVisible" :skill="currentEditSkill" @save="handleSaveSkill" />
  <ConfirmDialog
    v-model:visible="showDeleteConfirm"
    title="Delete Skill"
    :content="`Are you sure you want to delete the skill '${skillToDelete?.name}'?`"
    @confirm="confirmDelete"
  />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import IconClose from '@/assets/icon/close.svg'
import IconAdd from '@/assets/icon/add.svg'
import { useSkillStore } from '@/store/skill'
import { useStoryStore } from '@/store/story'
import type { SkillInfo } from '@/interface/skill'
import SkillEditor from './SkillEditor.vue'
import { Message } from '@/mobile/components/Message'
import { deleteSkill } from '@/api/skill'
import ConfirmDialog from '@/components/ConfirmDialog.vue'

const props = defineProps<{
  visible: boolean
  modelValue: SkillInfo[]
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'update:modelValue', skills: SkillInfo[]): void
  (e: 'update-skill', skill: SkillInfo): void
}>()

const skillStore = useSkillStore()
const storyStore = useStoryStore()
const selectedSkills = ref<SkillInfo[]>(props.modelValue || [])

const skillList = computed(() => skillStore.skillList)

const editorVisible = ref(false)
const currentEditSkill = ref<SkillInfo>()

const showDeleteConfirm = ref(false)
const skillToDelete = ref<SkillInfo | null>(null)

const isSkillSelected = (skillId: string) => {
  return selectedSkills.value.some((skill) => skill.id === skillId)
}

const toggleSkill = (skill: SkillInfo) => {
  const index = selectedSkills.value.findIndex((s) => s.id === skill.id)
  if (index === -1) {
    selectedSkills.value.push(skill)
  } else {
    selectedSkills.value.splice(index, 1)
  }
}

const removeSkill = (skill: SkillInfo) => {
  const index = selectedSkills.value.findIndex((s) => s.id === skill.id)
  if (index !== -1) {
    selectedSkills.value.splice(index, 1)
  }
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleConfirm = () => {
  emit('update:modelValue', selectedSkills.value)
  emit('update:visible', false)
}

const handleEditSkill = (skill: SkillInfo) => {
  currentEditSkill.value = skill
  editorVisible.value = true
}

const handleSaveSkill = (updatedSkill: SkillInfo) => {
  if (!updatedSkill.id) {
    updatedSkill.id = `temp_${Date.now()}`
    skillStore.addSkill(updatedSkill)
  }
  emit('update-skill', updatedSkill)
}

const handleCreateSkill = () => {
  currentEditSkill.value = undefined
  editorVisible.value = true
}

const handleDeleteSkill = async (skill: SkillInfo) => {
  if (!skill.id) return
  skillToDelete.value = skill
  showDeleteConfirm.value = true
}

const confirmDelete = async () => {
  if (!skillToDelete.value?.id) return

  try {
    const { data } = await deleteSkill(skillToDelete.value.id)
    if (data.code === '0') {
      Message.success('Skill deleted successfully')
      // Refresh skill list
      await skillStore.fetchSkillList(storyStore.currentStory.id)
    } else {
      throw new Error(data.message || 'Failed to delete skill')
    }
  } catch (error) {
    console.error('Failed to delete skill:', error)
    Message.error(error instanceof Error ? error.message : 'Failed to delete skill')
  } finally {
    skillToDelete.value = null
  }
}
</script>

<style lang="less" scoped>
.desktop-skill-select {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  background: linear-gradient(180deg, #2b1b3b 0%, #1a0f24 100%);
  border-radius: 24px;
  display: flex;
  flex-direction: column;
  animation: scaleIn 0.3s ease;
  overflow: hidden;
}

.modal-header {
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  h2 {
    color: white;
    font-size: 24px;
    font-weight: 600;
    margin: 0;
  }
}

.close-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  padding: 0;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  :deep(svg) {
    width: 24px;
    height: 24px;
  }
}

.modal-body {
  flex: 1;
  display: flex;
  gap: 24px;
  padding: 24px;
  overflow: hidden;
}

.skill-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  overflow-y: auto;
  padding-right: 16px;
  padding-bottom: 16px;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

.skill-card {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 16px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid transparent;
  height: 280px;

  &:hover {
    transform: translateY(-4px);
    border-color: rgba(202, 147, 242, 0.5);
  }

  &.is-selected {
    border-color: #ca93f2;
    box-shadow: 0 0 20px rgba(202, 147, 242, 0.3);
    transform: translateY(-4px);

    .skill-name {
      color: #ca93f2;
    }
  }

  &.create-card {
    border: 2px dashed rgba(255, 255, 255, 0.1);
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:hover {
      border-color: #ca93f2;
      background: rgba(202, 147, 242, 0.05);

      .create-icon {
        background: rgba(202, 147, 242, 0.2);
        transform: translateY(-2px);
      }

      .create-text {
        color: #ca93f2;
      }
    }

    .card-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 16px;
      height: 100%;
    }

    .create-icon {
      width: 64px;
      height: 64px;
      border-radius: 32px;
      background: rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      :deep(svg) {
        width: 32px;
        height: 32px;
        color: white;
      }
    }

    .create-text {
      font-size: 16px;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.8);
      transition: color 0.3s ease;
    }
  }

  .card-content {
    position: relative;
    width: 100%;
    height: 100%;

    .card-actions {
      position: absolute;
      top: 12px;
      right: 12px;
      z-index: 2;
      display: flex;
      gap: 8px;
    }

    .edit-button,
    .delete-button {
      padding: 6px 12px;
      border-radius: 16px;
      backdrop-filter: blur(4px);
      border: none;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;
      font-size: 14px;
    }

    .edit-button {
      background: rgba(0, 0, 0, 0.5);

      &:hover {
        background: rgba(202, 147, 242, 0.8);
        transform: translateY(-2px);
      }
    }

    .delete-button {
      background: rgba(255, 77, 79, 0.5);

      &:hover {
        background: rgba(255, 77, 79, 0.8);
        transform: translateY(-2px);
      }
    }
  }

  .preview-url {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .skill-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  }

  .skill-name {
    font-size: 18px;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
    transition: color 0.3s ease;
  }

  .skill-description {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.selected-skills-panel {
  width: 300px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  display: flex;
  flex-direction: column;

  .panel-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    h3 {
      color: white;
      font-size: 18px;
      font-weight: 600;
      margin: 0;
    }
  }

  .selected-skills-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 2px;
    }
  }
}

.selected-skill-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  .skill-avatar {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    object-fit: cover;
  }

  .skill-info {
    flex: 1;
    min-width: 0;

    .skill-name {
      font-size: 16px;
      font-weight: 500;
      color: white;
      margin-bottom: 4px;
    }

    .skill-effects {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .effect-item {
        font-size: 12px;
        color: #daff96;
        background: rgba(218, 255, 150, 0.1);
        padding: 2px 8px;
        border-radius: 10px;
      }
    }
  }

  .remove-button {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    padding: 0;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    :deep(svg) {
      width: 16px;
      height: 16px;
    }
  }
}

.modal-footer {
  padding: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.skill-count {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.confirm-button {
  min-width: 200px;
  height: 44px;
  border-radius: 22px;
  border: none;
  background: linear-gradient(90deg, #ca93f2 0%, #9b6cc8 100%);
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(202, 147, 242, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
