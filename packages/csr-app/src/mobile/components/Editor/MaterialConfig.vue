<template>
  <div class="material-config">
    <div class="material-header">
      <div class="title">素材生成配置</div>
    </div>

    <div class="material-content">
      <div class="form-item">
        <div class="form-label">场景描述</div>
        <textarea
          class="custom-textarea"
          :value="scene?.story_generation_params?.plots?.[0]?.sentence"
          @input="updateSceneDescription(($event.target as HTMLTextAreaElement).value)"
          :rows="3"
          placeholder="请输入场景描述"
        ></textarea>
      </div>

      <div class="character-list">
        <div class="section-title">角色配置</div>
        <div
          v-for="(character, index) in scene?.story_generation_params?.plots?.[0]?.character"
          :key="index"
          class="character-item"
        >
          <div class="form-item">
            <div class="form-label">角色名称</div>
            <input
              type="text"
              class="custom-input"
              :value="character.name"
              @input="updateCharacter(index, 'name', ($event.target as HTMLInputElement).value)"
              placeholder="请输入角色名称"
            />
          </div>
          <div class="form-item">
            <div class="form-label">Lora ID</div>
            <input
              type="text"
              class="custom-input"
              :value="character.lora_id"
              @input="updateCharacter(index, 'lora_id', ($event.target as HTMLInputElement).value)"
              placeholder="请输入Lora ID"
            />
          </div>
          <div class="form-item">
            <div class="form-label">服装描述</div>
            <input
              type="text"
              class="custom-input"
              :value="character.clothes"
              @input="updateCharacter(index, 'clothes', ($event.target as HTMLInputElement).value)"
              placeholder="请输入服装描述"
            />
          </div>
          <button class="delete-button" @click="removeCharacter(index)">
            <svg viewBox="0 0 24 24" class="icon-delete">
              <path
                fill="currentColor"
                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
              />
            </svg>
          </button>
        </div>
        <button class="add-button" @click="addCharacter">
          <svg viewBox="0 0 24 24" class="icon-add">
            <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
          </svg>
          <span>添加角色</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import type { Scene, Character } from '@/types/editor'
import { useEditorStore } from '@/store/editor'

const props = defineProps<{
  scene?: Scene
}>()

const editorStore = useEditorStore()

const updateSceneDescription = (description: string) => {
  if (!props.scene) return

  const updatedScene = cloneDeep(props.scene)
  if (!updatedScene.story_generation_params) {
    updatedScene.story_generation_params = {
      plots: [
        {
          sentence: description,
          character: [],
          location: ''
        }
      ]
    }
  } else if (!updatedScene.story_generation_params.plots?.[0]) {
    updatedScene.story_generation_params.plots = [
      {
        sentence: description,
        character: [],
        location: ''
      }
    ]
  } else {
    updatedScene.story_generation_params.plots[0].sentence = description
  }

  editorStore.updateScene(updatedScene)
}

const updateCharacter = (index: number, key: keyof Character, value: string) => {
  if (!props.scene) return

  const updatedScene = cloneDeep(props.scene)
  if (!updatedScene.story_generation_params?.plots?.[0]?.character?.[index]) {
    if (!updatedScene.story_generation_params) {
      updatedScene.story_generation_params = {
        plots: [{ sentence: '', character: [], location: '' }]
      }
    }
    if (!updatedScene.story_generation_params.plots?.[0]) {
      updatedScene.story_generation_params.plots = [{ sentence: '', character: [], location: '' }]
    }
    updatedScene.story_generation_params.plots[0].character =
      updatedScene.story_generation_params.plots[0].character || []
    updatedScene.story_generation_params.plots[0].character[index] = {
      name: '',
      lora_id: '',
      clothes: ''
    }
  }

  updatedScene.story_generation_params.plots[0].character[index][key] = value
  editorStore.updateScene(updatedScene)
}

const addCharacter = () => {
  if (!props.scene) return

  const updatedScene = cloneDeep(props.scene)
  if (!updatedScene.story_generation_params) {
    updatedScene.story_generation_params = {
      plots: [{ sentence: '', character: [], location: '' }]
    }
  }
  if (!updatedScene.story_generation_params.plots?.[0]) {
    updatedScene.story_generation_params.plots = [{ sentence: '', character: [], location: '' }]
  }
  if (!updatedScene.story_generation_params.plots[0].character) {
    updatedScene.story_generation_params.plots[0].character = []
  }

  updatedScene.story_generation_params.plots[0].character.push({
    name: '',
    lora_id: '',
    clothes: ''
  })

  editorStore.updateScene(updatedScene)
}

const removeCharacter = (index: number) => {
  if (!props.scene?.story_generation_params?.plots?.[0]?.character) return

  const updatedScene = cloneDeep(props.scene)
  updatedScene.story_generation_params!.plots[0].character.splice(index, 1)
  editorStore.updateScene(updatedScene)
}
</script>

<style lang="less" scoped>
.material-config {
  height: 100%;
  color: #fff;

  .material-header {
    padding: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .title {
      font-size: 15px;
      font-weight: 600;
      color: #fff;
    }
  }

  .material-content {
    padding: 16px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      margin: 16px 0;
      color: #fff;
    }

    .character-item {
      position: relative;
      padding: 12px;
      margin-bottom: 12px;
      border-radius: 8px;
      background: rgba(204, 213, 255, 0.05);
      border: 1px solid rgba(184, 196, 255, 0.1);
    }
  }

  .form-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .form-label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 8px;
    }
  }

  .custom-input {
    width: 100%;
    height: 42px;
    padding: 0 16px;
    border: 1px solid rgba(184, 196, 255, 0.1);
    background: rgba(204, 213, 255, 0.05);
    color: #fff;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;

    &:hover,
    &:focus {
      border-color: #ca93f2;
      background: rgba(204, 213, 255, 0.08);
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.3);
    }
  }

  .custom-textarea {
    width: 100%;
    min-height: 120px;
    padding: 12px 16px;
    border: 1px solid rgba(184, 196, 255, 0.1);
    background: rgba(204, 213, 255, 0.05);
    color: #fff;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;
    resize: vertical;

    &:hover,
    &:focus {
      border-color: #ca93f2;
      background: rgba(204, 213, 255, 0.08);
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.3);
    }
  }

  .add-button {
    width: 100%;
    height: 42px;
    border: 1px dashed rgba(184, 196, 255, 0.1);
    background: none;
    color: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #ca93f2;
      color: #ca93f2;
    }

    .icon-add {
      width: 20px;
      height: 20px;
    }
  }

  .delete-button {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 28px;
    height: 28px;
    border: none;
    background: rgba(255, 77, 79, 0.1);
    color: #ff4d4f;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 77, 79, 0.2);
    }

    .icon-delete {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
