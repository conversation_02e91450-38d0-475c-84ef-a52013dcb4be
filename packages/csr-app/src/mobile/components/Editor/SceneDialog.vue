<template>
  <ConfirmDialog
    v-model:visible="modelValue"
    :title="isEdit ? '编辑故事' : '新建故事'"
    :confirm-text="isEdit ? '保存' : '创建'"
    :cancel-text="isEdit ? '取消' : '取消'"
    :on-before-confirm="validateForm"
    @confirm="handleConfirm"
  >
    <template #content>
      <div class="scene-form">
        <div class="form-item">
          <input
            v-model="formData.name"
            class="custom-input"
            :placeholder="'请输入故事名称'"
            @keyup.enter="handleConfirm"
          />
          <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>
        </div>
      </div>
    </template>
  </ConfirmDialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import ConfirmDialog from '@/mobile/components/ConfirmDialog.vue'
import type { Scene } from '@/types/editor'

const props = defineProps<{
  scene?: Scene
}>()

const modelValue = defineModel<boolean>()

const emit = defineEmits<{
  (e: 'confirm', data: { name: string }): void
}>()

const isEdit = computed(() => !!props.scene)
const formData = ref({
  name: ''
})
const errorMessage = ref('')

watch(
  () => modelValue.value,
  (val) => {
    if (val) {
      formData.value.name = props.scene?.name || ''
      errorMessage.value = ''
    }
  }
)

const validateForm = () => {
  if (!formData.value.name.trim()) {
    errorMessage.value = '请输入场景名称'
    return false
  }
  return true
}

const handleConfirm = () => {
  if (validateForm()) {
    emit('confirm', {
      name: formData.value.name.trim()
    })
  }
}
</script>

<style lang="less" scoped>
.scene-form {
  width: 100%;

  .form-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .custom-input {
    width: 100%;
    height: 42px;
    padding: 0 16px;
    border: 1px solid rgba(184, 196, 255, 0.1);
    background: rgba(204, 213, 255, 0.05);
    color: #fff;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;

    &:hover,
    &:focus {
      border-color: #ca93f2;
      background: rgba(204, 213, 255, 0.08);
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.3);
    }
  }

  .error-message {
    color: #ff4d4f;
    font-size: 12px;
    margin-top: 4px;
    padding-left: 16px;
  }
}
</style>
