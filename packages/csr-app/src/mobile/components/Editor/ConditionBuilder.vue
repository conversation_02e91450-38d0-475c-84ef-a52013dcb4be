<template>
  <div class="condition-flow">
    <!-- <div class="condition-when">当</div> -->
    <div class="condition-builder">
      <div v-for="(condition, index) in getConditions" :key="index" class="condition-content">
        <div class="condition-header">
          <div class="condition-title">条件 {{ index + 1 }}</div>
          <button
            v-if="getConditions.length > 1"
            class="delete-condition"
            @click="removeCondition(index)"
          >
            <svg viewBox="0 0 24 24" class="icon-delete">
              <path
                fill="currentColor"
                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
              />
            </svg>
          </button>
        </div>

        <div class="condition-type">
          <Select
            :model-value="condition.type || 'single'"
            @update:model-value="
              (value) => updateConditionType(index, value as 'single' | 'and' | 'or')
            "
            :options="[
              { label: '单个属性条件', value: 'single' },
              { label: '多个属性与条件', value: 'and' },
              { label: '多个属性或条件', value: 'or' }
            ]"
          />
        </div>

        <!-- 单个属性条件 -->
        <div v-if="condition.type === 'single'" class="condition-content">
          <div class="attribute-condition">
            <Select
              :model-value="condition.attribute"
              @update:model-value="(value) => updateSingleAttribute(index, value)"
              :options="attributeOptions"
              placeholder="请选择属性"
            />
            <Select
              :model-value="condition.operator"
              @update:model-value="(value) => updateSingleOperator(index, value)"
              :options="operatorOptions"
              placeholder="请选择运算符"
            />
            <input
              type="number"
              class="value-input"
              :value="condition.value"
              @input="(e) => updateSingleValue(index, Number((e.target as HTMLInputElement).value))"
              min="0"
              max="100"
              step="1"
              placeholder="请输入值"
            />
          </div>
        </div>

        <!-- 多个属性与条件 -->
        <div v-if="condition.type === 'and'" class="condition-content">
          <div
            v-for="(subCondition, subIndex) in condition.conditions"
            :key="subIndex"
            class="attribute-condition"
          >
            <Select
              :model-value="subCondition.attribute"
              @update:model-value="(value) => updateAndAttribute(index, subIndex, value)"
              :options="attributeOptions"
              placeholder="请选择属性"
            />
            <Select
              :model-value="subCondition.operator"
              @update:model-value="(value) => updateAndOperator(index, subIndex, value)"
              :options="operatorOptions"
              placeholder="请选择运算符"
            />
            <input
              type="number"
              class="value-input"
              :value="subCondition.value"
              @input="
                (e) => updateAndValue(index, subIndex, Number((e.target as HTMLInputElement).value))
              "
              min="0"
              max="100"
              step="1"
              placeholder="请输入值"
            />
            <button
              v-if="condition.conditions.length > 1"
              class="delete-condition"
              @click="removeAndCondition(index, subIndex)"
            >
              <svg viewBox="0 0 24 24" class="icon-delete">
                <path
                  fill="currentColor"
                  d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                />
              </svg>
            </button>
          </div>
          <button class="add-condition" @click="addAndCondition(index)">
            <svg viewBox="0 0 24 24" class="icon-add">
              <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
            </svg>
            <span>添加条件</span>
          </button>
        </div>

        <!-- 多个属性或条件 -->
        <div v-if="condition.type === 'or'" class="condition-content">
          <div
            v-for="(subCondition, subIndex) in condition.conditions"
            :key="subIndex"
            class="attribute-condition"
          >
            <Select
              :model-value="subCondition.attribute"
              @update:model-value="(value) => updateOrAttribute(index, subIndex, value)"
              :options="attributeOptions"
              placeholder="请选择属性"
            />
            <Select
              :model-value="subCondition.operator"
              @update:model-value="(value) => updateOrOperator(index, subIndex, value)"
              :options="operatorOptions"
              placeholder="请选择运算符"
            />
            <input
              type="number"
              class="value-input"
              :value="subCondition.value"
              @input="
                (e) => updateOrValue(index, subIndex, Number((e.target as HTMLInputElement).value))
              "
              min="0"
              max="100"
              step="1"
              placeholder="请输入值"
            />
            <button
              v-if="condition.conditions.length > 1"
              class="delete-condition"
              @click="removeOrCondition(index, subIndex)"
            >
              <svg viewBox="0 0 24 24" class="icon-delete">
                <path
                  fill="currentColor"
                  d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                />
              </svg>
            </button>
          </div>
          <button class="add-condition" @click="addOrCondition(index)">
            <svg viewBox="0 0 24 24" class="icon-add">
              <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
            </svg>
            <span>添加条件</span>
          </button>
        </div>

        <!-- 跳转目标章节选择 -->
        <div class="condition-content target-chapter-section">
          <div class="condition-target">
            <div class="target-label">跳转到：</div>
            <Select
              :model-value="condition.echo"
              @update:model-value="(value) => updateCondition(index, 'echo', value)"
              :options="chapterOptions"
              placeholder="请选择目标章节"
            />
          </div>
        </div>
      </div>

      <button class="add-condition" @click="addCondition">
        <svg viewBox="0 0 24 24" class="icon-add">
          <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
        </svg>
        <span>添加条件配置</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import Select from '../Select.vue'
import type { Scene } from '@/types/editor'

interface SubCondition {
  attribute: string
  operator: string
  value: number
}

interface ConditionConfig {
  type: 'single' | 'and' | 'or'
  attribute?: string
  operator?: string
  value?: number
  conditions?: SubCondition[]
  echo: string
  expression: string
}

const props = defineProps<{
  modelValue?: ConditionConfig[]
  chapterOptions: { label: string; value: string }[]
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: ConditionConfig[]): void
}>()

// Initialize default value when component is mounted
onMounted(() => {
  if (!props.modelValue) {
    addCondition()
  }
})

const attributeOptions = [
  { label: '智慧', value: 'intelligence' },
  { label: '力量', value: 'strength' },
  { label: '幸福', value: 'happiness' },
  { label: '财富', value: 'wealth' }
]

const operatorOptions = [
  { label: '大于', value: '>' },
  { label: '小于', value: '<' },
  { label: '大于等于', value: '>=' },
  { label: '小于等于', value: '<=' },
  { label: '等于', value: '==' }
]

const getConditions = computed(() => props.modelValue || [])

const addCondition = () => {
  const conditions = [...(props.modelValue || [])]
  conditions.push({
    type: 'single',
    attribute: 'intelligence',
    operator: '>',
    value: 0,
    echo: '',
    expression: ''
  })
  emit('update:modelValue', conditions)
}

const removeCondition = (index: number) => {
  if (!props.modelValue) return
  const conditions = [...props.modelValue]
  if (conditions.length <= 1) return // Keep at least one condition
  conditions.splice(index, 1)
  emit('update:modelValue', conditions)
}

const updateCondition = (index: number, field: keyof ConditionConfig, value: string) => {
  if (!props.modelValue) return
  const conditions = [...props.modelValue]
  conditions[index] = { ...conditions[index], [field]: value }
  // 更新表达式
  conditions[index].expression = generateExpression(conditions[index])
  emit('update:modelValue', conditions)
}

const generateExpression = (condition: ConditionConfig): string => {
  if (!condition.attribute || !condition.operator || condition.value === undefined) return ''

  if (condition.type === 'single') {
    return `${condition.attribute} ${condition.operator} ${condition.value}`
  } else if (condition.type === 'and') {
    return (
      condition.conditions?.map((c) => `${c.attribute} ${c.operator} ${c.value}`).join(' && ') || ''
    )
  } else if (condition.type === 'or') {
    return (
      condition.conditions?.map((c) => `${c.attribute} ${c.operator} ${c.value}`).join(' || ') || ''
    )
  }
  return ''
}

// Single condition
const updateSingleAttribute = (index: number, value: string) => {
  if (!props.modelValue) return
  const conditions = [...props.modelValue]
  conditions[index] = {
    ...conditions[index],
    attribute: value,
    type: 'single'
  }
  conditions[index].expression = generateExpression(conditions[index])
  emit('update:modelValue', conditions)
}

const updateSingleOperator = (index: number, value: string) => {
  if (!props.modelValue) return
  const conditions = [...props.modelValue]
  conditions[index] = {
    ...conditions[index],
    operator: value,
    type: 'single'
  }
  conditions[index].expression = generateExpression(conditions[index])
  emit('update:modelValue', conditions)
}

const updateSingleValue = (index: number, value: number) => {
  if (!props.modelValue) return
  const conditions = [...props.modelValue]
  conditions[index] = {
    ...conditions[index],
    value,
    type: 'single'
  }
  conditions[index].expression = generateExpression(conditions[index])
  emit('update:modelValue', conditions)
}

// AND conditions
const updateAndAttribute = (index: number, subIndex: number, value: string) => {
  if (!props.modelValue) return
  const conditions = [...props.modelValue]
  if (!conditions[index].conditions) {
    conditions[index].conditions = []
  }
  conditions[index].conditions![subIndex] = {
    ...conditions[index].conditions![subIndex],
    attribute: value
  }
  conditions[index].expression = generateExpression(conditions[index])
  emit('update:modelValue', conditions)
}

const updateAndOperator = (index: number, subIndex: number, value: string) => {
  if (!props.modelValue) return
  const conditions = [...props.modelValue]
  if (!conditions[index].conditions) {
    conditions[index].conditions = []
  }
  conditions[index].conditions![subIndex] = {
    ...conditions[index].conditions![subIndex],
    operator: value
  }
  conditions[index].expression = generateExpression(conditions[index])
  emit('update:modelValue', conditions)
}

const updateAndValue = (index: number, subIndex: number, value: number) => {
  if (!props.modelValue) return
  const conditions = [...props.modelValue]
  if (!conditions[index].conditions) {
    conditions[index].conditions = []
  }
  conditions[index].conditions![subIndex] = {
    ...conditions[index].conditions![subIndex],
    value
  }
  conditions[index].expression = generateExpression(conditions[index])
  emit('update:modelValue', conditions)
}

const addAndCondition = (index: number) => {
  if (!props.modelValue) return
  const conditions = [...props.modelValue]
  if (!conditions[index].conditions) {
    conditions[index].conditions = []
  }
  conditions[index].conditions!.push({ attribute: 'intelligence', operator: '>', value: 0 })
  conditions[index].expression = generateExpression(conditions[index])
  emit('update:modelValue', conditions)
}

const removeAndCondition = (index: number, subIndex: number) => {
  if (!props.modelValue) return
  const conditions = [...props.modelValue]
  if (!conditions[index].conditions || conditions[index].conditions.length <= 1) return
  conditions[index].conditions!.splice(subIndex, 1)
  conditions[index].expression = generateExpression(conditions[index])
  emit('update:modelValue', conditions)
}

// OR conditions
const updateOrAttribute = (index: number, subIndex: number, value: string) => {
  if (!props.modelValue) return
  const conditions = [...props.modelValue]
  if (!conditions[index].conditions) {
    conditions[index].conditions = []
  }
  conditions[index].conditions![subIndex] = {
    ...conditions[index].conditions![subIndex],
    attribute: value
  }
  conditions[index].expression = generateExpression(conditions[index])
  emit('update:modelValue', conditions)
}

const updateOrOperator = (index: number, subIndex: number, value: string) => {
  if (!props.modelValue) return
  const conditions = [...props.modelValue]
  if (!conditions[index].conditions) {
    conditions[index].conditions = []
  }
  conditions[index].conditions![subIndex] = {
    ...conditions[index].conditions![subIndex],
    operator: value
  }
  conditions[index].expression = generateExpression(conditions[index])
  emit('update:modelValue', conditions)
}

const updateOrValue = (index: number, subIndex: number, value: number) => {
  if (!props.modelValue) return
  const conditions = [...props.modelValue]
  if (!conditions[index].conditions) {
    conditions[index].conditions = []
  }
  conditions[index].conditions![subIndex] = {
    ...conditions[index].conditions![subIndex],
    value
  }
  conditions[index].expression = generateExpression(conditions[index])
  emit('update:modelValue', conditions)
}

const addOrCondition = (index: number) => {
  if (!props.modelValue) return
  const conditions = [...props.modelValue]
  if (!conditions[index].conditions) {
    conditions[index].conditions = []
  }
  conditions[index].conditions!.push({ attribute: 'intelligence', operator: '>', value: 0 })
  conditions[index].expression = generateExpression(conditions[index])
  emit('update:modelValue', conditions)
}

const removeOrCondition = (index: number, subIndex: number) => {
  if (!props.modelValue) return
  const conditions = [...props.modelValue]
  if (!conditions[index].conditions || conditions[index].conditions.length <= 1) return
  conditions[index].conditions!.splice(subIndex, 1)
  conditions[index].expression = generateExpression(conditions[index])
  emit('update:modelValue', conditions)
}

const updateConditionType = (index: number, value: 'single' | 'and' | 'or') => {
  if (!props.modelValue) return
  const conditions = [...props.modelValue]
  conditions[index] = {
    ...conditions[index],
    type: value,
    conditions:
      value === 'single' ? undefined : [{ attribute: 'intelligence', operator: '>', value: 0 }]
  }
  conditions[index].expression = generateExpression(conditions[index])
  emit('update:modelValue', conditions)
}
</script>

<style lang="less" scoped>
.condition-flow {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  background: rgba(204, 213, 255, 0.03);
  border-radius: 8px;

  .condition-when {
    color: #fff;
    font-size: 14px;
  }

  .condition-builder {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .condition-content {
      background: rgba(204, 213, 255, 0.05);
      border-radius: 8px;
      padding: 12px;

      .condition-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .condition-title {
          color: #fff;
          font-size: 14px;
          font-weight: 500;
        }
      }

      .condition-type {
        width: 100%;
        margin-bottom: 12px;
      }

      .condition-target {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);

        .target-label {
          color: #fff;
          font-size: 14px;
          white-space: nowrap;
        }
      }

      .attribute-condition {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .value-input {
          width: 80px;
          height: 32px;
          padding: 4px 8px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          background: rgba(255, 255, 255, 0.05);
          color: #fff;
          font-size: 14px;
          outline: none;

          &:hover {
            border-color: rgba(255, 255, 255, 0.2);
          }

          &:focus {
            border-color: #1890ff;
          }
        }
      }
    }
  }
  .delete-condition {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 77, 79, 0.1);
    border: 1px solid rgba(255, 77, 79, 0.2);
    border-radius: 6px;
    color: #ff4d4f;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 77, 79, 0.2);
      border-color: rgba(255, 77, 79, 0.3);
    }

    .icon-delete {
      width: 16px;
      height: 16px;
    }
  }
  .add-condition {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    height: 42px;
    padding: 0 16px;
    border: 1px dashed rgba(184, 196, 255, 0.1);
    background: rgba(204, 213, 255, 0.05);
    color: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      border-color: #ca93f2;
      background: rgba(204, 213, 255, 0.08);
      color: #ca93f2;
    }

    :deep(svg) {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
