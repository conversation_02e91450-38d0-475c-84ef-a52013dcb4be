<template>
  <div>
    <div class="material-list">
      <div class="material-content">
        <div class="material-item" v-for="scene in scenes" :key="scene.id">
          <div class="material-item-header" @click="toggleMaterial(scene.id)">
            <div class="material-info">
              <icon-folder />
              <span class="material-name">{{ scene.name }}（素材配置）</span>
            </div>
          </div>

          <div v-show="expandedMaterials[scene.id]" class="material-item-content">
            <div class="plot-list">
              <div
                v-for="(plot, plotIndex) in getScenePlots(scene)"
                :key="plotIndex"
                class="plot-item"
              >
                <div class="plot-header">
                  <div class="plot-title">场景描述 {{ plotIndex + 1 }}</div>
                  <button
                    class="plot-delete-button"
                    @click="removePlot(scene.id, plotIndex)"
                    v-if="getScenePlots(scene).length > 1"
                  >
                    <svg viewBox="0 0 24 24" class="icon-delete">
                      <path
                        fill="currentColor"
                        d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                      />
                    </svg>
                    <span>删除场景</span>
                  </button>
                </div>

                <div class="form-item">
                  <div class="form-label">场景描述</div>
                  <textarea
                    class="custom-textarea"
                    :value="plot.sentence"
                    @input="
                      updatePlotField(
                        scene.id,
                        plotIndex,
                        'sentence',
                        ($event.target as HTMLTextAreaElement).value
                      )
                    "
                    :rows="3"
                    placeholder="请输入场景描述"
                  ></textarea>
                </div>

                <div class="form-item">
                  <div class="form-label">场景位置</div>
                  <input
                    type="text"
                    class="custom-input"
                    :value="plot.location"
                    @input="
                      updatePlotField(
                        scene.id,
                        plotIndex,
                        'location',
                        ($event.target as HTMLInputElement).value
                      )
                    "
                    placeholder="请输入场景位置"
                  />
                </div>

                <div class="character-list">
                  <div class="section-title">角色配置</div>
                  <div
                    v-for="(character, charIndex) in plot.character"
                    :key="charIndex"
                    class="character-item"
                  >
                    <div class="form-item">
                      <div class="form-label">角色名称</div>
                      <input
                        type="text"
                        class="custom-input"
                        :value="character.name"
                        @input="
                          updateCharacter(
                            scene.id,
                            plotIndex,
                            charIndex,
                            'name',
                            ($event.target as HTMLInputElement).value
                          )
                        "
                        placeholder="请输入角色名称"
                      />
                    </div>
                    <div class="form-item">
                      <div class="form-label">Lora ID</div>
                      <input
                        type="text"
                        class="custom-input"
                        :value="character.lora_id"
                        @input="
                          updateCharacter(
                            scene.id,
                            plotIndex,
                            charIndex,
                            'lora_id',
                            ($event.target as HTMLInputElement).value
                          )
                        "
                        placeholder="请输入Lora ID"
                      />
                    </div>
                    <div class="form-item">
                      <div class="form-label">服装描述</div>
                      <input
                        type="text"
                        class="custom-input"
                        :value="character.clothes"
                        @input="
                          updateCharacter(
                            scene.id,
                            plotIndex,
                            charIndex,
                            'clothes',
                            ($event.target as HTMLInputElement).value
                          )
                        "
                        placeholder="请输入服装描述"
                      />
                    </div>
                    <button
                      class="delete-button"
                      @click="removeCharacter(scene.id, plotIndex, charIndex)"
                    >
                      <svg viewBox="0 0 24 24" class="icon-delete">
                        <path
                          fill="currentColor"
                          d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                        />
                      </svg>
                    </button>
                  </div>
                  <button class="add-button" @click="addCharacter(scene.id, plotIndex)">
                    <svg viewBox="0 0 24 24" class="icon-add">
                      <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                    </svg>
                    <span>添加角色</span>
                  </button>
                </div>
              </div>

              <button class="add-plot-button" @click="addPlot(scene.id)">
                <svg viewBox="0 0 24 24" class="icon-add">
                  <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                </svg>
                <span>添加场景描述</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="user-id-input">
      <div class="form-label">白日梦用户ID</div>
      <input
        type="text"
        class="custom-input"
        :value="editorStore.gameConfig.daydream_user_id"
        @input="updateDaydreamUserId(($event.target as HTMLInputElement).value)"
        placeholder="请输入白日梦用户ID"
      />
    </div>
    <button
      class="generate-button"
      :class="{
        'generate-button-disabled': !editorStore.currentStoryId || hasSubmitted,
        generating: isGenerating
      }"
      :disabled="isGenerating || hasSubmitted"
      @click="handleGenerate"
    >
      <svg v-if="isGenerating" viewBox="0 0 24 24" class="icon-loading">
        <path fill="currentColor" d="M12 4V2C6.48 2 2 6.48 2 12h2c0-4.41 3.59-8 8-8z" />
      </svg>
      <span>{{
        isGenerating ? '生成中...' : hasSubmitted ? '素材生成任务已提交' : '生成素材'
      }}</span>
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { cloneDeep } from 'lodash-es'
import type { Scene, Character, Plot } from '@/types/editor'
import { useEditorStore } from '@/store/editor'
import { Message } from '@/mobile/components/Message'

const editorStore = useEditorStore()
const expandedMaterials = ref<Record<string, boolean>>({})
const isGenerating = ref(false)
const hasSubmitted = ref(false)

const scenes = computed(() => {
  // 对场景进行排序，确保开始场景在最前，结束场景在最后
  return [...editorStore.gameConfig.scenes]
    .filter((scene) => scene.id !== '~')
    .sort((a, b) => {
      // _BEGIN_ 场景始终在最前
      if (a.id === '_BEGIN_') return -1
      if (b.id === '_BEGIN_') return 1
      // _END_ 场景始终在最后
      if (a.id === '_END_') return 1
      if (b.id === '_END_') return -1
      // 其他场景保持原有顺序
      return 0
    })
})

const toggleMaterial = (sceneId: string) => {
  expandedMaterials.value[sceneId] = !expandedMaterials.value[sceneId]
}

const getScenePlots = (scene: Scene): Plot[] => {
  if (!scene.story_generation_params?.plots) {
    return [
      {
        sentence: '',
        character: [],
        location: ''
      }
    ]
  }
  return scene.story_generation_params.plots
}

const updatePlotField = (
  sceneId: string,
  plotIndex: number,
  field: Exclude<keyof Plot, 'character'>,
  value: string
) => {
  const scene = scenes.value.find((s) => s.id === sceneId)
  if (!scene) return

  const updatedScene = cloneDeep(scene)
  if (!updatedScene.story_generation_params) {
    updatedScene.story_generation_params = {
      plots: [
        {
          sentence: '',
          character: [],
          location: ''
        }
      ]
    }
  }

  if (!updatedScene.story_generation_params.plots[plotIndex]) {
    updatedScene.story_generation_params.plots[plotIndex] = {
      sentence: '',
      character: [],
      location: ''
    }
  }

  updatedScene.story_generation_params.plots[plotIndex][field] = value
  editorStore.updateScene(updatedScene)
}

const updateCharacter = (
  sceneId: string,
  plotIndex: number,
  charIndex: number,
  key: keyof Character,
  value: string
) => {
  const scene = scenes.value.find((s) => s.id === sceneId)
  if (!scene) return

  const updatedScene = cloneDeep(scene)
  if (!updatedScene.story_generation_params) {
    updatedScene.story_generation_params = {
      plots: [
        {
          sentence: '',
          character: [],
          location: ''
        }
      ]
    }
  }

  if (!updatedScene.story_generation_params.plots[plotIndex]) {
    updatedScene.story_generation_params.plots[plotIndex] = {
      sentence: '',
      character: [],
      location: ''
    }
  }

  if (!updatedScene.story_generation_params.plots[plotIndex].character[charIndex]) {
    updatedScene.story_generation_params.plots[plotIndex].character[charIndex] = {
      name: '',
      lora_id: '',
      clothes: ''
    }
  }

  updatedScene.story_generation_params.plots[plotIndex].character[charIndex][key] = value
  editorStore.updateScene(updatedScene)
}

const addCharacter = (sceneId: string, plotIndex: number) => {
  const scene = scenes.value.find((s) => s.id === sceneId)
  if (!scene) return

  const updatedScene = cloneDeep(scene)
  if (!updatedScene.story_generation_params) {
    updatedScene.story_generation_params = {
      plots: [
        {
          sentence: '',
          character: [],
          location: ''
        }
      ]
    }
  }

  if (!updatedScene.story_generation_params.plots[plotIndex]) {
    updatedScene.story_generation_params.plots[plotIndex] = {
      sentence: '',
      character: [],
      location: ''
    }
  }

  updatedScene.story_generation_params.plots[plotIndex].character.push({
    name: '',
    lora_id: '',
    clothes: ''
  })

  editorStore.updateScene(updatedScene)
}

const removeCharacter = (sceneId: string, plotIndex: number, charIndex: number) => {
  const scene = scenes.value.find((s) => s.id === sceneId)
  if (!scene?.story_generation_params?.plots[plotIndex]?.character) return

  const updatedScene = cloneDeep(scene)
  updatedScene.story_generation_params.plots[plotIndex].character.splice(charIndex, 1)
  editorStore.updateScene(updatedScene)
}

const addPlot = (sceneId: string) => {
  const scene = scenes.value.find((s) => s.id === sceneId)
  if (!scene) return

  const updatedScene = cloneDeep(scene)
  if (!updatedScene.story_generation_params) {
    updatedScene.story_generation_params = {
      plots: []
    }
  }

  updatedScene.story_generation_params.plots.push({
    sentence: '',
    character: [],
    location: ''
  })

  editorStore.updateScene(updatedScene)
}

const removePlot = (sceneId: string, plotIndex: number) => {
  const scene = scenes.value.find((s) => s.id === sceneId)
  if (!scene?.story_generation_params?.plots) return

  const updatedScene = cloneDeep(scene)
  updatedScene.story_generation_params.plots.splice(plotIndex, 1)
  editorStore.updateScene(updatedScene)
}

const handleGenerate = async () => {
  if (!editorStore.currentStoryId) {
    Message.warning('请先保存故事配置后再生成素材')
    return
  }
  if (!editorStore.gameConfig.daydream_user_id) {
    Message.warning('请输入白日梦用户ID')
    return
  }

  try {
    isGenerating.value = true
    const res = await editorStore.generateResources()
    if (res) {
      Message.success('素材生成任务提交成功')
      hasSubmitted.value = true
    } else {
      Message.error('素材生成任务提交失败')
    }
  } catch (error) {
    Message.error(error instanceof Error ? error.message : '素材生成任务提交失败')
    hasSubmitted.value = true
  } finally {
    isGenerating.value = false
  }
}

const updateDaydreamUserId = (userId: string) => {
  editorStore.gameConfig.daydream_user_id = userId
  editorStore.isModified = true
}
</script>

<style lang="less" scoped>
.user-id-input {
  margin-bottom: 16px;
  padding: 0 16px;

  .form-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 8px;
  }

  .custom-input {
    width: 100%;
    height: 42px;
    padding: 0 16px;
    border: 1px solid rgba(184, 196, 255, 0.1);
    background: rgba(204, 213, 255, 0.05);
    color: #fff;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;

    &:hover,
    &:focus {
      border-color: #ca93f2;
      background: rgba(204, 213, 255, 0.08);
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.3);
    }
  }
}

.generate-button {
  width: 100%;
  height: 48px;
  margin-bottom: 16px;
  border: none;
  background: linear-gradient(45deg, #ca93f2, #9747ff);
  color: #fff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 15px;
  font-weight: 600;

  &:hover:not(.generate-button-disabled):not(.generating) {
    opacity: 0.9;
    transform: translateY(-1px);
  }

  &:active:not(.generate-button-disabled):not(.generating) {
    transform: translateY(0);
  }

  &.generate-button-disabled {
    background: #4a4a4a;
    cursor: not-allowed;
    opacity: 0.5;
    transform: none !important;
  }

  &.generating {
    cursor: not-allowed;
    position: relative;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.1);
      animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    .icon-loading {
      width: 20px;
      height: 20px;
      animation: spin 1s linear infinite;
    }
  }

  .icon-generate {
    width: 20px;
    height: 20px;
  }

  span {
    line-height: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

.material-list {
  height: 100%;
  color: #fff;

  .material-content {
    .material-item {
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      .material-item-header {
        padding: 16px;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background: rgba(255, 255, 255, 0.05);
        }

        .material-info {
          display: flex;
          align-items: center;
          gap: 8px;

          :deep(svg) {
            width: 20px;
            height: 20px;
            color: rgba(255, 255, 255, 0.8);
          }

          .material-name {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }

      .material-item-content {
        padding: 16px;
        background: rgba(0, 0, 0, 0.2);

        .plot-list {
          display: flex;
          flex-direction: column;
          gap: 24px;

          .plot-item {
            padding: 16px;
            background: rgba(204, 213, 255, 0.05);
            border: 1px solid rgba(184, 196, 255, 0.1);
            border-radius: 8px;

            .plot-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 16px;

              .plot-title {
                font-size: 15px;
                font-weight: 600;
                color: #fff;
              }

              .plot-delete-button {
                display: flex;
                align-items: center;
                gap: 4px;
                padding: 6px 12px;
                border: none;
                background: rgba(255, 77, 79, 0.1);
                color: #ff4d4f;
                border-radius: 6px;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                  background: rgba(255, 77, 79, 0.2);
                }

                .icon-delete {
                  width: 16px;
                  height: 16px;
                }

                span {
                  font-size: 13px;
                }
              }
            }
          }
        }

        .section-title {
          font-size: 14px;
          font-weight: 600;
          margin: 16px 0;
          color: #fff;
        }

        .character-item {
          position: relative;
          padding: 12px;
          margin-bottom: 12px;
          border-radius: 8px;
          background: rgba(204, 213, 255, 0.05);
          border: 1px solid rgba(184, 196, 255, 0.1);
        }
      }
    }
  }

  .form-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .form-label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 8px;
    }
  }

  .custom-input {
    width: 100%;
    height: 42px;
    padding: 0 16px;
    border: 1px solid rgba(184, 196, 255, 0.1);
    background: rgba(204, 213, 255, 0.05);
    color: #fff;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;

    &:hover,
    &:focus {
      border-color: #ca93f2;
      background: rgba(204, 213, 255, 0.08);
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.3);
    }
  }

  .custom-textarea {
    width: 100%;
    min-height: 120px;
    padding: 12px 16px;
    border: 1px solid rgba(184, 196, 255, 0.1);
    background: rgba(204, 213, 255, 0.05);
    color: #fff;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;
    resize: vertical;

    &:hover,
    &:focus {
      border-color: #ca93f2;
      background: rgba(204, 213, 255, 0.08);
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.3);
    }
  }

  .add-button,
  .add-plot-button {
    width: 100%;
    height: 42px;
    border: 1px dashed rgba(184, 196, 255, 0.1);
    background: none;
    color: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #ca93f2;
      color: #ca93f2;
    }

    .icon-add {
      width: 20px;
      height: 20px;
    }
  }

  .delete-button {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 28px;
    height: 28px;
    border: none;
    background: rgba(255, 77, 79, 0.1);
    color: #ff4d4f;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 77, 79, 0.2);
    }

    .icon-delete {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
