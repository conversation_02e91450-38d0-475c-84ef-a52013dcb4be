<template>
  <div class="menu-wrapper">
    <router-link
      to="/stories"
      class="menu-item"
      active-class="active"
      @click="handleMenuClick('/stories')"
    >
      <div class="icon-wrapper">
        <template v-if="themeStore.isDarkTheme">
          <icon-home-active v-if="isHomeActive" />
          <icon-home-unactive v-else />
        </template>
        <template v-else>
          <icon-light-home-active v-if="isHomeActive" />
          <icon-light-home-unactive v-else />
        </template>
      </div>
      <span>Home</span>
    </router-link>
    <!-- <router-link
      v-if="userStore.isAdmin"
      to="/creation"
      class="menu-item"
      active-class="active"
      @click="handleMenuClick('/creation')"
    >
      <div class="icon-wrapper">
        <icon-creation-active v-if="route.path === '/creation'" />
        <icon-creation-unactive v-else />
      </div>
      <span>Creation</span>
    </router-link> -->
    <!-- <router-link
      v-if="AppName === 'ReelPlay'"
      to="/user-character"
      class="menu-item"
      active-class="active"
      @click="handleMenuClick('/user-character')"
    >
      <div class="icon-wrapper">
        <icon-character-active v-if="route.path === '/user-character'" />
        <icon-character-unactive v-else />
        <div v-if="showRedDot" class="red-dot"></div>
      </div>
      <span>AI Character</span>
    </router-link> -->
    <router-link
      to="/user/profile"
      class="menu-item"
      active-class="active"
      @click="handleMenuClick('/user/profile')"
    >
      <div class="icon-wrapper">
        <template v-if="themeStore.isDarkTheme">
          <icon-user-active v-if="isProfileActive" />
          <icon-user-unactive v-else />
        </template>
        <template v-else>
          <icon-light-user-active v-if="isProfileActive" />
          <icon-light-user-unactive v-else />
        </template>
      </div>
      <span>Account</span>
    </router-link>
  </div>
</template>

<script setup lang="ts">
import { IconHome, IconUser } from '@arco-design/web-vue/es/icon'
import IconHomeActive from '@/assets/icon/home-active.svg'
import IconUserActive from '@/assets/icon/account-active.svg'
import IconHomeUnactive from '@/assets/icon/home-unactive.svg'
import IconUserUnactive from '@/assets/icon/account-unactive.svg'
import IconLightHomeActive from '@/assets/icon/light-home-active.svg'
import IconLightUserActive from '@/assets/icon/light-account-active.svg'
import IconLightHomeUnactive from '@/assets/icon/light-home-unactive.svg'
import IconLightUserUnactive from '@/assets/icon/light-account-unactive.svg'
import IconCharacterActive from '@/assets/icon/user-character-active.svg'
import IconCharacterUnactive from '@/assets/icon/user-character-unactive.svg'
import IconCreationActive from '@/assets/icon/creation-active.svg'
import IconCreationUnactive from '@/assets/icon/creation-unactive.svg'
import { useRoute } from 'vue-router'
import { watch, computed } from 'vue'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface/report'
import { useUserAvatarStore } from '@/store/user-character'
import { useUserStore } from '@/store/user'
import { useThemeStore } from '@/store/theme'

const route = useRoute()
const avatarStore = useUserAvatarStore()
const userStore = useUserStore()
const themeStore = useThemeStore()
const AppName = import.meta.env.VITE_APP_NAME || 'Playshot'

// 计算是否为首页激活状态
const isHomeActive = computed(() => route.path === '/' || route.path === '/stories')
const isProfileActive = computed(() => route.path === '/user/profile')

const showRedDot = computed(() => {
  // Show red dot if user hasn't visited and has no avatars
  if (!avatarStore.hasVisited && avatarStore.avatars.length === 0) {
    return true
  }
  // Show red dot if there are unviewed generated avatars
  if (Object.keys(avatarStore.unviewedFinishedAvatars).length > 0) {
    return true
  }
  return false
})

const handleMenuClick = (path: string) => {
  reportEvent(ReportEvent.ClickMenu, {
    menu: path
  })
}
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.menu-wrapper {
  // margin: 8px 16px;
  padding-bottom: calc(8px + env(safe-area-inset-bottom));
  // padding: 8px;
  background: var(--mobile-menu-bg);
  display: flex;
  justify-content: space-around;
  align-items: center;
  transition: background 0.3s ease;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 0;
  flex: 1;
  color: var(--text-tertiary);
  text-decoration: none;
  transition: all 0.3s;

  .icon-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
  }
}

.red-dot {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background-color: #ff4d4f;
  border-radius: 50%;
}
</style>
