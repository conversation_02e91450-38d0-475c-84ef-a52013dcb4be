<template>
  <Teleport to="body">
    <div v-if="visible" class="auth-drawer" :class="{ visible }" @click.self="handleClose">
      <div class="drawer-content" :class="{ visible }">
        <div class="close-button" @click="handleClose">
          <icon-close />
        </div>

        <!-- Login View -->
        <template v-if="type === 'login'">
          <div class="logo-wrapper">
            <img :src="logoUrl" alt="Playshot" />
            <h2>{{ title || 'Sign up to continue !' }}</h2>
          </div>

          <div class="form-wrapper">
            <div class="social-buttons">
              <div class="social-row">
                <div class="social-item" v-if="!isAndroidWebView()">
                  <button
                    class="social-button google"
                    @click="handleSocialLogin('google')"
                    :disabled="loading"
                  >
                    <GoogleIcon />
                  </button>
                  <span class="social-name">Google</span>
                </div>
                <div class="social-item">
                  <button
                    class="social-button discord"
                    @click="handleSocialLogin('discord')"
                    :disabled="loading"
                  >
                    <DiscordIcon />
                  </button>
                  <span class="social-name">Discord</span>
                </div>
                <div class="social-item">
                  <button
                    class="social-button facebook"
                    @click="handleSocialLogin('facebook')"
                    :class="{ disabled: false }"
                  >
                    <FacebookIcon />
                  </button>
                  <span class="social-name">Facebook</span>
                </div>
              </div>
            </div>

            <div class="divider">
              <span>or</span>
            </div>

            <div class="input-group">
              <input
                v-model="loginForm.email"
                type="email"
                placeholder="Enter your email"
                :class="{ error: loginErrors.email }"
                @blur="validateLoginEmail"
              />
              <span v-if="loginErrors.email" class="error-text">{{ loginErrors.email }}</span>
            </div>

            <div class="input-group verification-group">
              <input
                v-model="loginForm.verificationCode"
                type="text"
                placeholder="Enter code"
                :class="{ error: loginErrors.verificationCode }"
              />
              <button
                class="send-code-button"
                :class="{ 'is-inactive': !loginForm.email }"
                @click="handleSendCode"
                :disabled="loading || countdown > 0"
              >
                {{ countdown > 0 ? `Resend (${countdown}s)` : 'Send' }}
              </button>
              <span v-if="loginErrors.verificationCode" class="error-text">{{
                loginErrors.verificationCode
              }}</span>
            </div>

            <button
              class="continue-button"
              :class="{ 'is-inactive': !loginForm.email || !loginForm.verificationCode }"
              @click="handleLogin"
              :disabled="loading"
            >
              <span v-if="!loading">Continue</span>
              <a-spin v-else />
            </button>

            <!-- <div class="switch-link">
              Don't have an account?
              <span class="switch-text" @click="handleChangeType('register')">Sign Up</span>
            </div> -->
          </div>
        </template>

        <!-- Register View -->
        <template v-else>
          <div class="logo-wrapper">
            <img src="https://cdn.magiclight.ai/assets/playshot/logo-v2.png" alt="Playshot" />
            <h2>Create Account</h2>
          </div>

          <div class="form-wrapper">
            <div class="input-group">
              <label>Email Address</label>
              <input
                v-model="registerForm.email"
                type="email"
                placeholder="Enter"
                :class="{ error: registerErrors.email }"
              />
              <span v-if="registerErrors.email" class="error-text">{{ registerErrors.email }}</span>
            </div>

            <div class="input-group">
              <label>Name</label>
              <input
                v-model="registerForm.name"
                type="text"
                placeholder="Enter"
                :class="{ error: registerErrors.name }"
              />
              <span v-if="registerErrors.name" class="error-text">{{ registerErrors.name }}</span>
            </div>

            <div class="input-group">
              <label>Password</label>
              <input
                v-model="registerForm.password"
                type="password"
                placeholder="Enter"
                :class="{ error: registerErrors.password }"
              />
              <span v-if="registerErrors.password" class="error-text">{{
                registerErrors.password
              }}</span>
            </div>

            <div class="input-group">
              <label>Confirm Password</label>
              <input
                v-model="registerForm.confirmPassword"
                type="password"
                placeholder="Enter"
                :class="{ error: registerErrors.confirmPassword }"
              />
              <span v-if="registerErrors.confirmPassword" class="error-text">{{
                registerErrors.confirmPassword
              }}</span>
            </div>

            <button class="continue-button" :disabled="loading" @click="handleRegister">
              <span v-if="!loading">❤️ Continue</span>
              <a-spin v-else />
            </button>

            <div class="switch-link">
              Already have an account?
              <span class="switch-text" @click="type = 'login'">Sign in</span>
            </div>
          </div>
        </template>

        <div class="terms-text">
          By signing in you agree with our<br />
          <a href="/terms" @click.prevent="router.push('/terms')">Terms of Service</a>
          ,
          <a href="/privacy" @click.prevent="router.push('/privacy')">Privacy Policy</a>,
          <a href="/complaints" @click.prevent="router.push('/complaints')">Complaints Policy</a>,
          <a href="/content-removal" @click.prevent="router.push('/content-removal')"
            >Content Removal Policy</a
          >,
          <a href="/record-keeping" @click.prevent="router.push('/record-keeping')"
            >18 U.S.C. 2257 Compliance</a
          >, <a href="/about" @click.prevent="router.push('/about')">About Us</a>,
          <a href="/refund" @click.prevent="router.push('/refund')">Refund and Returns Policy</a>.
        </div>
      </div>
      <browser-guide-modal v-model:visible="showBrowserGuideModal" />
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@/mobile/components/Message'
import { useUserStore } from '@/store/user'
import { useStoryStore } from '@/store/story'
import { IconEmail, IconClose } from '@arco-design/web-vue/es/icon'
import GoogleIcon from '@/assets/icon/google.svg'
import DiscordIcon from '@/assets/icon/discord.svg'
import FacebookIcon from '@/assets/icon/facebook.svg'
import { getSocialLoginUrl } from '@/api/social-login'
import type { SocialLoginType } from '@/api/social-login'
import { reportEvent, isInAppBrowser } from '@/utils'
import { ReportEvent } from '@/interface'
import BrowserGuideModal from '@/mobile/components/BrowserGuideModal.vue'
import { sendVerificationCode } from '@/api/user'
import { isAndroidWebView } from '@/utils/isAndroidWebView'
import { getDeploymentConfig } from '@/config/deployment'

const logoUrl = computed(
  () => import.meta.env.VITE_LOGO_URL || 'https://cdn.magiclight.ai/assets/playshot/logo-v2.png'
)

const props = defineProps<{
  visible: boolean
  isInLandingPage: boolean
  title?: string
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'login'): void
  (e: 'register'): void
}>()

const router = useRouter()
const userStore = useUserStore()
const storyStore = useStoryStore()
const loading = ref(false)
const type = ref<'login' | 'register'>('login')
const showBrowserGuideModal = ref(false)
const countdown = ref(0)

// Login form
const loginForm = reactive({
  email: '',
  verificationCode: ''
})

const loginErrors = reactive({
  email: '',
  verificationCode: ''
})

// Register form
const registerForm = reactive({
  email: '',
  name: '',
  password: '',
  confirmPassword: ''
})

const registerErrors = reactive({
  email: '',
  name: '',
  password: '',
  confirmPassword: ''
})

const handleClose = () => {
  emit('update:visible', false)
}

const handleFacebookLogin = () => {
  reportEvent(ReportEvent.ClickLoginPageSignInWithFacebook, {
    userId: userStore.userInfo?.uuid
  })
  Message.info('Facebook login is coming soon!')
}

const handleSendCode = async () => {
  if (!validateLoginEmail()) {
    Message.error(loginErrors.email || 'Invalid email')
    return
  }
  try {
    loading.value = true
    const response = await sendVerificationCode({
      email: loginForm.email,
      code_type: 'login'
    })
    if (!response.data.isOk) {
      Message.error(response.data.message)
      return
    }

    // Start countdown
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)

    Message.success('Verification code sent')
  } catch (error: any) {
    console.error(error)
    Message.error(error.message || 'Failed to send verification code')
  } finally {
    loading.value = false
  }
}

const validateLoginEmail = () => {
  if (!loginForm.email) {
    loginErrors.email = 'Email is required'
    return false
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(loginForm.email)) {
    loginErrors.email = 'Invalid email format'
    return false
  }

  loginErrors.email = ''
  return true
}

const validateLoginForm = (): boolean => {
  let isValid = true
  loginErrors.verificationCode = ''

  // 验证邮箱
  if (!validateLoginEmail()) {
    isValid = false
  }

  if (!loginForm.verificationCode) {
    loginErrors.verificationCode = 'Verification code is required'
    isValid = false
  }

  return isValid
}

const validateRegisterForm = (): boolean => {
  reportEvent(ReportEvent.AuthDrawerClickRegisterPageSignUp, {
    userId: userStore.userInfo?.uuid
  })
  let isValid = true
  registerErrors.email = ''
  registerErrors.name = ''
  registerErrors.password = ''
  registerErrors.confirmPassword = ''

  if (!registerForm.email) {
    registerErrors.email = 'Email is required'
    isValid = false
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(registerForm.email)) {
    registerErrors.email = 'Invalid email format'
    isValid = false
  }

  if (!registerForm.name) {
    registerErrors.name = 'Name is required'
    isValid = false
  } else if (registerForm.name.length < 2) {
    registerErrors.name = 'Name must be at least 2 characters'
    isValid = false
  }

  if (!registerForm.password) {
    registerErrors.password = 'Password is required'
    isValid = false
  } else if (registerForm.password.length < 6) {
    registerErrors.password = 'Password must be at least 6 characters'
    isValid = false
  }

  if (!registerForm.confirmPassword) {
    registerErrors.confirmPassword = 'Please confirm your password'
    isValid = false
  } else if (registerForm.password !== registerForm.confirmPassword) {
    registerErrors.confirmPassword = 'Passwords do not match'
    isValid = false
  }

  return isValid
}

const handleLogin = async () => {
  if (!validateLoginForm()) return

  try {
    loading.value = true
    const isLogin = await userStore.loginWithCode({
      email: loginForm.email,
      code: loginForm.verificationCode,
      code_type: 'login',
      gender: 'male',
      user_id: userStore.userInfo?.uuid
    })
    if (!isLogin) {
      return false
    }
    Message.success('Verification code login successful')
    reportEvent(ReportEvent.LoginSuccess, {
      userId: userStore.userInfo?.uuid,
      type: 'email'
    })
    emit('login')
    return true
  } catch (error: any) {
    console.error(error)
    Message.error(error.message || 'Login failed')
  } finally {
    loading.value = false
  }
}

const handleChangeType = (actionType: 'login' | 'register') => {
  reportEvent(ReportEvent.AuthDrawerChangeType, {
    userId: userStore.userInfo?.uuid,
    actionType
  })
  type.value = actionType
}

const handleSocialLogin = async (type: SocialLoginType) => {
  if (type === 'google' && isInAppBrowser()) {
    showBrowserGuideModal.value = true
    return false
  }

  reportEvent(ReportEvent.AuthDrawerClickLoginPageSignInWithGoogleOrDiscord, {
    userId: userStore.userInfo?.uuid,
    loginType: type
  })
  loading.value = true
  try {
    // 检测是否在iframe环境中
    const isInIframe = window.self !== window.top

    if (isInIframe) {
      // 在iframe中，使用主应用的域名进行重定向
      const config = getDeploymentConfig()
      const social_redirect_url = `${config.mainAppUrl}/user/social-callback?login_type=${type}`
      const app_redirect_url = props.isInLandingPage
        ? `${config.mainAppUrl}/chat/${storyStore.currentStory?.id}/${storyStore.currentActor?.id}`
        : window.location.href

      // 通知主应用进行社交登录
      const { sendMessageToParent } = await import('@/utils/iframeNavigation')
      const success = sendMessageToParent('SOCIAL_LOGIN_REQUEST', {
        type,
        social_redirect_url,
        app_redirect_url
      })

      if (success) {
        console.log('已通知主应用进行社交登录')
        return true
      } else {
        throw new Error('无法通知主应用进行社交登录')
      }
    } else {
      // 不在iframe中，使用原有逻辑
      const origin = window.location.origin
      const social_redirect_url = `${origin}/user/social-callback?login_type=${type}`
      const app_redirect_url = props.isInLandingPage
        ? `${origin}/chat/${storyStore.currentStory?.id}/${storyStore.currentActor?.id}`
        : window.location.href
      sessionStorage.setItem('login_redirect', app_redirect_url)
      const response = await getSocialLoginUrl(type, social_redirect_url)
      if (!response.data.isOk) {
        return false
      }
      reportEvent(ReportEvent.LoginSuccess, {
        userId: userStore.userInfo?.uuid,
        type: type
      })
      window.location.href = response.data.data.url
    }
  } catch (error: any) {
    console.error('Social login error:', error)
    Message.error(error.message || 'Failed to initiate social login')
  } finally {
    loading.value = false
  }
}

const handleRegister = async () => {
  reportEvent(ReportEvent.AuthDrawerClickRegisterPageSignUp, {
    userId: userStore.userInfo?.uuid
  })
  if (!validateRegisterForm()) return

  loading.value = true
  try {
    const isRegister = await userStore.register(registerForm)
    if (!isRegister) {
      loading.value = false
      return false
    }
    Message.success('Registration successful')
    emit('register')
    type.value = 'login'
  } catch (error: any) {
    console.error(error)
    Message.error(error.message || 'Registration failed')
  } finally {
    loading.value = false
  }
}

watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      reportEvent(ReportEvent.AuthDrawerView, {
        userId: userStore.userInfo?.uuid,
        storyId: storyStore.currentStory?.id,
        actorId: storyStore.currentActor
      })
    }
  }
)
</script>

<style lang="less" scoped>
.auth-drawer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.visible {
    opacity: 1;
    visibility: visible;
  }
}

.drawer-content {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #1f0038;
  border-radius: 24px 24px 0 0;
  padding: 24px;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 90vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;

  &.visible {
    transform: translateY(0);
  }
}

.close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.6);
  transition: color 0.2s;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;

  &:hover {
    color: white;
  }

  :deep(.arco-icon) {
    font-size: 18px;
  }
}

// 复用现有的样式
.logo-wrapper {
  margin-bottom: 48px;
  text-align: center;

  img {
    height: 28px;
    margin-bottom: 24px;
  }

  h2 {
    color: #fff;
    font-size: 18px;
    font-weight: 700;
    margin: 0;
    line-height: 1.5;
    background: linear-gradient(90deg, #ca93f2 0%, #daff96 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 8px rgba(202, 147, 242, 0.3);
  }
}

.form-wrapper {
  max-width: 320px;
  margin: 0 auto;
  width: 100%;
}

// 复用 login.vue 和 register.vue 中的其他样式...
.social-buttons {
  margin-bottom: 24px;

  .social-row {
    display: flex;
    justify-content: center;
    gap: 24px;
  }

  .social-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .social-name {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
      font-weight: 500;
    }
  }

  .social-button {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transition: all 0.3s;
    border: none;
    padding: 0;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: 0.5s;
    }

    &:hover::before {
      left: 100%;
    }

    &:hover {
      transform: translateY(-2px);
    }

    &:active {
      transform: translateY(1px);
    }

    &.google {
      background: #fff;
      box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
    }

    &.discord {
      background: #6563ff;
      box-shadow: 0 2px 8px rgba(88, 101, 242, 0.2);
    }

    &.facebook {
      background: #0866ff;
      box-shadow: 0 2px 8px rgba(24, 119, 242, 0.2);
    }

    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: auto;
    }

    :deep(svg) {
      width: 24px;
      height: 24px;
    }
  }
}

.divider {
  display: flex;
  align-items: center;
  margin: 24px 0;

  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
  }

  span {
    padding: 0 16px;
    color: rgba(255, 255, 255, 0.5);
    font-size: 14px;
  }
}
.email-button {
  width: 100%;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  border-radius: 24px;
  color: white;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
  background: #ca93f2;
  color: #241d49;
  :deep(.arco-icon) {
    font-size: 20px;
  }

  &:hover {
    background: rgba(255, 255, 255, 0.15);
  }

  &:active {
    transform: scale(0.98);
  }
}
.input-group {
  margin-bottom: 16px;

  label {
    display: block;
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
    margin-bottom: 8px;
  }

  input {
    width: 100%;
    height: 48px;
    background: #524567;
    border: 1px solid #524567;
    border-radius: 24px;
    padding: 0 20px;
    color: #fff;
    font-size: 15px;
    font-weight: 600;

    &::placeholder {
      color: #fff;
      font-size: 15px;
      font-weight: 600;
      line-height: normal;
      opacity: 0.3;
    }

    &:focus {
      outline: none;
      border-color: #524567;
    }

    &.error {
      border-color: #ff4d4d;
    }
  }

  .error-text {
    color: #ff4d4d;
    font-size: 12px;
    margin-top: 4px;
  }
}

.continue-button {
  width: 100%;
  height: 48px;
  border: none;
  border-radius: 24px;
  background: #ca93f2;
  color: #241d49;
  font-size: 15px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s;

  &.is-inactive {
    opacity: 0.5;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

.switch-link {
  margin-top: 24px;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;

  .switch-text {
    color: #ca93f2;
    cursor: pointer;
    margin-left: 4px;

    &:hover {
      text-decoration: underline;
    }
  }
}

.terms-text {
  margin-top: 24px;
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  line-height: 1.5;

  a {
    color: #ca93f2;
    text-decoration: none;
    transition: opacity 0.3s;

    &:hover {
      text-decoration: underline;
    }

    &.disabled {
      opacity: 0.5;
      pointer-events: none;
      cursor: not-allowed;
    }
  }
}

.verification-group {
  position: relative;

  .send-code-button {
    position: absolute;
    right: 8px;
    top: 24px; /* 固定位置，不再使用相对定位 */
    transform: translateY(-50%);
    background: #ca93f2;
    border: none;
    border-radius: 16px;
    padding: 6px 12px;
    color: #241d49;
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s;
    z-index: 2; /* 确保按钮在最上层 */

    &.is-inactive {
      opacity: 0.5;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }

  /* 确保错误文本不会影响按钮位置 */
  .error-text {
    margin-top: 4px;
    display: block;
    position: relative;
    z-index: 1;
  }
}

.login-options {
  text-align: right;
  margin-top: 8px;

  .switch-mode {
    color: #ca93f2;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
