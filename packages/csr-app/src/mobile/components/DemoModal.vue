<template>
  <!-- 资源加载器 -->
  <ResourceLoader
    :visible="showLoader"
    :progress="loadingProgress.percentage"
    :loading-text="loadingProgress.currentResource"
    :logo-url="logoUrl"
  />

  <Teleport to="body">
    <Transition name="fade">
      <div v-if="visible" class="demo-modal-overlay" @click.self="handleClose">
        <div class="demo-modal">
          <!-- 上半部分：试玩区域 -->
          <div class="demo-section">
            <!-- 角色图片区域 -->
            <div class="image-container">
              <!-- 角色滑动容器 -->
              <div
                class="character-slider"
                :style="{
                  transform: `translateX(${finalTransform}%)`,
                  transition: isDragging ? 'none' : 'transform 0.5s ease-in-out'
                }"
                @transitionend="handleTransitionEnd"
                @touchstart="handleTouchStart"
                @touchmove="handleTouchMove"
                @touchend="handleTouchEnd"
                @mousedown="handleMouseDown"
                @mousemove="handleMouseMove"
                @mouseup="handleMouseUp"
                @mouseleave="handleMouseUp"
              >
                <!-- 为了实现无限循环，在前面添加最后一个角色的副本 -->
                <div class="character-slide">
                  <div class="media-container">
                    <div class="media-layer background-layer">
                      <img
                        :src="
                          demoCharacters[demoCharacters.length - 1]?.mediaResources.find(
                            (m) =>
                              m.scene === 'living-room' &&
                              m.outfit === 'sailor' &&
                              m.type === 'image'
                          )?.src
                        "
                        alt="Demo Character"
                        class="character-image"
                      />
                    </div>
                  </div>
                </div>

                <!-- 原始角色列表 -->
                <div
                  v-for="(character, charIndex) in demoCharacters"
                  :key="character.id"
                  class="character-slide"
                >
                  <!-- 图片容器，支持溶解过渡效果（仅用于demo按钮切换） -->
                  <div class="media-container">
                    <!-- 背景层 - 当前显示的媒体 -->
                    <div class="media-layer background-layer">
                      <video
                        v-if="charIndex === currentCharacterIndex && currentMedia?.type === 'video'"
                        :src="currentMedia.src"
                        class="character-image"
                        autoplay
                        loop
                        muted
                        playsinline
                        @loadeddata="handleMediaLoaded"
                        @canplay="handleMediaLoaded"
                      />
                      <img
                        v-else-if="charIndex === currentCharacterIndex && currentMedia"
                        :src="currentMedia.src"
                        alt="Demo Character"
                        class="character-image"
                        @load="handleMediaLoaded"
                        @error="handleMediaError"
                      />
                      <!-- 非当前角色显示默认图片 -->
                      <img
                        v-else
                        :src="
                          character.mediaResources.find(
                            (m) =>
                              m.scene === 'living-room' &&
                              m.outfit === 'sailor' &&
                              m.type === 'image'
                          )?.src
                        "
                        alt="Demo Character"
                        class="character-image"
                      />
                    </div>

                    <!-- 前景层 - 过渡中的媒体（仅用于demo按钮切换） -->
                    <div
                      v-if="charIndex === currentCharacterIndex"
                      class="media-layer foreground-layer"
                      :class="{
                        dissolving: isTransitioning && isNewMediaReady,
                        'pre-dissolve': isTransitioning && !isNewMediaReady
                      }"
                      :style="{ visibility: transitionMedia ? 'visible' : 'hidden' }"
                    >
                      <video
                        v-if="transitionMedia?.type === 'video'"
                        :src="transitionMedia.src"
                        class="character-image"
                        autoplay
                        loop
                        muted
                        playsinline
                      />
                      <img
                        v-else-if="transitionMedia"
                        :src="transitionMedia.src"
                        alt="Demo Character"
                        class="character-image"
                      />
                    </div>
                  </div>
                </div>

                <!-- 为了实现无限循环，在后面添加第一个角色的副本 -->
                <div class="character-slide">
                  <div class="media-container">
                    <div class="media-layer background-layer">
                      <img
                        :src="
                          demoCharacters[0]?.mediaResources.find(
                            (m) =>
                              m.scene === 'living-room' &&
                              m.outfit === 'sailor' &&
                              m.type === 'image'
                          )?.src
                        "
                        alt="Demo Character"
                        class="character-image"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- Logo -->
              <img :src="logoUrl" alt="Reelplay" class="logo" />

              <!-- 左右切换按钮 -->
              <button class="nav-button nav-left" @click="previousCharacter">
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                  <path
                    d="M20 24L12 16L20 8"
                    stroke="#FFFFFF"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>

              <button class="nav-button nav-right" @click="nextCharacter">
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                  <path
                    d="M12 24L20 16L12 8"
                    stroke="#FFFFFF"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>

              <!-- 底部指示器 -->
              <div class="image-indicators">
                <button
                  v-for="(_, index) in demoCharacters"
                  :key="index"
                  :class="['indicator', { active: currentCharacterIndex === index }]"
                  @click="selectCharacter(index)"
                ></button>
              </div>
            </div>
          </div>

          <!-- Demo按钮区域（包含问题文字） -->
          <div class="demo-section-with-question">
            <!-- 问题文字 -->
            <div class="question-section">
              <h3 class="question-text">How do you want to treat her ?</h3>
            </div>

            <!-- Demo按钮 -->
            <div class="demo-buttons">
              <button
                class="demo-button"
                :class="{ disabled: isTransitioning }"
                :disabled="isTransitioning"
                @click="handleDemoClick('remove')"
              >
                <div class="demo-icon">
                  <template v-if="isClothesOff">
                    <TShirtIcon></TShirtIcon>
                  </template>
                  <template v-else>
                    <ClothesIcon></ClothesIcon>
                  </template>
                </div>
                <span>{{ removeButtonText }}</span>
              </button>

              <button
                class="demo-button"
                :class="{ disabled: isTransitioning }"
                :disabled="isTransitioning"
                @click="handleDemoClick('beach')"
              >
                <div class="demo-icon">
                  <template v-if="currentScene === 'living-room'">
                    <BeachIcon></BeachIcon>
                  </template>
                  <template v-else>
                    <BedIcon></BedIcon>
                  </template>
                </div>
                <span>{{ beachButtonText }}</span>
              </button>

              <button
                class="demo-button"
                :class="{ disabled: isTransitioning }"
                :disabled="isTransitioning"
                @click="handleDemoClick('shake')"
              >
                <div class="demo-icon">
                  <BreastsIcon></BreastsIcon>
                </div>
                <span>Shake Boobs</span>
              </button>
            </div>
          </div>

          <!-- 下半部分：登录区域 -->
          <div class="login-section">
            <!-- 登录按钮 -->
            <button class="login-button" @click="handleLogin">Log in</button>

            <!-- 稍后注册链接 -->
            <button class="later-button" @click="handleSignUpLater">sign up later</button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import ResourceLoader from '@/shared/components/ResourceLoader.vue'
import {
  preloadWithMinimumTime,
  type PreloadProgress,
  type Character
} from '@/shared/utils/resourcePreloader'
import ClothesIcon from '@/assets/icon/clothes.svg'
import BeachIcon from '@/assets/icon/beach.svg'
import BreastsIcon from '@/assets/icon/breasts.svg'
import TShirtIcon from '@/assets/icon/tshirt.svg'
import BedIcon from '@/assets/icon/bed.svg'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { useUserStore } from '@/store/user'

const props = defineProps<{
  visible: boolean
  title?: string
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'login'): void
  (e: 'signup-later'): void
}>()

const userStore = useUserStore()

const logoUrl = computed(
  () => import.meta.env.VITE_LOGO_URL || 'https://cdn.magiclight.ai/assets/playshot/logo-v2.png'
)

// Demo功能的角色数据
const demoCharacters = ref([
  {
    id: 1,
    name: 'Character 2',
    mediaResources: [
      // Beach场景 - 水手服
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/2-suit-beach.png',
        scene: 'beach',
        outfit: 'sailor'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/2-suit-beach.mp4',
        scene: 'beach',
        outfit: 'sailor'
      },
      // Beach场景 - 比基尼
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/2-bikini-beach.png',
        scene: 'beach',
        outfit: 'bikini'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/2-bikini-beach.mp4',
        scene: 'beach',
        outfit: 'bikini'
      },
      // 客厅场景 - 水手服
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/2-suit-parlor.png',
        scene: 'living-room',
        outfit: 'sailor'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/2-suit-parlor.mp4',
        scene: 'living-room',
        outfit: 'sailor'
      },
      // 客厅场景 - 比基尼
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/2-bikini-parlor.png',
        scene: 'living-room',
        outfit: 'bikini'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/2-bikini-parlor.mp4',
        scene: 'living-room',
        outfit: 'bikini'
      }
    ]
  },
  {
    id: 2,
    name: 'Character 1',
    mediaResources: [
      // Beach场景 - 水手服
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/1-sailorsuit-beach.png',
        scene: 'beach',
        outfit: 'sailor'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/1-sailorsuit-beach.mp4',
        scene: 'beach',
        outfit: 'sailor'
      },
      // Beach场景 - 比基尼
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/1-bikini-beach.png',
        scene: 'beach',
        outfit: 'bikini'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/1-bikini-beach.mp4',
        scene: 'beach',
        outfit: 'bikini'
      },
      // 客厅场景 - 水手服
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/1-suit-parlor.png',
        scene: 'living-room',
        outfit: 'sailor'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/1-suit-parlor.mp4',
        scene: 'living-room',
        outfit: 'sailor'
      },
      // 客厅场景 - 比基尼
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/1-bikini-parlor.png',
        scene: 'living-room',
        outfit: 'bikini'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/1-bikini-parlor.mp4',
        scene: 'living-room',
        outfit: 'bikini'
      }
    ]
  },

  {
    id: 3,
    name: 'Character 3',
    mediaResources: [
      // Beach场景 - 水手服
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/3-suit-beach.png',
        scene: 'beach',
        outfit: 'sailor'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/3-suit-beach.mp4',
        scene: 'beach',
        outfit: 'sailor'
      },
      // Beach场景 - 比基尼
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/3-bikini-beach.png',
        scene: 'beach',
        outfit: 'bikini'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/3-bikini-beach.mp4',
        scene: 'beach',
        outfit: 'bikini'
      },
      // 客厅场景 - 水手服
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/3-suit-parlor.png',
        scene: 'living-room',
        outfit: 'sailor'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/3-suit-parlor.mp4',
        scene: 'living-room',
        outfit: 'sailor'
      },
      // 客厅场景 - 比基尼
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/3-bikini-parlor.png',
        scene: 'living-room',
        outfit: 'bikini'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/3-bikini-parlor.mp4',
        scene: 'living-room',
        outfit: 'bikini'
      }
    ]
  }
])

// Demo状态管理
const currentCharacterIndex = ref(0) // 当前角色索引
const currentScene = ref<'living-room' | 'beach'>('living-room') // 场景状态
const currentOutfit = ref<'sailor' | 'bikini'>('sailor') // 服装状态
const currentMediaType = ref<'image' | 'video'>('image') // 媒体类型
const isClothesOff = ref(false) // 脱衣服状态
const isTransitioning = ref(false) // 过渡状态

// 过渡状态管理
const transitionMedia = ref<any>(null)
const isNewMediaReady = ref(false)

// 资源预加载状态
const showLoader = ref(false)
const loadingProgress = ref<PreloadProgress>({
  loaded: 0,
  total: 0,
  percentage: 0,
  currentResource: ''
})

// 标志：用户是否已经点击了登录或注册按钮
const userHasInteracted = ref(false)

// 拖拽状态管理
const isDragging = ref(false)
const dragOffset = ref(0)
const startX = ref(0)
const startY = ref(0)
const currentX = ref(0)
const currentY = ref(0)
const isMouseDrag = ref(false)

// 计算最终的transform值
const finalTransform = computed(() => {
  // 现在有5个slide：[副本3, 角色1, 角色2, 角色3, 副本1]
  // 每个slide占20%宽度
  // 角色1在索引1位置，角色2在索引2位置，角色3在索引3位置
  const slideWidth = 20 // 每个slide的宽度百分比

  // 基础位置：当前角色的位置（需要偏移1个位置，因为前面有一个副本）
  const baseTransform = -(currentCharacterIndex.value + 1) * slideWidth

  // 如果正在拖拽，添加拖拽偏移
  if (isDragging.value) {
    return baseTransform + dragOffset.value
  }

  return baseTransform
})

// 计算当前显示的媒体
const currentMedia = computed(() => {
  const character = demoCharacters.value[currentCharacterIndex.value]
  if (!character) return null

  const outfit = currentOutfit.value
  const scene = currentScene.value
  const type = currentMediaType.value

  return (
    character.mediaResources.find(
      (media: any) => media.scene === scene && media.outfit === outfit && media.type === type
    ) || character.mediaResources[0]
  )
})

// 动态按钮文案
const removeButtonText = computed(() => {
  return isClothesOff.value ? 'Put On Clothes' : 'Take Off Clothes'
})

const beachButtonText = computed(() => {
  return currentScene.value === 'living-room' ? 'Go To The Beach' : 'Go To The R soom'
})

// 溶解过渡函数
const triggerDissolveTransition = (oldMedia: any) => {
  if (!oldMedia || isTransitioning.value) return

  // 设置过渡媒体为旧媒体（即将被溶解的）
  transitionMedia.value = oldMedia

  // 开始过渡
  isTransitioning.value = true
  isNewMediaReady.value = false

  // 延迟一帧确保DOM更新
  nextTick(() => {
    // 预加载新媒体以减少闪烁
    const newMedia = currentMedia.value
    if (newMedia?.type === 'image') {
      const img = new Image()
      img.onload = () => {
        console.log('新图片预加载完成')
        // 图片加载完成后延迟确保渲染完成
        setTimeout(() => {
          isNewMediaReady.value = true
        }, 100)
      }
      img.onerror = () => {
        console.error('图片预加载失败')
        isNewMediaReady.value = true // 即使失败也继续过渡
      }
      img.src = newMedia.src
    } else if (newMedia?.type === 'video') {
      // 视频类型，等待视频元素加载事件
      // 如果300ms内没有收到加载事件，强制开始过渡
      const fallbackTimer = setTimeout(() => {
        console.log('视频加载超时，强制开始过渡')
        isNewMediaReady.value = true
      }, 300)

      // 清除定时器的函数
      const clearFallback = () => {
        clearTimeout(fallbackTimer)
      }

      // 监听一次性事件
      const checkVideoReady = () => {
        clearFallback()
        setTimeout(() => {
          isNewMediaReady.value = true
        }, 50)
      }

      // 存储清理函数，以便在组件卸载时清理
      window.addEventListener('video-ready', checkVideoReady, { once: true })
    }

    // 2秒后强制结束过渡（防止卡住）
    setTimeout(() => {
      isTransitioning.value = false
      transitionMedia.value = null
      // 不重置 isNewMediaReady，保持背景层显示
    }, 2000)
  })
}

// 媒体加载处理
const handleMediaLoaded = () => {
  console.log('媒体加载完成')
  if (isTransitioning.value && !isNewMediaReady.value) {
    // 只有在过渡期间且新媒体还未准备好时才设置为准备好
    setTimeout(() => {
      isNewMediaReady.value = true
    }, 50)
  } else if (!isTransitioning.value) {
    // 非过渡期间，直接设置为准备好
    isNewMediaReady.value = true
  }
}

const handleMediaError = (error: Event) => {
  console.error('媒体加载失败:', error)
  if (isTransitioning.value) {
    // 即使加载失败，也要继续过渡避免卡住
    isNewMediaReady.value = true
  }
}

// Demo按钮点击处理
const handleDemoClick = (action: string) => {
  // 防止在过渡期间重复点击
  if (isTransitioning.value) {
    console.log('过渡中，忽略点击')
    return
  }

  // 上报Demo按钮点击事件
  reportEvent(ReportEvent.DemoModalButtonClick, {
    userId: userStore.userInfo?.uuid,
    action: action,
    currentCharacter: currentCharacterIndex.value,
    currentScene: currentScene.value,
    currentOutfit: currentOutfit.value,
    currentMediaType: currentMediaType.value
  })

  // 获取当前媒体作为过渡的起点
  const oldMedia = currentMedia.value

  // 预先计算新状态，检查是否真的需要切换
  let newScene = currentScene.value
  let newOutfit = currentOutfit.value
  let newMediaType = currentMediaType.value
  let newIsClothesOff = isClothesOff.value

  switch (action) {
    case 'remove':
      // 切换服装状态
      if (currentOutfit.value === 'sailor') {
        newOutfit = 'bikini'
        newIsClothesOff = true
      } else {
        newOutfit = 'sailor'
        newIsClothesOff = false
      }
      newMediaType = 'image' // 重置为图片
      break

    case 'beach':
      // 切换场景，保持当前服装状态
      newScene = currentScene.value === 'living-room' ? 'beach' : 'living-room'
      newMediaType = 'image' // 重置为图片
      break

    case 'shake':
      // 切换到视频
      newMediaType = 'video'
      break
  }

  // 计算新媒体
  const character = demoCharacters.value[currentCharacterIndex.value]
  const newMedia = character?.mediaResources.find(
    (media: any) =>
      media.scene === newScene && media.outfit === newOutfit && media.type === newMediaType
  )

  // 如果新媒体和当前媒体相同，则不需要过渡
  if (oldMedia && newMedia && oldMedia.src === newMedia.src) {
    console.log('媒体相同，无需过渡')
    return
  }

  // 先触发溶解过渡效果（在状态改变前）
  if (oldMedia) {
    triggerDissolveTransition(oldMedia)
  }

  // 然后应用新状态（这会更新背景层的媒体）
  currentScene.value = newScene
  currentOutfit.value = newOutfit
  currentMediaType.value = newMediaType
  isClothesOff.value = newIsClothesOff

  console.log(
    `Demo clicked: ${action}, scene: ${currentScene.value}, outfit: ${currentOutfit.value}, type: ${currentMediaType.value}`
  )
}

// 事件处理
const handleClose = () => {
  // 标记用户已经交互，防止重新显示加载器
  userHasInteracted.value = true

  // 立即隐藏ResourceLoader
  showLoader.value = false

  // 上报弹窗关闭事件
  reportEvent(ReportEvent.DemoModalClose, {
    userId: userStore.userInfo?.uuid
  })
  emit('update:visible', false)
}

const handleLogin = () => {
  // 标记用户已经交互，防止重新显示加载器
  userHasInteracted.value = true

  // 立即隐藏ResourceLoader
  showLoader.value = false

  // 上报登录按钮点击事件
  reportEvent(ReportEvent.DemoModalLoginClick, {
    userId: userStore.userInfo?.uuid
  })
  emit('login')
  emit('update:visible', false)
}

const handleSignUpLater = () => {
  // 标记用户已经交互，防止重新显示加载器
  userHasInteracted.value = true

  // 立即隐藏ResourceLoader
  showLoader.value = false

  // 上报稍后注册按钮点击事件
  reportEvent(ReportEvent.DemoModalSignupLaterClick, {
    userId: userStore.userInfo?.uuid
  })
  emit('signup-later')
  emit('update:visible', false)
}

// 角色切换功能
const nextCharacter = () => {
  // 角色切换使用滑动效果，不使用溶解效果
  const nextIndex = (currentCharacterIndex.value + 1) % demoCharacters.value.length

  // 上报角色切换事件
  reportEvent(ReportEvent.DemoModalCharacterSwitch, {
    userId: userStore.userInfo?.uuid,
    fromCharacter: currentCharacterIndex.value,
    toCharacter: nextIndex,
    direction: 'next'
  })

  // 切换到下一个角色并重置状态
  currentCharacterIndex.value = nextIndex
  currentScene.value = 'living-room'
  currentOutfit.value = 'sailor'
  currentMediaType.value = 'image'
  isClothesOff.value = false
  // 重置过渡状态
  isTransitioning.value = false
  transitionMedia.value = null
  isNewMediaReady.value = true
  // 重置拖拽状态
  isDragging.value = false
  dragOffset.value = 0
  isMouseDrag.value = false
}

const previousCharacter = () => {
  // 角色切换使用滑动效果，不使用溶解效果
  const prevIndex =
    currentCharacterIndex.value === 0
      ? demoCharacters.value.length - 1
      : currentCharacterIndex.value - 1

  // 上报角色切换事件
  reportEvent(ReportEvent.DemoModalCharacterSwitch, {
    userId: userStore.userInfo?.uuid,
    fromCharacter: currentCharacterIndex.value,
    toCharacter: prevIndex,
    direction: 'previous'
  })

  // 切换到上一个角色并重置状态
  currentCharacterIndex.value = prevIndex
  currentScene.value = 'living-room'
  currentOutfit.value = 'sailor'
  currentMediaType.value = 'image'
  isClothesOff.value = false
  // 重置过渡状态
  isTransitioning.value = false
  transitionMedia.value = null
  isNewMediaReady.value = true
  // 重置拖拽状态
  isDragging.value = false
  dragOffset.value = 0
  isMouseDrag.value = false
}

// 无限循环的角色切换（用于拖拽）
const nextCharacterInfinite = () => {
  const nextIndex = (currentCharacterIndex.value + 1) % demoCharacters.value.length
  currentCharacterIndex.value = nextIndex
  currentScene.value = 'living-room'
  currentOutfit.value = 'sailor'
  currentMediaType.value = 'image'
  isClothesOff.value = false
  isTransitioning.value = false
  transitionMedia.value = null
  isNewMediaReady.value = true

  // 延迟检查是否需要边界重置
  setTimeout(checkAndResetBoundary, 500)
}

const previousCharacterInfinite = () => {
  const prevIndex =
    currentCharacterIndex.value === 0
      ? demoCharacters.value.length - 1
      : currentCharacterIndex.value - 1
  currentCharacterIndex.value = prevIndex
  currentScene.value = 'living-room'
  currentOutfit.value = 'sailor'
  currentMediaType.value = 'image'
  isClothesOff.value = false
  isTransitioning.value = false
  transitionMedia.value = null
  isNewMediaReady.value = true

  // 延迟检查是否需要边界重置
  setTimeout(checkAndResetBoundary, 500)
}

// 处理过渡结束事件
const handleTransitionEnd = () => {
  if (isDragging.value) return // 如果正在拖拽，不进行重置

  const currentTransform = finalTransform.value

  // 检查是否在副本位置，如果是则无缝重置到真实位置
  if (currentTransform === 0) {
    // 在第一个副本位置（副本3），重置到真实的角色3位置
    if (currentCharacterIndex.value === 2) {
      // 角色3
      // 临时禁用过渡，无缝跳转到真实位置
      const slider = document.querySelector('.character-slider') as HTMLElement
      if (slider) {
        slider.style.transition = 'none'
        slider.style.transform = 'translateX(-60%)' // 角色3的真实位置
        // 强制重绘
        slider.offsetHeight
        // 恢复过渡
        setTimeout(() => {
          slider.style.transition = 'transform 0.5s ease-in-out'
        }, 10)
      }
    }
  } else if (currentTransform === -80) {
    // 在最后一个副本位置（副本1），重置到真实的角色1位置
    if (currentCharacterIndex.value === 0) {
      // 角色1
      // 临时禁用过渡，无缝跳转到真实位置
      const slider = document.querySelector('.character-slider') as HTMLElement
      if (slider) {
        slider.style.transition = 'none'
        slider.style.transform = 'translateX(-20%)' // 角色1的真实位置
        // 强制重绘
        slider.offsetHeight
        // 恢复过渡
        setTimeout(() => {
          slider.style.transition = 'transform 0.5s ease-in-out'
        }, 10)
      }
    }
  }
}

// 检查并重置边界位置（保留用于延迟调用）
const checkAndResetBoundary = () => {
  // 现在由handleTransitionEnd处理
}

const selectCharacter = (index: number) => {
  if (index === currentCharacterIndex.value) return

  // 上报角色选择事件
  reportEvent(ReportEvent.DemoModalCharacterSwitch, {
    userId: userStore.userInfo?.uuid,
    fromCharacter: currentCharacterIndex.value,
    toCharacter: index,
    direction: 'select'
  })

  // 角色切换使用滑动效果，不使用溶解效果
  // 切换到指定角色并重置状态
  currentCharacterIndex.value = index
  currentScene.value = 'living-room'
  currentOutfit.value = 'sailor'
  currentMediaType.value = 'image'
  isClothesOff.value = false
  // 重置过渡状态
  isTransitioning.value = false
  transitionMedia.value = null
  isNewMediaReady.value = true
  // 重置拖拽状态
  isDragging.value = false
  dragOffset.value = 0
  isMouseDrag.value = false
}

// 拖拽处理函数
const getClientX = (event: TouchEvent | MouseEvent): number => {
  if ('touches' in event) {
    return event.touches[0]?.clientX || 0
  }
  return event.clientX
}

const getClientY = (event: TouchEvent | MouseEvent): number => {
  if ('touches' in event) {
    return event.touches[0]?.clientY || 0
  }
  return event.clientY
}

// 触摸开始
const handleTouchStart = (event: TouchEvent) => {
  if (isTransitioning.value) return

  isDragging.value = true
  isMouseDrag.value = false
  startX.value = getClientX(event)
  startY.value = getClientY(event)
  currentX.value = startX.value
  currentY.value = startY.value
  dragOffset.value = 0

  // 阻止默认行为，避免页面滚动
  event.preventDefault()
}

// 触摸移动
const handleTouchMove = (event: TouchEvent) => {
  if (!isDragging.value || isTransitioning.value) return

  currentX.value = getClientX(event)
  currentY.value = getClientY(event)

  const deltaX = currentX.value - startX.value
  const deltaY = Math.abs(currentY.value - startY.value)

  // 如果垂直滑动距离大于水平滑动距离，则认为是垂直滚动，不处理
  if (deltaY > Math.abs(deltaX) && Math.abs(deltaX) < 10) {
    return
  }

  // 重新设计拖拽偏移计算
  const containerElement = document.querySelector('.image-container') as HTMLElement
  const containerWidth = containerElement?.offsetWidth || 343 // 动态获取容器宽度，默认343px
  const sliderWidth = containerWidth * 5 // slider总宽度（5个slide）

  // 将像素偏移转换为相对于slider总宽度的百分比
  // 限制拖拽范围，防止过度拖拽
  const maxDrag = containerWidth * 0.6 // 最大拖拽距离
  const clampedDeltaX = Math.max(-maxDrag, Math.min(maxDrag, deltaX))

  // 计算百分比偏移（相对于slider总宽度）
  const dragPercentage = (clampedDeltaX / sliderWidth) * 100

  dragOffset.value = dragPercentage

  // 阻止默认行为
  event.preventDefault()
}

// 触摸结束
const handleTouchEnd = () => {
  if (!isDragging.value) return

  const deltaX = currentX.value - startX.value
  const threshold = 50 // 切换阈值
  const velocityThreshold = 30 // 速度阈值，用于快速滑动

  // 检查是否需要切换角色
  let shouldSwitch = false
  let switchDirection = 0 // -1: 向左切换, 1: 向右切换

  if (Math.abs(deltaX) > threshold) {
    shouldSwitch = true
    switchDirection = deltaX > 0 ? 1 : -1 // 向右拖拽切换到上一个，向左拖拽切换到下一个
  } else if (Math.abs(deltaX) > velocityThreshold) {
    // 快速滑动，即使距离不够也切换
    shouldSwitch = true
    switchDirection = deltaX > 0 ? 1 : -1
  }

  if (shouldSwitch) {
    if (switchDirection > 0) {
      // 向右拖拽，切换到上一个角色
      previousCharacterInfinite()
    } else {
      // 向左拖拽，切换到下一个角色
      nextCharacterInfinite()
    }
  }

  // 重置拖拽状态
  isDragging.value = false
  dragOffset.value = 0
  isMouseDrag.value = false
}

// 鼠标事件处理（用于桌面端测试）
const handleMouseDown = (event: MouseEvent) => {
  if (isTransitioning.value) return

  isDragging.value = true
  isMouseDrag.value = true
  startX.value = getClientX(event)
  startY.value = getClientY(event)
  currentX.value = startX.value
  currentY.value = startY.value
  dragOffset.value = 0

  event.preventDefault()
}

const handleMouseMove = (event: MouseEvent) => {
  if (!isDragging.value || !isMouseDrag.value || isTransitioning.value) return

  currentX.value = getClientX(event)
  currentY.value = getClientY(event)

  const deltaX = currentX.value - startX.value

  // 使用与触摸事件相同的计算逻辑
  const containerElement = document.querySelector('.image-container') as HTMLElement
  const containerWidth = containerElement?.offsetWidth || 343 // 动态获取容器宽度，默认343px
  const sliderWidth = containerWidth * 5 // 5个slide
  const maxDrag = containerWidth * 0.6
  const clampedDeltaX = Math.max(-maxDrag, Math.min(maxDrag, deltaX))
  const dragPercentage = (clampedDeltaX / sliderWidth) * 100

  dragOffset.value = dragPercentage

  event.preventDefault()
}

const handleMouseUp = () => {
  if (!isDragging.value || !isMouseDrag.value) return

  const deltaX = currentX.value - startX.value
  const threshold = 50
  const velocityThreshold = 30

  // 使用与触摸事件相同的简化逻辑
  let shouldSwitch = false
  let switchDirection = 0

  if (Math.abs(deltaX) > threshold) {
    shouldSwitch = true
    switchDirection = deltaX > 0 ? 1 : -1
  } else if (Math.abs(deltaX) > velocityThreshold) {
    shouldSwitch = true
    switchDirection = deltaX > 0 ? 1 : -1
  }

  if (shouldSwitch) {
    if (switchDirection > 0) {
      previousCharacterInfinite()
    } else {
      nextCharacterInfinite()
    }
  }

  isDragging.value = false
  dragOffset.value = 0
  isMouseDrag.value = false
}

// 资源预加载函数
const startResourcePreload = async () => {
  try {
    showLoader.value = true

    const shouldShowLoader = await preloadWithMinimumTime(
      demoCharacters.value as Character[],
      (progress) => {
        loadingProgress.value = progress
      },
      800 // 最小显示时间800ms
    )

    // 如果加载时间过短，不显示加载器
    if (!shouldShowLoader) {
      showLoader.value = false
    } else {
      // 延迟隐藏加载器，确保用户看到100%
      setTimeout(() => {
        showLoader.value = false
      }, 300)
    }
  } catch (error) {
    console.error('资源预加载失败:', error)
    showLoader.value = false
  }
}

// 监听模态框显示状态，开始预加载
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      // 上报弹窗显示事件
      reportEvent(ReportEvent.DemoModalView, {
        userId: userStore.userInfo?.uuid
      })

      // 只有在用户没有交互过且满足预加载条件时才预加载
      if (!userHasInteracted.value && !showLoader.value && loadingProgress.value.total === 0) {
        // 只在第一次显示时预加载
        startResourcePreload()
      }
    } else {
      // 弹窗隐藏时重置交互标志，以便下次显示时能正常工作
      // 但保留已加载的资源状态
      userHasInteracted.value = false
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.demo-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(31, 0, 56, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 16px;
}

.demo-modal {
  width: 100%;
  max-width: 375px;
  background: rgba(31, 0, 56, 0.95);
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  animation: slideUp 0.3s ease;
  padding: 16px;
  box-sizing: border-box;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 上半部分：试玩区域 */
.demo-section {
  background: #1f0038;
  border-radius: 20px;
  width: 100%;
  max-width: 343px; /* 375px - 32px(左右padding) */
  height: 400px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.image-container {
  width: 100%;
  height: 383px;
  position: relative;
  background: #e9e9e9;
  overflow: hidden;
}

.character-slider {
  display: flex;
  width: 500%; /* 5个slide：副本+3个原始+副本 */
  height: 100%;
  /* transition现在通过JavaScript动态控制 */
  user-select: none; /* 防止拖拽时选中文本 */
  touch-action: pan-y; /* 允许垂直滚动，但阻止水平滚动 */
}

.character-slide {
  width: 20%; /* 每个slide占1/5宽度 */
  height: 100%;
  flex-shrink: 0;
}

.media-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.media-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.background-layer {
  z-index: 1;
  opacity: 1; // 默认显示
  transition: opacity 0.3s ease-in-out;
}

.foreground-layer {
  z-index: 2;
  opacity: 1;
  transition: opacity 1.2s ease-in-out;

  &.pre-dissolve {
    opacity: 1; // 保持完全不透明，等待新媒体准备好
  }

  &.dissolving {
    opacity: 0; // 开始溶解
  }
}

.character-image {
  width: 100%;
  height: 538px;
  object-fit: cover;
  position: absolute;
  top: -9px;
  left: 0;
  background: #f5f5f5;
  display: block;

  // 确保媒体元素在加载时不会闪烁
  &[src=''] {
    opacity: 0;
  }

  // 图片加载完成前的占位
  img& {
    background-color: #f5f5f5;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Cg fill='%23ddd'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 40px 40px;
  }

  // 视频加载时的处理
  video& {
    background-color: #f5f5f5;
  }
}

.logo {
  position: absolute;
  top: 16px;
  left: 16px;
  width: 90px;
  height: 23px;
  z-index: 2;
}

.nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.3);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 2;

  &:hover {
    background: rgba(255, 255, 255, 0.5);
    transform: translateY(-50%) scale(1.1);
  }

  &.nav-left {
    left: 12px;
  }

  &.nav-right {
    right: 12px;
  }
}

.image-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 4px;
  z-index: 2;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.2s ease;

  &.active {
    background: #ca93f2;
    transform: scale(1.2);
  }
}

/* Demo按钮区域（包含问题文字） */
.demo-section-with-question {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 0;
  width: 100%;
}

/* 问题文字样式 */
.question-section {
  text-align: center;
  padding: 8px 0;
}

.question-text {
  font-family: 'Rammetto One', sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  color: #ffffff;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Demo按钮区域 */
.demo-buttons {
  display: flex;
  gap: 8px;
  padding: 0;
  width: 100%;
  font-family: 'Rammetto One', sans-serif;
}

.demo-button {
  background: #ca93f2;
  border-radius: 20px;
  padding: 8px 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  height: fit-content;
  box-sizing: border-box;
  border: 1px solid #1f0038;

  &:active:not(:disabled) {
    transform: translateY(1px);
    box-shadow: none;
  }

  &:disabled,
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    background: #ca93f2 !important;
  }

  .demo-icon {
    width: 20px;
    height: 20px;
    background: white;
    border: 1px solid #1f0038;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    svg {
      width: 14px;
      height: 14px;
    }
  }

  span {
    font-family: 'Rammetto One', sans-serif;
    font-size: 8px;
    font-weight: 400;
    line-height: 1.2;
    color: white;
    -webkit-text-stroke: 0.5px #1f0038;
    text-shadow: 0px 1px 0px rgba(31, 0, 56, 1);
    flex: 1;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* 下半部分：登录区域 */
.login-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0;
  width: 100%;
}

.login-button {
  width: 100%;
  height: 42px;
  background: #ca93f2;
  border: none;
  border-radius: 54px;
  color: #241d49;
  font-family: 'Work Sans', sans-serif;
  font-size: 15px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: #b857ff;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

.later-button {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-family: 'Work Sans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  cursor: pointer;
  padding: 8px;
  transition: all 0.2s ease;

  &:hover {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: underline;
  }
}
</style>
