<template>
  <div
    ref="containerRef"
    class="virtual-story-grid-mobile"
    v-bind="containerProps"
    :style="{ height: containerHeight + 'px', overflow: 'hidden' }"
  >
    <div v-bind="wrapperProps">
      <template v-for="{ data: rowItem, index } in list" :key="`row-${rowItem?.rowIndex || index}`">
        <div
          v-if="rowItem && rowItem.stories && isComponentMounted"
          class="story-row-mobile"
          :style="{
            gridTemplateColumns: `repeat(${currentColumns}, 1fr)`,
            gap: `${currentGap}px`,
            padding: `${currentPadding}px ${currentHorizontalPadding}px`
          }"
        >
          <StoryCard
            v-for="(story, storyIndex) in rowItem.stories"
            :key="story?.id || `skeleton-${rowItem.rowIndex || index}-${storyIndex}`"
            :story="story"
            :is-pc="false"
            :loading="loading"
            class="story-card-item"
            @click="$emit('story-click', story)"
            @image-loaded="$emit('image-loaded', story)"
            @subscription-change="$emit('subscription-change', $event)"
            @need-login="$emit('need-login', $event)"
            @need-email="$emit('need-email', $event)"
          />
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed, ref, onUnmounted, onBeforeUnmount } from 'vue'
import { useVirtualList } from '@vueuse/core'
import type { Story } from '@/api/stories'
import { performanceMonitor } from '@/utils/performance'
import StoryCard from '@/shared/components/StoryCard.vue'

interface Props {
  stories: Story[]
  itemHeight?: number
  columnsCount?: number
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  columnsCount: 2, // 移动端默认2列
  loading: false
})

defineEmits<{
  'story-click': [story: Story]
  'image-loaded': [story: Story]
  'subscription-change': [story: Story]
  'need-login': [message?: string]
  'need-email': [message?: string]
}>()

// 响应式列数计算
const currentColumns = ref(props.columnsCount)
const currentGap = ref(16)
const currentPadding = ref(8)
const currentHorizontalPadding = ref(16)

// 动态计算行高
const currentItemHeight = ref(props.itemHeight)

// 容器引用和高度
const containerRef = ref<HTMLElement>()
const containerHeight = ref(600)
const isComponentMounted = ref(false)

// 根据屏幕宽度计算列数和行高
const calculateColumns = () => {
  const width = window.innerWidth
  let columns = 2 // 移动端默认2列
  let gap = 16

  if (width >= 768) {
    columns = 3 // 平板显示3列
    gap = 18
  } else if (width >= 480) {
    columns = 2 // 大手机显示2列
    gap = 16
  } else {
    columns = 2 // 小手机也显示2列，但间距更小
    gap = 12
  }

  currentColumns.value = columns
  currentGap.value = gap

  // 根据屏幕大小调整padding
  if (width >= 768) {
    currentPadding.value = 12
    currentHorizontalPadding.value = 20
  } else if (width >= 480) {
    currentPadding.value = 8
    currentHorizontalPadding.value = 16
  } else {
    currentPadding.value = 6
    currentHorizontalPadding.value = 12
  }

  // 移动端使用固定行高，根据列数调整，增加高度确保内容完全可见
  if (width >= 768) {
    currentItemHeight.value = 340 // 平板3列，增加高度
  } else if (width >= 480) {
    currentItemHeight.value = 320 // 大手机2列，增加高度
  } else {
    currentItemHeight.value = 300 // 小手机2列，增加高度
  }
}

// 将故事数据按行分组
const rowData = computed(() => {
  try {
    const rows: { rowIndex: number; stories: Story[] }[] = []
    const stories = props.stories || []
    const columns = currentColumns.value || 2

    for (let i = 0; i < stories.length; i += columns) {
      rows.push({
        rowIndex: i,
        stories: stories.slice(i, i + columns)
      })
    }
    return rows
  } catch (error) {
    console.warn('Error computing rowData:', error)
    return []
  }
})

// VueUse 虚拟列表 - 优化配置
const { list, containerProps, wrapperProps } = useVirtualList(rowData, {
  itemHeight: () => currentItemHeight.value,
  overscan: props.loading ? 2 : 5 // 加载时减少预渲染，提升首屏速度
})

// 监听窗口大小变化
let resizeObserver: ResizeObserver | null = null

// 性能监控
onMounted(() => {
  isComponentMounted.value = true
  performanceMonitor.mark('virtual-scroll-mobile-mount')

  // 初始化列数
  calculateColumns()

  // 计算容器高度
  const updateContainerHeight = () => {
    // 移动端需要减去：
    // 1. 头部区域(~100px)
    // 2. 筛选按钮(~60px)
    // 3. 底部菜单高度(60px)
    // 4. 底部菜单的安全区域padding(8px + env(safe-area-inset-bottom))
    // 5. footer区域(~60px)
    // 6. 额外安全空间(~20px)

    const headerHeight = 100
    const filterHeight = 60
    const menuHeight = 60 // 底部菜单高度
    const menuSafePadding = 8 // 菜单的安全区域padding基础值
    const footerHeight = 60
    const extraSpace = 20

    // 获取底部安全区域高度（如果有的话）
    const safeAreaBottom =
      parseInt(
        getComputedStyle(document.documentElement)
          .getPropertyValue('--safe-area-inset-bottom')
          .replace('px', '')
      ) || 0

    containerHeight.value =
      window.innerHeight -
      headerHeight -
      filterHeight -
      menuHeight -
      menuSafePadding -
      safeAreaBottom -
      footerHeight -
      extraSpace

    // 确保最小高度
    if (containerHeight.value < 250) {
      containerHeight.value = 250
    }
  }
  updateContainerHeight()

  // 监听窗口大小变化
  const handleResize = () => {
    if (!isComponentMounted.value) return
    try {
      calculateColumns()
      updateContainerHeight()
    } catch (error) {
      console.warn('Error in handleResize:', error)
    }
  }

  window.addEventListener('resize', handleResize)

  // 清理函数
  const cleanup = () => {
    isComponentMounted.value = false
    window.removeEventListener('resize', handleResize)
    if (resizeObserver) {
      resizeObserver.disconnect()
      resizeObserver = null
    }
  }

  onBeforeUnmount(cleanup)
  onUnmounted(cleanup)

  // if (import.meta.env.DEV) {
  //   console.log(`📊 Mobile VueUse Virtual List initialized:`, {
  //     totalItems: props.stories.length,
  //     totalRows: rowData.value.length,
  //     columns: currentColumns.value,
  //     itemHeight: currentItemHeight.value,
  //     gap: currentGap.value,
  //     padding: `${currentPadding.value}px ${currentHorizontalPadding.value}px`,
  //     containerHeight: containerHeight.value,
  //     screenWidth: window.innerWidth,
  //     virtualListItems: list.value.length
  //   })
  // }
})
</script>

<style lang="less" scoped>
.virtual-story-grid-mobile {
  width: 100%;
  padding-bottom: 40px; // 增加底部padding，确保最后一行不被底部菜单遮挡
  // height 通过动态样式设置
}

.story-row-mobile {
  display: grid;
  width: 100%;
  box-sizing: border-box;
  // grid-template-columns, gap, padding 通过动态样式设置
}

.story-card-item {
  width: 100%;
  height: auto;
}
</style>
