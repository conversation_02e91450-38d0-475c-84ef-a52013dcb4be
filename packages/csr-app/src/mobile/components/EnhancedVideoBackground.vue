<template>
  <div
    class="enhanced-video-background"
    :class="{ 'fade-out': shouldFadeOut, 'fade-in': isTransitioning }"
  >
    <canvas ref="canvasRef" class="video-canvas" />

    <video
      ref="videoRef"
      class="video-element"
      :muted="muted"
      :loop="loop"
      :autoplay="autoplay"
      playsinline
      webkit-playsinline="true"
      x5-playsinline="true"
      x5-video-player-type="h5"
      x5-video-player-fullscreen="true"
      preload="none"
      @loadedmetadata="handleVideoLoaded"
      @ended="handleVideoEnded"
    />

    <!-- 视频控制 -->
    <div v-if="videoUrl && (showVolumeButton || showSkipButton)" class="video-controls">
      <button v-if="showVolumeButton" class="volume-button" @click="toggleSound">
        <icon-volume-notice v-if="!muted" />
        <icon-volume-mute v-else />
      </button>
      <button v-if="showSkipButton" class="skip-button" @click="skipVideo">Skip</button>
    </div>

    <!-- 播放按钮覆盖层 -->
    <div v-if="needsPlayButton" class="play-button-overlay">
      <button class="play-button" @click="handlePlayClick">
        <icon-play-circle-fill class="play-icon" />
        <span>Click to Play</span>
      </button>
    </div>

    <!-- 缓冲层 - 用于平滑过渡 -->
    <div
      v-if="showBuffer"
      ref="bufferCanvasRef"
      class="buffer-canvas"
      :style="{ opacity: bufferOpacity }"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { watch, onMounted, ref, onBeforeUnmount, computed } from 'vue'
import { useVideoCanvas } from '@/mobile/composables/useVideoCanvas'
import { professionalVideoCache } from '@/utils/professionalVideoCache'
import IconVolumeNotice from '@/assets/icon/volume-notice.svg'
import IconVolumeMute from '@/assets/icon/volume-mute.svg'
import IconPlayCircleFill from '@/assets/icon/play-circle-fill.svg'
import { onBeforeRouteUpdate } from 'vue-router'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { animate } from 'animejs'

// 组件属性
const props = defineProps<{
  videoUrl?: string
  muted?: boolean
  loop?: boolean
  autoplay?: boolean
  fadeOutOnEnd?: boolean
  showVolumeButton?: boolean
  showSkipButton?: boolean
  transitionDuration?: number
}>()

// 组件事件
const emit = defineEmits<{
  (e: 'video-ended'): void
  (e: 'update:muted', value: boolean): void
  (e: 'video-skipped'): void
  (e: 'video-loaded'): void
  (e: 'transition-complete'): void
}>()

// 状态
const shouldFadeOut = ref(false)
const isLoading = ref(false)
const isTransitioning = ref(false)
const bufferOpacity = ref(0)
const showBuffer = ref(false)
const previousVideoUrl = ref<string | null>(null)
const bufferCanvasRef = ref<HTMLCanvasElement | null>(null)
const bufferContext = ref<CanvasRenderingContext2D | null>(null)
const currentObjectURL = ref<string | null>(null) // 用于跟踪当前的ObjectURL
const isPreloading = ref(false) // 是否正在预加载视频

// 使用视频画布和缓存
const {
  canvasRef,
  videoRef,
  startVideo,
  stopVideo,
  clearVideo,
  needsPlayButton,
  resetCanvas,
  resumeAnimation,
  pauseAnimation
} = useVideoCanvas()

// 计算属性
const transitionDuration = computed(() => props.transitionDuration || 600)

// 捕获当前视频帧到缓冲画布
const captureCurrentFrameToBuffer = () => {
  if (!canvasRef.value || !bufferCanvasRef.value) return

  const canvas = canvasRef.value
  const bufferCanvas = bufferCanvasRef.value

  // 设置缓冲画布尺寸与主画布相同
  bufferCanvas.width = canvas.width
  bufferCanvas.height = canvas.height

  // 获取上下文
  const ctx = bufferCanvas.getContext('2d')
  if (!ctx) return

  // 将当前帧复制到缓冲画布
  ctx.drawImage(canvas, 0, 0, canvas.width, canvas.height)
  bufferContext.value = ctx

  // 显示缓冲层
  showBuffer.value = true
  bufferOpacity.value = 1
}

// 淡出缓冲层
const fadeOutBuffer = () => {
  if (!showBuffer.value) return

  animate(bufferOpacity, {
    value: [1, 0],
    duration: transitionDuration.value,
    easing: 'easeOutCubic',
    update: () => {
      // 动画更新时会自动更新bufferOpacity.value
    },
    complete: () => {
      showBuffer.value = false
      emit('transition-complete')
    }
  })
}

// 添加页面可见性变化监听
const handleVisibilityChange = () => {
  if (document.hidden) {
    // 页面隐藏时暂停视频
    if (videoRef.value) {
      videoRef.value.pause()
    }
  } else {
    // 页面显示时恢复视频
    if (videoRef.value && props.videoUrl) {
      videoRef.value.play().catch((error) => {
        console.error('Failed to resume video:', error)
        needsPlayButton.value = true
      })
    }
  }
}

// 监听键盘事件
const handleKeyboardUp = () => {
  console.log('Keyboard up detected in EnhancedVideoBackground')
}

const handleKeyboardDown = () => {
  console.log('Keyboard down detected in EnhancedVideoBackground')
  resumeAnimation()
}

// 监听页面可见性变化
onMounted(() => {
  document.addEventListener('visibilitychange', handleVisibilityChange)
  document.addEventListener('KeyboardUp', handleKeyboardUp)
  document.addEventListener('KeyboardDown', handleKeyboardDown)

  if (props.videoUrl) {
    startVideo(props.videoUrl)
  }
})

// 清理ObjectURL的函数
const cleanupObjectURL = () => {
  if (currentObjectURL.value) {
    URL.revokeObjectURL(currentObjectURL.value)
    currentObjectURL.value = null
  }
}

// 清理事件监听
onBeforeUnmount(() => {
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  document.removeEventListener('KeyboardUp', handleKeyboardUp)
  document.removeEventListener('KeyboardDown', handleKeyboardDown)

  // 清理ObjectURL
  cleanupObjectURL()

  stopVideo(true) // 重置时间
  clearVideo()
  resetCanvas()
  const video = videoRef.value
  if (video) {
    video.pause()
    video.removeAttribute('src')
    video.load()
  }
})

// 路由更新时处理视频状态
onBeforeRouteUpdate((to, from, next) => {
  // 如果视频正在播放，先暂停
  if (videoRef.value && !videoRef.value.paused) {
    videoRef.value.pause()
  }
  next()
})

// 处理视频加载完成
const handleVideoLoaded = () => {
  const video = videoRef.value
  if (!video) return

  // 设置初始静音状态
  video.muted = props.muted

  if (video.readyState >= 2) {
    // 尝试播放视频（可能带声音）
    video
      .play()
      .then(() => {
        needsPlayButton.value = false

        // 等待视频缓冲足够的数据后再触发loaded事件
        const checkBuffer = () => {
          if (!video.buffered.length) return false
          const bufferedEnd = video.buffered.end(0)
          const minBufferTime = Math.min(5, video.duration)
          return bufferedEnd >= minBufferTime
        }

        const bufferCheck = setInterval(() => {
          if (checkBuffer()) {
            clearInterval(bufferCheck)
            emit('video-loaded')
            isTransitioning.value = false
          }
        }, 100)

        // 如果视频很短或加载很快，也可以用canplaythrough事件来判断
        video.oncanplaythrough = () => {
          clearInterval(bufferCheck)
          emit('video-loaded')
          isTransitioning.value = false
        }
      })
      .catch((error) => {
        console.log('Autoplay with sound prevented, trying muted:', error)

        // 降级策略：如果带声音自动播放失败，尝试静音自动播放
        if (!props.muted) {
          // 设置为静音
          video.muted = true

          // 再次尝试播放
          video
            .play()
            .then(() => {
              needsPlayButton.value = false

              // 通知父组件静音状态已更改
              emit('update:muted', true)

              // 等待视频缓冲足够的数据后再触发loaded事件
              const checkBuffer = () => {
                if (!video.buffered.length) return false
                const bufferedEnd = video.buffered.end(0)
                const minBufferTime = Math.min(5, video.duration)
                return bufferedEnd >= minBufferTime
              }

              const bufferCheck = setInterval(() => {
                if (checkBuffer()) {
                  clearInterval(bufferCheck)
                  emit('video-loaded')
                  isTransitioning.value = false
                }
              }, 100)

              // 如果视频很短或加载很快，也可以用canplaythrough事件来判断
              video.oncanplaythrough = () => {
                clearInterval(bufferCheck)
                emit('video-loaded')
                isTransitioning.value = false
              }
            })
            .catch((mutedError) => {
              console.error('Even muted autoplay failed:', mutedError)
              needsPlayButton.value = true
            })
        } else {
          // 如果已经是静音但仍然失败，显示播放按钮
          needsPlayButton.value = true
        }
      })
  }
}

// 处理视频结束
const handleVideoEnded = () => {
  // 发出视频结束事件
  emit('video-ended')

  if (props.fadeOutOnEnd) {
    shouldFadeOut.value = true
    // 等待动画完成后再清理资源
    setTimeout(() => {
      // 清理资源，重置时间
      stopVideo(true)
      clearVideo()
    }, transitionDuration.value) // 动画时长
  } else if (!props.loop) {
    // 如果视频不循环，暂停视频但保持最后一帧
    if (videoRef.value) {
      videoRef.value.pause()
    }
    // 停止视频播放但不清除画布，这样最后一帧会保持显示
    // 不重置时间，保持在最后一帧
    stopVideo(false)
    // 不调用 clearVideo()，这样最后一帧会保持在画布上
  } else {
    // 如果是循环的，但由于某些原因触发了ended事件，清理资源
    stopVideo(true)
    clearVideo()
  }
}

// 切换声音
const toggleSound = () => {
  if (!videoRef.value) return
  const newMutedState = !props.muted
  videoRef.value.muted = newMutedState
  emit('update:muted', newMutedState)
}

// 跳过视频
const skipVideo = () => {
  const video = videoRef.value
  if (!video) return

  try {
    video.pause()
    video.currentTime = 0
    stopVideo(true) // 重置时间
    clearVideo()
    emit('video-skipped')
  } catch (error) {
    console.error('Error in skipVideo:', error)
    emit('video-skipped')
  }
}

// 处理播放按钮点击
const handlePlayClick = async () => {
  const video = videoRef.value
  if (!video) return

  try {
    needsPlayButton.value = false
    // 尝试播放视频（可能带声音）
    await video.play()
  } catch (error) {
    console.log('Play with sound failed, trying muted:', error)

    // 降级策略：如果带声音播放失败，尝试静音播放
    if (!props.muted) {
      // 设置为静音
      video.muted = true

      try {
        // 再次尝试播放
        await video.play()

        // 通知父组件静音状态已更改
        emit('update:muted', true)
      } catch (mutedError) {
        console.error('Even muted play failed:', mutedError)
        needsPlayButton.value = true
      }
    } else {
      // 如果已经是静音但仍然失败，保持显示播放按钮
      console.error('Play failed:', error)
      needsPlayButton.value = true
    }
  }
}

// 🚀 专业视频缓存解决方案
const preloadCompleteVideo = async (url: string): Promise<string> => {
  console.log('🚀 Professional video preload starting:', url)
  isPreloading.value = true

  try {
    // 使用专业缓存管理器
    const objectURL = await professionalVideoCache.preloadAndCache(url)

    if (objectURL) {
      console.log('✅ Professional video cache success:', url)
      cleanupObjectURL()
      currentObjectURL.value = objectURL
      return objectURL
    } else {
      throw new Error('Professional cache failed')
    }
  } catch (error) {
    console.error('❌ Professional video cache failed:', error)

    // 降级到直接下载
    try {
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const blob = await response.blob()

      // 尝试缓存到专业缓存管理器
      professionalVideoCache.cacheVideo(url, blob).catch(console.warn)

      cleanupObjectURL()
      const objectURL = URL.createObjectURL(blob)
      currentObjectURL.value = objectURL
      return objectURL
    } catch (fallbackError) {
      console.error('❌ All cache strategies failed:', fallbackError)
      throw fallbackError
    }
  } finally {
    isPreloading.value = false
  }
}

// 监听视频URL变化
watch(
  () => props.videoUrl,
  async (newUrl, oldUrl) => {
    if (newUrl === oldUrl) return

    // 如果有旧的视频URL，保存它用于缓冲
    if (oldUrl) {
      previousVideoUrl.value = oldUrl
      captureCurrentFrameToBuffer()
    }

    shouldFadeOut.value = false

    if (newUrl) {
      isLoading.value = true
      isTransitioning.value = true

      try {
        // 先停止当前视频
        stopVideo(true)
        const videoObjectURL = await preloadCompleteVideo(newUrl)

        // 开始播放视频（使用ObjectURL，不会有Range请求）
        startVideo(videoObjectURL)

        isLoading.value = false
        fadeOutBuffer()
      } catch (error) {
        console.error('❌ Video preload failed, using fallback:', error)

        // 降级方案：直接使用URL（会有Range请求）
        try {
          startVideo(newUrl)
          isLoading.value = false
          fadeOutBuffer()
        } catch (fallbackError) {
          console.error('❌ Fallback also failed:', fallbackError)
          isLoading.value = false
        }
      }
    } else {
      // 如果没有新的视频URL，淡出当前视频
      shouldFadeOut.value = true
      setTimeout(() => {
        stopVideo(true)
        clearVideo()
        resetCanvas()
        emit('transition-complete')
      }, transitionDuration.value)
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.enhanced-video-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
  opacity: 1;
  transition: opacity 0.6s ease;

  &.fade-out {
    opacity: 0;
  }

  &.fade-in {
    animation: fadeIn 0.6s ease forwards;
  }

  .video-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1001;
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .volume-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    transition: all 0.2s ease;
    backdrop-filter: blur(4px);
    border: none;
  }

  .skip-button {
    height: 50px;
    padding: 0 20px;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    color: #1f0038;
    font-size: 14px;
    transition: all 0.2s ease;
    backdrop-filter: blur(4px);
    border: none;
  }

  .video-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }

  .buffer-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 2;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }

  .video-element {
    position: absolute;
    width: 1px;
    height: 1px;
    opacity: 0;
    pointer-events: none;
  }

  .play-button-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9998;
    backdrop-filter: blur(2px);
  }

  .play-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 32px;
    border-radius: 24px;
    background: transparent;
    border: none;
    cursor: pointer;
    color: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(8px);
    z-index: 9999;
    &:hover {
      transform: scale(1.02);
    }

    &:active {
      transform: scale(0.98);
    }

    .play-icon {
      width: 64px;
      height: 64px;
      opacity: 0.9;
      filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.2));
    }

    span {
      font-size: 16px;
      font-weight: 500;
      letter-spacing: 1px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
