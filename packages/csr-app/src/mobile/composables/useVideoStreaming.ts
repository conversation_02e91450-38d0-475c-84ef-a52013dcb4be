import { ref } from 'vue'

// 定义分片大小（2MB）
const SEGMENT_SIZE = 2 * 1024 * 1024

// 定义预加载的分片数量
const PRELOAD_SEGMENTS = 2

// 定义视频MIME类型
const MIME_TYPES = {
  mp4: 'video/mp4; codecs="avc1.42E01E, mp4a.40.2"',
  webm: 'video/webm; codecs="vp9, opus"'
}

export function useVideoStreaming() {
  const isSupported = ref(!!window.MediaSource)
  const mediaSource = ref<MediaSource | null>(null)
  const sourceBuffer = ref<SourceBuffer | null>(null)
  const isInitialized = ref(false)
  const isLoading = ref(false)
  const loadingProgress = ref(0)
  const videoUrl = ref<string | null>(null)
  const videoSize = ref<number | null>(null)
  const videoType = ref<string | null>(null)
  const abortController = ref<AbortController | null>(null)
  const segmentsLoaded = ref<number>(0)
  const totalSegments = ref<number>(0)
  const currentXHR = ref<XMLHttpRequest | null>(null)

  // 初始化MediaSource
  const initMediaSource = (video: HTMLVideoElement): Promise<string> => {
    return new Promise((resolve, reject) => {
      if (!isSupported.value) {
        reject(new Error('MediaSource API is not supported in this browser'))
        return
      }

      try {
        // 创建新的MediaSource实例
        const ms = new MediaSource()
        mediaSource.value = ms

        // 当MediaSource打开时，返回URL
        ms.addEventListener(
          'sourceopen',
          () => {
            isInitialized.value = true
            resolve(URL.createObjectURL(ms))
          },
          { once: true }
        )

        // 处理错误
        ms.addEventListener(
          'error',
          (e) => {
            reject(new Error(`MediaSource error: ${e}`))
          },
          { once: true }
        )

        // 设置视频源为MediaSource对象
        video.src = URL.createObjectURL(ms)
      } catch (error) {
        reject(error)
      }
    })
  }

  // 添加SourceBuffer
  const addSourceBuffer = (mimeType: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!mediaSource.value || mediaSource.value.readyState !== 'open') {
        reject(new Error('MediaSource is not open'))
        return
      }

      try {
        // 创建SourceBuffer
        sourceBuffer.value = mediaSource.value.addSourceBuffer(mimeType)

        // 设置模式为segments，允许追加分片
        sourceBuffer.value.mode = 'segments'

        // 监听更新结束事件
        sourceBuffer.value.addEventListener(
          'updateend',
          () => {
            resolve()
          },
          { once: true }
        )

        // 监听错误
        sourceBuffer.value.addEventListener(
          'error',
          (e) => {
            reject(new Error(`SourceBuffer error: ${e}`))
          },
          { once: true }
        )
      } catch (error) {
        reject(error)
      }
    })
  }

  // 获取视频信息（大小和类型）
  const getVideoInfo = async (url: string): Promise<{ size: number; type: string }> => {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest()
      xhr.open('HEAD', url, true)

      xhr.onload = () => {
        if (xhr.status === 200) {
          const contentLength = xhr.getResponseHeader('content-length')
          const contentType = xhr.getResponseHeader('content-type')

          if (contentLength && contentType) {
            resolve({
              size: parseInt(contentLength, 10),
              type: contentType
            })
          } else {
            reject(new Error('Failed to get video info: missing headers'))
          }
        } else {
          reject(new Error(`Failed to get video info: ${xhr.status}`))
        }
      }

      xhr.onerror = () => {
        reject(new Error('Network error while getting video info'))
      }

      xhr.send()
    })
  }

  // 加载视频分片
  const loadSegment = async (
    url: string,
    start: number,
    end: number,
    onProgress?: (loaded: number, total: number) => void
  ): Promise<ArrayBuffer> => {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest()
      currentXHR.value = xhr

      xhr.open('GET', url, true)
      xhr.responseType = 'arraybuffer'
      xhr.setRequestHeader('Range', `bytes=${start}-${end}`)

      xhr.onprogress = (event) => {
        if (event.lengthComputable && onProgress) {
          onProgress(event.loaded, event.total)
        }
      }

      xhr.onload = () => {
        currentXHR.value = null
        if (xhr.status === 206) {
          resolve(xhr.response)
        } else {
          reject(new Error(`Failed to load segment: ${xhr.status}`))
        }
      }

      xhr.onerror = () => {
        currentXHR.value = null
        reject(new Error('Network error while loading segment'))
      }

      xhr.onabort = () => {
        currentXHR.value = null
        reject(new Error('Segment loading aborted'))
      }

      xhr.send()
    })
  }

  // 追加分片到SourceBuffer
  const appendSegment = async (buffer: ArrayBuffer): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!sourceBuffer.value) {
        reject(new Error('SourceBuffer not initialized'))
        return
      }

      // 如果SourceBuffer正在更新，等待更新完成
      if (sourceBuffer.value.updating) {
        sourceBuffer.value.addEventListener(
          'updateend',
          () => {
            appendSegment(buffer).then(resolve).catch(reject)
          },
          { once: true }
        )
        return
      }

      try {
        sourceBuffer.value.appendBuffer(buffer)
        sourceBuffer.value.addEventListener(
          'updateend',
          () => {
            resolve()
          },
          { once: true }
        )
      } catch (error) {
        reject(error)
      }
    })
  }

  // 结束流
  const endStream = (): void => {
    if (mediaSource.value && mediaSource.value.readyState === 'open') {
      try {
        mediaSource.value.endOfStream()
      } catch (error) {
        console.error('Error ending stream:', error)
      }
    }
  }

  // 取消当前加载
  const abortLoading = (): void => {
    if (currentXHR.value) {
      currentXHR.value.abort()
      currentXHR.value = null
    }

    if (abortController.value) {
      abortController.value.abort()
      abortController.value = null
    }

    isLoading.value = false
  }

  // 开始流式加载视频
  const startStreaming = async (
    video: HTMLVideoElement,
    url: string,
    onProgress?: (progress: number) => void
  ): Promise<void> => {
    if (!isSupported.value) {
      throw new Error('MediaSource API is not supported in this browser')
    }

    // 重置状态
    abortLoading()
    isLoading.value = true
    loadingProgress.value = 0
    videoUrl.value = url
    segmentsLoaded.value = 0

    try {
      // 创建新的AbortController
      abortController.value = new AbortController()

      // 获取视频信息
      const info = await getVideoInfo(url)
      videoSize.value = info.size
      videoType.value = info.type

      // 计算总分片数
      totalSegments.value = Math.ceil(info.size / SEGMENT_SIZE)

      // 初始化MediaSource
      await initMediaSource(video)

      // 确定MIME类型
      let mimeType = MIME_TYPES.mp4
      if (info.type.includes('webm')) {
        mimeType = MIME_TYPES.webm
      }

      // 添加SourceBuffer
      await addSourceBuffer(mimeType)

      // 加载初始分片
      const initialSegments = Math.min(PRELOAD_SEGMENTS, totalSegments.value)

      for (let i = 0; i < initialSegments; i++) {
        if (abortController.value.signal.aborted) {
          throw new Error('Loading aborted')
        }

        const start = i * SEGMENT_SIZE
        const end = Math.min(start + SEGMENT_SIZE - 1, videoSize.value - 1)

        const buffer = await loadSegment(url, start, end, (loaded, total) => {
          const segmentProgress = loaded / total
          const overallProgress = (i + segmentProgress) / totalSegments.value
          loadingProgress.value = Math.round(overallProgress * 100)

          if (onProgress) {
            onProgress(loadingProgress.value)
          }
        })

        await appendSegment(buffer)
        segmentsLoaded.value++

        // 如果是第一个分片，尝试开始播放
        if (i === 0) {
          try {
            video.play().catch((e) => console.warn('Auto-play prevented:', e))
          } catch (e) {
            console.warn('Error starting playback:', e)
          }
        }
      }

      // 设置视频播放进度监听，预加载后续分片
      video.addEventListener('timeupdate', () => {
        loadNextSegmentsIfNeeded(video, url)
      })

      isLoading.value = false
    } catch (error) {
      isLoading.value = false
      console.error('Error in startStreaming:', error)
      throw error
    }
  }

  // 根据播放进度加载后续分片
  const loadNextSegmentsIfNeeded = async (video: HTMLVideoElement, url: string) => {
    if (!videoSize.value || !sourceBuffer.value || segmentsLoaded.value >= totalSegments.value) {
      return
    }

    // 计算当前播放位置对应的分片
    const currentTime = video.currentTime
    const duration = video.duration
    const playbackRatio = currentTime / duration
    const estimatedBytePosition = Math.floor(playbackRatio * videoSize.value)
    const estimatedSegment = Math.floor(estimatedBytePosition / SEGMENT_SIZE)

    // 预加载后续分片
    const nextSegmentToLoad = segmentsLoaded.value
    if (
      nextSegmentToLoad <= estimatedSegment + PRELOAD_SEGMENTS &&
      nextSegmentToLoad < totalSegments.value
    ) {
      const start = nextSegmentToLoad * SEGMENT_SIZE
      const end = Math.min(start + SEGMENT_SIZE - 1, videoSize.value - 1)

      try {
        const buffer = await loadSegment(url, start, end)
        await appendSegment(buffer)
        segmentsLoaded.value++

        // 递归加载下一个分片
        loadNextSegmentsIfNeeded(video, url)
      } catch (error) {
        console.error('Error loading next segment:', error)
      }
    }

    // 如果已加载所有分片，结束流
    if (segmentsLoaded.value >= totalSegments.value) {
      endStream()
    }
  }

  return {
    isSupported,
    isLoading,
    loadingProgress,
    startStreaming,
    abortLoading
  }
}
