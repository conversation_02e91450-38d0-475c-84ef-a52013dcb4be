import { ref, computed, watch, Ref, onBeforeUnmount } from 'vue'
import { animate } from 'animejs'
type AnimeInstance = any // 简化类型定义

// 背景资源类型
export type BackgroundResourceType = 'video' | 'image' | 'animated-images'

// 背景资源状态
export interface BackgroundResource {
  type: BackgroundResourceType
  url: string | string[]
  loaded: boolean
  loading: boolean
  error: boolean
}

// 过渡模式
export type TransitionMode = 'fade' | 'slide' | 'zoom'

// 过渡配置
export interface TransitionConfig {
  mode: TransitionMode
  duration: number
  easing: string
  delay: number
}

// 背景管理器选项
export interface BackgroundManagerOptions {
  defaultTransition?: TransitionConfig
  preloadThreshold?: number // 预加载阈值，单位为像素
  cacheSize?: number // 缓存大小
}

/**
 * 背景过渡管理器
 * 用于管理背景资源的加载、预加载和切换动画
 */
export function useBackgroundTransitionManager(options: BackgroundManagerOptions = {}) {
  // 默认选项
  const defaultOptions: Required<BackgroundManagerOptions> = {
    defaultTransition: {
      mode: 'fade',
      duration: 600,
      easing: 'easeOutCubic',
      delay: 0
    },
    preloadThreshold: 1000, // 默认预加载阈值
    cacheSize: 10 // 默认缓存大小
  }

  // 合并选项
  const mergedOptions = { ...defaultOptions, ...options }

  // 当前背景资源
  const currentResource = ref<BackgroundResource | null>(null)

  // 下一个背景资源
  const nextResource = ref<BackgroundResource | null>(null)

  // 缓冲背景资源
  const bufferResource = ref<BackgroundResource | null>(null)

  // 资源缓存
  const resourceCache = ref<Map<string, BackgroundResource>>(new Map())

  // 当前动画实例
  const currentAnimation = ref<AnimeInstance | null>(null)

  // 是否正在过渡
  const isTransitioning = ref(false)

  // 是否正在加载资源
  const isLoading = ref(false)

  // 预加载的资源队列
  const preloadQueue = ref<string[]>([])

  /**
   * 预加载资源
   * @param url 资源URL
   * @param type 资源类型
   */
  const preloadResource = async (url: string, type: BackgroundResourceType): Promise<void> => {
    // 如果已经在缓存中，直接返回
    if (resourceCache.value.has(url)) {
      return
    }

    // 创建新的资源对象
    const resource: BackgroundResource = {
      type,
      url: type === 'animated-images' ? [url] : url,
      loaded: false,
      loading: true,
      error: false
    }

    // 添加到缓存
    resourceCache.value.set(url, resource)

    try {
      if (type === 'image' || type === 'animated-images') {
        await preloadImage(url)
      } else if (type === 'video') {
        await preloadVideo(url)
      }

      // 更新资源状态
      resource.loaded = true
      resource.loading = false
    } catch (error) {
      console.error(`Failed to preload ${type}:`, url, error)
      resource.error = true
      resource.loading = false
    }
  }

  /**
   * 预加载图片
   * @param url 图片URL
   */
  const preloadImage = (url: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve()
      img.onerror = () => reject(new Error(`Failed to load image: ${url}`))
      img.src = url
    })
  }

  /**
   * 预加载视频
   * @param url 视频URL
   */
  const preloadVideo = (url: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video')
      video.preload = 'metadata'

      video.onloadedmetadata = () => {
        // 只预加载元数据即可，不需要加载整个视频
        resolve()
      }

      video.onerror = () => {
        reject(new Error(`Failed to load video metadata: ${url}`))
      }

      video.src = url
      video.load()
    })
  }

  /**
   * 管理缓存大小
   */
  const manageCache = () => {
    if (resourceCache.value.size <= mergedOptions.cacheSize) {
      return
    }

    // 将缓存转换为数组以便排序
    const cacheEntries = Array.from(resourceCache.value.entries())

    // 按最后访问时间排序（这里需要在资源对象中添加lastAccessed属性）
    // 为简化示例，这里直接删除最早的条目
    const entriesToRemove = cacheEntries.slice(0, cacheEntries.length - mergedOptions.cacheSize)

    // 从缓存中删除
    entriesToRemove.forEach(([key]) => {
      resourceCache.value.delete(key)
    })
  }

  /**
   * 创建过渡动画
   * @param element 目标元素
   * @param config 过渡配置
   * @param isIn 是否是进入动画
   */
  const createTransitionAnimation = (
    element: HTMLElement | null | undefined,
    config: TransitionConfig = mergedOptions.defaultTransition,
    isIn: boolean = true
  ): AnimeInstance => {
    // 检查元素是否存在
    if (!element) {
      console.warn('Cannot create animation: element is null or undefined')
      return {
        pause: () => {},
        play: () => {},
        restart: () => {},
        finished: Promise.resolve()
      } as AnimeInstance
    }

    try {
      // 设置初始样式
      if (isIn) {
        element.style.opacity = '0'
      }

      // 创建动画属性对象
      let translateXValue = undefined
      let scaleValue = undefined
      let opacityValue = undefined

      // 根据过渡模式设置动画属性
      switch (config.mode) {
        case 'slide':
          translateXValue = isIn ? [30, 0] : [0, -30]
          opacityValue = isIn ? [0, 1] : [1, 0]
          break
        case 'zoom':
          scaleValue = isIn ? [1.1, 1] : [1, 0.9]
          opacityValue = isIn ? [0, 1] : [1, 0]
          break
        case 'fade':
        default:
          opacityValue = isIn ? [0, 1] : [1, 0]
          scaleValue = isIn ? [1.05, 1] : [1, 1.05]
          break
      }

      // 使用正确的 animate 调用方式
      return animate(element, {
        duration: config.duration,
        easing: config.easing,
        delay: config.delay,
        translateX: translateXValue,
        scale: scaleValue,
        opacity: opacityValue,
        complete: () => {
          if (!isIn && element) {
            // 如果是退出动画，隐藏元素
            element.style.display = 'none'
          }
        }
      })
    } catch (error) {
      console.error('Animation creation error:', error)
      // 返回一个空的动画实例，避免后续代码出错
      return {
        pause: () => {},
        play: () => {},
        restart: () => {},
        finished: Promise.resolve()
      } as AnimeInstance
    }
  }

  /**
   * 清理资源
   */
  onBeforeUnmount(() => {
    // 停止当前动画
    if (currentAnimation.value) {
      currentAnimation.value.pause()
      currentAnimation.value = null
    }

    // 清空缓存
    resourceCache.value.clear()
    preloadQueue.value = []
  })

  /**
   * 切换背景资源
   * @param newResource 新的背景资源
   * @param transitionConfig 过渡配置
   */
  const transitionTo = async (
    newResourceUrl: string | string[],
    type: BackgroundResourceType,
    transitionConfig?: TransitionConfig
  ): Promise<void> => {
    // 如果正在过渡，等待当前过渡完成
    if (isTransitioning.value) {
      // 可以选择取消当前过渡或等待完成
      if (currentAnimation.value) {
        currentAnimation.value.pause()
        currentAnimation.value = null
      }
    }

    // 标记开始过渡
    isTransitioning.value = true

    // 保存当前资源作为缓冲
    if (currentResource.value) {
      bufferResource.value = { ...currentResource.value }
    }

    // 创建新的资源对象
    const resource: BackgroundResource = {
      type,
      url:
        type === 'animated-images'
          ? Array.isArray(newResourceUrl)
            ? newResourceUrl
            : [newResourceUrl as string]
          : (newResourceUrl as string),
      loaded: false,
      loading: true,
      error: false
    }

    // 设置为下一个资源
    nextResource.value = resource

    // 标记开始加载
    isLoading.value = true

    try {
      // 预加载资源
      if (type === 'image') {
        await preloadImage(resource.url as string)
      } else if (type === 'video') {
        await preloadVideo(resource.url as string)
      } else if (type === 'animated-images') {
        await Promise.all((resource.url as string[]).map((url) => preloadImage(url)))
      }

      // 更新资源状态
      resource.loaded = true
      resource.loading = false

      // 切换资源
      currentResource.value = resource
      nextResource.value = null

      // 触发过渡完成事件
      isTransitioning.value = false
      isLoading.value = false

      // 管理缓存
      manageCache()

      return Promise.resolve()
    } catch (error) {
      // 处理加载错误
      console.error('Failed to transition to new resource:', error)
      resource.error = true
      resource.loading = false

      // 如果加载失败，保持当前资源
      nextResource.value = null

      // 触发过渡完成事件
      isTransitioning.value = false
      isLoading.value = false

      return Promise.reject(error)
    }
  }

  /**
   * 应用过渡动画到DOM元素 - 优化版本，支持更流畅的过渡
   * @param targetElement 目标元素
   * @param bufferElement 缓冲元素
   * @param transitionConfig 过渡配置
   */
  const applyTransitionToElements = (
    targetElement: HTMLElement | null | undefined,
    bufferElement: HTMLElement | null | undefined,
    transitionConfig: TransitionConfig = mergedOptions.defaultTransition
  ): Promise<void> => {
    return new Promise((resolve) => {
      // 检查目标元素是否存在
      if (!targetElement) {
        console.warn('Cannot apply transition: targetElement is null or undefined')
        resolve()
        return
      }

      try {
        // 优化的过渡配置
        const optimizedConfig = {
          ...transitionConfig,
          duration: Math.min(transitionConfig.duration, 500), // 限制最大动画时间
          easing: 'easeOutCubic' // 使用更流畅的缓动函数
        }

        // 设置目标元素初始状态和性能优化属性
        targetElement.style.opacity = '0'
        targetElement.style.display = 'block'
        targetElement.style.willChange = 'transform, opacity'

        // 如果有缓冲元素，设置其性能优化属性
        if (bufferElement) {
          bufferElement.style.opacity = '1'
          bufferElement.style.display = 'block'
          bufferElement.style.willChange = 'transform, opacity'
        }

        // 使用双重 requestAnimationFrame 确保 DOM 完全更新
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            try {
              // 创建目标元素的进入动画
              currentAnimation.value = createTransitionAnimation(
                targetElement,
                optimizedConfig,
                true
              )

              // 如果有缓冲元素，创建它的退出动画
              if (bufferElement) {
                // 更短的延迟，更快的过渡
                setTimeout(() => {
                  try {
                    createTransitionAnimation(
                      bufferElement,
                      {
                        ...optimizedConfig,
                        duration: optimizedConfig.duration * 0.7 // 缓冲元素动画更短
                      },
                      false
                    )
                  } catch (error) {
                    console.error('Error creating buffer element animation:', error)
                    // 快速隐藏缓冲元素
                    bufferElement.style.opacity = '0'
                    bufferElement.style.willChange = 'auto'
                    setTimeout(() => {
                      bufferElement.style.display = 'none'
                    }, optimizedConfig.duration * 0.7)
                  }
                }, optimizedConfig.duration * 0.1) // 更短的延迟
              }

              // 动画完成后的清理和解析
              const cleanupAndResolve = () => {
                try {
                  // 清理性能优化属性
                  targetElement.style.willChange = 'auto'
                  if (bufferElement) {
                    bufferElement.style.willChange = 'auto'
                  }
                } catch (e) {
                  console.warn('Error cleaning up willChange properties:', e)
                }
                resolve()
              }

              // 更精确的完成时间计算
              setTimeout(cleanupAndResolve, optimizedConfig.duration + optimizedConfig.delay + 50) // 减少额外等待时间
            } catch (error) {
              console.error('Error in applyTransitionToElements:', error)
              // 快速回退方案
              targetElement.style.opacity = '1'
              targetElement.style.willChange = 'auto'
              if (bufferElement) {
                bufferElement.style.opacity = '0'
                bufferElement.style.willChange = 'auto'
                bufferElement.style.display = 'none'
              }
              resolve()
            }
          })
        })
      } catch (error) {
        console.error('Error in applyTransitionToElements outer block:', error)
        // 确保元素可见并清理
        try {
          targetElement.style.opacity = '1'
          targetElement.style.display = 'block'
          targetElement.style.willChange = 'auto'
          if (bufferElement) {
            bufferElement.style.opacity = '0'
            bufferElement.style.display = 'none'
            bufferElement.style.willChange = 'auto'
          }
        } catch (e) {
          console.error('Error setting element styles:', e)
        }
        resolve()
      }
    })
  }

  /**
   * 获取当前资源的URL
   */
  const currentUrl = computed(() => {
    if (!currentResource.value) return null

    if (currentResource.value.type === 'animated-images') {
      return (currentResource.value.url as string[])[0] || null
    }

    return currentResource.value.url as string
  })

  /**
   * 获取缓冲资源的URL
   */
  const bufferUrl = computed(() => {
    if (!bufferResource.value) return null

    if (bufferResource.value.type === 'animated-images') {
      return (bufferResource.value.url as string[])[0] || null
    }

    return bufferResource.value.url as string
  })

  return {
    // 状态
    currentResource,
    nextResource,
    bufferResource,
    isTransitioning,
    isLoading,
    currentUrl,
    bufferUrl,

    // 方法
    preloadResource,
    createTransitionAnimation,
    transitionTo,
    applyTransitionToElements,

    // 工具方法
    preloadImage,
    preloadVideo
  }
}
