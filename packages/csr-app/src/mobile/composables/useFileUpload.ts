import { ref } from 'vue'
import axios from 'axios'
import { Message } from '@/mobile/components/Message'
import { useStoryStore } from '@/store/story'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface/report'

export interface UseFileUploadOptions {
  requireFaceDetect?: boolean
  maxSize?: number // in MB
  onSuccess?: (url: string) => void
  onError?: (error: any) => void
  // API endpoints configuration
  uploadUrlEndpoint?: string
  uploadMethod?: 'PUT' | 'POST'
  faceDetectEndpoint?: string
}

export function useFileUpload(options: UseFileUploadOptions = {}) {
  const {
    requireFaceDetect = false,
    maxSize = 5,
    onSuccess,
    onError,
    // Default API endpoints
    uploadUrlEndpoint = '/api/v1/chat-video.upload',
    uploadMethod = 'PUT',
    faceDetectEndpoint = '/api/v1/chat-video.detect-face'
  } = options

  const uploading = ref(false)
  const loadingText = ref('')
  const uploadedUrl = ref('')

  const handleFileSelected = async (event: Event) => {
    const input = event.target as HTMLInputElement
    if (!input.files?.length) return

    const file = input.files[0]

    // Check file size
    if (file.size > maxSize * 1024 * 1024) {
      const error = `Image size should be less than ${maxSize}MB`
      Message.error(error)
      onError?.(new Error(error))
      return
    }

    uploading.value = true

    try {
      const storyStore = useStoryStore()
      // 1. Get upload URL
      loadingText.value = 'Getting upload URL...'
      const response = await axios.post(uploadUrlEndpoint, {})
      const { data } = response
      const { upload_url, file_uri, download_url } = data.data

      // Check if URL is empty
      if (!upload_url || !download_url) {
        throw new Error('Failed to get upload URL')
      }

      // 2. Upload file
      loadingText.value = 'Uploading image...'
      try {
        const uploadResponse = await axios[uploadMethod.toLowerCase()](upload_url, file, {
          headers: {
            'Content-Type': file.type
          }
        })

        // Check upload response status
        if (uploadResponse.status !== 200) {
          throw new Error('File upload failed')
        }
      } catch (uploadError) {
        throw new Error('Failed to upload image')
      }

      // 3. Face detection if required
      if (requireFaceDetect) {
        loadingText.value = 'Detecting face...'
        const detectResponse = await axios.post(faceDetectEndpoint, {
          url: download_url
        })
        const detectData = detectResponse.data
        if (!detectData || !detectData?.data?.success) {
          throw new Error('Face detection failed')
        }
      }

      uploadedUrl.value = download_url
      onSuccess?.(download_url)
      reportEvent(ReportEvent.UploadImageSuccess)
    } catch (error) {
      reportEvent(ReportEvent.UploadImageFailed, {
        error: error instanceof Error ? error.message : 'Upload failed. Please try again.'
      })
      const errorMessage =
        error instanceof Error ? error.message : 'Upload failed. Please try again.'
      Message.error(errorMessage)
      onError?.(error)
    } finally {
      uploading.value = false
      loadingText.value = ''
      if (input) input.value = '' // Clear input to allow re-uploading same file
    }
  }

  const triggerFileUpload = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/jpeg,image/png'
    input.onchange = handleFileSelected
    input.click()
  }

  return {
    uploading,
    loadingText,
    uploadedUrl,
    handleFileSelected,
    triggerFileUpload
  }
}
