import { ref, onMounted, watch } from 'vue'
import { useUserAvatarStore } from '@/store/user-character'
import type { UserAvatar } from '@/api/user-character'
import FakeProgress from 'fake-progress'
import { useRoute } from 'vue-router'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface/report'
import { useUserStore } from '@/store/user'
// 使用一个全局变量来跟踪轮询状态
let globalPollingInterval: ReturnType<typeof setInterval> | null = null
let globalProgressInterval: ReturnType<typeof setInterval> | null = null

// 存储每个头像的 FakeProgress 实例
const progressInstances: Record<string, FakeProgress> = {}

export const useGlobalAvatarPolling = () => {
  const store = useUserAvatarStore()
  const userStore = useUserStore()
  const route = useRoute()
  const POLLING_DELAY = 5000 // 5 seconds
  const PROGRESS_DELAY = 100 // 100ms for smoother updates

  // Add watch for avatar status changes
  watch(
    () => store.avatars,
    (newAvatars, oldAvatars = []) => {
      // 如果在角色页面，不需要处理
      if (route.path === '/user-character') return

      // 存储旧的状态，方便快速查找
      const oldStatusMap = new Map(oldAvatars.map((avatar) => [avatar.id, avatar.status]))

      // 只处理状态从非finish变为finish的头像
      const newlyFinishedAvatars = newAvatars.filter(
        (avatar) => avatar.status === 'finish' && oldStatusMap.get(avatar.id) !== 'finish'
      )
      // 批量添加新完成的头像到未查看列表
      if (newlyFinishedAvatars.length > 0) {
        newlyFinishedAvatars.forEach((avatar) => {
          store.addUnviewedAvatar(avatar.id)
          // 上报成功生成的头像
          reportEvent(ReportEvent.GenerateCharacterSuccess, {
            gender: avatar.gender,
            age: avatar.age,
            style: avatar.style,
            image: avatar.image_urls[0]
          })
        })
      }
    },
    { deep: true }
  )

  const hasProcessingAvatars = (avatars: UserAvatar[]) => {
    return avatars.some((avatar) => avatar.status === 'start' || avatar.status === 'submitted')
  }

  const createProgressInstance = (avatarId: string) => {
    if (!progressInstances[avatarId]) {
      progressInstances[avatarId] = new FakeProgress({
        timeConstant: 10000, // 10 seconds to reach ~63% progress
        autoStart: true
      })
    }
    return progressInstances[avatarId]
  }

  const updateFakeProgress = () => {
    store.avatars.forEach((avatar) => {
      if (avatar.status === 'start' || avatar.status === 'submitted') {
        const progressInstance = createProgressInstance(avatar.id)
        // 将进度值转换为百分比并更新到 store
        const progress = Math.round(progressInstance.progress * 100)
        store.updateFakeProgress(avatar.id, Math.min(progress, 99))
      } else if (avatar.status === 'finish') {
        // 如果处理完成，设置为 100%
        store.updateFakeProgress(avatar.id, 100)
        // 清理实例
        delete progressInstances[avatar.id]
      }
    })
  }

  const startProgressAnimation = () => {
    if (globalProgressInterval) return
    globalProgressInterval = setInterval(updateFakeProgress, PROGRESS_DELAY)
  }

  const stopProgressAnimation = () => {
    if (globalProgressInterval) {
      clearInterval(globalProgressInterval)
      globalProgressInterval = null
    }
    // 清理所有进度实例
    Object.keys(progressInstances).forEach((key) => {
      delete progressInstances[key]
    })
  }

  const startPolling = async () => {
    // 需要为登录状态
    if (globalPollingInterval || !userStore.userInfo) return
    console.log('startPolling')
    startProgressAnimation()
    await store.fetchAvatars()

    globalPollingInterval = setInterval(async () => {
      await store.fetchAvatars()

      if (!hasProcessingAvatars(store.avatars)) {
        stopPolling()
      }
    }, POLLING_DELAY)
  }

  const stopPolling = () => {
    if (globalPollingInterval) {
      clearInterval(globalPollingInterval)
      globalPollingInterval = null
    }
    stopProgressAnimation()
  }

  const checkAndStartPolling = async () => {
    if (!userStore.userInfo) return
    await store.fetchAvatars()
    if (hasProcessingAvatars(store.avatars)) {
      startPolling()
    }
  }

  onMounted(() => {
    checkAndStartPolling()
  })

  return {
    startPolling,
    stopPolling,
    checkAndStartPolling
  }
}
