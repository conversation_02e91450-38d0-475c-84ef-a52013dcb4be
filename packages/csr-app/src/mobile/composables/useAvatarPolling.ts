import { ref, onUnmounted } from 'vue'
import { useUserAvatarStore } from '@/store/user-character'
import type { UserAvatar } from '@/api/user-character'

export const useAvatarPolling = () => {
  const store = useUserAvatarStore()
  const pollingInterval = ref<ReturnType<typeof setInterval> | null>(null)
  const progressInterval = ref<ReturnType<typeof setInterval> | null>(null)
  const POLLING_DELAY = 5000 // 5 seconds
  const PROGRESS_DELAY = 1000 // 1 second

  const hasProcessingAvatars = (avatars: UserAvatar[]) => {
    return avatars.some((avatar) => avatar.status === 'start' || avatar.status === 'submitted')
  }

  const updateFakeProgress = () => {
    store.avatars.forEach((avatar) => {
      if (avatar.status === 'start' || avatar.status === 'submitted') {
        // Initialize progress if not exists
        store.initFakeProgress(avatar.id)

        // Increment progress with diminishing returns
        const currentProgress = store.fakeProgress[avatar.id]
        if (currentProgress < 95) {
          const increment = Math.max(1, Math.floor((95 - currentProgress) / 10))
          store.updateFakeProgress(avatar.id, Math.min(95, currentProgress + increment))
        }
      }
    })
  }

  const startProgressAnimation = () => {
    if (progressInterval.value) return
    progressInterval.value = setInterval(updateFakeProgress, PROGRESS_DELAY)
  }

  const stopProgressAnimation = () => {
    if (progressInterval.value) {
      clearInterval(progressInterval.value)
      progressInterval.value = null
    }
  }

  const startPolling = () => {
    if (pollingInterval.value) return

    startProgressAnimation()
    pollingInterval.value = setInterval(async () => {
      await store.fetchAvatars()

      // If no more processing avatars, stop polling and progress animation
      if (!hasProcessingAvatars(store.avatars)) {
        stopPolling()
      }
    }, POLLING_DELAY)
  }

  const stopPolling = () => {
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value)
      pollingInterval.value = null
    }
    stopProgressAnimation()
  }

  const checkAndStartPolling = () => {
    if (hasProcessingAvatars(store.avatars)) {
      startPolling()
    }
  }

  // Clean up on component unmount
  onUnmounted(() => {
    stopPolling()
  })

  return {
    startPolling,
    stopPolling,
    checkAndStartPolling
  }
}
