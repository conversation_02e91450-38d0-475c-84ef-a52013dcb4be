/**
 * 智能加载状态管理 Composable
 * 优化加载体验，减少不必要的加载界面闪烁
 */

import { ref, computed, watch, onBeforeUnmount } from 'vue'

interface SmartLoadingOptions {
  /** 延迟显示加载界面的时间（毫秒） */
  showDelay?: number
  /** 最小显示时间（毫秒） */
  minShowDuration?: number
  /** 快速加载阈值（毫秒），小于此时间的加载被认为是快速加载 */
  fastLoadingThreshold?: number
  /** 淡出动画时间（毫秒） */
  fadeOutDuration?: number
}

export function useSmartLoading(options: SmartLoadingOptions = {}) {
  const {
    showDelay = 150,
    minShowDuration = 300,
    fastLoadingThreshold = 800,
    fadeOutDuration = 200
  } = options

  // 状态管理
  const isLoading = ref(false)
  const isVisible = ref(false)
  const loadingStartTime = ref(0)
  const showStartTime = ref(0)

  // 定时器管理
  let showTimer: ReturnType<typeof setTimeout> | null = null
  let hideTimer: ReturnType<typeof setTimeout> | null = null

  // 清理定时器
  const clearTimers = () => {
    if (showTimer) {
      clearTimeout(showTimer)
      showTimer = null
    }
    if (hideTimer) {
      clearTimeout(hideTimer)
      hideTimer = null
    }
  }

  // 开始加载
  const startLoading = () => {
    if (isLoading.value) return

    clearTimers()
    isLoading.value = true
    loadingStartTime.value = Date.now()

    // 延迟显示加载界面
    showTimer = setTimeout(() => {
      if (isLoading.value) {
        isVisible.value = true
        showStartTime.value = Date.now()
      }
    }, showDelay)
  }

  // 停止加载
  const stopLoading = () => {
    if (!isLoading.value) return

    clearTimers()
    isLoading.value = false

    const loadingDuration = Date.now() - loadingStartTime.value

    if (!isVisible.value) {
      // 加载界面还没显示，直接结束
      return
    }

    // 计算已显示时间
    const showDuration = Date.now() - showStartTime.value
    const remainingMinTime = Math.max(0, minShowDuration - showDuration)

    // 确定淡出延迟时间
    let fadeDelay = 0
    if (remainingMinTime > 0) {
      // 如果还没达到最小显示时间，等待剩余时间
      fadeDelay = remainingMinTime
    } else if (loadingDuration < fastLoadingThreshold) {
      // 快速加载，使用较短的淡出延迟
      fadeDelay = Math.min(fadeOutDuration / 2, 100)
    } else {
      // 正常加载，使用标准淡出延迟
      fadeDelay = fadeOutDuration
    }

    hideTimer = setTimeout(() => {
      isVisible.value = false
    }, fadeDelay)
  }

  // 强制隐藏（用于错误情况）
  const forceHide = () => {
    clearTimers()
    isLoading.value = false
    isVisible.value = false
  }

  // 计算属性
  const shouldShowLoading = computed(() => isVisible.value)
  const loadingDuration = computed(() => {
    return isLoading.value ? Date.now() - loadingStartTime.value : 0
  })

  // 监听外部加载状态变化
  const watchLoadingState = (loadingState: () => boolean) => {
    return watch(
      loadingState,
      (newValue) => {
        if (newValue) {
          startLoading()
        } else {
          stopLoading()
        }
      },
      { immediate: true }
    )
  }

  // 清理资源
  onBeforeUnmount(() => {
    clearTimers()
  })

  return {
    // 状态
    isLoading: computed(() => isLoading.value),
    isVisible: computed(() => isVisible.value),
    shouldShowLoading,
    loadingDuration,

    // 方法
    startLoading,
    stopLoading,
    forceHide,
    watchLoadingState,

    // 工具方法
    clearTimers
  }
}

/**
 * 用于聊天页面的智能加载管理
 */
export function useChatLoading() {
  return useSmartLoading({
    showDelay: 150,        // 150ms后显示加载界面
    minShowDuration: 300,  // 最少显示300ms
    fastLoadingThreshold: 800, // 800ms以下认为是快速加载
    fadeOutDuration: 200   // 200ms淡出时间
  })
}

/**
 * 用于资源预加载的智能加载管理
 */
export function useResourceLoading() {
  return useSmartLoading({
    showDelay: 300,        // 300ms后显示加载界面（资源加载通常更慢）
    minShowDuration: 500,  // 最少显示500ms
    fastLoadingThreshold: 1000, // 1000ms以下认为是快速加载
    fadeOutDuration: 300   // 300ms淡出时间
  })
}
