import { onMounted, onUnmounted, ref } from 'vue'
import { useDeviceDetection } from '@/composables/useDeviceDetection'
import isMobile from 'ismobilejs'
import { useRoute } from 'vue-router'

/**
 * Force portrait mode composable
 * Detects device orientation and shows a message when in landscape mode
 * asking the user to rotate to portrait mode for the best experience
 * Only applies to mobile devices, not desktop/PC
 */
export function useForcePortrait() {
  const route = useRoute()
  const isLandscape = ref(false)
  const isPortrait = ref(true)
  const { isDesktop } = useDeviceDetection()

  // Check if current device is a mobile device
  const isMobileDevice = () => {
    // 检查是否为PC路由
    if (route.path.startsWith('/pc')) {
      return false
    }

    // 检查屏幕宽度是否为PC尺寸
    if (window.innerWidth >= 1366) {
      return false
    }

    // 使用ismobilejs检测是否为移动设备
    const mobileInfo = isMobile()
    return mobileInfo.any
  }

  // Check current screen orientation
  const checkOrientation = () => {
    // 如果是桌面设备，不应用强制竖屏
    if (isDesktop.value) {
      // 移除可能存在的方向类名
      document.body.classList.remove('orientation-landscape')
      document.body.classList.remove('orientation-portrait')
      return
    }

    // Use screen width/height ratio to determine orientation
    // This is more reliable than window.orientation which may be inaccurate on some devices
    const isLandscapeMode = window.innerWidth > window.innerHeight
    isLandscape.value = isLandscapeMode
    isPortrait.value = !isLandscapeMode

    // Add appropriate class names based on orientation
    document.body.classList.toggle('orientation-landscape', isLandscapeMode)
    document.body.classList.toggle('orientation-portrait', !isLandscapeMode)
  }

  // Listen for orientation change events
  const handleOrientationChange = () => {
    // Delay execution to ensure correct screen dimensions are obtained
    setTimeout(checkOrientation, 300)
  }

  onMounted(() => {
    // Initial check
    checkOrientation()

    // Add event listeners
    window.addEventListener('resize', handleOrientationChange)
    window.addEventListener('orientationchange', handleOrientationChange)

    // 只在移动设备上添加强制竖屏样式
    if (!isDesktop.value) {
      // Add global styles
      const styleElement = document.createElement('style')
      styleElement.id = 'force-portrait-style'
      styleElement.textContent = `
        /* Base styles for landscape and portrait orientations */
        .orientation-landscape {
          --screen-orientation: landscape;
        }

        .orientation-portrait {
          --screen-orientation: portrait;
        }

        /* Overlay for landscape mode - only on mobile devices */
        .orientation-landscape::before {
          content: "";
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: #180430;
          z-index: 9999;

          /* 确保覆盖安全区域 */
          padding-top: env(safe-area-inset-top);
          padding-bottom: env(safe-area-inset-bottom);
          padding-left: env(safe-area-inset-left);
          padding-right: env(safe-area-inset-right);
        }

        /* Message for landscape mode - only on mobile devices */
        .orientation-landscape::after {
          content: "Please rotate your device to portrait mode for the best experience";
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: white;
          font-size: 18px;
          font-weight: 500;
          z-index: 10000;
          width: 80%;
          text-align: center;
          padding: 20px;
          box-sizing: border-box;
          background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 2H7C5.9 2 5 2.9 5 4v16c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"></path><path d="M12 18h.01"></path><path d="M12 22v-4"></path><path d="M8 6h8"></path></svg>');
          background-repeat: no-repeat;
          background-position: center top;
          background-size: 40px;
          padding-top: 60px;
          line-height: 1.5;
        }
      `
      document.head.appendChild(styleElement)
    }
  })

  onUnmounted(() => {
    // Remove event listeners
    window.removeEventListener('resize', handleOrientationChange)
    window.removeEventListener('orientationchange', handleOrientationChange)

    // 只有在移动设备上才需要移除样式元素
    if (isMobileDevice()) {
      // Remove global styles
      const styleElement = document.getElementById('force-portrait-style')
      if (styleElement) {
        document.head.removeChild(styleElement)
      }
    }

    // Remove orientation class names
    document.body.classList.remove('orientation-landscape')
    document.body.classList.remove('orientation-portrait')
  })

  return {
    isLandscape,
    isPortrait,
    isDesktop
  }
}
