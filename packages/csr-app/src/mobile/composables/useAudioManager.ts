import { Howl, Howler } from 'howler'
import { ref, reactive } from 'vue'
import { generateTTS } from '@/api/chat'

// Create a reactive state object for audio manager
const state = reactive({
  bgmAudio: null as Howl | null,
  bgmUrl: null as string | null,
  isBgmPlaying: false,
  isMuted: false,
  currentAudio: null as Howl | null,
  isPlaying: false,
  currentPlayingMessageId: null as string | null,
  ttsLoading: false,
  audioInstances: new Map<string, Howl>(),
  ttsCache: new Map<string, string>()
})

export function useAudioManager() {
  const isInitialized = ref(false)

  /**
   * 初始化音频管理器和 Howler 全局设置
   */
  const init = () => {
    if (isInitialized.value) return

    // 设置全局 Howler 配置
    Howler.autoUnlock = true // 自动解锁音频（在用户交互时）
    Howler.html5PoolSize = 10 // 增加池大小以处理多个音频

    // 检查编解码器支持情况
    const isMp3Supported = Howler.codecs('mp3')
    const isOpusSupported = Howler.codecs('opus')
    console.log('Howler initialized, codec support:', {
      mp3: isMp3Supported,
      opus: isOpusSupported
    })

    // 确保所有音频实例的状态一致
    updateAllAudioStates()
    isInitialized.value = true

    // 初始化静音状态
    state.isMuted = false
  }

  /**
   * 清理所有音频资源
   */
  const cleanup = () => {
    // 停止背景音乐并重置相关状态
    if (state.bgmAudio) {
      state.bgmAudio.stop()
      state.bgmAudio = null
    }
    state.bgmUrl = null
    state.isBgmPlaying = false

    // 停止当前播放的音频并重置相关状态
    if (state.currentAudio) {
      state.currentAudio.stop()
      state.currentAudio = null
    }
    state.currentPlayingMessageId = null
    state.isPlaying = false
    state.ttsLoading = false

    // 停止所有已注册的音频实例
    for (const howl of state.audioInstances.values()) {
      howl.stop()
    }

    // 清空实例集合
    state.audioInstances.clear()
    state.ttsCache.clear()

    // 全局静音解除
    Howler.mute(false)

    // 重置初始化状态
    isInitialized.value = false

    // 确保Howler内部资源也被释放
    Howler.unload()
  }

  /**
   * 切换全局静音状态
   */
  const toggleMute = () => {
    console.log('toggleMute called, current state:', state.isMuted)
    state.isMuted = !state.isMuted

    // 使用Howler全局静音
    Howler.mute(state.isMuted)
    console.log('Set Howler global mute to:', state.isMuted)

    // 更新所有音频状态
    updateAllAudioStates()

    // 如果是静音状态，暂停所有音频播放
    if (state.isMuted) {
      console.log('Muting all audio')

      // 暂停背景音乐
      if (state.bgmAudio && state.isBgmPlaying) {
        console.log('Pausing main BGM')
        state.bgmAudio.pause()
        state.isBgmPlaying = false
      }

      // 暂停当前播放的音频
      if (state.currentAudio) {
        console.log('Pausing current audio')
        state.currentAudio.pause()
      }

      // 暂停所有其他音频实例
      for (const [key, howl] of state.audioInstances.entries()) {
        console.log('Pausing audio instance:', key)
        howl.pause()
      }
    } else {
      console.log('Unmuting audio')

      // 取消静音时，恢复背景音乐播放
      if (state.bgmAudio && !state.isBgmPlaying) {
        console.log('Resuming main BGM')
        playBgm()
      }

      // 恢复当前音频
      if (state.currentAudio) {
        console.log('Resuming current audio')
        state.currentAudio.play()
      }
    }
  }

  /**
   * 更新所有音频实例的状态
   */
  const updateAllAudioStates = () => {
    console.log('updateAllAudioStates called, isMuted:', state.isMuted)

    // 使用Howler全局静音
    Howler.mute(state.isMuted)

    if (state.bgmAudio) {
      console.log('Setting mute state for main BGM')
      state.bgmAudio.mute(state.isMuted)
    }

    for (const [key, howl] of state.audioInstances.entries()) {
      console.log('Setting mute state for audio instance:', key)
      howl.mute(state.isMuted)
    }

    if (state.currentAudio) {
      console.log('Setting mute state for current audio')
      state.currentAudio.mute(state.isMuted)
    }
  }

  /**
   * 播放背景音乐
   * @param {string} url 可选，如果提供则加载并播放新的背景音乐
   */
  const playBgm = async (url?: string) => {
    // 播放背景音乐
    console.log('playBgm called with url:', url)

    // 停止所有可能的BGM实例，不仅仅是state.bgmAudio
    // 遍历所有音频实例，停止所有标记为BGM的实例
    for (const [key, howl] of state.audioInstances.entries()) {
      if (key.startsWith('bgm_')) {
        howl.stop()
        state.audioInstances.delete(key)
      }
    }

    // 确保当前BGM也被停止
    if (state.bgmAudio) {
      console.log('Stopping current main BGM')
      state.bgmAudio.stop()
      state.isBgmPlaying = false
    }

    // 如果当前是静音状态，只更新URL但不播放
    if (state.isMuted) {
      console.log('Audio is muted, not playing BGM')
      if (url) {
        state.bgmUrl = url
      }
      return
    }

    // 如果没有提供URL，但有已加载的BGM，直接播放它
    if (!url && state.bgmAudio && state.bgmUrl) {
      try {
        console.log('Playing existing BGM')
        state.bgmAudio.play()
        state.isBgmPlaying = true
      } catch (error) {
        console.error('Failed to play existing BGM:', error)
      }
      return
    }

    // 如果提供了URL
    if (url) {
      console.log('Creating new BGM with URL:', url)
      // 清除之前的BGM实例
      state.bgmAudio = null

      // 创建新的Howl实例
      const howl = new Howl({
        src: [url],
        loop: true,
        volume: 1.0,
        html5: true,
        mute: state.isMuted,
        onloaderror: (_id, error) => {
          console.error('BGM load error:', error)
        }
      })

      // 更新状态
      state.bgmAudio = howl
      state.bgmUrl = url

      // 将BGM实例添加到audioInstances中以便跟踪
      const bgmKey = `bgm_${Date.now()}`
      state.audioInstances.set(bgmKey, howl)

      // 播放新的BGM
      try {
        howl.play()
        state.isBgmPlaying = true
      } catch (error) {
        console.error('Failed to play new BGM:', error)
      }
    }
  }

  /**
   * 暂停背景音乐
   */
  const pauseBgm = () => {
    // 暂停背景音乐

    if (state.bgmAudio && state.isBgmPlaying) {
      state.bgmAudio.pause()
      state.isBgmPlaying = false

      // 更新本地状态
    }
  }

  /**
   * 停止背景音乐
   */
  const stopBgm = () => {
    // 停止背景音乐

    if (state.bgmAudio) {
      state.bgmAudio.stop()
      state.isBgmPlaying = false

      // 更新本地状态
    }
  }

  /**
   * 准备 TTS 但不播放
   * @param {string} text 要转换的文本
   * @param {string} messageId 消息 ID
   * @param {string} actorId 角色ID
   * @param {string} voice_id 可选的语音ID
   */
  const prepareTTS = async (
    text: string,
    messageId: string,
    actorId: string | null,
    voice_id?: string,
    provider?: string
  ): Promise<void> => {
    // 准备 TTS

    // 如果已经在缓存中，直接返回
    if (state.ttsCache.has(messageId)) {
      return
    }

    // 添加超时防止长时间阻塞
    const timeoutPromise = new Promise<void>((_, reject) => {
      setTimeout(() => reject(new Error('TTS preparation timeout')), 5000) // 5秒超时
    })

    try {
      if (!text || !actorId) return
      state.ttsLoading = true

      // 请求生成 TTS
      const response = await generateTTS(text, actorId, voice_id, provider)
      if (response.data?.data?.audio_url) {
        const audioUrl = response.data.data.audio_url

        // 将 URL 存入缓存
        state.ttsCache.set(messageId, audioUrl)

        // 存储到本地缓存

        // 优化：简化预加载逻辑，只缓存URL，延迟创建Howl实例到播放时
        return Promise.race([
          new Promise<void>((resolve) => {
            // 只需要验证URL有效性，不需要立即创建Howl实例
            // 这样可以减少内存占用和预加载时间
            resolve()
          }),
          timeoutPromise
        ])
      }
    } catch (error) {
      console.warn('Error preparing TTS:', error)
      state.ttsCache.delete(messageId)
      throw error
    } finally {
      state.ttsLoading = false
    }
  }

  /**
   * 播放 TTS
   * @param {string} text 要播放的文本
   * @param {string} messageId 消息 ID
   * @param {string} voice_id 可选的语音ID
   */
  const playTTS = async (
    text: string,
    messageId: string,
    voice_id?: string,
    provider?: string
  ): Promise<void> => {
    if (!text || !messageId) {
      console.warn('Invalid text or messageId provided')
      return
    }

    if (state.isMuted) {
      console.log('Audio is muted, skipping TTS playback')
      return
    }

    if (state.currentPlayingMessageId === messageId && state.isPlaying) {
      console.log('Message already playing, skipping')
      return
    }

    // 检查缓存中的音频URL
    const audioUrl = state.ttsCache.get(messageId)
    if (!audioUrl) {
      console.log('TTS url not in cache for message:', messageId)
      return
    }

    console.log('Found cached TTS URL for message:', messageId, 'URL:', audioUrl)

    // 停止之前的播放
    await stopTTS(messageId)

    // 重试配置
    const maxRetries = 2
    let retryCount = 0

    // 播放尝试函数
    const attemptPlayback = (): Promise<boolean> => {
      return new Promise((resolve) => {
        console.log(`TTS playback attempt ${retryCount + 1} for message:`, messageId)

        // 设置当前播放消息 ID
        state.currentPlayingMessageId = messageId

        // 创建 Howl 实例
        const howl = new Howl({
          src: [audioUrl],
          html5: true,
          volume: 1.0,
          mute: state.isMuted,
          onend: () => {
            if (state.currentPlayingMessageId === messageId) {
              state.isPlaying = false
              state.currentPlayingMessageId = null
              state.audioInstances.delete(`tts_${messageId}`)
              if (state.currentAudio === howl) {
                state.currentAudio = null
              }
            }
            resolve(true)
          },
          onplay: () => {
            if (state.currentPlayingMessageId === messageId) {
              state.isPlaying = true
            }
          },
          onloaderror: (_id, error) => {
            state.isPlaying = false
            state.currentPlayingMessageId = null
            if (state.currentAudio === howl) {
              state.currentAudio = null
            }
            state.audioInstances.delete(`tts_${messageId}`)
            resolve(false)
          },
          onplayerror: (_id, error) => {
            state.isPlaying = false
            state.currentPlayingMessageId = null
            if (state.currentAudio === howl) {
              state.currentAudio = null
            }
            state.audioInstances.delete(`tts_${messageId}`)
            resolve(false)
          }
        })

        // 将实例添加到 Map 中以便管理
        state.audioInstances.set(`tts_${messageId}`, howl)
        state.currentAudio = howl

        // 处理背景音乐
        if (state.bgmAudio && state.isBgmPlaying) {
          const originalVolume = state.bgmAudio.volume()
          state.bgmAudio.volume(0.2)

          howl.once('end', () => {
            if (state.bgmAudio) {
              state.bgmAudio.volume(originalVolume)
            }
          })
        }

        // 播放音频
        console.log('Starting TTS playback for message:', messageId)
        howl.play()

        // 设置超时，防止音频播放卡住
        setTimeout(() => {
          if (!state.isPlaying && state.currentPlayingMessageId === messageId) {
            console.log('TTS playback timed out for message:', messageId)
            resolve(false)
          }
        }, 5000)
      })
    }

    // 重试逻辑
    while (retryCount <= maxRetries) {
      const success = await attemptPlayback()

      if (success) {
        console.log('TTS playback successful for message:', messageId)
        return
      }

      retryCount++
      if (retryCount <= maxRetries) {
        console.log(`TTS playback failed, retrying... (${retryCount}/${maxRetries})`)
        await new Promise((resolve) => setTimeout(resolve, 300))
      }
    }

    // 所有重试都失败了
    console.error('TTS playback failed after all retries for message:', messageId)
    if (state.currentPlayingMessageId === messageId) {
      state.currentPlayingMessageId = null
      state.isPlaying = false
    }
  }

  /**
   * 停止 TTS 播放
   * @param {string} exceptMessageId 可选，指定不停止播放的消息 ID
   */
  const stopTTS = async (exceptMessageId?: string) => {
    // 停止 TTS

    // 如果指定了消息ID且与当前播放的消息ID相同，不停止播放
    if (exceptMessageId && state.currentPlayingMessageId === exceptMessageId) {
      return
    }

    state.isPlaying = false
    state.currentPlayingMessageId = null
    state.ttsLoading = false

    // 重置状态

    if (state.currentAudio) {
      state.currentAudio.stop()
      state.currentAudio = null
    }

    // 停止所有正在播放的 TTS 音频（除了被排除的）
    for (const [key, howl] of state.audioInstances.entries()) {
      if (key.startsWith('tts_') && (!exceptMessageId || !key.includes(exceptMessageId))) {
        howl.stop()
        state.audioInstances.delete(key)
        // 删除实例
      }
    }

    // 确保异步操作完成
    await new Promise((resolve) => setTimeout(resolve, 50))
  }

  /**
   * 从 URL 直接播放 TTS
   * @param {string} audioUrl 音频 URL
   * @param {string} messageId 可选消息 ID
   * @param {string} voice_id 可选的语音ID，仅用于日志记录目的
   */
  const playTTSFromUrl = async (
    audioUrl: string,
    messageId?: string,
    voice_id?: string,
    provider?: string
  ) => {
    // 从 URL 播放 TTS

    if (voice_id) {
      console.log(`Playing TTS with voice_id: ${voice_id}`)
    }

    if (provider) {
      console.log(`Playing TTS with provider: ${provider}`)
    }

    try {
      // 使用 stopTTS 方法停止之前的播放
      if (messageId !== state.currentPlayingMessageId) {
        stopTTS(messageId)
      }

      // 创建新的 Howl 实例
      const howl = new Howl({
        src: [audioUrl],
        html5: true,
        volume: 1.0,
        mute: state.isMuted,
        onend: () => {
          if (state.currentPlayingMessageId === messageId) {
            state.currentAudio = null
            state.currentPlayingMessageId = null
            // 重置状态
          }
        },
        onplay: () => {
          console.log('TTS playback started from URL')
        },
        onloaderror: (_id, error) => {
          console.error('Howler load error:', error)
          if (state.currentPlayingMessageId === messageId) {
            state.currentAudio = null
            state.currentPlayingMessageId = null
          }
        },
        onplayerror: (_id, error) => {
          console.error('Howler play error:', error)
          if (state.currentPlayingMessageId === messageId) {
            state.currentAudio = null
            state.currentPlayingMessageId = null
          }
        }
      })

      // 处理背景音乐
      if (state.bgmAudio && state.isBgmPlaying) {
        // 暂时降低 BGM 音量
        const originalVolume = state.bgmAudio.volume()
        state.bgmAudio.volume(0.2)

        // 播放完成后恢复音量
        howl.once('end', () => {
          if (state.bgmAudio) {
            state.bgmAudio.volume(originalVolume)
          }
        })
      }

      // 播放音频
      howl.play()
      state.currentAudio = howl
      // 设置当前音频

      if (messageId) {
        state.currentPlayingMessageId = messageId
        // 设置当前消息 ID
      }
    } catch (error) {
      console.error('Failed to play TTS from URL:', error)
      state.currentAudio = null
      state.currentPlayingMessageId = null
      // 重置状态
    }
  }

  /**
   * 播放音效
   * @param {string} url 音效 URL
   * @param {boolean} loop 是否循环播放
   * @param {number} volume 音量，0-1 之间
   * @returns {Howl} 音效实例，可用于停止播放
   */
  const playSound = (url: string, loop: boolean = false, volume: number = 1.0): Howl => {
    // 播放音效

    // 创建新的 Howl 实例
    const howl = new Howl({
      src: [url],
      loop,
      volume,
      html5: true,
      mute: state.isMuted
    })

    // 播放音效
    howl.play()

    // 将实例添加到 Map 中以便管理
    const soundId = `sound_${Date.now()}`
    state.audioInstances.set(soundId, howl)
    // 存储实例

    // 非循环音效播放结束后自动清理
    if (!loop) {
      howl.once('end', () => {
        state.audioInstances.delete(soundId)
        // 删除实例
      })
    }

    return howl
  }

  /**
   * 停止指定的音效
   * @param {Howl} sound 要停止的音效实例
   */
  const stopSound = (sound: Howl) => {
    // 停止音效

    if (sound) {
      sound.stop()
      // 尝试从 audioInstances 中移除该实例
      for (const [key, howl] of state.audioInstances.entries()) {
        if (howl === sound) {
          state.audioInstances.delete(key)
          // 删除实例
          break
        }
      }
    }
  }

  return {
    init,
    cleanup,
    toggleMute,
    updateAllAudioStates,
    playBgm,
    pauseBgm,
    stopBgm,
    prepareTTS,
    playTTS,
    playTTSFromUrl,
    stopTTS,
    playSound,
    stopSound,
    get isMuted() {
      return state.isMuted
    },
    set isMuted(value: boolean) {
      state.isMuted = value
    },
    // Expose state for components that need to observe it
    state
  }
}
