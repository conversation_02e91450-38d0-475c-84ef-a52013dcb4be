import { ref, onBeforeUnmount } from 'vue'

interface Action {
  type: 'pay_now' | 'try_again'
  position: number
  option_id?: string
}

interface TypedSegment {
  text: string
  isAction: boolean
  html?: string
  buttonType?: 'pay_now' | 'try_again'
}

export function useTypingEffect() {
  let animationFrameId: number | null = null
  let isDestroyed = false

  const calculateBatchSize = (text: string) => {
    const length = text.length
    return Math.max(3, Math.min(10, Math.floor(length / 50)))
  }

  const calculateInterval = (text: string) => {
    const baseSpeed = 30
    const length = text.length
    return Math.max(10, Math.min(baseSpeed, baseSpeed - Math.floor(length / 100) * 5))
  }

  // 将文本分割成动作和普通文本段
  const parseText = (text: string): TypedSegment[] => {
    const segments: TypedSegment[] = []
    let currentIndex = 0

    while (currentIndex < text.length) {
      const startIndex = text.indexOf('*', currentIndex)

      // 如果没有找到 * 或者是最后一个字符，将剩余文本作为普通文本
      if (startIndex === -1) {
        if (currentIndex < text.length) {
          segments.push({
            text: text.slice(currentIndex),
            isAction: false
          })
        }
        break
      }

      // 添加 * 之前的普通文本
      if (startIndex > currentIndex) {
        segments.push({
          text: text.slice(currentIndex, startIndex),
          isAction: false
        })
      }

      // 查找结束的 *
      const endIndex = text.indexOf('*', startIndex + 1)
      if (endIndex === -1) {
        // 如果没有找到结束的 *，将剩余文本作为普通文本
        segments.push({
          text: text.slice(currentIndex),
          isAction: false
        })
        break
      }

      // 添加动作文本
      const actionText = text.slice(startIndex + 1, endIndex)
      segments.push({
        text: `(${actionText})`,
        isAction: true,
        html: `<span class="action-text">(${actionText})</span>`
      })

      currentIndex = endIndex + 1
    }

    return segments
  }

  const parseTextWithActions = (text: string, actions?: Action[]): TypedSegment[] => {
    // 先处理 *text* 格式的动作文本
    const segments = parseText(text)

    if (!actions || actions.length === 0) {
      return segments
    }

    // 按位置排序
    const sortedActions = [...actions].sort((a, b) => a.position - b.position)

    const result: TypedSegment[] = []
    let currentPosition = 0

    // 处理每个 action
    sortedActions.forEach((action) => {
      // 添加 action 位置之前的文本
      if (action.position >= currentPosition) {
        const textBeforeAction = {
          text: text.slice(currentPosition, action.position + 1),
          isAction: false
        }
        result.push(textBeforeAction)
      }

      // 添加按钮
      const buttonSegment = {
        text: '',
        isAction: true,
        buttonType: action.type,
        html:
          action.type === 'pay_now'
            ? '<button class="action-button pay-now" data-action="pay_now"><img src="https://cdn.magiclight.ai/assets/playshot/pay-now-1-4x.png" alt="pay now" /></button>'
            : `<button class="action-button try-again" data-action="try_again" data-option-id="${
                action.option_id || ''
              }"><img src="https://cdn.magiclight.ai/assets/playshot/try-again-1-4x.png" alt="try again" /></button>`
      }
      result.push(buttonSegment)

      // 更新当前位置
      currentPosition = action.position + 1
    })

    // 添加剩余的文本
    if (currentPosition < text.length) {
      const remainingText = {
        text: text.slice(currentPosition),
        isAction: false
      }
      result.push(remainingText)
    }

    // 处理 *text* 格式的动作文本
    const finalResult = result.map((segment) => {
      if (!segment.isAction || segment.buttonType) {
        return segment
      }
      // 处理 *text* 格式的动作文本
      return {
        ...segment,
        html: `<span class="action-text">(${segment.text})</span>`
      }
    })

    return finalResult
  }

  const typeText = async (
    text: string,
    actions: Action[] | undefined,
    updateCallback: (text: string) => void
  ) => {
    // 预处理文本，移除多余的换行符和引号
    const cleanedText = text.replace(/\n{3,}/g, '\n\n').replace(/"{2,}/g, '"')
    const segments = parseTextWithActions(cleanedText, actions)
    let currentText = ''
    let currentSegmentIndex = 0
    let currentSegmentProgress = 0

    const batchSize = calculateBatchSize(cleanedText)
    const interval = calculateInterval(cleanedText)
    let lastTime = performance.now()

    const animate = (currentTime: number) => {
      if (isDestroyed) return

      if (currentTime - lastTime >= interval) {
        if (currentSegmentIndex < segments.length) {
          const segment = segments[currentSegmentIndex]

          if (segment.isAction) {
            // 按钮或动作文本直接完整插入
            currentText += segment.html || segment.text
            currentSegmentIndex++
            currentSegmentProgress = 0
            lastTime = currentTime
          } else {
            // 普通文本逐字显示
            const segmentText = segment.text
            const start = currentSegmentProgress
            const end = Math.min(start + batchSize, segmentText.length)

            // 拼接新文本
            if (currentSegmentProgress === 0) {
              currentText += segmentText.slice(0, end)
            } else {
              currentText =
                currentText.slice(0, currentText.length - currentSegmentProgress) +
                segmentText.slice(0, end)
            }

            currentSegmentProgress = end

            if (currentSegmentProgress >= segmentText.length) {
              currentSegmentIndex++
              currentSegmentProgress = 0
            }

            lastTime = currentTime
          }

          updateCallback(currentText)
        }
      }

      if (currentSegmentIndex < segments.length && !isDestroyed) {
        animationFrameId = requestAnimationFrame(animate)
      }
    }

    if (!isDestroyed) {
      animationFrameId = requestAnimationFrame(animate)
    }

    return () => {
      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId)
        animationFrameId = null
      }
    }
  }

  const cleanup = () => {
    isDestroyed = true
    if (animationFrameId !== null) {
      cancelAnimationFrame(animationFrameId)
      animationFrameId = null
    }
  }

  onBeforeUnmount(() => {
    cleanup()
  })

  return {
    typeText,
    cleanup
  }
}
