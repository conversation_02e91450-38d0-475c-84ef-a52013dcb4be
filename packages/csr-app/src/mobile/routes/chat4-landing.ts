import type { RouteRecordRaw } from 'vue-router'
import { createRouteLoader } from '@/utils/advanced-route-loader'

/**
 * Chat4 Landing Page Routes
 * Provides landing pages specifically designed for Chat4 experiences
 */

const chat4LandingRoutes: RouteRecordRaw[] = [
  {
    path: '/chat4-landing',
    name: 'Chat4Landing',
    component: createRouteLoader(
      () => import('../views/chat4-landing/index.vue'),
      'chat4-landing',
      {
        priority: 'high',
        preload: true,
      },
    ),
    meta: {
      requiresAuth: false,
      showMenu: false,
      title: 'Chat4 - Interactive Entertainment',
      description: 'Experience next-generation interactive entertainment with live streaming, real-time chat, and immersive experiences.',
      seo: {
        pageType: 'landing',
        keywords: 'chat4, interactive entertainment, live streaming, real-time chat, immersive experience',
      },
    },
  },
  {
    path: '/chat4-landing/:characterKey',
    name: 'Chat4LandingCharacter',
    component: createRouteLoader(
      () => import('../views/chat4-landing/index.vue'),
      'chat4-landing-character',
      {
        priority: 'high',
        preload: true,
      },
    ),
    meta: {
      requiresAuth: false,
      showMenu: false,
      title: 'Chat4 - Interactive Entertainment',
      description: 'Experience next-generation interactive entertainment with your favorite characters.',
      seo: {
        pageType: 'landing',
        dynamic: true,
        keywords: 'chat4, interactive entertainment, character chat, live streaming',
      },
    },
  },
]

export default chat4LandingRoutes
