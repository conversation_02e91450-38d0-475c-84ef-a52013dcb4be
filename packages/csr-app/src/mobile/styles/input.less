// 全局输入框样式
@import '@/assets/style/theme.less';

:deep(.arco-input),
:deep(.arco-input-number),
:deep(.arco-textarea),
:deep(.arco-select-view),
.form-input {
  width: 100%;
  min-height: 42px;
  border: 1px solid var(--mobile-input-border);
  background: var(--mobile-input-bg);
  color: var(--text-primary);
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;

  &:hover,
  &:focus {
    border-color: var(--accent-color);
    background: var(--bg-hover);
  }

  &::placeholder {
    color: var(--text-tertiary);
  }

  // 输入框内的文字颜色
  .arco-input-inner-wrapper {
    background: transparent;
    border: none;

    input {
      color: var(--text-primary);

      &::placeholder {
        color: var(--text-tertiary);
      }
    }
  }

  // 数字输入框的按钮样式
  .arco-input-number-step {
    border-left: 1px solid rgba(184, 196, 255, 0.1);

    &:hover {
      background: rgba(202, 147, 242, 0.1);
    }

    .arco-input-number-step-button {
      color: rgba(255, 255, 255, 0.6);

      &:hover {
        color: #ca93f2;
      }
    }
  }
}

// 禁用状态
:deep(.arco-input)[disabled],
:deep(.arco-input-number)[disabled],
:deep(.arco-textarea)[disabled],
:deep(.arco-select-view)[disabled],
.form-input[disabled] {
  background: rgba(204, 213, 255, 0.02);
  border-color: rgba(184, 196, 255, 0.05);
  color: rgba(255, 255, 255, 0.3);
  cursor: not-allowed;
}
