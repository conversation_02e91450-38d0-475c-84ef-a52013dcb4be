// 平板和大屏设备适配样式
@import '@/assets/style/theme.less';

// 平板设备 (768px - 1024px)
@media screen and (min-width: 768px) and (max-width: 1024px) {
  body {
    background: linear-gradient(
      135deg,
      var(--mobile-bg-gradient-start) 0%,
      var(--mobile-bg-gradient-end) 100%
    );
    height: calc(var(--vh, 1vh) * 100);
    margin: 0;
    display: flex;
    justify-content: center;
    transition: background 0.3s ease;
  }

  #app {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    background: var(--mobile-app-bg);
    box-shadow: 0 0 20px var(--shadow-color);
    overflow-y: auto;
  }

  // 隐藏滚动条
  * {
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  // 基础交互效果
  button,
  .clickable {
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.02);
    }

    &:active {
      transform: scale(0.98);
    }
  }

  input,
  textarea {
    &:hover {
      border-color: var(--accent-color);
    }
  }
}

// iPad Pro 和大尺寸平板 (1024px - 1366px)
@media screen and (min-width: 1024px) and (max-width: 1366px) {
  body {
    background: linear-gradient(
      135deg,
      var(--mobile-bg-gradient-start) 0%,
      var(--mobile-bg-gradient-end) 100%
    );
    height: calc(var(--vh, 1vh) * 100);
    margin: 0;
    display: flex;
    justify-content: center;
    transition: background 0.3s ease;
  }

  #app {
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    background: var(--mobile-app-bg);
    box-shadow: 0 0 20px var(--shadow-color);
    overflow-y: auto;
  }

  // 隐藏滚动条
  * {
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  // 基础交互效果
  button,
  .clickable {
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.02);
    }

    &:active {
      transform: scale(0.98);
    }
  }

  input,
  textarea {
    &:hover {
      border-color: var(--accent-color);
    }
  }
}

// PC 设备 (1366px 以上) - 仅在移动端组件中使用时生效
@media screen and (min-width: 1366px) {
  // 注意：PC端现在使用专门的PC组件，这里的样式主要用于移动端组件在大屏设备上的显示
  body:not(.pc-mode) {
    background: linear-gradient(
      135deg,
      var(--mobile-bg-gradient-start) 0%,
      var(--mobile-bg-gradient-end) 100%
    );
    height: 100vh;
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
    transition: background 0.3s ease;
  }

  // PC模式下的样式重置
  body.pc-mode {
    background: var(--bg-primary);
    height: 100vh;
    margin: 0;
    padding: 0;
    display: block;
    align-items: unset;
    justify-content: unset;
  }

  // 移动端组件在大屏设备上的显示样式（仅在非PC模式下生效）
  body:not(.pc-mode) #app {
    width: 100%;
    max-width: 450px;
    height: 95vh;
    max-height: 900px;
    margin: 0 auto;
    position: relative;
    background: var(--mobile-app-bg);
    box-shadow: 0 0 20px var(--shadow-color);
    overflow-y: auto;
    border-radius: 20px;
  }

  // PC模式下的#app样式重置
  body.pc-mode #app {
    max-width: 100% !important;
    height: 100vh !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    margin: 0;
    padding: 0;
  }

  // 隐藏滚动条
  * {
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  // 基础交互效果
  button,
  .clickable {
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.02);
    }

    &:active {
      transform: scale(0.98);
    }
  }

  input,
  textarea {
    &:hover {
      border-color: #ca93f2;
    }
  }
}
