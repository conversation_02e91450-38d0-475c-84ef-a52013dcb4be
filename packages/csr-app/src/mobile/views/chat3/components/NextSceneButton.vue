<template>
  <div class="next-scene-button" ref="nextButtonRef" @click="handleNextScene">
    <span v-text-stroke="{ color: '#542C74' }">Next Scene</span>
    <next-scene-button-icon></next-scene-button-icon>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useStoryStore } from '@/store/story'
import { useChatEventsStore } from '@/store/chat-events'
// import { useChatResourcesStore } from '@/store/chat-resources' // 暂时未使用
import { cancelAllSSEConnections } from '@/utils/EventSourcePolyfill'
import { animate } from 'animejs'
import NextSceneButtonIcon from '@/assets/icon/next-scene-button.svg'

defineProps<{
  actorId?: string
}>()

const emit = defineEmits<{
  (e: 'next-success'): void
}>()

const route = useRoute()
const storyStore = useStoryStore()
const chatEventsStore = useChatEventsStore()
// const chatResourcesStore = useChatResourcesStore() // 暂时未使用
const nextButtonRef = ref<HTMLElement | null>(null)

// 处理跳转到下一场景
const handleNextScene = async () => {
  const storyId = (route.params.storyId as string) || storyStore.currentStory?.id

  if (!storyId) return

  // 添加按钮动画效果
  animate(nextButtonRef.value, {
    scale: [1, 0.9, 1],
    rotate: [0, 5, 0],
    duration: 400,
    easing: 'easeInOutQuad'
  })

  try {
    // 取消所有活跃的 SSE 连接和正在进行的请求
    cancelAllSSEConnections()

    // 调用 store 中的方法
    await chatEventsStore.jumpToNextScene(storyId)

    // 处理完成后通知父组件
    emit('next-success')
  } catch (error) {
    console.error('跳转到下一场景失败:', error)
  }
}

// 组件挂载时添加入场动画
onMounted(() => {
  if (nextButtonRef.value) {
    animate(nextButtonRef.value, {
      opacity: [0, 1],
      duration: 500,
      easing: 'easeOutQuad'
    })
  }
})
</script>

<style lang="less" scoped>
.next-scene-button {
  position: absolute;
  bottom: 280px;
  height: 36px;
  left: 50%;
  transform: translate(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #daff96;
  padding: 2px 16px;
  border-radius: 38px;
  border: 1.5px solid #1f0038;
  background: #e0b6ff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  z-index: 12;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  animation:
    fadeInUp 0.5s ease-out,
    breathe 3s infinite ease-in-out;

  svg {
    width: 20px;
    height: 20px;
  }

  &:hover {
    background: rgba(94, 52, 137, 0.9);
    transform: translateX(-50%) translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
    animation-play-state: paused; /* 悬停时暂停呼吸动画 */
  }

  &:active {
    transform: translateX(-50%) translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    animation-play-state: paused; /* 点击时暂停呼吸动画 */
  }

  span {
    margin-right: 4px;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes breathe {
  0% {
    transform: translate(-50%) scale(1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border-color: #1f0038;
    filter: brightness(1);
    text-shadow: 0 0 4px rgba(218, 255, 150, 0.5);
  }
  50% {
    transform: translate(-50%) scale(1.08);
    box-shadow: 0 8px 20px rgba(94, 52, 137, 0.6);
    border-color: #542c74;
    filter: brightness(1.1);
    text-shadow: 0 0 8px rgba(218, 255, 150, 0.8);
  }
  100% {
    transform: translate(-50%) scale(1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border-color: #1f0038;
    filter: brightness(1);
    text-shadow: 0 0 4px rgba(218, 255, 150, 0.5);
  }
}
</style>
