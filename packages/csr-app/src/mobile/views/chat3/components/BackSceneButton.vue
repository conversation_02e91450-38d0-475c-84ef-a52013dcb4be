<template>
  <div
    class="back-scene-button"
    ref="backButtonRef"
    @click="handleBackToPreviousScene"
    :class="{ disabled: props.isThinking }"
  >
    <back-scene-button-icon />
    <span v-text-stroke="{ color: '#542C74' }">Finish</span>
    <div v-if="props.isThinking" class="loading-spinner"></div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { useStoryStore } from '@/store/story'
import { useChatEventsStore } from '@/store/chat-events'
import { cancelAllSSEConnections } from '@/utils/EventSourcePolyfill'
import { animate } from 'animejs'
import BackSceneButtonIcon from '@/assets/icon/back-scene-button.svg'

const props = defineProps<{
  actorId?: string
  isThinking?: boolean
}>()

const emit = defineEmits<{
  (e: 'back-success'): void
}>()

const route = useRoute()
const storyStore = useStoryStore()
const backButtonRef = ref<HTMLElement | null>(null)

// 处理返回上一场景
const handleBackToPreviousScene = async () => {
  // 如果正在思考中，禁止操作
  if (props.isThinking) return
  const storyId = (route.params.storyId as string) || storyStore.currentStory?.id

  if (!storyId) return

  // 添加按钮动画效果
  animate(backButtonRef.value, {
    scale: [1, 0.9, 1],
    rotate: [0, -5, 0],
    duration: 400,
    easing: 'easeInOutQuad'
  })

  const chatEventsStore = useChatEventsStore()

  try {
    // 取消所有活跃的 SSE 连接和正在进行的请求
    cancelAllSSEConnections()

    // 调用 store 中的方法返回上一场景
    await chatEventsStore.jumpToPreviousScene(storyId)

    // 处理完成后通知父组件
    emit('back-success')
  } catch (error) {
    console.error('返回上一场景失败:', error)
  }
}

// 组件挂载时添加入场动画
// onMounted(() => {
//   if (backButtonRef.value) {
//     animate(backButtonRef.value, {
//       opacity: [0, 1],
//       translateY: [20, 0],
//       duration: 500,
//       easing: 'easeOutQuad'
//     })
//   }
// })
</script>

<style lang="less" scoped>
.back-scene-button {
  position: absolute;
  bottom: 300px;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 38px 0px 0px 38px;
  border-top: 1px solid #000;
  border-bottom: 1px solid #000;
  border-left: 1px solid #000;
  background: #e0b6ff;
  color: white;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  z-index: 12;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  &.disabled {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
  }

  &:hover {
    background: rgba(94, 52, 137, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  span {
    margin-left: 4px;
    color: #daff96;
    font-size: 14px;
    font-weight: 500;
  }

  .loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #daff96;
    animation: spin 0.8s linear infinite;
  }

  @keyframes spin {
    to {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }
}
</style>
