<template>
  <div class="task-tip-wrapper">
    <div
      class="task-tip"
      :class="{ collapsed: chatUIStore.isTaskTipCollapsed, animating: isAnimating }"
      ref="taskTipRef"
    >
      <div class="task-tip-header" @click="throttledToggle" ref="headerRef">
        <div class="header-top">
          <div class="task-icon" ref="iconRef">
            <img
              src="https://static.playshot.ai/static/images/icon/task.png"
              alt="mission"
              ref="iconImgRef"
            />
          </div>
          <div class="task-title" ref="titleRef">Mission</div>
          <div class="collapse-icon" ref="collapseIconRef">
            <icon-right />
          </div>
        </div>
        <div class="task-description">
          <div class="description-content" ref="contentRef">
            <span v-html="formattedDescription"></span>
            <span class="complete-icon" v-if="allActorsCompletedMission"><complete-icon /></span>
          </div>
        </div>
      </div>
      <div v-if="chatUIStore.isShowAlert && !chatUIStore.isTaskTipCollapsed" class="skip-button">
        <button class="skip-action" @click="handleSkip">Game stuck? Skip to next scene</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, onUnmounted } from 'vue'
import { IconRight } from '@arco-design/web-vue/es/icon'
import { animate } from 'motion'
import { useThrottleFn } from '@vueuse/core'
import { useChatUIStore } from '@/store/chat-ui'
import { useChatTasksStore } from '@/store/chat-tasks'
import { useChatEventsStore } from '@/store/chat-events'
import { useChatStore } from '@/store/chat'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface'
import CompleteIcon from '@/assets/icon/complete.svg'

const chatStore = useChatStore()
const chatUIStore = useChatUIStore()
const chatTasksStore = useChatTasksStore()
const chatEventsStore = useChatEventsStore()

const props = defineProps<{ description: string }>()

// ===== 基础状态 =====
const isAnimating = ref(false)
const taskTipRef = ref<HTMLElement>()
const headerRef = ref<HTMLElement>()
const iconRef = ref<HTMLElement>()
const iconImgRef = ref<HTMLElement>()
const contentRef = ref<HTMLElement>()
const collapseIconRef = ref<HTMLElement>()
const titleRef = ref<HTMLElement>()
let styleObserver: MutationObserver | null = null

// ===== 计算属性 =====
const formattedDescription = computed(() => {
  if (!props.description) return ''
  const matches = props.description.match(/(.*?)\*(.*?)\*(.*)/)
  if (!matches) return props.description
  const [_, beforeText, highlightText, afterText] = matches
  return `${beforeText}<span class="highlight" style="color: #ED2A2E; font-weight: 600;">${highlightText}</span>${afterText}`
})

const currentTaskData = computed(() => {
  if (!chatTasksStore.currentTaskId) return { task: { percent: 0, description: '' }, index: 0 }
  const index = chatTasksStore.tasks.findIndex(
    (task) => task.task_id === chatTasksStore.currentTaskId
  )
  return {
    task: index === -1 ? { percent: 0, description: '' } : chatTasksStore.tasks[index],
    index: index === -1 ? 0 : index
  }
})

const currentTask = computed(() => currentTaskData.value.task)
const currentTaskIndex = computed(() => currentTaskData.value.index)
const currentLevel = computed(() => `Level ${currentTaskIndex.value + 1}`)
const progress = computed(() => currentTask.value.percent)
const allActorsCompletedMission = computed(() => {
  return chatEventsStore.checkAllActorsCompletedMission()
})

// ===== 工具函数 =====
// 设置样式的辅助函数
const setStyle = (element: Element | null, styles: Record<string, string>) => {
  if (!element) return
  const elementStyle = element as HTMLElement
  Object.entries(styles).forEach(([key, value]) => {
    elementStyle.style[key as any] = value
  })
}

// ===== 状态同步 =====
// 同步视觉样式与折叠状态
const syncCollapsedState = () => {
  if (!taskTipRef.value) return

  const taskDescriptionElement =
    contentRef.value || taskTipRef.value.querySelector('.task-description')
  const taskTitleElement = titleRef.value || taskTipRef.value.querySelector('.task-title')
  const collapseIconElement =
    collapseIconRef.value || taskTipRef.value.querySelector('.collapse-icon')
  const taskIconImgElement = iconImgRef.value || taskTipRef.value.querySelector('.task-icon img')
  const headerTopElement = headerRef.value || taskTipRef.value.querySelector('.task-tip-header')

  if (chatUIStore.isTaskTipCollapsed) {
    // 收起状态样式
    setStyle(taskTipRef.value, {
      width: '46px',
      maxWidth: '46px',
      height: '46px',
      maxHeight: '46px',
      borderRadius: '20px',
      background: '#ca93f2',
      padding: '0',
      opacity: '1',
      transform: 'none',
      transition: 'none' // 清除过渡效果，确保状态立即生效
    })

    // 隐藏内容元素
    const hideStyles = {
      visibility: 'hidden',
      opacity: '0',
      display: 'none'
    }

    setStyle(taskDescriptionElement, hideStyles)
    setStyle(taskTitleElement, hideStyles)
    setStyle(collapseIconElement, hideStyles)

    // 设置图标样式 - 确保尺寸保持一致，避免闪烁
    setStyle(taskIconImgElement, {
      width: '30px',
      height: '30px',
      position: 'absolute',
      left: '50%',
      top: '50%',
      transform: 'translate(-50%, -50%)',
      transition: 'none' // 清除过渡效果，确保状态立即生效
    })

    // 调整头部元素样式
    setStyle(headerTopElement, {
      padding: '0',
      height: '100%',
      alignItems: 'center',
      justifyContent: 'center'
    })
  } else {
    // 展开状态样式
    // 先确保内容元素可见，以便正确计算高度
    if (taskDescriptionElement) {
      setStyle(taskDescriptionElement, {
        visibility: 'visible',
        display: 'block',
        opacity: '0' // 设为透明，仅用于计算高度
      })
    }

    // 强制重排以获取准确高度
    if (taskDescriptionElement) {
      void taskDescriptionElement.offsetHeight
    }

    // 重新获取内容高度
    let contentHeight = taskDescriptionElement
      ? (taskDescriptionElement as HTMLElement).offsetHeight
      : 0

    // 如果内容高度为0，则使用固定的最小高度
    if (contentHeight === 0 && taskDescriptionElement) {
      // 尝试再次强制重排
      void (taskDescriptionElement as HTMLElement).offsetHeight
      contentHeight = (taskDescriptionElement as HTMLElement).offsetHeight

      // 如果仍然为0，则使用固定的最小高度
      if (contentHeight === 0) {
        // 根据文本长度设置一个适当的高度
        contentHeight = 120 // 根据文本长度设置高度，最小60px
      }
    }

    // 添加额外的高度缓冲区，确保文本完全显示
    // 根据文本长度动态调整缓冲区大小
    if (taskDescriptionElement) {
      const extraPadding = 20 // 增加缓冲区大小
      contentHeight += extraPadding
    }

    const skipButtonHeight = chatUIStore.isShowAlert ? 54 : 0
    // 头部高度（包含图标和标题）
    const headerHeight = 30

    // 计算总高度：头部 + 内容 + 跳过按钮（如果有）+ 内边距
    const totalPadding = 30 // 增加内边距总和，从24增加到30
    const minHeight = headerHeight + contentHeight + skipButtonHeight + totalPadding

    // 确保最小高度，根据是否显示跳过按钮决定
    const isShowSkipButton = chatUIStore.isShowAlert && !chatUIStore.isTaskTipCollapsed
    const calculatedHeight = Math.max(minHeight, isShowSkipButton ? 160 : 130) // 增加最小高度从120到130

    setStyle(taskTipRef.value, {
      width: 'calc(100vw - 50px)',
      maxWidth: '400px',
      height: `${calculatedHeight}px`,
      maxHeight: 'none', // 移除maxHeight限制
      borderRadius: '20px',
      background: 'linear-gradient(180deg, #e0b6ff 0%, #ca93f2 100%)',
      padding: '12px 16px',
      opacity: '1',
      transform: 'none',
      transition: 'none' // 清除过渡效果，确保状态立即生效
    })

    // 显示内容元素
    const showStyles = {
      visibility: 'visible',
      opacity: '1',
      display: 'block',
      transform: 'translateX(0)'
    }

    setStyle(taskDescriptionElement, showStyles)
    setStyle(taskTitleElement, showStyles)

    setStyle(collapseIconElement, {
      opacity: '1',
      transform: 'scale(1)',
      visibility: 'visible',
      display: 'flex'
    })

    // 设置图标样式 - 清除transition，避免与动画冲突
    setStyle(taskIconImgElement, {
      width: '20px',
      height: '20px',
      position: '',
      left: '',
      top: '',
      transform: 'none',
      transition: 'none' // 清除过渡效果，确保状态立即生效
    })

    // 调整头部元素样式
    setStyle(headerTopElement, {
      padding: '',
      height: '',
      alignItems: '',
      justifyContent: ''
    })
  }
}

// ===== 动画控制 =====
// 展开动画 - 超简化版本
const expandAnimation = async () => {
  try {
    if (!chatUIStore.isTaskTipCollapsed) {
      return
    }

    if (!taskTipRef.value) {
      return
    }

    isAnimating.value = true

    // 1. 先添加过渡效果 - 使用相同的过渡时间和缓动函数
    const transitionTiming = '0.35s cubic-bezier(0.4, 0, 0.2, 1)'

    // 为容器添加过渡效果
    taskTipRef.value.style.transition = `width ${transitionTiming}, height ${transitionTiming}, padding ${transitionTiming}, border-radius ${transitionTiming}, background ${transitionTiming}`

    // 为图标添加过渡效果
    if (iconImgRef.value) {
      iconImgRef.value.style.transition = `width ${transitionTiming}, height ${transitionTiming}, transform ${transitionTiming}, left ${transitionTiming}, top ${transitionTiming}`
    }

    // 提前准备标题和内容元素，但保持透明
    if (titleRef.value) {
      setStyle(titleRef.value, {
        display: 'block',
        visibility: 'visible',
        opacity: '0',
        transform: 'translateX(-10px)',
        transition: `opacity ${transitionTiming}, transform ${transitionTiming}`
      })
    }

    if (contentRef.value) {
      setStyle(contentRef.value, {
        display: 'block',
        visibility: 'visible',
        opacity: '0',
        transform: 'translateX(-10px)',
        transition: `opacity ${transitionTiming}, transform ${transitionTiming}`
      })
    }

    if (collapseIconRef.value) {
      setStyle(collapseIconRef.value, {
        display: 'flex',
        visibility: 'visible',
        opacity: '0',
        transform: 'scale(0.8)',
        transition: `opacity ${transitionTiming}, transform ${transitionTiming}`
      })
    }

    // 2. 设置展开后的尺寸和样式
    await new Promise((resolve) => {
      // 使用setTimeout确保CSS动画可以触发
      setTimeout(() => {
        if (!taskTipRef.value) return resolve(null)

        // 内容元素已在前面准备好，这里不需要重复设置

        // 强制重排以获取准确高度
        if (contentRef.value) {
          void contentRef.value.offsetHeight
        }

        // 计算所需高度
        // 获取内容高度，如果为0，则使用固定的最小高度
        let contentHeight = contentRef.value ? contentRef.value.offsetHeight : 0

        // 调试输出
        console.log('expandAnimation contentHeight before:', contentHeight)

        // 如果内容高度为0，则使用固定的最小高度
        if (contentHeight === 0 && contentRef.value) {
          // 尝试再次强制重排
          void contentRef.value.offsetHeight
          contentHeight = contentRef.value.offsetHeight

          // 如果仍然为0，则使用固定的最小高度
          if (contentHeight === 0) {
            // 根据文本长度设置一个适当的高度
            const textLength = contentRef.value.textContent?.length || 0
            contentHeight = Math.max(30, Math.min(textLength * 0.6, 120)) // 根据文本长度设置高度，最小30px
          }
        }

        // 添加额外的高度缓冲区，确保文本完全显示
        if (contentRef.value) {
          const textLength = contentRef.value.textContent?.length || 0
          const extraPadding = Math.min(Math.max(textLength / 8, 20), 60) // 增加缓冲区大小
          contentHeight += extraPadding
        }

        const skipButtonHeight = chatUIStore.isShowAlert ? 54 : 0
        const headerHeight = 30
        const totalPadding = 30 // 增加内边距总和，从24增加到30
        const totalHeight = headerHeight + contentHeight + skipButtonHeight + totalPadding
        const isShowSkipButton = chatUIStore.isShowAlert && !chatUIStore.isTaskTipCollapsed
        // 设置展开状态样式
        setStyle(taskTipRef.value, {
          width: 'calc(100vw - 50px)',
          maxWidth: '400px',
          height: `${Math.max(totalHeight, isShowSkipButton ? 160 : 130)}px`, // 确保最小高度130px
          maxHeight: 'none', // 清除maxHeight限制
          padding: '12px 16px',
          borderRadius: '20px',
          background: 'linear-gradient(180deg, #e0b6ff 0%, #ca93f2 100%)'
        })

        // 将图标设置到正确位置
        if (iconImgRef.value) {
          setStyle(iconImgRef.value, {
            width: '20px',
            height: '20px',
            position: 'static',
            transform: 'none',
            left: 'auto',
            top: 'auto'
          })
        }

        // 状态改变
        chatUIStore.isTaskTipCollapsed = false

        // 内容元素的过渡效果已在前面设置好

        // 等待容器动画完成后再显示内容
        // 同步显示所有元素，并添加平滑的过渡效果
        if (contentRef.value) {
          contentRef.value.style.opacity = '1'
          contentRef.value.style.transform = 'translateX(0)'
        }

        if (titleRef.value) {
          titleRef.value.style.opacity = '1'
          titleRef.value.style.transform = 'translateX(0)'
        }

        if (collapseIconRef.value) {
          collapseIconRef.value.style.opacity = '1'
          collapseIconRef.value.style.transform = 'scale(1)'
        }

        // 等待内容淡入完成后再次计算高度并应用

        // 再次计算高度并应用，确保内容完全显示
        if (contentRef.value && taskTipRef.value) {
          // 重新计算高度
          let recalcHeight = contentRef.value.offsetHeight

          // 如果高度为0，使用文本长度估算
          if (recalcHeight === 0) {
            const textLength = contentRef.value.textContent?.length || 0
            recalcHeight = Math.max(60, Math.min(textLength * 0.3, 120))
          }

          // 添加额外缓冲区
          const textLength = contentRef.value.textContent?.length || 0
          const extraPadding = Math.min(Math.max(textLength / 8, 20), 60)
          recalcHeight += extraPadding

          // 重新计算总高度
          const skipButtonHeight = chatUIStore.isShowAlert ? 54 : 0
          const headerHeight = 30
          const totalPadding = 30
          const totalHeight = headerHeight + recalcHeight + skipButtonHeight + totalPadding
          const isShowSkipButton = chatUIStore.isShowAlert && !chatUIStore.isTaskTipCollapsed
          const calculatedHeight = Math.max(totalHeight, isShowSkipButton ? 160 : 130)
          // 应用新高度
          taskTipRef.value.style.height = `${calculatedHeight}px`
        }

        resolve(null)
      }, 10)
    })

    // 最后确保样式与状态一致
    syncCollapsedState()
  } catch (error) {
    console.error('[TaskTip] 展开动画错误:', error)
    chatUIStore.isTaskTipCollapsed = false
    syncCollapsedState()
  } finally {
    isAnimating.value = false
  }
}

// 收起动画 - 超简化版本
const collapseAnimation = async () => {
  try {
    if (chatUIStore.isTaskTipCollapsed) {
      return
    }

    isAnimating.value = true

    if (!taskTipRef.value) {
      chatUIStore.isTaskTipCollapsed = true
      syncCollapsedState()
      isAnimating.value = false
      return
    }

    // 1. 先隐藏内容，等动画结束后再完全隐藏
    await new Promise((resolve) => {
      // 使用相同的过渡时间和缓动函数
      const transitionTiming = '0.35s cubic-bezier(0.4, 0, 0.2, 1)'

      // 添加平滑的退出动画
      if (contentRef.value) {
        setStyle(contentRef.value, {
          opacity: '0',
          transform: 'translateX(-10px)',
          transition: `opacity ${transitionTiming}, transform ${transitionTiming}`
        })
      }

      if (titleRef.value) {
        setStyle(titleRef.value, {
          opacity: '0',
          transform: 'translateX(-10px)',
          transition: `opacity ${transitionTiming}, transform ${transitionTiming}`
        })
      }

      if (collapseIconRef.value) {
        setStyle(collapseIconRef.value, {
          opacity: '0',
          transform: 'scale(0.8)',
          transition: `opacity ${transitionTiming}, transform ${transitionTiming}`
        })
      }

      // 等待内容淡出
      setTimeout(() => {
        // 内容完全隐藏
        if (contentRef.value) setStyle(contentRef.value, { visibility: 'hidden', display: 'none' })
        if (titleRef.value) setStyle(titleRef.value, { visibility: 'hidden', display: 'none' })
        if (collapseIconRef.value)
          setStyle(collapseIconRef.value, { visibility: 'hidden', display: 'none' })

        resolve(null)
      }, 200)
    })

    // 2. 为图标提前设置好过渡效果和尺寸，避免先缩小后放大的问题
    if (iconImgRef.value) {
      // 直接设置为较大尺寸，避免先缩小后放大
      setStyle(iconImgRef.value, {
        width: '30px',
        height: '30px',
        transition:
          'width 0.3s ease, height 0.3s ease, transform 0.3s ease, left 0.3s ease, top 0.3s ease',
        position: 'absolute',
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)'
      })
    }

    // 3. 添加容器过渡效果 - 使用相同的过渡时间和缓动函数
    const transitionTiming = '0.35s cubic-bezier(0.4, 0, 0.2, 1)'
    taskTipRef.value.style.transition = `width ${transitionTiming}, height ${transitionTiming}, padding ${transitionTiming}, border-radius ${transitionTiming}, background ${transitionTiming}`

    // 4. 执行收缩动画
    await new Promise((resolve) => {
      setTimeout(() => {
        // 收缩容器
        setStyle(taskTipRef.value, {
          width: '46px',
          maxWidth: '46px',
          height: '46px',
          padding: '0',
          borderRadius: '23px',
          background: '#ca93f2'
        })

        // 改变状态
        chatUIStore.isTaskTipCollapsed = true

        // 等待动画完成
        setTimeout(() => {
          resolve(null)
        }, 300)
      }, 10)
    })

    // 5. 确保最终样式正确
    syncCollapsedState()
  } catch (error) {
    console.error('[TaskTip] 收起动画错误:', error)
    chatUIStore.isTaskTipCollapsed = true
    syncCollapsedState()
  } finally {
    isAnimating.value = false
  }
}

// ===== 事件处理 =====
// 切换状态
const handleToggle = async () => {
  reportEvent(ReportEvent.Chat2TaskTipClick, {
    isCollapsed: chatUIStore.isTaskTipCollapsed
  })

  if (isAnimating.value) {
    return
  }

  isAnimating.value = true

  try {
    if (chatUIStore.isTaskTipCollapsed) {
      await expandAnimation()
    } else {
      await collapseAnimation()
    }
  } catch (error) {
    console.error('[TaskTip] 切换错误:', error)
    const newState = !chatUIStore.isTaskTipCollapsed
    chatUIStore.isTaskTipCollapsed = newState
    syncCollapsedState()
  } finally {
    isAnimating.value = false
  }
}

const throttledToggle = useThrottleFn(handleToggle, 300)

// 跳过当前场景
const handleSkip = () => {
  reportEvent(ReportEvent.ClickChat2TaskTipSkip, {
    isCollapsed: chatUIStore.isTaskTipCollapsed
  })
  chatUIStore.isShowAlert = false
  collapseAnimation()
  chatUIStore.isTaskTipCollapsed = true
  syncCollapsedState()
  chatStore.sendMessage('__NEXT_SCENE__', '__NEXT_SCENE__')
}

// ===== 监听和生命周期 =====
// 监听折叠状态变化
watch(
  () => chatUIStore.isTaskTipCollapsed,
  () => {
    syncCollapsedState()
    // 设置延时确保样式被正确应用
    setTimeout(() => syncCollapsedState(), 10)
    setTimeout(() => syncCollapsedState(), 100)
  },
  { immediate: true }
)

// 监听提示显示状态
watch(
  () => chatUIStore.isShowAlert,
  (newVal) => {
    if (newVal) handleToggle()
  }
)

// 监听任务等级变化
watch(
  () => currentLevel.value,
  async (newLevel) => {
    if (newLevel) await expandAnimation()
  }
)

onMounted(() => {
  // 初始状态同步
  syncCollapsedState()

  // 设置样式观察器
  styleObserver = new MutationObserver(() => {
    if (chatUIStore.isTaskTipCollapsed) {
      setTimeout(() => {
        const taskDescriptionEl = taskTipRef.value?.querySelector(
          '.task-description'
        ) as HTMLElement
        const taskTitleEl = taskTipRef.value?.querySelector('.task-title') as HTMLElement

        const needsSync =
          (taskDescriptionEl &&
            (taskDescriptionEl.style.opacity === '1' ||
              taskDescriptionEl.style.visibility === 'visible' ||
              taskDescriptionEl.style.display !== 'none')) ||
          (taskTitleEl &&
            (taskTitleEl.style.opacity === '1' || taskTitleEl.style.visibility === 'visible'))

        if (needsSync) {
          syncCollapsedState()
        }
      }, 0)
    }
  })

  if (taskTipRef.value) {
    styleObserver.observe(taskTipRef.value, {
      attributes: true,
      attributeFilter: ['style', 'class'],
      childList: true,
      subtree: true
    })
  }
})

onUnmounted(() => {
  if (styleObserver) {
    styleObserver.disconnect()
    styleObserver = null
  }
})

// 导出组件方法
defineExpose({
  collapse: () => {
    collapseAnimation()
    chatUIStore.isTaskTipCollapsed = true
    syncCollapsedState()
  }
})
</script>

<style lang="less" scoped>
.task-tip-wrapper {
  position: fixed;
  z-index: 100;
}

.task-tip {
  position: absolute;
  top: 0;
  right: 0;
  background: linear-gradient(180deg, #e0b6ff 0%, #ca93f2 100%);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 2px solid #1f0038;
  padding: 12px 16px;
  overflow: hidden;
  width: calc(100vw - 50px);
  max-width: 400px;
  will-change: transform, width, height, border-radius, background;
  transform-origin: right center;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  transition: background 0.35s cubic-bezier(0.4, 0, 0.2, 1);

  // 动画中状态样式
  &.animating {
    .task-description,
    .task-title,
    .collapse-icon,
    .skip-button {
      opacity: 0 !important;
      visibility: hidden !important;
      display: none !important;
    }
  }

  // 收起状态样式
  &.collapsed {
    backdrop-filter: none;
    cursor: pointer;
    width: 46px !important;
    height: 46px !important;
    border-radius: 20px;
    border: 2px solid #1f0038;
    background: #ca93f2;
    padding: 0;
    max-height: 46px;
    transition: none;

    .task-tip-header {
      padding: 0;
      height: 100%;
      align-items: center;
      justify-content: center;

      .task-icon {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 22px;
        height: 22px;
        margin: 0;
        z-index: 1;
        pointer-events: none;

        img {
          width: 30px;
          height: 30px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }

        .progress-text {
          position: absolute;
          margin: auto;
          height: fit-content;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #daff96;
          font-size: 14px;
          font-weight: 600;
          text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
          z-index: 1;
          &::before {
            content: attr(data-content);
            -webkit-text-stroke: 1px #542c74;
            position: absolute;
            left: 0;
            top: 0;
            z-index: -1;
          }
        }
      }

      .task-title,
      .task-description,
      .collapse-icon {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
      }
    }
  }
}

.task-tip-header {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px;
  cursor: pointer;
  box-sizing: border-box;
  flex: 1;

  .header-top {
    display: flex;
    align-items: center;
    gap: 5px;

    .task-title {
      flex: 1;
      font-size: 14px;
      font-weight: 600;
      color: #1f0038;
      line-height: 20px;
    }

    .collapse-icon {
      width: 20px;
      height: 20px;
      background: rgba(0, 0, 0, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      color: rgba(255, 255, 255, 0.8);
      pointer-events: none;
      z-index: 2;
      :deep(svg) {
        width: 12px;
        height: 12px;
      }
    }
  }

  .task-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin: 0;
    z-index: 2;

    img {
      width: 20px;
      height: 20px;
    }
  }

  .task-description {
    position: relative;
    font-weight: 600;
    font-size: 15px;
    color: #1f0038;
    line-height: 22px; /* 增加行高，从20px增加到22px */
    background: #e6c3ff;
    border-radius: 14px;
    padding: 12px 14px; /* 进一步增加内边距 */
    width: 100%;
    box-sizing: border-box;
    overflow: visible; /* 从 hidden 改为 visible，确保内容不被裁剪 */
    min-height: 60px; /* 添加最小高度 */
    height: auto; /* 确保高度自适应 */
    flex: 1;
    &.with-progress {
      padding-right: 40px;
    }

    .description-content {
      position: relative;
      z-index: 1;
      word-break: break-word;
      word-wrap: break-word;
      min-height: 40px; /* 增加最小高度从22px到40px */
      display: block; /* 确保内容块级显示 */
      padding-bottom: 5px; /* 添加底部内边距 */
      .complete-icon {
        display: inline-block;
        margin-left: 5px;
        transform: translateY(5px);
      }
    }

    .progress-bar {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      background: #e1fc92;
      transition: width 0.3s ease;
      z-index: 0;
    }

    .progress-text {
      position: absolute;
      right: 5px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 15px;
      font-weight: 600;
      z-index: 2;
      color: #daff96;
      &::before {
        content: attr(data-content);
        -webkit-text-stroke: 1px #542c74;
        position: absolute;
        left: 0;
        top: 0;
        z-index: -1;
      }
    }
  }
}

.skip-button {
  margin-top: 10px;
  width: 100%;
  display: flex;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.skip-action {
  border-radius: 26px;
  border-top: 2px solid #1f0038;
  border-right: 2px solid #1f0038;
  border-bottom: 6px solid #1f0038;
  border-left: 2px solid #1f0038;
  background: linear-gradient(180deg, #f5ffe2 0%, #daff96 100%);
  box-shadow: 0px 1.855px 11.13px 0px #daff96;
  height: 44px;
  color: #241d49;
  font-size: 15px;
  font-weight: 600;
  padding: 0 16px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, 10px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}
</style>
