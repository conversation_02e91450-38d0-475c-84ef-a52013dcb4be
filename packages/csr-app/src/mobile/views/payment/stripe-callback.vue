<template>
  <div class="stripe-callback">
    <div class="loading-spinner"></div>
    <p>Processing payment...</p>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { setPaymentSuccessful } from '../../routes'
import { Message } from '@/mobile/components/Message'

const router = useRouter()
const route = useRoute()

onMounted(async () => {
  try {
    // 这里可以添加验证支付状态的 API 调用
    // const result = await verifyPayment(route.query.session_id)

    // 设置支付成功标记
    setPaymentSuccessful()

    // 获取 URL 参数中的 amount
    const amount = route.query.amount

    // 重定向到充值成功页面
    router.replace({
      path: '/recharge-success',
      query: { amount }
    })
  } catch (error) {
    console.error('Payment verification failed:', error)
    Message.error('Payment verification failed')
    router.replace('/')
  }
})
</script>

<style lang="less" scoped>
.stripe-callback {
  height: calc(var(--vh, 1vh) * 100);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #2b1b3b 0%, #1a0f24 100%);
  color: white;
  gap: 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #ca93f2;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
