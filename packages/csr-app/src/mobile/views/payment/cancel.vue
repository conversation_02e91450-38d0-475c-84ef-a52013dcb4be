<template>
  <div class="payment-cancel"></div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@/mobile/components/Message'

const router = useRouter()

onMounted(() => {
  Message.info('Payment cancelled')
  router.replace('/')
})
</script>

<style lang="less" scoped>
.payment-cancel {
  height: calc(var(--vh, 1vh) * 100);
  background-color: #f5f6f7;
}
</style>
