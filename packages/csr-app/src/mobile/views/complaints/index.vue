<template>
  <div class="legal-page">
    <div class="header">
      <div class="back-button" @click="router.back()">
        <icon-left />
      </div>
      <h1>Complaints Policy</h1>
    </div>

    <div class="content">
      <div class="section">
        <h2>Complaints Policy</h2>
        <p>
          At {{ appName }} AI, we value user feedback and strive to address any concerns or
          complaints promptly and effectively. This Complaints Policy outlines the procedures for
          users to report and resolve issues related to our services.
        </p>
      </div>

      <div class="section notice">
        <h3>Submitting a Complaint</h3>
        <ul>
          <li>Complaints can be submitted through our Discord available on the Contact Us page.</li>
          <li>Complaints can also be sent via email to support@{{ appName.toLowerCase() }}.ai.</li>
        </ul>
      </div>

      <div class="section">
        <h3>Information Required</h3>
        <ul>
          <li>Detailed description of the complaint.</li>
          <li>Any supporting evidence or documentation.</li>
          <li>Contact information for follow-up.</li>
        </ul>
      </div>

      <div class="section">
        <h3>Acknowledgment</h3>
        <p>Upon receipt of a complaint, we will acknowledge the complaint within 24 hours.</p>
      </div>

      <div class="section">
        <h3>Confidentiality</h3>
        <p>All complaints and related information will be handled with strict confidentiality.</p>
      </div>

      <div class="section">
        <h3>Resolution</h3>
        <p>All reported complaints will be reviewed and resolved within 7 business days.</p>
      </div>

      <div class="section">
        <h3>Contact Information</h3>
        <p>
          For any questions or concerns regarding this policy, please contact us at support@{{
            appName.toLowerCase()
          }}.ai.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { IconLeft } from '@arco-design/web-vue/es/icon'
import { computed } from 'vue'

const router = useRouter()
const appName = computed(() => import.meta.env.VITE_APP_NAME || 'Playshot')
</script>

<style lang="less" scoped>
.legal-page {
  height: calc(var(--vh, 1vh) * 100);
  background: #1f0038;
  color: rgba(255, 255, 255, 0.9);
  padding-bottom: 40px;
}

.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #1f0038;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .back-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.6);
    transition: all 0.3s;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;

    &:hover {
      color: white;
      background: rgba(255, 255, 255, 0.15);
    }

    :deep(.arco-icon) {
      font-size: 18px;
    }
  }

  h1 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
  }
}

.content {
  margin: 0 auto;
  padding: 24px 20px;
  background: #1f0038;
  .section {
    margin-bottom: 32px;

    &.notice {
      background: rgba(202, 147, 242, 0.1);
      border-radius: 12px;
      padding: 20px;
      border: 1px solid rgba(202, 147, 242, 0.2);
    }

    h2 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 16px;
      color: #ca93f2;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 12px;
      color: #ca93f2;
    }

    p {
      margin: 0 0 16px;
      line-height: 1.6;
      color: rgba(255, 255, 255, 0.8);
      font-size: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    ul {
      margin: 12px 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        color: rgba(255, 255, 255, 0.8);
        font-size: 15px;
        line-height: 1.6;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
