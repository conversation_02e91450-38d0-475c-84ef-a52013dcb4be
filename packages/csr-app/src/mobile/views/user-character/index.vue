<template>
  <div class="character-page">
    <div class="page-header">Your AI character</div>

    <!-- Main Content -->
    <div class="content">
      <template v-if="!avatarStore.avatars.length">
        <div class="no-character-prompt" @click="handleCreate('no-character')">
          <!-- <div class="no-character-prompt-icon">
            <IconCharacterAdd />
          </div>
          <div class="no-character-prompt-title">Create your AI character</div> -->
        </div>
        <div class="no-character-prompt-text">You don't have any AI character yet.</div>
      </template>

      <template v-else>
        <!-- Current Character Detail -->
        <div class="current-character">
          <div
            v-show="currentAvatar && ['finish', 'error', 'failed'].includes(currentAvatar?.status)"
            class="delete-button"
            @click="handleDelete(currentAvatar!, $event, 'top')"
          >
            <div class="delete-icon">×</div>
          </div>
          <div v-if="currentAvatar" class="character-detail">
            <img
              v-if="currentAvatar.status === 'finish' && currentAvatar.image_urls[0]"
              :src="currentAvatar.image_urls[0]"
              class="detail-image"
              alt="Character"
            />
            <div v-if="currentAvatar.status === 'finish'" class="character-info">
              {{ currentAvatar.gender }}, {{ currentAvatar.age }},
              {{ currentAvatar.clothes_style }}
            </div>
            <div
              v-else-if="currentAvatar.status === 'start' || currentAvatar.status === 'submitted'"
              class="generating-state"
            >
              <!-- <div class="rainbow-bg"></div> -->
              <div class="generating-content">
                <div class="generating-text">
                  Generating... {{ avatarStore.fakeProgress[currentAvatar.id] || 0 }}%
                </div>
                <div class="generating-tip">Exit this page will continue generating</div>
              </div>
              <div class="character-info">
                {{ currentAvatar.gender }}, {{ currentAvatar.age }},
                {{ currentAvatar.clothes_style }}
              </div>
            </div>
            <div
              v-else-if="currentAvatar.status === 'error' || currentAvatar.status === 'failed'"
              class="error-state"
            >
              <span>Generation failed</span>
              <div class="character-info">
                {{ currentAvatar.gender }}, {{ currentAvatar.age }},
                {{ currentAvatar.clothes_style }}
              </div>
            </div>
          </div>
          <div
            v-else-if="!avatarStore.avatars.length"
            class="create-prompt"
            @click="showCreateDrawer = true"
          >
            <div class="plus-icon">
              <IconCharacterAdd />
            </div>
            <div class="prompt-text">Create your AI character</div>
          </div>
        </div>

        <!-- History Title -->
        <div class="history-header">
          <div class="history-title">History</div>
          <div class="create-button" @click="handleCreate('history')">
            <IconCharacterAdd />
            <span>Create</span>
          </div>
        </div>

        <!-- Character List -->
        <div class="character-list-wrapper" ref="listWrapperRef">
          <div class="character-list" ref="listRef">
            <div
              v-for="avatar in avatarStore.avatars"
              :key="avatar.id"
              class="character-item"
              :class="{ active: currentAvatar?.id === avatar.id }"
              @click="selectAvatar(avatar, $event)"
            >
              <div
                v-if="['finish', 'error', 'failed'].includes(avatar.status)"
                class="delete-button"
                @click="handleDelete(avatar, $event, 'bottom')"
              >
                <div class="delete-icon">×</div>
              </div>
              <img
                v-if="avatar.status === 'finish' && avatar.image_urls[0]"
                :src="avatar.image_urls[0]"
                class="avatar-image"
                alt="Character"
              />
              <div
                v-else-if="avatar.status === 'start' || avatar.status === 'submitted'"
                class="generating-preview"
              >
                {{ avatarStore.fakeProgress[avatar.id] || 0 }}%
              </div>
              <div v-else class="error-preview">
                <span>Failed</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- Create Character Drawer -->
    <CreateDrawer v-model:visible="showCreateDrawer" @created="handleCharacterCreated" />

    <!-- Delete Confirmation Dialog -->
    <ConfirmDialog
      v-model:visible="showDeleteDialog"
      title="Are you sure you want to delete the photo?"
      cancel-text="No, keep it"
      confirm-text="Yes, delete"
      @confirm="confirmDelete"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import IconCharacterAdd from '@/assets/icon/character-add.svg'
import CreateDrawer from './components/CreateDrawer.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import { useUserAvatarStore } from '@/store/user-character'
import type { UserAvatar } from '@/api/user-character'
import { useGlobalAvatarPolling } from '@/mobile/composables/useGlobalAvatarPolling'
import { useRouter } from 'vue-router'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'

const showCreateDrawer = ref(false)
const showDeleteDialog = ref(false)
const avatarToDelete = ref<UserAvatar | null>(null)
const avatarStore = useUserAvatarStore()
const currentAvatarId = ref<string | null>(null)
const listWrapperRef = ref<HTMLElement>()
const listRef = ref<HTMLElement>()
const route = useRouter()

const { startPolling } = useGlobalAvatarPolling()

const currentAvatar = computed(() => {
  if (!currentAvatarId.value) return null
  return avatarStore.avatars.find((avatar) => avatar.id === currentAvatarId.value) || null
})

onMounted(async () => {
  reportEvent(ReportEvent.UserCharacterPageView)
  await avatarStore.fetchAvatars()
  if (avatarStore.avatars.length > 0) {
    currentAvatarId.value = avatarStore.avatars[0].id
  }
  avatarStore.markAsVisited()
})

const selectAvatar = (avatar: UserAvatar, event: MouseEvent) => {
  currentAvatarId.value = avatar.id

  // Get the clicked element
  const clickedItem = event.currentTarget as HTMLElement
  const list = listRef.value

  if (clickedItem && list) {
    // Calculate the center position
    const listRect = list.getBoundingClientRect()
    const itemRect = clickedItem.getBoundingClientRect()

    // Calculate the target scroll position to center the item
    const itemCenterX = itemRect.left + itemRect.width / 2
    const listCenterX = listRect.left + listRect.width / 2
    const scrollDiff = itemCenterX - listCenterX

    // Add the scroll difference to current scroll position
    list.scrollBy({
      left: scrollDiff,
      behavior: 'smooth'
    })
  }
}

const handleCharacterCreated = async (avatar: UserAvatar) => {
  await avatarStore.fetchAvatars()
  currentAvatarId.value = avatar.id
  // 创建新头像后立即开始轮询
  startPolling()

  // 等待DOM更新后滚动到新创建的角色（列表最左边）
  await nextTick()
  const list = listRef.value
  if (list) {
    list.scrollTo({
      left: 0,
      behavior: 'smooth'
    })
  }
}

const handleCreate = (type: 'no-character' | 'history') => {
  reportEvent(ReportEvent.ClickCreateCharacter, {
    type
  })
  showCreateDrawer.value = true
}

const handleDelete = (avatar: UserAvatar, event?: MouseEvent, position?: 'top' | 'bottom') => {
  event?.stopPropagation()
  reportEvent(ReportEvent.ClickDeleteCharacter, {
    character: avatar.id,
    position
  })
  avatarToDelete.value = avatar
  showDeleteDialog.value = true
}

const confirmDelete = async () => {
  reportEvent(ReportEvent.ClickDeleteCharacterConfirm)
  if (!avatarToDelete.value) return
  await avatarStore.removeAvatar(avatarToDelete.value.id)
  if (currentAvatarId.value === avatarToDelete.value.id) {
    const index = avatarStore.avatars.findIndex((avatar) => avatar?.id === currentAvatarId.value)
    
    // 优先选择下一个头像
    if (index < avatarStore.avatars.length - 1) {
      currentAvatarId.value = avatarStore.avatars[index + 1].id
    }
    // 如果是最后一个，则选择前一个
    else if (index > 0) {
      currentAvatarId.value = avatarStore.avatars[index - 1].id
    }
    // 如果只有一个头像，选择第一个
    else if (avatarStore.avatars.length > 0) {
      currentAvatarId.value = avatarStore.avatars[0].id
    }
  }
}
</script>

<style lang="less" scoped>
.character-page {
  min-height: calc(var(--vh, 1vh) * 100);
  background: #1f0038;
  color: white;
  padding: 0 16px;
  display: flex;
  flex-direction: column;
}

.page-header {
  font-size: 20px;
  font-weight: 600;
  padding: 16px 0;
  text-align: center;
  flex-shrink: 0;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-bottom: 16px;
  min-height: 0;
  position: relative;
}

.no-character-prompt {
  height: 140px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: url('https://static.playshot.ai/static/images/user/character/create-v1.png')
    center/cover;
  margin-bottom: 6px;
  border-radius: 12px;
  &-icon {
    width: 36px;
    height: 36px;
  }
  &-title {
    color: #fff;
    font-size: 20px;
    font-weight: 700;
  }
  &-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: rgba(255, 255, 255, 0.5);
    font-size: 13px;
    font-weight: 400;
    white-space: nowrap;
  }
}

.current-character {
  width: 100%;
  flex: 2;
  min-height: 0;
  border-radius: 16px;
  overflow: hidden;
  position: relative;

  .delete-button {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 2;
    transition: background-color 0.2s ease;

    &:active {
      background: rgba(0, 0, 0, 0.7);
    }

    .delete-icon {
      color: white;
      font-size: 24px;
      line-height: 1;
      transform: translateY(-1px);
    }
  }
}

.character-detail {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: #4c3360;

  .error-state {
    color: rgba(255, 255, 255, 0.6);
    text-align: center;
    font-size: 22px;
    font-weight: 500;
    opacity: 0.5;
  }
}

.detail-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: transparent;
}

.character-info {
  display: flex;
  align-items: flex-end;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  font-size: 13px;
  color: white;
  z-index: 1;
  font-weight: 600;
  padding: 15px;
  height: 80px;
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    opacity: 0.7;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000 100%);
    z-index: -1;
  }
}

.generating-state {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background: url('https://static.playshot.ai/static/images/user/character/detail-bg.png')
    center/cover;

  .rainbow-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      45deg,
      #ff69b4,
      #4b0082,
      #0000ff,
      #00ff00,
      #ffff00,
      #ff7f00,
      #ff0000
    );
    background-size: 200% 200%;
    animation: rainbow 10s linear infinite;
    filter: blur(20px);
    opacity: 0.3;
  }

  .generating-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    width: 100%;
    padding: 0 20px;

    .generating-text {
      font-size: 22px;
      font-weight: 600;
      margin-bottom: 22px;
      color: #1f0038;
    }

    .generating-tip {
      font-size: 12px;
      opacity: 0.5;
      color: #1f0038;
    }
  }
}

.history-header {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .history-title {
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
  }

  .create-button {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 10px;
    border-radius: 40px;
    background: #ca93f2;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 14px;
    color: #1f0038;
    font-weight: 500;
    &:active {
      background: #ca93f2;
    }

    :deep(svg) {
      width: 16px;
      height: 16px;
    }

    :deep(svg path) {
      fill: #1f0038;
    }
  }
}

.character-list-wrapper {
  flex: 1;
  min-height: 105px;
  position: relative;
  margin: 0 -16px;
  padding: 0 16px;
  overflow: hidden;
}

.character-list {
  display: flex;
  gap: 12px;
  padding-bottom: 16px;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;

  &::-webkit-scrollbar {
    display: none;
  }
}

.create-prompt {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  gap: 16px;
  background: url('https://static.playshot.ai/static/images/user/character/detail-bg.png')
    center/cover;
  transition: background-color 0.2s ease;

  &:active {
    background: rgba(255, 255, 255, 0.08);
  }

  .plus-icon {
    width: 48px;
    height: 48px;
    border-radius: 24px;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    color: black;
    font-size: 24px;

    :deep(svg path) {
      fill: #1f0038;
      opacity: 0.7;
    }
  }

  .prompt-text {
    font-size: 12px;
    font-weight: 500;
    color: #1f0038;
    opacity: 0.5;
  }
}

.character-item {
  width: 105px;
  height: 105px;
  flex-shrink: 0;
  border-radius: 12px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.2s ease;
  position: relative;

  &.active {
    border-color: #ca93f2;
  }

  .avatar-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background: #4c3360;
  }

  .generating-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1f0038;
    font-weight: 500;
    font-size: 15px;
    background: url('https://static.playshot.ai/static/images/user/character/detail-bg.png')
      center/cover;
  }

  .error-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
  }

  .delete-button {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 24px;
    height: 24px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1;
    transition: background-color 0.2s ease;

    &:active {
      background: rgba(0, 0, 0, 0.7);
    }

    .delete-icon {
      color: white;
      font-size: 18px;
      line-height: 1;
      transform: translateY(-1px);
    }
  }
}

@keyframes rainbow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
</style>
