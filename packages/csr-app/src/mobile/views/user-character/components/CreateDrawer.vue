<template>
  <BaseDrawer
    :visible="visible"
    @update:visible="(val) => emit('update:visible', val)"
    height="auto"
    :mask-color="'rgba(0, 0, 0, 0.6)'"
    background="#1f0038"
  >
    <div class="create-drawer">
      <div class="drawer-header">Create your AI character</div>

      <!-- Step 1: Customize -->
      <div class="step-section">
        <div class="step-title">Step 1, Customize</div>
        <div class="options-row">
          <SelectDrawer
            v-model="gender"
            :options="[
              { value: 'male', label: 'Male' },
              { value: 'female', label: 'Female' }
            ]"
            :style="{ width: '48%' }"
          />
          <SelectDrawer
            v-model="age"
            :options="[
              // { value: 'teenager', label: 'Teenager' },
              { value: 'youth', label: 'Youth' },
              { value: 'middle-age', label: 'Middle-age' }
            ]"
            :style="{ width: '48%' }"
          />
        </div>
      </div>

      <!-- Step 2: Select clothing style -->
      <div class="step-section">
        <div class="step-title">Step 2, Select style</div>
        <div class="style-grid">
          <div
            v-for="style in clothingStyles"
            :key="style.id"
            class="style-item"
            :class="{ active: selectedStyle === style.id }"
            @click="selectedStyle = style.id"
          >
            <img class="style-image" :src="style.image" :alt="style.name" />
            <span class="style-name">{{ style.name }}</span>
          </div>
        </div>
      </div>

      <!-- Step 3: Upload photo -->
      <div class="step-section">
        <div class="step-title">Step 3, Upload your photo (optional)</div>
        <div class="upload-section" @click="handleUpload">
          <div class="upload-box">
            <img
              v-if="!photoUrl"
              src="https://static.playshot.ai/static/images/user/character/upload-photo.png"
              alt="Uploaded photo"
            />
            <template v-else>
              <img :src="photoUrl" class="uploaded-photo" alt="Uploaded photo" />
              <div class="delete-btn" @click.stop="handleDeletePhoto">
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"
                    fill="currentColor"
                  />
                </svg>
              </div>
            </template>
          </div>
        </div>
      </div>

      <!-- Generate Button -->
      <Button :loading="generating" class="generate-btn" @click="handleGenerate"> Generate </Button>
    </div>
  </BaseDrawer>

  <FaceRequirementsModal
    :loading="uploading"
    :loading-text="loadingText"
    v-model:visible="showFaceModal"
    @select="triggerFileUpload"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Message } from '@/mobile/components/Message'
import BaseDrawer from '@/mobile/components/BaseDrawer.vue'
import SelectDrawer from '@/mobile/components/SelectDrawer.vue'
import FaceRequirementsModal from '@/mobile/components/FaceRequirementsModal/index.vue'
import LoadingOverlay from '@/mobile/components/LoadingOverlay.vue'
import { useUserAvatarStore } from '@/store/user-character'
import type { GenderType, AgeType, ClothesStyleType, UserAvatar } from '@/api/user-character'
import Button from '@/mobile/components/Button.vue'
import { useFileUpload } from '@/mobile/composables/useFileUpload'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface/report'

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'created', avatar: UserAvatar): void
}>()

const avatarStore = useUserAvatarStore()

// Form data
const gender = ref<GenderType>('male')
const age = ref<AgeType>('youth')
const selectedStyle = ref<ClothesStyleType>('yamato')
const photoUrl = ref('')
const generating = ref(false)
const showFaceModal = ref(false)

const { uploadedUrl, triggerFileUpload, uploading, loadingText } = useFileUpload({
  requireFaceDetect: true,
  uploadUrlEndpoint: '/api/v1/image.upload',
  uploadMethod: 'PUT',
  onSuccess: (url) => {
    photoUrl.value = url
    showFaceModal.value = false
    Message.success('Photo uploaded successfully')
    reportEvent(ReportEvent.UploadImageToCreateCharacterSuccess)
  },
  onError: () => {
    showFaceModal.value = false
    Message.error('Upload failed. Please try again.')
    reportEvent(ReportEvent.UploadImageToCreateCharacterFailed)
  }
})

// Clothing styles data
const clothingStyles: Array<{
  id: ClothesStyleType
  name: string
  image: string
}> = [
  // {
  //   id: 'suit',
  //   name: 'Suit',
  //   image: 'https://static.playshot.ai/static/images/user/character/suit-v1.png'
  // }
  {
    id: 'yamato',
    name: 'Yamato',
    image: 'https://static.playshot.ai/static/images/user/character/yamato.png'
  }
  // {
  //   id: 'agent',
  //   name: 'Agent',
  //   image: 'https://static.playshot.ai/static/images/user/character/agent-v1.png'
  // },
  // {
  //   id: 'gym',
  //   name: 'Gym-goer',
  //   image: 'https://static.playshot.ai/static/images/user/character/gym-goer.png'
  // }
]

// Methods
const handleUpload = () => {
  reportEvent(ReportEvent.ClickSelectFromAlbumToCreateCharacter)
  showFaceModal.value = true
}

const handleGenerate = async () => {
  reportEvent(ReportEvent.ClickCreateCharacter, {
    gender: gender.value,
    age: age.value,
    style: selectedStyle.value,
    image: photoUrl.value
  })
  if (!selectedStyle.value) {
    Message.warning('Please select a clothing style')
    return
  }

  try {
    generating.value = true
    const avatar = await avatarStore.createAvatar({
      gender: gender.value,
      age: age.value,
      style: 'yamato',
      clothes_style: selectedStyle.value,
      image_urls: photoUrl.value ? [photoUrl.value] : null
    })

    emit('created', avatar)
    emit('update:visible', false)
    Message.success('The character generation task was created successfully.')
  } catch (error) {
    Message.error(error instanceof Error ? error.message : 'Failed to create character')
  } finally {
    generating.value = false
  }
}

const handleDeletePhoto = (e: Event) => {
  e.stopPropagation()
  photoUrl.value = ''
}
</script>

<style lang="less" scoped>
.create-drawer {
  padding-bottom: 32px;
  color: white;
}

.drawer-header {
  color: #fff;
  font-size: 15px;
  font-weight: 500;
  text-align: center;
  margin-bottom: 24px;
}

.step-section {
  margin-bottom: 24px;

  .step-title {
    font-size: 13px;
    margin-bottom: 15px;
    color: rgba(255, 255, 255, 0.5);
  }
}

.options-row {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.style-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;

  .style-item {
    aspect-ratio: 1;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    border: 2px solid transparent;

    &.active {
      border-color: #7c4dff;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .style-name {
      position: absolute;
      bottom: 8px;
      left: 50%;
      transform: translateX(-50%);
      color: #fff;
      font-size: 13px;
      font-weight: 600;
      text-shadow:
        1px 1px 0 #000,
        -1px 1px 0 #000,
        1px -1px 0 #000,
        -1px -1px 0 #000;
      white-space: nowrap;
    }
  }
}

.upload-section {
  .upload-box {
    width: 120px;
    height: 120px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    .uploaded-photo {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    &:hover {
      border-color: rgba(255, 255, 255, 0.5);
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 10px;
      transition: opacity 0.3s ease;
    }

    .delete-btn {
      position: absolute;
      top: 0px;
      right: 2px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.8);
        transform: scale(1.1);
      }
    }
  }
}

.generate-btn {
  margin-top: 32px;
}

:deep(.arco-select) {
  background: rgba(255, 255, 255, 0.1);
  border: none;

  .arco-select-view {
    background: transparent;
    border: none;
    color: white;
  }
}

.drawer-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  pointer-events: auto;

  .drawer-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.2);
    z-index: 1;
    cursor: pointer;
    backdrop-filter: blur(2px);
    opacity: 0;
    transition: opacity 0.3s ease;
    &.active {
      opacity: 1;
    }
  }

  .drawer-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #200238;
    border-radius: 16px 16px 0 0;
    padding: 20px;
    transform: translateY(100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    max-height: 90vh;
    overflow-y: auto;
    z-index: 2;

    &.active {
      transform: translateY(0);
    }
  }
}

.face-requirements {
  color: white;

  h3 {
    margin-bottom: 16px;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
  }

  .requirements-container {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 16px;

    .requirement-image {
      flex: 1;
      img {
        max-width: 200px;
        max-height: 200px;
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
      }
    }

    .requirements-list {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 12px;

      .requirement-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        svg {
          width: 20px;
          height: 20px;
        }
        span {
          white-space: nowrap;
        }
      }
    }
  }

  .select-button {
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    background: #ca93f2;
    color: #241d49;
    border: none;
    font-size: 16px;
    font-weight: 600;
    margin-top: 16px;
  }
}

// Drawer transition animations
.drawer-enter-active,
.drawer-leave-active {
  transition: opacity 0.3s ease;
  .drawer-content {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.drawer-enter-from,
.drawer-leave-to {
  opacity: 0;
  .drawer-content {
    transform: translateY(100%);
  }
}

.drawer-enter-to,
.drawer-leave-from {
  opacity: 1;
  .drawer-content {
    transform: translateY(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
