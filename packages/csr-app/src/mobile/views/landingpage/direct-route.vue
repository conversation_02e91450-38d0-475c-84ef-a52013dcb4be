<template>
  <div class="landing-page">
    <div class="landing-page-bg"></div>
    <div class="main-logo">
      <img src="https://static.playshot.ai/static/images/logo/reelplay_logo.png" alt="main-logo" />
    </div>
    <!-- Main Content -->

    <div v-if="config" class="main-content-landingpage">
      <div class="main-content-landingpage-banner">
        <img
          src="https://static.playshot.ai/static/images/banner/reelplay_landingpage.png"
          alt="banner"
          loading="lazy"
          decoding="async"
        />
      </div>
      <div ref="descriptionCard" class="description-card">
        <div class="description-text"></div>
        <div class="character-wrapper" :class="{ loading: isLoading }">
          <!-- Loading indicator -->
          <div v-if="isLoading" class="loading-indicator">
            <div class="spinner"></div>
            <div class="loading-text">Loading...</div>
          </div>
          <div class="content-wrapper" ref="contentWrapper">
            <img
              v-if="!isLoading"
              v-img-compress
              :src="storyStore.currentStory?.preview_url"
              alt="character"
              class="character-image"
              fetchpriority="high"
              loading="eager"
              @load="handleImageLoad"
              @click="handleStartChat('cardButton')"
            />
            <img
              v-if="!isLoading"
              src="https://cdn.magiclight.ai/assets/playshot/landingpage/click-to-play.png"
              alt="click to play"
              class="click-to-play"
              @click="handleStartChat('clickToPlayButton')"
            />
          </div>
          <div class="desc-wrapper">
            <div class="desc-title">
              Description
              <div class="desc-subtitle">{{ storyStore.currentStory?.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Preview Gallery -->
      <!-- <PreviewGallery
        :actors="
          storyStore.stories
            .filter((story) => config.otherStoryIds.includes(story.id))
            .map((story) => ({
              id: story.id,
              name: story.title,
              subtitle: [
                'Your Exclusive Girlfriend',
                'Dream Date Adventure',
                'Flirt, Love, Repeat',
                'Romance Quest Online'
              ][Math.floor(Math.random() * 4)],
              preview_url: story.preview_url,
              avatar_url: story.actors[0]?.avatar_url || '',
              count: story.hot || 0
            }))
        "
        @click="(actor) => handleStartChat('actorButton', String(actor.id))"
      /> -->
    </div>
    <div v-else class="loading-state">
      <div class="loading-indicator">
        <div class="spinner"></div>
        <div class="loading-text">Loading...</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, onUnmounted, getCurrentInstance, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useChatStore, useUserStore, useStoryStore } from '@/store'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { landingPageConfigs } from '@/mobile/routes/landing'

const router = useRouter()
const route = useRoute()

const chatStore = useChatStore()
const userStore = useUserStore()
const storyStore = useStoryStore()

// 根据路由路径获取配置
const config = computed(() => {
  const path = route.path.replace('/', '')
  // 从路径中提取基本角色名称
  const characterKey = Object.keys(landingPageConfigs).reduce((result, key) => {
    const config = landingPageConfigs[key]
    if (path.startsWith(config.characterKey)) {
      return config.characterKey
    }
    return result
  }, 'tsunade') // 默认为 tsunade

  const foundConfig = Object.entries(landingPageConfigs).find(
    ([_, config]) => config.characterKey === characterKey
  )?.[1]
  return foundConfig || landingPageConfigs.character1
})

const isLoading = ref(true)
const cardHeight = ref(0)
const descriptionCard = ref<HTMLElement | null>(null)
const contentWrapper = ref<HTMLElement | null>(null)

const { proxy } = getCurrentInstance()

const initializeStory = async () => {
  try {
    await storyStore.fetchStories()
    const storyIndex = storyStore.stories.findIndex((story) => story.id === config.value.storyId)
    if (storyIndex !== -1) {
      storyStore.setCurrentStory(storyStore.stories[storyIndex])
      storyStore.setCurrentActor(storyStore.stories[storyIndex].actors[0])

      // @ts-ignore
      proxy?.$tracker?.start()
      // @ts-ignore
      proxy?.$tracker?.setUserID(userStore.userInfo?.uuid)

      await storyStore.getPreloadResources(storyStore.currentActor?.id, storyStore.currentStory?.id)
    }
  } finally {
    isLoading.value = false
  }
}

// 监听路由变化时重新初始化
watch(
  () => route.path,
  () => {
    isLoading.value = true
    initializeStory()
  },
  { immediate: true }
)

const handleStartChat = async (
  buttonType: 'actorButton' | 'cardButton' | 'clickToPlayButton',
  storyId?: string
) => {
  reportEvent(ReportEvent.StartChat, {
    userId: userStore.userInfo?.uuid,
    character: config.value.characterKey,
    storyId: storyStore.currentStory?.id,
    actorId: storyStore.currentActor?.id,
    buttonType
  })
  if (window.fbq) {
    window.fbq('trackCustom', 'StartChat', {
      userId: userStore.userInfo?.uuid,
      character: config.value.characterKey,
      storyId: storyStore.currentStory?.id,
      actorId: storyStore.currentActor?.id,
      buttonType
    })
  }
  chatStore.isFirstVideo = false

  if (buttonType === 'actorButton' && storyId) {
    const configEntry = Object.values(landingPageConfigs).find((cfg) => cfg.storyId === storyId)
    if (configEntry) {
      router.push(`/${configEntry.characterKey}`)
    }
    return
  }

  // 直接导航到相应的故事路由
  if (storyStore.currentStory) {
    router.push({
      name: 'StoryIntro',
      params: {
        storyId: storyStore.currentStory.id
      }
    })
  }
}

const calculateSize = async () => {
  await nextTick()
  if (contentWrapper.value) {
    const characterImage = contentWrapper.value.querySelector('.character-image') as HTMLElement
    const imageHeight = characterImage?.offsetHeight || 0
    cardHeight.value = imageHeight
  }
}

const handleImageLoad = () => {
  calculateSize()
}

onMounted(() => {
  calculateSize()
  window.addEventListener('resize', calculateSize)

  const sourceParam = route.query.source as string
  if (sourceParam) {
    localStorage.setItem('visit_source', sourceParam)
  }

  reportEvent(ReportEvent.VisitLandingPage, {
    userId: userStore.userInfo?.uuid,
    character: config.value.characterKey,
    storyId: config.value.storyId
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', calculateSize)
})
</script>

<style lang="less" scoped>
@import '@/assets/style/mixin.less';

.landing-page {
  height: calc(var(--vh, 1vh) * 100);
  background-color: #1f0038;
  color: #fff;
  overflow: auto;
  position: relative;
  z-index: 1;

  &-bg {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: calc(var(--vh, 1vh) * 100);
    background-image: url('https://cdn.magiclight.ai/assets/playshot/landingpage/bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 2;
    pointer-events: none;
  }
}

.main-logo {
  position: relative;
  z-index: 3;
  margin: 12px 16px 0 16px;
  img {
    width: 117px;
    object-fit: contain;
  }
}

.main-content-landingpage {
  position: relative;
  z-index: 3;
  padding: 0 16px;
  &-title {
    color: #ca93f2;
    font-size: 13px;
    font-weight: 500;
  }
  &-banner {
    padding: 16px 0;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}
.character-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border-radius: 16px;
  background: #ca93f2;
  color: #1f0038;
  font-family: 'Work Sans';
  font-size: 13px;
  font-style: normal;
  font-weight: 500;
  line-height: 150%;

  &.loading {
    pointer-events: none;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(4px);
      z-index: 2;
      border-radius: 16px;
    }

    .loading-indicator {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 3;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;

      .spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s linear infinite;
      }

      .loading-text {
        color: #fff;
        font-size: 14px;
        font-weight: 500;
        text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.3);
      }
    }
  }

  .desc-wrapper {
    padding: 16px;
    .desc-title,
    .desc-subtitle {
      color: #1f0038;
      font-family: 'Work Sans';
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.description-card {
  width: 100%;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 16px;
  position: relative;

  .description-text {
    // padding: 10px;
    z-index: 2;
  }

  .content-wrapper {
    width: 100%;
    background-color: #000;
    margin-bottom: 10px;
    position: relative;
    min-height: 400px;
    .character-desc {
      position: absolute;
      bottom: 23px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      flex-direction: column;
      width: 284.846px;
      padding: 9.043px 20px;
      justify-content: center;
      align-items: center;
      gap: 9.043px;
      border-radius: 7.234px;
      border-top: 1.809px solid #1f0038;
      border-right: 1.809px solid #1f0038;
      border-bottom: 5.426px solid #1f0038;
      border-left: 1.809px solid #1f0038;
      background: linear-gradient(180deg, #f0dcff 0%, #daff96 100%);
      box-shadow: 0px 0.904px 5.426px 0px #9e81fe;
      color: #1f0038;
      text-align: center;
      font-family: 'Work Sans';
      font-size: 12.66px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;

      .character-name {
        width: 100%;
        display: flex;
        gap: 8px;
        justify-content: center;
        align-items: center;
        margin-bottom: 4px;
      }

      .info-item {
        display: flex;
        gap: 8px;
        align-items: flex-start;
        width: 100%;
        justify-content: center;

        .label {
          flex: 0 0 80px;
          text-align: right;
          white-space: nowrap;
          // overflow: hidden;
          // text-overflow: ellipsis;
        }

        .value {
          flex: 1;
          text-align: center;
          word-break: break-word;
          min-width: 0;
        }
      }
    }
    .character-image {
      width: 100%;
      display: block;
      cursor: pointer;
      transition: transform 0.2s ease;
      border: 4px solid #ca93f2;
      border-radius: 15px;
      // &:hover {
      //   transform: translateY(-2px);
      // }
    }

    .click-to-play {
      position: absolute;
      top: 80%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 120px;
      height: auto;
      cursor: pointer;
      animation: breathe 2s ease-in-out infinite;
      z-index: 2;
    }
  }
}

@keyframes breathe {
  0% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(0.95);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(0.95);
  }
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;

  .loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    .spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      border-top-color: #fff;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      color: #fff;
      font-size: 14px;
      font-weight: 500;
      text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.3);
    }
  }
}
</style>
