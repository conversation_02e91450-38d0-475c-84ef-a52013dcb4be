<template>
  <div class="friend-request-message" v-if="visible">
    <div class="message-card">
      <!-- 头像 -->
      <div class="avatar">
        <img :src="actorAvatar" :alt="actorName" />
      </div>

      <!-- 消息内容 -->
      <div class="message-content">
        <div class="actor-name">{{ actorName }}</div>
        <div class="message-text">{{ message }}</div>
      </div>

      <!-- 接受按钮 -->
      <button class="accept-button" @click="handleAccept">
        <CheckIcon class="check-icon" />
        <span>Accept</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, withDefaults } from 'vue'
import CheckIcon from '@/assets/icon/correct.svg'

interface Props {
  visible: boolean
  actorName: string
  actorAvatar: string
  message?: string
}

withDefaults(defineProps<Props>(), {
  message: 'wants to be friends with you'
})

const emit = defineEmits<{
  accept: []
  close: []
}>()

const handleAccept = () => {
  emit('accept')
}
</script>

<style lang="less" scoped>
.friend-request-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 200; /* 提高 z-index，确保显示在 chat-button-container (z-index: 150) 之上 */
  animation: fadeIn 0.3s ease-out;
  width: calc(100% - 20px);
  max-width: 400px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.message-card {
  background: linear-gradient(180deg, rgba(214, 202, 254, 0) 0%, #ca93f2 100%);
  border-radius: 16px;
  box-shadow: 0px 2px 12px 0px rgba(176, 152, 255, 1);
  padding: 16px 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(20px);
}

.avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.message-content {
  flex: 1;
  min-width: 0;

  .actor-name {
    font-family: 'Work Sans', sans-serif;
    font-weight: 600;
    font-size: 15px;
    line-height: 1.17;
    color: #1f0038;
    margin-bottom: 2px;
  }

  .message-text {
    font-family: 'Work Sans', sans-serif;
    font-weight: 400;
    font-size: 13px;
    line-height: 1.38;
    color: #1f0038;
    opacity: 0.9;
  }
}

.accept-button {
  background: linear-gradient(180deg, #f5ffe2 0%, #daff96 100%);
  border: 2px solid #1f0038;
  border-bottom: 4px solid #1f0038;
  border-radius: 26px;
  box-shadow: 0px 1.86px 11.13px 0px rgba(218, 255, 150, 1);
  padding: 6px 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0px 3px 15px 0px rgba(218, 255, 150, 0.8);
  }

  &:active {
    transform: translateY(1px);
    border-bottom: 2px solid #1f0038;
  }

  .check-icon {
    width: 16px;
    height: 16px;
    color: #1f0038;
  }

  span {
    font-family: 'Work Sans', sans-serif;
    font-weight: 600;
    font-size: 14px;
    line-height: 1.17;
    color: #1f0038;
  }
}
</style>
