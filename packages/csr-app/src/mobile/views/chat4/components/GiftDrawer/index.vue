<template>
  <div v-if="visible" class="gift-drawer-overlay" @click="handleClose">
    <div class="gift-drawer" @click.stop @click="handleClickOutside">
      <!-- 头部 -->
      <div class="drawer-header">
        <h3 class="drawer-title">Send Gift</h3>
        <button class="close-button" @click="handleClose">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
              d="M18 6L6 18M6 6l12 12"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>

      <!-- 礼物网格 -->
      <div v-if="!loading && !error" class="gift-grid">
        <div
          v-for="gift in presents"
          :key="gift.id"
          class="gift-item"
          :class="{ selected: selectedGift?.id === gift.id }"
          @click="selectGift(gift)"
        >
          <div class="gift-image">
            <img :src="gift.image_url" :alt="gift.title" />
          </div>
          <div class="gift-name">{{ gift.title }}</div>
          <div class="gift-price">
            <img
              :src="icons.diamond.value"
              alt="Diamond"
              class="diamond-icon"
            />
            <span class="price-text">{{ gift.coins }}</span>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading gifts...</div>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="error-state">
        <div class="error-text">{{ error }}</div>
        <button class="retry-button" @click="fetchPresents">Retry</button>
      </div>

      <!-- 底部 -->
      <div class="drawer-footer">
        <!-- 左侧：余额和Top up -->
        <div class="balance-section">
          <div class="balance-info">
            <img
              src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
              alt="Diamond"
              class="diamond-icon"
            />
            <span class="balance-text">{{ userBalance }}</span>
          </div>
          <button class="top-up-button" @click="handleTopUp">
            <span class="top-up-text">top up ></span>
          </button>
        </div>

        <!-- 操作区域：数量选择器和发送按钮 -->
        <div class="action-section">
          <div class="quantity-section">
            <div
              class="quantity-selector"
              :class="{ active: showQuantitySelector }"
              @click="toggleQuantitySelector"
            >
              <div class="quantity-info">
                <span class="quantity-label">×</span>
                <span class="quantity-text">{{ giftQuantity }}</span>
              </div>
              <svg
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                class="dropdown-icon"
              >
                <path
                  d="M6 9l6 6 6-6"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>

            <!-- 数量选择弹窗 -->
            <div v-if="showQuantitySelector" class="quantity-dropdown">
              <div v-if="!isCustomQuantityMode" class="quantity-options">
                <div
                  v-for="option in quantityOptions"
                  :key="option"
                  class="quantity-option"
                  @click.stop="selectQuantity(option)"
                >
                  {{ option }}
                </div>
                <div
                  class="quantity-option custom"
                  @click.stop="selectCustomQuantity"
                >
                  Custom
                </div>
              </div>
              <div v-else class="custom-quantity-input">
                <input
                  ref="customInput"
                  v-model="customQuantity"
                  type="number"
                  min="1"
                  max="999"
                  placeholder="Enter quantity"
                  class="custom-input"
                  @keyup.enter="confirmCustomQuantity"
                />
                <div class="custom-actions">
                  <button class="custom-cancel" @click="cancelCustomQuantity"
                    >Cancel</button
                  >
                  <button class="custom-confirm" @click="confirmCustomQuantity"
                    >OK</button
                  >
                </div>
              </div>
            </div>

            <button
              class="send-button"
              :disabled="!selectedGift || sending"
              @click="handleSendGift"
            >
              <span class="send-text">{{
                sending ? 'Sending...' : 'Send'
              }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { useUserStore } from '@/store/user'
import { useChatEventsStore } from '@/store/chat-events'
import { useRechargeStore } from '@/store/recharge'
import { getPresentsListAPI, type Present } from '@/api/chat-multivariate'
import { useIconConfig } from '@/composables/useIconConfig'

// Props
interface Props {
  visible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'gift-sent': [gift: Present, quantity: number]
}>()

// Store
const userStore = useUserStore()
const chatEventsStore = useChatEventsStore()
const rechargeStore = useRechargeStore()

// 图标配置
const { icons } = useIconConfig()

// 状态
const presents = ref<Present[]>([])
const selectedGift = ref<Present | null>(null)
const loading = ref(false)
const error = ref('')
const sending = ref(false)
// 用户钻石余额 - 使用真实数据
const userBalance = computed(() => userStore.userInfo?.coins || 0)

// 新增状态：礼物数量相关
const giftQuantity = ref(1)
const showQuantitySelector = ref(false)
const customQuantity = ref('')
const isCustomQuantityMode = ref(false)

// 预设数量选项
const quantityOptions = [1, 3, 88, 999]

// 检查是否可以购买选中的礼物
const canBuySelectedGift = computed(() => {
  if (!selectedGift.value) return false
  const totalCost = selectedGift.value.coins * giftQuantity.value
  return userBalance.value >= totalCost
})

// 获取礼物列表
const fetchPresents = async () => {
  loading.value = true
  error.value = ''

  try {
    console.log('Fetching presents list from API...')
    const response = await getPresentsListAPI()

    if (response.data.data?.present_list) {
      presents.value = response.data.data.present_list
      console.log(
        'Presents loaded successfully:',
        presents.value.length,
        'items',
      )
      // 默认选择第一个礼物
      if (presents.value.length > 0) {
        selectedGift.value = presents.value[0]
      }
    } else {
      throw new Error(response.data.message || 'Failed to load presents')
    }
  } catch (err: any) {
    console.error('Failed to fetch presents:', err)
    error.value =
      err.response?.data?.message || err.message || 'Failed to load gifts'
  } finally {
    loading.value = false
  }
}

// 选择礼物
const selectGift = (gift: Present) => {
  selectedGift.value = gift
}

// 处理Top up点击
const handleTopUp = () => {
  rechargeStore.showRechargeModal()
}

// 切换数量选择器显示状态
const toggleQuantitySelector = () => {
  showQuantitySelector.value = !showQuantitySelector.value
}

// 选择预设数量
const selectQuantity = (quantity: number) => {
  giftQuantity.value = quantity
  isCustomQuantityMode.value = false
  customQuantity.value = ''
  showQuantitySelector.value = false
}

// 选择自定义数量模式
const selectCustomQuantity = () => {
  isCustomQuantityMode.value = true
  customQuantity.value = giftQuantity.value.toString()
  // 使用setTimeout确保DOM更新完成
  setTimeout(() => {
    const customInput = document.querySelector(
      '.custom-input',
    ) as HTMLInputElement
    if (customInput) {
      customInput.focus()
      customInput.select()
    }
  }, 50)
}

// 确认自定义数量
const confirmCustomQuantity = () => {
  const quantity = parseInt(customQuantity.value)
  if (isNaN(quantity) || quantity < 1 || quantity > 999) {
    Message.error('Please enter a valid quantity (1-999)')
    return
  }
  giftQuantity.value = quantity
  isCustomQuantityMode.value = false
  showQuantitySelector.value = false
  customQuantity.value = ''
}

// 取消自定义数量
const cancelCustomQuantity = () => {
  isCustomQuantityMode.value = false
  customQuantity.value = ''
}

// 发送礼物
const handleSendGift = async () => {
  if (!selectedGift.value || sending.value) return

  // 如果余额不足，弹出充值弹窗
  if (!canBuySelectedGift.value) {
    rechargeStore.showRechargeModal()
    return
  }

  try {
    sending.value = true

    console.log('Sending gift:', {
      presentId: selectedGift.value.id,
      title: selectedGift.value.title,
      quantity: giftQuantity.value,
    })

    // 发送礼物数据到服务器（不产生聊天消息）- 优化：一次性发送带数量参数
    await chatEventsStore.sendMessage(
      '', // 空消息文本，不在聊天中显示
      null, // optionId
      'config', // 使用config类型，不会在聊天中显示
      false, // isTelepathyComplete
      null, // sceneId
      800, // delay
      false, // isJump
      { 
        present_id: selectedGift.value.id,
        present_nums: giftQuantity.value // 添加礼物数量参数
      }, // extraData - present_id 和 present_nums 会被添加到 data 中
    )

    // 触发礼物发送事件，这会添加礼物消息到聊天列表
    emit('gift-sent', selectedGift.value, giftQuantity.value)

    // 更新用户余额信息
    try {
      await userStore.getUserInfo()
    } catch (error) {
      console.error('Failed to update user balance:', error)
      // 即使更新余额失败，也不影响礼物发送的成功状态
    }

    handleClose()
  } catch (err: any) {
    console.error('Failed to send gift:', err)
    const errorMessage = err.message || 'Network error, please try again'
    Message.error(errorMessage)
  } finally {
    sending.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  selectedGift.value = null
  // 重置数量选择器状态
  giftQuantity.value = 1
  showQuantitySelector.value = false
  isCustomQuantityMode.value = false
  customQuantity.value = ''
}

// 点击外部关闭数量选择器
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.quantity-section')) {
    showQuantitySelector.value = false
    isCustomQuantityMode.value = false
  }
}

// 监听visible变化
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      if (presents.value.length === 0) {
        fetchPresents()
      } else {
        // 如果已经有礼物列表但没有选中的礼物，设置默认选择
        if (!selectedGift.value && presents.value.length > 0) {
          selectedGift.value = presents.value[0]
        }
      }
    }
  },
)

// 组件挂载时获取礼物列表
onMounted(() => {
  if (props.visible) {
    fetchPresents()
  }
})
</script>

<style scoped lang="less">
.gift-drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.gift-drawer {
  background: #ffffff;
  border-radius: 20px 20px 0 0;
  width: 100%;
  max-width: 375px;
  max-height: 70vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  position: relative;

  .drawer-title {
    color: #333333;
    font-weight: 600;
    font-size: 18px;
    margin: 0;
  }

  .close-button {
    position: absolute;
    right: 16px;
    background: none;
    border: none;
    color: #666666;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      color: #333333;
      background: rgba(0, 0, 0, 0.05);
    }
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.gift-grid {
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
  justify-items: center;
}

.gift-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 77px;
  padding: 4px 8px;
  gap: 2px;
  border: 1.5px solid transparent;
  border-radius: 12px;

  &.selected {
    background: rgba(202, 147, 242, 0.15);
    border-color: #ca93f2;
  }

  .gift-image {
    width: 52px;
    height: 52px;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 2px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .gift-name {
    font-weight: 400;
    font-size: 12px;
    color: #333333;
    text-transform: capitalize;
    text-align: center;
    margin-bottom: 2px;
    height: 16px;
    line-height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .gift-price {
    display: flex;
    align-items: center;
    gap: 2px;

    .diamond-icon {
      width: 9px;
      height: 9px;
    }

    .price-text {
      font-weight: 400;
      font-size: 10px;
      color: rgba(0, 0, 0, 0.45);
    }
  }
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  gap: 16px;

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top: 3px solid #daff96;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text,
  .error-text {
    font-family: 'Work Sans', sans-serif;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
  }

  .retry-button {
    padding: 8px 16px;
    background: #daff96;
    border: none;
    border-radius: 8px;
    color: #1f0038;
    font-family: 'Work Sans', sans-serif;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #c5e885;
    }
  }
}

.drawer-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px 24px;
  border-top: 0.5px solid #e0e0e0;
  gap: 16px;

  .balance-section {
    display: flex;
    align-items: center;
    gap: 12px;

    .balance-info {
      display: flex;
      align-items: center;
      gap: 6px;

      .diamond-icon {
        width: 16px;
        height: 16px;
      }

      .balance-text {
        font-weight: 600;
        font-size: 16px;
        color: #333333;
      }
    }

    .top-up-button {
      display: flex;
      align-items: center;
      gap: 4px;
      background: none;
      border: none;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 12px;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(202, 147, 242, 0.1);
      }

      .top-up-text {
        font-weight: 400;
        font-size: 14px;
        color: #ca93f2;
        text-transform: capitalize;
      }

      .top-up-icon {
        width: 12px;
        height: 12px;
        color: #ca93f2;
      }
    }
  }

  .action-section {
    display: flex;
    align-items: center;

    .quantity-section {
      position: relative;
      display: flex;

      .quantity-selector {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 8px;
        padding: 8px 12px 8px 16px;
        border: 1px solid #ca93f2;
        border-radius: 20px 0 0 20px;
        border-right: none;
        background: #ffffff;
        cursor: pointer;
        transition: all 0.2s ease;
        min-width: 70px;

        .quantity-info {
          display: flex;
          align-items: center;
          gap: 2px;
        }

        .quantity-label {
          font-weight: 500;
          font-size: 12px;
          color: #ca93f2;
        }

        .quantity-text {
          font-weight: 500;
          font-size: 14px;
          color: #333333;
        }

        .dropdown-icon {
          width: 12px;
          height: 12px;
          color: #666666;
          transition: transform 0.2s ease;
        }

        &.active .dropdown-icon {
          transform: rotate(180deg);
        }
      }
    }

    .quantity-dropdown {
      position: absolute;
      bottom: 100%;
      left: 0;
      right: 0;
      background: #ffffff;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
      z-index: 1001;
      margin-bottom: 12px;
      border: 1px solid #f0f0f0;
      overflow: hidden;

      .quantity-options {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1px;
        background: #f8f8f8;

        .quantity-option {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 16px 12px;
          background: #ffffff;
          font-weight: 500;
          color: #333333;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: #f0f8ff;
            color: #ca93f2;
            transform: scale(1.02);
          }

          &.custom {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #ca93f2 0%, #b580e8 100%);
            color: #ffffff;
            font-weight: 600;

            &:hover {
              background: linear-gradient(135deg, #b580e8 0%, #a06dd9 100%);
              transform: scale(1.02);
            }
          }
        }
      }
    }

    .custom-quantity-input {
      padding: 12px;
      background: #ffffff;

      .custom-input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #e8e8e8;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        margin-bottom: 8px;
        box-sizing: border-box;
        transition: all 0.2s ease;
        background: #fff;
        color: #000;
        &:focus {
          outline: none;
          border-color: #ca93f2;
          box-shadow: 0 0 0 2px rgba(202, 147, 242, 0.1);
        }

        &::placeholder {
          color: #999999;
          font-weight: 400;
          font-size: 12px;
        }
      }

      .custom-actions {
        display: flex;
        gap: 8px;

        .custom-cancel,
        .custom-confirm {
          flex: 1;
          padding: 6px 12px;
          border: none;
          border-radius: 6px;
          font-size: 12px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .custom-cancel {
          background: #f5f5f5;
          color: #666666;

          &:hover {
            background: #e8e8e8;
          }
        }

        .custom-confirm {
          background: #ca93f2;
          color: #ffffff;

          &:hover {
            background: #b580e8;
          }
        }
      }
    }
  }
}

.send-button {
  padding: 8px 16px 8px 12px;
  background: #ca93f2;
  border: none;
  border-radius: 0 20px 20px 0;
  color: #ffffff;
  font-weight: 500;
  font-size: 14px;
  text-transform: capitalize;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover:not(:disabled) {
    background: #b580e8;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .send-text {
    font-weight: 500;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
