<template>
  <div class="moment-post">
    <div class="post-header">
      <!-- 用户头像 -->
      <div class="user-avatar">
        <img :src="post.user.avatar" :alt="post.user.name" />
      </div>

      <!-- 动态内容 -->
      <div class="post-content">
        <!-- 用户名 -->
        <div class="user-name">{{ post.user.name }}</div>

        <!-- 动态文案 -->
        <div class="post-text">
          <div
            class="text-content"
            :class="{
              expanded: post.isExpanded,
              collapsed: !post.isExpanded && shouldShowToggle,
            }"
          >
            {{ post.content.text }}
          </div>

          <!-- 展开/收起按钮 -->
          <button
            v-if="shouldShowToggle"
            class="toggle-button"
            @click="handleToggleExpand"
          >
            {{ post.isExpanded ? 'Show Less' : 'Show More' }}
          </button>
        </div>

        <!-- 媒体内容 -->
        <div v-if="post.content.source_url" class="post-media">
          <!-- 图片 -->
          <div
            v-if="isImage(post.content.source_url)"
            class="media-image"
            @click="handleMediaClick(post.content.source_url, 'image')"
          >
            <img
              :src="post.content.source_url"
              :alt="'Moment image'"
              @load="handleImageLoad"
              @error="handleImageError"
            />
          </div>

          <!-- 视频 -->
          <div
            v-else-if="isVideo(post.content.source_url)"
            class="media-video"
            @click="handleMediaClick(post.content.source_url, 'video')"
          >
            <video
              :src="post.content.source_url"
              :poster="getVideoPoster(post.content.source_url)"
              preload="metadata"
              @loadedmetadata="handleVideoLoad"
              @error="handleVideoError"
            >
              Your browser does not support the video tag.
            </video>
            <div class="video-play-overlay">
              <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                <circle cx="24" cy="24" r="24" fill="rgba(0, 0, 0, 0.6)" />
                <path d="M18 12L36 24L18 36V12Z" fill="white" />
              </svg>
            </div>
          </div>
        </div>

        <!-- 操作区域 -->
        <div class="post-actions">
          <!-- 评论按钮 -->
          <button class="action-button comment-button" @click="handleComment">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
            >
              <path
                d="M18.3333 3.83333C18.3333 3.09695 17.7363 2.5 17 2.5H2.99996C2.26358 2.5 1.66663 3.09695 1.66663 3.83333V13.6667C1.66663 14.403 2.26358 15 2.99996 15H5.41663V17.0833L9.58329 15H17C17.7363 15 18.3333 14.403 18.3333 13.6667V3.83333Z"
                stroke="black"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M5.83337 8.95837V8.95847"
                stroke="black"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M10 8.95837V8.95847"
                stroke="black"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M14.1666 8.95837V8.95847"
                stroke="black"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
          <!-- 点赞按钮 -->
          <button
            class="action-button like-button"
            :class="{ liked: post.isLiked }"
            @click="handleLike"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
            >
              <path
                d="M6.24996 3.33337C3.71866 3.33337 1.66663 5.38542 1.66663 7.91671C1.66663 12.5 7.08329 16.6667 9.99996 17.636C12.9166 16.6667 18.3333 12.5 18.3333 7.91671C18.3333 5.38542 16.2813 3.33337 13.75 3.33337C12.1998 3.33337 10.8294 4.10292 9.99996 5.28079C9.1705 4.10292 7.80008 3.33337 6.24996 3.33337Z"
                stroke="black"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>

        <!-- 评论列表 -->
        <div
          v-if="post.content.comments && post.content.comments.length > 0"
          class="comments-section"
        >
          <div class="comments-list">
            <div
              v-for="(comment, index) in post.content.comments"
              :key="index"
              class="comment-item"
            >
              <span class="comment-author">{{
                getCommentAuthorName(comment.author)
              }}</span>
              <span class="comment-separator">: </span>
              <span class="comment-text">{{ comment.text }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分割线 -->
    <div class="post-divider"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { MomentPost } from '@/types/moment'

interface Props {
  post: MomentPost
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'like': [postId: string]
  'comment': [postId: string]
  'toggle-expand': [postId: string]
  'media-click': [url: string, type: 'image' | 'video']
}>()

// 判断是否需要显示展开/收起按钮
const shouldShowToggle = computed(() => {
  return props.post.content.text.length > 100
})

// 获取评论作者显示名称
const getCommentAuthorName = (author: string): string => {
  return author === 'user' ? 'You' : author
}

// 处理点赞
const handleLike = () => {
  emit('like', props.post.id || '')
}

// 处理评论
const handleComment = () => {
  emit('comment', props.post.id || '')
}

// 处理展开/收起
const handleToggleExpand = () => {
  emit('toggle-expand', props.post.id || '')
}

// 判断是否为图片
const isImage = (url: string): boolean => {
  const imageExtensions = [
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.webp',
    '.bmp',
    '.svg',
  ]
  const lowerUrl = url.toLowerCase()
  return (
    imageExtensions.some((ext) => lowerUrl.includes(ext)) ||
    lowerUrl.includes('image')
  )
}

// 判断是否为视频
const isVideo = (url: string): boolean => {
  const videoExtensions = [
    '.mp4',
    '.webm',
    '.ogg',
    '.avi',
    '.mov',
    '.wmv',
    '.flv',
    '.mkv',
  ]
  const lowerUrl = url.toLowerCase()
  return (
    videoExtensions.some((ext) => lowerUrl.includes(ext)) ||
    lowerUrl.includes('video')
  )
}

// 获取视频封面图
const getVideoPoster = (url: string): string => {
  // 可以根据视频URL生成封面图URL，或者返回默认封面
  return ''
}

// 处理媒体点击
const handleMediaClick = (url: string, type: 'image' | 'video') => {
  emit('media-click', url, type)
}

// 处理图片加载
const handleImageLoad = (event: Event) => {
  console.log('Image loaded successfully')
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  console.error('Failed to load image')
}

// 处理视频加载
const handleVideoLoad = (event: Event) => {
  console.log('Video metadata loaded')
}

// 处理视频加载错误
const handleVideoError = (event: Event) => {
  console.error('Failed to load video')
}
</script>

<style scoped>
.moment-post {
  margin-bottom: 16px;
}

.post-header {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.post-content {
  flex: 1;
  min-width: 0;
}

.user-name {
  color: #ca92f2;
  font-weight: 600;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.post-text {
  margin-bottom: 8px;
}

.text-content {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4;
  text-align: justify;
  word-break: break-word;
}

.text-content.collapsed {
  display: -webkit-box;
  -webkit-line-clamp: 8;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-content.expanded {
  display: block;
}

.toggle-button {
  color: #8254a4;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.4;
  background: none;
  border: none;
  padding: 0;
  margin-top: 4px;
  cursor: pointer;
  display: block;
}

.post-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  justify-content: flex-end;
}

.action-button {
  width: 20px;
  height: 20px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.like-button.liked svg path {
  stroke: #ff3e3b;
  fill: #ff3e3b;
}

.post-divider {
  height: 1px;
  background: rgba(0, 0, 0, 0.05);
  margin: 16px 0 0 44px;
}

/* 评论区域样式 */
.comments-section {
  margin-top: 12px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 6px;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.comment-item {
  font-size: 14px;
  line-height: 1.4;
  color: rgba(0, 0, 0, 0.8);
}

.comment-author {
  font-weight: 500;
  color: #007aff;
}

.comment-separator {
  color: rgba(0, 0, 0, 0.6);
}

.comment-text {
  color: rgba(0, 0, 0, 0.8);
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
}

/* 媒体内容样式 */
.post-media {
  margin-top: 12px;
  border-radius: 8px;
  overflow: hidden;
}

.media-image {
  position: relative;
  cursor: pointer;
  overflow: hidden;
  margin-bottom: 12px; /* 添加下方空隙 */
}

.media-image img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.2s ease;
}

.media-image:hover img {
  transform: scale(1.02);
}

.media-video {
  position: relative;
  cursor: pointer;
  overflow: hidden;
  margin-bottom: 12px; /* 添加下方空隙 */
  max-height: 300px; /* 限制最大高度，保持合理尺寸 */
}

.media-video video {
  width: 100%;
  height: auto;
  display: block;
  max-height: 300px; /* 限制视频最大高度，保持原始比例 */
}

.video-play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  transition: opacity 0.2s ease;
}

.media-video:hover .video-play-overlay {
  opacity: 0.8;
}
</style>
