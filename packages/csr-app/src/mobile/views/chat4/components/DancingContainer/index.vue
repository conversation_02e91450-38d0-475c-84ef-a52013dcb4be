<template>
  <div class="dancing-container">
    <!-- 增强背景组件 -->
    <EnhancedBackground
      :background-video="backgroundVideo"
      :background-image="backgroundImage"
      :default-image="backgroundImage"
      :transition-mode="'fade'"
      class="background-container"
    />
    <!-- 返回按钮 -->
    <div class="back-button" @click="handleBackClick">
      <IconLeft class="back-icon" />
    </div>

    <!-- 金币显示插槽 -->
    <div class="coin-display">
      <slot name="credit-display" />
    </div>

    <!-- 好感度系统显示插槽 -->
    <div class="stage-container">
      <slot name="stage" />
    </div>

    <!-- 角色对话区域 -->
    <div v-if="shouldShowAnyMessage" class="character-dialogue">
      <div class="dialogue-bubble">
        <!-- 用户消息 -->
        <div v-if="userMessage" class="user-message-content">
          {{ userMessage }}
        </div>

        <!-- 角色消息 -->
        <div v-else-if="shouldShowMessage" class="character-message-wrapper">
          <div class="character-avatar">
            <img :src="characterAvatar" :alt="characterName" />
          </div>
          <div class="dialogue-content">
            <span v-if="isActorThinking" class="thinking-text">
              {{ characterName }} is thinking...
            </span>
            <span v-else>{{ displayMessage }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部行为区域 -->
    <div class="action-area">
      <!-- 行为按钮组 -->
      <div class="action-buttons">
        <!-- 返回按钮 -->
        <!-- 动态加载的舞蹈动作按钮 -->
        <button
          v-for="action in sceneActions.filter((a) => a.key !== 'chat')"
          :key="action.key"
          class="action-btn"
          :class="`${action.key}-btn`"
          @click="handleActionClick(action)"
        >
          <div class="btn-icon">
            <!-- 优先使用服务器返回的 image_url，否则使用本地图标 -->
            <img
              v-if="action.imageUrl"
              :src="action.imageUrl"
              :alt="action.label"
              class="action-icon-image"
            />
            <component v-else :is="getActionIcon(action.icon)" />
          </div>
          <div class="btn-label">
            <span class="btn-text">{{ action.label }}</span>
            <!-- 解锁等级标记 -->
            <div v-if="action.requirement && !isActionUnlocked(action)" class="unlock-level-badge">
              {{ getActionRequiredLevelText(action) }}
            </div>
            <!-- 已解锁的等级标记 -->
            <div v-else-if="action.requirement" class="level-badge">
              {{ getActionRequiredLevelText(action) }}
            </div>
          </div>
        </button>
      </div>
    </div>

    <!-- 底部聊天框 -->
    <div class="bottom-chat-box">
      <!-- 聊天历史按钮 -->
      <div class="chat-history-btn" @click="handleChatClick">
        <svg width="14" height="14" viewBox="0 0 18 18" fill="none">
          <path
            d="M9 0C4.03 0 0 4.03 0 9s4.03 9 9 9 9-4.03 9-9S13.97 0 9 0zm0 16.2c-3.96 0-7.2-3.24-7.2-7.2S5.04 1.8 9 1.8s7.2 3.24 7.2 7.2-3.24 7.2-7.2 7.2z"
            fill="#1F0038"
          />
          <path
            d="M9 4.5v4.5l3.6 2.16"
            stroke="#1F0038"
            stroke-width="1.5"
            stroke-linecap="round"
          />
        </svg>
      </div>

      <!-- 聊天输入区域 -->
      <div class="chat-input-area">
        <input
          v-model="chatMessage"
          placeholder="Type a message ..."
          class="chat-input"
          @keydown.enter.prevent="sendChatMessage"
        />
        <button class="send-button" @click="sendChatMessage" :disabled="!chatMessage.trim()">
          <svg width="12" height="12" viewBox="0 0 16 16" fill="none">
            <path
              d="M1.5 8L14.5 1.5L8 14.5L6.5 8L1.5 8Z"
              fill="currentColor"
              stroke="currentColor"
              stroke-width="1"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- 聊天输入框 -->
    <div v-if="showChatInput" class="chat-input-overlay" @click="closeChatInput">
      <div class="chat-input-container" @click.stop>
        <div class="input-row">
          <div class="input-field">
            <input
              v-model="chatMessage"
              placeholder="Chat here"
              class="message-input"
              @keydown.enter.prevent="sendChatMessage"
              ref="messageInputRef"
            />
          </div>

          <button class="send-btn" @click="sendChatMessage" :disabled="!chatMessage.trim()">
            <svg width="19" height="19" viewBox="0 0 19 19" fill="none">
              <path
                d="M2.5 9.5L16.5 2.5L9.5 16.5L7.5 9.5L2.5 9.5Z"
                stroke="white"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useChatEventsStore } from '@/store/chat-events'
import { useChatMessagesStore } from '@/store/chat-messages'
import { useChat4Store } from '@/store/chat4'
import { SceneUnlockUtils } from '@/types/favorability'
import EnhancedBackground from '@/mobile/views/chat2/components/ChatBackground/EnhancedBackground.vue'

// 图标组件导入
import IconLeft from '@/assets/icon/back-icon.svg'
import IconChat from '@/assets/icon/chat-icon.svg'
import IconKiss from '@/assets/icon/meetup-kiss-icon.svg'
import IconTouch from '@/assets/icon/meetup-touch-icon.svg'
import IconHug from '@/assets/icon/meetup-hug-icon.svg'

interface Props {
  characterName?: string
  characterAvatar?: string
  backgroundVideo?: string
  backgroundImage?: string
  currentScene?: string
}

const props = withDefaults(defineProps<Props>(), {
  characterName: 'Character',
  characterAvatar: '',
  backgroundVideo: '',
  backgroundImage: '',
  currentScene: ''
})

const emit = defineEmits<{
  'back-click': []
  'scene-event': [eventName: string, data: any]
  'message-sent': [message: string]
}>()

// Store
const chatEventsStore = useChatEventsStore()
const chatMessagesStore = useChatMessagesStore()
const chat4Store = useChat4Store()

// 状态
const showChatInput = ref(false)
const chatMessage = ref('')
const messageInputRef = ref<HTMLInputElement>()

// 计算属性

const sceneActions = computed(() => {
  return chat4Store.sceneActionsMenu || []
})

// 解锁检查方法
const isActionUnlocked = (action: any) => {
  // 如果没有解锁要求，默认解锁
  if (!action.requirement || !action.value) return true

  if (action.requirement === 'heart_value') {
    // 检查心值要求
    const currentHeartValue = chatEventsStore.favorabilityState.currentHeartValue || 0
    return currentHeartValue >= action.value
  }

  // 其他类型的要求可以在这里扩展
  return true
}

const getActionRequiredLevelText = (action: any) => {
  if (!action.requirement || !action.value) return ''

  if (action.requirement === 'heart_value') {
    // 将心值要求转换为等级显示
    const { levelInfos } = chatEventsStore.favorabilityState

    // 找到满足心值要求的最低等级
    let requiredLevel = null
    for (const levelInfo of levelInfos) {
      if (levelInfo.heart_value >= action.value) {
        requiredLevel = levelInfo.level
        break
      }
    }

    return requiredLevel ? SceneUnlockUtils.formatLevelText(requiredLevel) : `${action.value}♥`
  }

  return ''
}

// 方法
const getActionIcon = (iconName: string) => {
  const iconMap: Record<string, any> = {
    chat: IconChat,
    kiss: IconKiss,
    touch: IconTouch,
    hug: IconHug,
    left: IconLeft
  }
  return iconMap[iconName] || IconChat
}

// 消息显示逻辑
const displayMessage = computed(() => {
  // Chat4中actor消息通过TTS管理器处理
  const currentDisplayMessage = chat4Store.ttsState.currentDisplayMessage
  return currentDisplayMessage?.content?.text || currentDisplayMessage?.content?.html || ''
})

const shouldShowMessage = computed(() => {
  return !!displayMessage.value
})

const userMessage = computed(() => {
  // 获取最新的用户消息
  const latestUserMessage = chat4Store.getLatestUserMessage()
  return latestUserMessage || ''
})

const shouldShowAnyMessage = computed(() => {
  return shouldShowMessage.value || !!userMessage.value
})

const isActorThinking = computed(() => {
  // Chat4中使用chatMessagesStore的isActorThinking
  return chatMessagesStore.isActorThinking
})

// 事件处理
const handleBackClick = () => {
  emit('back-click')
}

const handleChatClick = () => {
  showChatInput.value = true
  nextTick(() => {
    messageInputRef.value?.focus()
  })
}

const closeChatInput = () => {
  showChatInput.value = false
  chatMessage.value = ''
}

const sendChatMessage = () => {
  if (!chatMessage.value.trim()) return

  emit('message-sent', chatMessage.value.trim())
  chatMessage.value = ''
  showChatInput.value = false
}

const handleActionClick = (action: any) => {
  console.log('Dancing action clicked:', action)

  // 检查是否已解锁
  if (!isActionUnlocked(action)) {
    console.log('Action not unlocked:', action)
    return
  }

  // 发送场景事件
  emit('scene-event', 'action-click', {
    actionKey: action.key,
    actionLabel: action.label,
    scene: 'dancing'
  })
}

// 生命周期
onMounted(() => {
  console.log('DancingContainer mounted')
})

onUnmounted(() => {
  console.log('DancingContainer unmounted')
})
</script>

<style scoped>
.dancing-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.background-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.stage-container {
  position: absolute;
  top: 50px;
  z-index: 10;
}

.coin-display {
  position: absolute;
  top: 61px;
  right: 10px;
  z-index: 10;
}

.character-dialogue {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 5;
  max-width: 80%;
}

.dialogue-bubble {
  background: rgba(0, 0, 0, 0.7);
  border-radius: 20px;
  padding: 16px 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.user-message-content {
  color: #ffffff;
  font-family: 'Work Sans', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4;
  text-align: center;
}

.character-message-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.character-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.dialogue-content {
  flex: 1;
  color: #ffffff;
  font-family: 'Work Sans', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4;
}

.thinking-text {
  font-style: italic;
  opacity: 0.8;
}

.action-area {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  padding: 0 16px 84px;
}

.action-buttons {
  display: flex;
  justify-content: right;
  align-items: flex-end;
  gap: 20px;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }

  &:active {
    transform: scale(0.95);
  }
}

.chat-btn,
.action-btn:not(.back-btn) {
  .btn-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #f5ffe2 0%, #daff96 100%);
    border: 1px solid #1f0038;
    border-radius: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 2px 2px 0px 0px rgba(115, 99, 166, 0.5);
    position: relative;

    .action-icon-image {
      width: 31.37px;
      height: 35.93px;
      object-fit: contain;
    }

    svg {
      width: 31.37px;
      height: 35.93px;
      fill: #ca92f2;
    }
  }

  .btn-label {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
  }

  .btn-text {
    background: linear-gradient(135deg, #ffdbd3 0%, #ffb762 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    border: 1px solid #1f0038;
    border-radius: 1.4px;
    padding: 3px 6px;
    font-family: 'Work Sans', sans-serif;
    font-weight: 500;
    font-size: 8px;
    line-height: 1.17;
    text-align: center;
    text-transform: uppercase;
    min-width: 31px;
    height: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.unlock-level-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #35f7ff 0%, #814bff 100%);
  border: 1px solid #ffffff;
  border-radius: 8px;
  padding: 0px 4px;
  font-family: 'Work Sans', sans-serif;
  font-weight: 500;
  font-size: 8px;
  line-height: 1.17;
  color: #daff96;
  text-align: center;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s ease-in-out infinite;
}

.level-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #35f7ff 0%, #814bff 100%);
  border: 1px solid #ffffff;
  border-radius: 8px;
  padding: 0px 4px;
  font-family: 'Work Sans', sans-serif;
  font-weight: 500;
  font-size: 8px;
  line-height: 1.17;
  color: #daff96;
  text-align: center;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-input-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: flex-end;
  z-index: 100;
}

.chat-input-container {
  width: 100%;
  background: #f6f6f6;
  padding: 8px 15px;
  height: 52px;
  display: flex;
  align-items: center;

  .input-row {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;

    .input-field {
      flex: 1;
      background: #4c3c59;
      border-radius: 20px;
      box-shadow: 0px 0px 10px 0px rgba(218, 255, 150, 0.15);
      padding: 0px 3px 0px 15px;
      height: 36px;
      display: flex;
      align-items: center;

      .message-input {
        flex: 1;
        background: transparent;
        border: none;
        outline: none;
        color: #ffffff;
        font-family: 'Work Sans', sans-serif;
        font-weight: 400;
        font-size: 12px;
        line-height: 1.17;

        &::placeholder {
          color: rgba(255, 255, 255, 0.7);
        }
      }
    }

    .send-btn {
      width: 36px;
      height: 36px;
      background: #ca93f2;
      border: none;
      border-radius: 20px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0.5;
      transition: opacity 0.3s ease;

      &:not(:disabled) {
        opacity: 1;
      }

      &:disabled {
        cursor: not-allowed;
      }

      svg {
        width: 18.57px;
        height: 18.57px;
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 返回按钮样式 */
.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 20;
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.back-icon {
  color: white;
}

/* 重新定位金币和好感度显示 */
.coin-display {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.stage-container {
  position: absolute;
  top: 80px;
  right: 20px;
  z-index: 10;
}

/* 底部聊天框样式 - 重新设计避免冲突 */
.bottom-chat-box {
  position: absolute;
  bottom: 20px;
  width: 100%;
  height: 44px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 22px;
  display: flex;
  align-items: center;
  padding: 0 6px;
  gap: 8px;
  z-index: 15;
}

.chat-history-btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.chat-history-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.05);
}

.chat-history-btn svg {
  width: 14px;
  height: 14px;
}

.chat-input-area {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: white;
  font-size: 12px;
  font-family: 'Work Sans', sans-serif;
  min-width: 0;
}

.chat-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.send-button {
  width: 28px;
  height: 28px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #1f0038;
  flex-shrink: 0;
}

.send-button svg {
  width: 12px;
  height: 12px;
}

.send-button:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.05);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.send-button:disabled:hover {
  transform: none;
}
</style>
