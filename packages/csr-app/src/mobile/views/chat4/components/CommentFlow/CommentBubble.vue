<template>
  <div
    class="comment-bubble"
    :class="{
      highlight: comment.type === 'highlight',
      system: comment.type === 'system',
      streamer: comment.type === 'streamer',
      self: comment.type === 'self',
      join: comment.type === 'join',
    }"
  >
    <!-- 加入房间提醒 -->
    <div v-if="comment.type === 'join'" class="join-content">
      <span class="join-message">{{ comment.content }}</span>
    </div>

    <!-- 普通弹幕 -->
    <div v-else class="comment-content">
      <span class="username">{{ comment.username }}</span>
      <span class="separator">:</span>
      <span class="message">{{ comment.content }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

interface Comment {
  id: string
  username: string
  content: string
  type: 'normal' | 'highlight' | 'system' | 'streamer' | 'self' | 'join'
}

interface Props {
  comment: Comment
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'animation-complete': [commentId: string]
}>()

onMounted(() => {
  // 弹幕不再自动消失，移除自动消失逻辑
  // setTimeout(() => {
  //   emit('animation-complete', props.comment.id)
  // }, 5000) // 5秒后触发消失
})
</script>

<style lang="less" scoped>
.comment-bubble {
  max-width: 406px;
  width: fit-content;
  padding: 8px 12px;
  border-radius: 16px;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  margin-bottom: 8px;
  animation: slideIn 0.3s ease-out;

  // 其他用户的弹幕（默认样式）
  &.normal {
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
  }

  // 主播的弹幕
  &.streamer {
    background: rgba(255, 215, 0, 0.2);
    border: 1px solid rgba(255, 215, 0, 0.5);
    backdrop-filter: blur(10px);
  }

  // 自己的弹幕
  &.self {
    background: #ca93f2;
    color: #daff96;
    border: 1px solid transparent;
  }

  // 高亮弹幕
  &.highlight {
    background: rgba(202, 147, 242, 0.2);
    border: 1px solid rgba(202, 147, 242, 0.5);
  }

  // 系统消息
  &.system {
    background: rgba(218, 255, 150, 0.2);
    border: 1px solid rgba(218, 255, 150, 0.5);
  }

  // 加入房间提醒
  &.join {
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid transparent;
    padding: 6px 10px;
  }
}

.comment-content {
  display: flex;
  align-items: flex-start;
  gap: 4px;
  flex-wrap: wrap;

  .username {
    font-size: 13px;
    font-weight: 600;
    line-height: 1.17;
    color: #ca93f2;
    flex-shrink: 0;
  }

  .separator {
    font-size: 13px;
    font-weight: 400;
    line-height: 1.17;
    flex-shrink: 0;
  }

  .message {
    font-size: 13px;
    font-weight: 400;
    line-height: 1.17;
    color: white;
    word-break: break-word;
    flex: 1;
  }
}

// 加入房间提醒样式
.join-content {
  display: flex;
  align-items: center;
  justify-content: left;

  .join-message {
    font-family: 'Work Sans', sans-serif;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.17;
    color: #000;
  }
}

// 不同类型弹幕的用户名颜色
.comment-bubble {
  &.streamer .username {
    color: #ffd700; // 主播用户名金色
  }

  &.self .username {
    color: #daff96; // 自己的用户名
  }

  &.highlight .username {
    color: #ca93f2; // 高亮弹幕用户名紫色
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 移除自动淡出动画，弹幕不再自动消失
.comment-bubble {
  animation: slideIn 0.3s ease-out;
}

// @keyframes fadeOut {
//   from {
//     opacity: 1;
//     transform: translateX(0);
//   }
//   to {
//     opacity: 0;
//     transform: translateX(20px);
//   }
// }
</style>
