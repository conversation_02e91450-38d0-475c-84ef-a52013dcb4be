<template>
  <div class="moment-container">
    <!-- 背景图 -->
    <div
      class="background-image"
      :style="{ backgroundImage: `url(${backgroundImage})` }"
    ></div>

    <!-- 遮罩层 -->
    <div class="overlay"></div>

    <!-- 标题栏 -->
    <div class="header" :style="{ backgroundColor: headerBackgroundColor }">
      <div class="header-left">
        <button class="back-button" @click="handleBackClick">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
              d="M15 18L9 12L15 6"
              :stroke="headerIconColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
      <div class="header-center">
        <h1 class="title" :style="{ color: headerTextColor }">Moments</h1>
      </div>
      <div class="header-right">
        <button class="sound-button" @click="handleSoundToggle">
          <!-- 未静音状态 -->
          <svg
            v-if="!audioManager.isMuted"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M12 4.5L8.25 8.25H4.5V15.75H8.25L12 19.5V4.5Z"
              :stroke="headerIconColor"
              stroke-width="1.5"
              :fill="headerIconColor"
            />
            <path
              d="M15.75 8.25C16.65 9.15 17.25 10.35 17.25 12C17.25 13.65 16.65 14.85 15.75 15.75"
              :stroke="headerIconColor"
              stroke-width="1.5"
              stroke-linecap="round"
            />
          </svg>
          <!-- 静音状态 -->
          <svg v-else width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
              d="M12 4.5L8.25 8.25H4.5V15.75H8.25L12 19.5V4.5Z"
              :stroke="headerIconColor"
              stroke-width="1.5"
              :fill="headerIconColor"
            />
            <path
              d="M18 9L21 12M21 9L18 12"
              :stroke="headerIconColor"
              stroke-width="1.5"
              stroke-linecap="round"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- 用户头像 - 独立于content之外 -->
    <div
      class="user-avatar-large"
      :style="{
        opacity: userInfoOpacity,
        top: `${contentMarginTop + 20}px`,
      }"
    >
      <img :src="characterAvatar" :alt="characterName" />
    </div>

    <!-- 主内容区域 -->
    <div class="content" :style="{ marginTop: `${contentMarginTop}px` }">
      <!-- 用户信息区域 -->
      <div
        class="user-section"
        :style="{
          opacity: userInfoOpacity,
          height: userInfoHeight,
          overflow: 'hidden',
        }"
      >
        <div class="user-info">
          <div class="user-name">{{ characterName }}</div>
          <div class="user-signature">{{
            chat4Store.momentState.personalizedSignature ||
            'The human spirit is the golden spirit'
          }}</div>
        </div>
      </div>

      <!-- 分割线 -->
      <div class="divider"></div>

      <!-- 朋友圈动态列表 -->
      <div class="moments-list" ref="momentsListRef" @scroll="handleScroll">
        <!-- 加载状态显示骨架屏 -->
        <MomentSkeleton v-if="chat4Store.momentState.isLoading" :count="3" />

        <!-- 朋友圈动态列表 -->
        <MomentPost
          v-for="post in momentPosts"
          :key="post.id"
          :post="post"
          @like="handleLike"
          @comment="handleComment"
          @toggle-expand="handleToggleExpand"
          @media-click="handleMediaClick"
        />

        <!-- 无数据状态 -->
        <div
          v-if="!chat4Store.momentState.isLoading && momentPosts.length === 0"
          class="empty-state"
        >
          <div class="empty-icon">📝</div>
          <div class="empty-text">No moments yet</div>
        </div>
      </div>
    </div>

    <!-- 评论输入框 -->
    <CommentInput
      v-model:show="commentInput.show"
      :max-length="200"
      placeholder="Write a comment..."
      @submit="handleCommentSubmit"
      @cancel="handleCommentCancel"
    />

    <!-- 媒体预览模态框 -->
    <div
      v-if="mediaPreview.show"
      class="media-preview-modal"
      @click="closeMediaPreview"
    >
      <div class="media-preview-content" @click.stop>
        <!-- 关闭按钮 -->
        <button class="close-button" @click="closeMediaPreview">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
              d="M18 6L6 18M6 6L18 18"
              stroke="white"
              stroke-width="2"
              stroke-linecap="round"
            />
          </svg>
        </button>

        <!-- 图片预览 -->
        <div v-if="mediaPreview.type === 'image'" class="image-preview">
          <img :src="mediaPreview.url" :alt="'Preview image'" />
        </div>

        <!-- 视频预览 -->
        <div v-if="mediaPreview.type === 'video'" class="video-preview">
          <video :src="mediaPreview.url" controls autoplay @click.stop>
            Your browser does not support the video tag.
          </video>
        </div>
      </div>
    </div>
  </div>
  <!-- sheet -->

  <!-- moment-container -->
</template>

<script setup lang="ts">
import { onMounted, computed, ref } from 'vue'
import MomentPost from '../MomentPost/index.vue'
import CommentInput from '../CommentInput/index.vue'
import MomentSkeleton from '../MomentSkeleton/index.vue'
import type { MomentPost as MomentPostType } from '@/types/moment'
import { useChatEventsStore } from '@/store/chat-events'
import { useChat4Store } from '@/store/chat4'
import { useChatResourcesStore } from '@/store/chat-resources'
import { useAudioManager } from '@/mobile/composables/useAudioManager'

interface Props {
  characterName: string
  characterAvatar?: string
}

const props = withDefaults(defineProps<Props>(), {
  characterAvatar:
    'https://cdn.magiclight.ai/assets/playshot/default-avatar.png',
})

const emit = defineEmits<{
  'back-click': []
}>()

// Store引用
const chatEventsStore = useChatEventsStore()
const chat4Store = useChat4Store()
const chatResourcesStore = useChatResourcesStore()
const audioManager = useAudioManager()

// 背景图片 - 从chat-resources store获取，如果没有则使用默认图片
const backgroundImage = computed(() => {
  return (
    chatResourcesStore.backgroundImage ||
    'https://cdn.magiclight.ai/assets/playshot/moment-bg.jpg'
  )
})

// 朋友圈消息发送方法
const sendMomentEvent = async (
  eventType: string = 'get_moment',
  momentData: Record<string, any> = {},
) => {
  chat4Store.setMomentLoading(true)
  try {
    // 将朋友圈相关数据放到option_data中
    const optionData = {
      ...momentData, // moment_id, liked, comment等参数
    }

    await chatEventsStore.sendMessage(
      '',
      null,
      'config',
      false,
      null,
      0,
      false,
      optionData, // 这里会被放到option_data中
      'Moment',
    )
    console.log(`Moment event sent: ${eventType}`, optionData)
  } catch (error) {
    console.error(`Failed to send moment event ${eventType}:`, error)
    chat4Store.setMomentError('Failed to send moment action')
  } finally {
    chat4Store.setMomentLoading(false)
  }
}

// 从chat4 store获取朋友圈数据
const momentPosts = computed(() => {
  return chat4Store.momentState.posts.map((post) => ({
    ...post,
    user: {
      ...post.user,
      name: props.characterName,
      avatar: props.characterAvatar,
    },
  }))
})

// 处理返回点击
const handleBackClick = () => {
  emit('back-click')
}

// 处理声音切换
const handleSoundToggle = () => {
  audioManager.toggleMute()
  console.log('Sound toggled, isMuted:', audioManager.isMuted)
}

// 处理点赞
const handleLike = async (postId: string) => {
  console.log('Like post:', postId)

  // 找到对应的动态
  const post = momentPosts.value.find((p) => p.id === postId)
  if (!post) return

  // 发送点赞请求，使用content.id作为moment_id
  const momentData = {
    moment_id: post.content.id, // 使用content.id作为真实的moment_id
    liked: (!post.isLiked).toString(), // 切换点赞状态
    comment: '',
  }

  try {
    await sendMomentEvent('moment_comment', momentData)

    // 乐观更新UI（可选，也可以等后端返回数据再更新）
    // post.isLiked = !post.isLiked
    // post.content.liked = post.isLiked
    // if (post.likeCount !== undefined) {
    //   post.likeCount += post.isLiked ? 1 : -1
    // }
  } catch (error) {
    console.error('Failed to like post:', error)
  }
}

// 处理评论
const handleComment = async (postId: string) => {
  console.log('Comment on post:', postId)

  // 找到对应的动态
  const post = momentPosts.value.find((p) => p.id === postId)
  if (!post) return

  // 显示评论输入框
  commentInput.value = {
    show: true,
    currentPostId: postId,
  }
}

// 处理展开/收起
const handleToggleExpand = (postId: string) => {
  console.log('Toggle expand for post:', postId)
  // TODO: 实现展开/收起功能
}

// 媒体预览状态
const mediaPreview = ref({
  show: false,
  url: '',
  type: 'image' as 'image' | 'video',
})

// 评论输入框状态
const commentInput = ref({
  show: false,
  currentPostId: '',
})

// 滚动状态管理
const momentsListRef = ref<HTMLElement>()
const scrollY = ref(0)
const userInfoOpacity = ref(1)
const userInfoHeight = ref('auto') // 用户信息区域高度
const contentMarginTop = ref(162) // 初始margin-top值
const headerTextColor = ref('white') // header文字颜色
const headerIconColor = ref('white') // header图标颜色
const headerBackgroundColor = ref('transparent') // header背景色

// 滚动阈值
const SCROLL_THRESHOLD = 100 // 滚动超过这个距离开始隐藏用户信息
const MAX_SCROLL_OFFSET = 162 // 最大滚动偏移量，让content可以到达顶部

// 处理媒体点击
const handleMediaClick = (url: string, type: 'image' | 'video') => {
  console.log('Media clicked:', url, type)
  mediaPreview.value = {
    show: true,
    url,
    type,
  }
}

// 关闭媒体预览
const closeMediaPreview = () => {
  mediaPreview.value.show = false
}

// 处理评论提交
const handleCommentSubmit = async (comment: string) => {
  console.log('Comment submitted:', comment)

  const postId = commentInput.value.currentPostId
  if (!postId) return

  // 找到对应的动态
  const post = momentPosts.value.find((p) => p.id === postId)
  if (!post) return

  // 发送评论请求，使用content.id作为moment_id
  const momentData = {
    moment_id: post.content.id, // 使用content.id作为真实的moment_id
    liked: 'false', // 评论操作不涉及点赞
    comment: comment,
  }

  try {
    await sendMomentEvent('moment_comment', momentData)
  } catch (error) {
    console.error('Failed to comment on post:', error)
  }
}

// 处理评论取消
const handleCommentCancel = () => {
  console.log('Comment cancelled')
  commentInput.value = {
    show: false,
    currentPostId: '',
  }
}

// 滚动处理方法
const handleScroll = (e: Event) => {
  const target = e.target as HTMLElement
  if (!target) return

  scrollY.value = target.scrollTop

  // 计算用户信息透明度和高度
  if (scrollY.value <= SCROLL_THRESHOLD) {
    // 未达到阈值，保持原状
    userInfoOpacity.value = 1
    userInfoHeight.value = 'auto'
    contentMarginTop.value = 162
    headerTextColor.value = 'white'
    headerIconColor.value = 'white'
    headerBackgroundColor.value = 'transparent'
  } else {
    // 超过阈值，开始变化
    const progress = Math.min(
      (scrollY.value - SCROLL_THRESHOLD) / MAX_SCROLL_OFFSET,
      1,
    )
    userInfoOpacity.value = 1 - progress
    // 当完全隐藏时，高度设为0
    userInfoHeight.value = progress >= 1 ? '0px' : 'auto'
    contentMarginTop.value = 162 - progress * 162 // 减少margin-top，最小为0

    // 当content接近顶部时，header变黑色并添加白色背景
    if (progress > 0.8) {
      headerTextColor.value = 'black'
      headerIconColor.value = 'black'
      headerBackgroundColor.value = '#fff'
    } else {
      headerTextColor.value = 'white'
      headerIconColor.value = 'white'
      headerBackgroundColor.value = 'transparent'
    }
  }
}

onMounted(async () => {
  // 进入朋友圈时自动发送get_moment消息
  await sendMomentEvent('get_moment')
})
</script>

<style scoped>
.moment-container {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  position: relative;
  overflow: hidden;
  background: #000;
  display: flex;
  flex-direction: column;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 267px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* 用户信息区域过渡动画 */
.user-section {
  transition: opacity 0.3s ease-out;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 198px;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
}

.header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  height: 44px;
}

.header-left,
.header-right {
  width: 32px;
  display: flex;
  align-items: center;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.back-button,
.sound-button {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 12.8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.title {
  color: white;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.4;
  margin: 0;
}

.content {
  position: relative;
  z-index: 5;
  background: #fdfeff;
  border-radius: 16px 16px 0 0;
  padding: 12px 0 0;
  transition: margin-top 0.3s ease-out;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: calc(var(--vh, 1vh) * 100); /* 使用--vh确保在移动端正确显示 */
}

.user-section {
  position: relative;
  padding: 0px 100px 16px 16px;
}

.user-avatar-large {
  position: absolute;
  right: 16px;
  width: 68px;
  height: 68px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10; /* 确保在content之上 */
  transition:
    opacity 0.3s ease-out,
    top 0.3s ease-out;
}

.user-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  text-align: left;
}

.user-name {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  font-size: 16px;
  line-height: 1.4;
  margin-bottom: 4px;
  text-align: right;
}

.user-signature {
  color: rgba(0, 0, 0, 0.7);
  font-weight: 300;
  font-size: 12px;
  line-height: 1.4;
  text-align: right;
}

.divider {
  height: 1px;
  background: rgba(0, 0, 0, 0.05);
  margin: 0 16px 0 60px;
}

.moments-list {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 16px;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 18px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.8);
  margin-bottom: 8px;
}

.empty-subtext {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.5);
}

/* 媒体预览模态框样式 */
.media-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.media-preview-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button {
  position: absolute;
  top: -50px;
  right: 0;
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  z-index: 10000;
}

.image-preview img {
  max-width: 100%;
  max-height: 90vh;
  object-fit: contain;
  border-radius: 8px;
}

.video-preview video {
  max-width: 100%;
  max-height: 90vh;
  border-radius: 8px;
}
</style>
