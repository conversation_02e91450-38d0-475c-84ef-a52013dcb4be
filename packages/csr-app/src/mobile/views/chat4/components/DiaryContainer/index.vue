<template>
  <div class="diary-container">
    <!-- 标题栏 -->
    <div class="header">
      <div class="header-left">
        <button class="back-button" @click="handleGoBack">
          <DiaryBackIcon />
        </button>
      </div>
      <div class="header-title">
        <span class="title-text">Diary</span>
      </div>
    </div>

    <!-- 分割线 -->
    <div class="divider"></div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 角色信息卡片 -->
      <div class="character-card">
        <div class="character-info">
          <!-- 角色大头像 -->
          <div class="character-avatar-large">
            <img
              :src="characterAvatar || '/default-avatar.png'"
              :alt="characterName"
              class="avatar-image"
            />
          </div>

          <!-- 角色详情 -->
          <div class="character-details">
            <div class="character-name">{{ characterName || 'Kurapika' }}</div>
            <div class="character-status">
              <div class="status-row">
                <div class="status-item">
                  <StatusLocationIcon class="status-icon" />
                  <span class="status-text">{{
                    diaryData.region || 'Bedroom'
                  }}</span>
                </div>
                <!-- <div class="status-item">
                  <StatusEmotionIcon class="status-icon" />
                  <span class="status-text">{{
                    diaryData.emotion || 'Calm'
                  }}</span>
                </div> -->
              </div>
              <div class="status-item activity">
                <StatusActivityIcon class="status-icon" />
                <span class="status-text">{{
                  diaryData.status || 'Sleeping'
                }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 好感度系统 -->
        <div class="favorability-section">
          <div class="favorability-header">
            <div class="favorability-label">
              <span class="favorability-title">Likability</span>
            </div>
            <div class="favorability-level">
              <span class="level-text">{{ currentLevelText }}</span>
            </div>
          </div>
          <div class="favorability-progress">
            <div class="progress-bar">
              <div class="progress-bg"></div>
              <div
                class="progress-fill"
                :style="{ width: `${progressPercentage}%` }"
              ></div>
              <div
                class="progress-handle"
                :style="{ left: `calc(${progressPercentage}% - 8px)` }"
              >
                <DiaryHeartIcon />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 日记卡片 -->
      <div class="diary-card">
        <!-- 日记头部 -->
        <div class="diary-header">
          <div class="diary-title">Inner monologue</div>
          <div class="diary-meta">
            <span class="diary-date">{{ currentDate }}</span>
            <span class="diary-weather">{{ currentWeather }}</span>
          </div>
        </div>
        <div class="diary-decoration">
          <DiaryLineVector />
        </div>
        <!-- 日记内容区域 -->
        <div class="diary-content">
          <div class="diary-lines">
            <div class="diary-text">
              {{ displayMessage }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useChat4Store } from '@/store/chat4'
import { useChatEventsStore } from '@/store/chat-events'
import { SceneUnlockUtils } from '@/types/favorability'
import DiaryBackIcon from '@/assets/icon/diary-back-icon.svg'
import DiaryLineVector from '@/assets/icon/diary-line-vector.svg'
import StatusLocationIcon from '@/assets/icon/status-location-icon.svg'
import StatusEmotionIcon from '@/assets/icon/status-emotion-icon.svg'
import StatusActivityIcon from '@/assets/icon/status-activity-icon.svg'
import DiaryHeartIcon from '@/assets/icon/diary-heart-icon.svg'

interface Props {
  characterName?: string
  characterAvatar?: string
}

interface Emits {
  (e: 'go-back'): void
}

const props = withDefaults(defineProps<Props>(), {
  characterName: 'Kurapika',
  characterAvatar: '',
})

const emit = defineEmits<Emits>()

// Store
const chat4Store = useChat4Store()
const chatEventsStore = useChatEventsStore()

// 获取好感度状态
const { levelInfos, currentHeartValue } = chatEventsStore.favorabilityState

// 从store获取diary数据
const diaryData = computed(() => chat4Store.diaryState)

// 计算当前等级
const currentLevel = computed(() => {
  return SceneUnlockUtils.calculateCurrentLevel(currentHeartValue, levelInfos)
})

// 计算当前等级显示文本
const currentLevelText = computed(() => {
  return SceneUnlockUtils.formatLevelText(currentLevel.value)
})

// 计算进度百分比
const progressPercentage = computed(() => {
  if (levelInfos.length === 0) return 0

  // 按好感度值排序
  const sortedLevels = [...levelInfos].sort(
    (a, b) => a.heart_value - b.heart_value,
  )

  // 找到当前等级和下一等级
  let currentLevelInfo = sortedLevels[0]
  let nextLevelInfo = sortedLevels[1]

  for (let i = 0; i < sortedLevels.length; i++) {
    if (currentHeartValue >= sortedLevels[i].heart_value) {
      currentLevelInfo = sortedLevels[i]
      nextLevelInfo = sortedLevels[i + 1]
    } else {
      break
    }
  }

  // 如果已经是最高等级
  if (!nextLevelInfo) {
    return 100
  }

  // 计算当前等级内的进度
  const currentLevelStart = currentLevelInfo.heart_value
  const nextLevelStart = nextLevelInfo.heart_value
  const progressInLevel = currentHeartValue - currentLevelStart
  const levelRange = nextLevelStart - currentLevelStart

  return Math.min(100, Math.max(0, (progressInLevel / levelRange) * 100))
})

// 计算显示消息
const displayMessage = computed(() => {
  return (
    diaryData.value.content ||
    "Sorry, I'm having trouble understanding your request. Please try again later."
  )
})

// 计算日期显示
const currentDate = computed(() => {
  if (diaryData.value.date) {
    // 将日期格式化为 "March 6 | 2025" 格式
    const date = new Date(diaryData.value.date)
    const options: Intl.DateTimeFormatOptions = {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    }
    const formattedDate = date.toLocaleDateString('en-US', options)
    return formattedDate.replace(',', ' |')
  }
  return 'March 6 | 2025'
})

// 计算天气显示
const currentWeather = computed(() => {
  // 根据emotion映射天气
  const emotionWeatherMap: Record<string, string> = {
    happy: 'Sunny',
    sad: 'Rainy',
    angry: 'Stormy',
    calm: 'Cloudy',
    excited: 'Sunny',
    tired: 'Overcast',
  }

  if (diaryData.value.emotion && emotionWeatherMap[diaryData.value.emotion]) {
    return emotionWeatherMap[diaryData.value.emotion]
  }

  return 'Sunny'
})

// 处理返回按钮点击
const handleGoBack = () => {
  emit('go-back')
}
</script>

<style lang="less" scoped>
.diary-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
}

/* 顶部状态栏 */
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 44px;
  z-index: 10;
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 17px 21px 12px;
  height: 100%;
}

.time {
  color: #000000;
  font-family: 'SF Pro', sans-serif;
  font-weight: 600;
  font-size: 15px;
  line-height: 20px;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.signal-icon,
.wifi-icon,
.battery-icon {
  width: 17px;
  height: 11px;
  background: #000000;
  opacity: 0.4;
}

/* 标题栏 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-button {
  width: 16px;
  height: 16px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12.8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  svg {
    color: rgba(0, 0, 0, 0.85);
  }
}

.header-title {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
}

.title-text {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
}

/* 分割线 */
.divider {
  height: 1px;
  margin: 0 16px;
  background: rgba(255, 255, 255, 0.05);
  flex-shrink: 0;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  overflow-y: auto;
}

/* 角色信息卡片 */
.character-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.character-info {
  display: flex;
  gap: 12px;
}

.character-avatar-large {
  width: 68px;
  height: 68px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.character-avatar-large .avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.character-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.character-name {
  color: #000000;

  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  padding: 0 2px;
}

.character-status {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-row {
  display: flex;
  gap: 32px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.status-text {
  color: #000000;

  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
}

.activity {
  align-self: stretch;
}

/* 好感度系统 */
.favorability-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  margin-left: auto;
}

.favorability-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.favorability-title {
  color: #000000;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
}

.favorability-level {
  display: flex;
  align-items: center;
  gap: 10px;
}

.level-text {
  color: #ff5b5b;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.favorability-progress {
  width: 100%;
  height: 14px;
  position: relative;
}

.progress-bar {
  width: 100%;
  height: 14px;
  position: relative;
}

.progress-bg {
  position: absolute;
  top: 3px;
  left: 0;
  width: 100%;
  height: 8px;
  background: #efefef;
  border-radius: 4px;
}

.progress-fill {
  position: absolute;
  top: 3px;
  left: 0;
  height: 8px;
  background: linear-gradient(90deg, #f273b9 0%, #c183fb 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-handle {
  position: absolute;
  top: -2px;
  transition: left 0.3s ease;
}

/* 日记卡片 */
.diary-card {
  background: #fdfbff;
  border: 1px solid #ebe8fb;
  border-radius: 16px;
  padding: 16px 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
  overflow: hidden;
}

/* 日记头部 */
.diary-header {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 0 16px;
  align-items: flex-start;
}

.diary-title {
  color: #b756ff;

  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  text-align: left;
}

.diary-meta {
  display: flex;
  gap: 10px;
  align-items: center;
}

.diary-date,
.diary-weather {
  color: rgba(0, 0, 0, 0.5);

  font-weight: 600;
  font-size: 10px;
  line-height: 14px;
}

/* 日记内容区域 */
.diary-content {
  flex: 1;
  padding: 0 10px;
  display: flex;
  align-items: stretch;
  justify-content: center;
  position: relative;
}

.diary-lines {
  flex: 1;
  position: relative;
  padding: 16px;
  overflow-y: auto;
  background-image: repeating-linear-gradient(
    transparent,
    transparent 31px,
    rgba(0, 0, 0, 0.15) 31px,
    rgba(0, 0, 0, 0.15) 32px
  );

  background-position:
    0 16px,
    0 16px;
}

.diary-text {
  color: #000000;
  font-weight: 400;
  font-size: 12px;
  line-height: 32px;
  text-align: justify;
  white-space: pre-line;
  word-wrap: break-word;
  margin: 0;
  padding: 0;
  position: relative;
  z-index: 1;
  min-height: 100%;
}

.diary-decoration {
  position: absolute;
  top: 0;
  right: 30px;
  width: 27px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
