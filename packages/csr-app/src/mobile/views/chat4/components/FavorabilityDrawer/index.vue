<template>
  <div class="favorability-drawer" v-if="visible" @click="handleBackdropClick">
    <div class="drawer-content" @click.stop>
      <!-- 标题 -->
      <div class="drawer-header">
        <div class="level-info">
          <div class="current-level">
            <span class="level-label">Current Level</span>
            <h3 class="level-value">{{ currentLevel || 'Lv0' }}</h3>
          </div>
          <div class="level-arrow">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path
                d="M8 4L16 12L8 20"
                stroke="#CA93F2"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
          <div class="next-level">
            <span class="level-label">Next Level</span>
            <h3 class="level-value">{{ nextLevel }}</h3>
          </div>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="drawer-body">
        <!-- 当前进度 -->
        <div class="progress-section">
          <div class="progress-background">
            <div class="progress-items">
              <!-- Hearts 进度 -->
              <div class="progress-item">
                <div class="option-label">Option 1: Earn Hearts</div>
                <div class="progress-info">
                  <div class="progress-header">
                    <div class="progress-icon-text">
                      <div class="heart-icon">
                        <svg width="12" height="13" viewBox="0 0 12 13" fill="none">
                          <path
                            d="M6 11.5L5.25 10.825C2.4 8.275 0.5 6.525 0.5 4.375C0.5 2.625 1.875 1.25 3.625 1.25C4.625 1.25 5.575 1.725 6 2.45C6.425 1.725 7.375 1.25 8.375 1.25C10.125 1.25 11.5 2.625 11.5 4.375C11.5 6.525 9.6 8.275 6.75 10.825L6 11.5Z"
                            stroke="#333333"
                            stroke-width="1.08"
                          />
                        </svg>
                      </div>
                      <span class="progress-label"
                        >{{ currentHeartValue }} /
                        {{ nextLevelHeartValue || currentHeartValue }} Hearts</span
                      >
                    </div>
                    <span class="progress-remaining" v-if="heartValueLeft && heartValueLeft > 0"
                      >{{ heartValueLeft }} more needed</span
                    >
                    <span class="progress-completed" v-else>Completed!</span>
                  </div>
                  <div class="progress-bar">
                    <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
                  </div>
                </div>
              </div>

              <!-- Coins 进度 (如果有金币解锁条件) -->
              <div class="progress-item" v-if="coinsRequiredForNextLevel > 0">
                <div class="option-label">Additional Requirement</div>
                <div class="progress-info">
                  <div class="progress-header">
                    <div class="progress-icon-text">
                      <div class="coin-icon">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                          <circle cx="6" cy="6" r="5" stroke="#333333" stroke-width="1.08" />
                          <text x="6" y="8" text-anchor="middle" font-size="6" fill="#333333">
                            $
                          </text>
                        </svg>
                      </div>
                      <span class="progress-label"
                        >{{ currentCoins }} /
                        {{ currentCoins + coinsRequiredForNextLevel }} Coins</span
                      >
                    </div>
                    <span class="progress-remaining"
                      >{{ coinsRequiredForNextLevel }} more needed</span
                    >
                  </div>
                  <div class="progress-bar">
                    <div
                      class="progress-fill"
                      :style="{ width: coinsProgressPercentage + '%' }"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 分隔文字 -->
        <div class="separator-text">or</div>
        <!-- 奖励展示 (如果有金币奖励) -->
        <div class="reward-section" v-if="maxCoinReward > 0">
          <div class="reward-background">
            <div class="option-label">Option 2: Purchase Instantly</div>
            <div class="reward-description">
              <span class="reward-text">Skip the wait and unlock {{ nextLevel }} now!</span>
            </div>
            <div class="reward-item" @click="handlePaymentClick">
              <div class="reward-badge">
                <div class="diamond-icon">
                  <img src="https://cdn.magiclight.ai/assets/mobile/diamond.png" alt="Diamond" />
                </div>
                <span class="reward-amount">{{ maxCoinReward }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 关闭按钮 -->
      <div class="drawer-footer">
        <button class="close-button" @click="handleClose">Close</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useUserStore } from '@/store/user'
import type { FavorabilityDrawerProps, FavorabilityDrawerEmits } from '@/types/favorability'
import { SceneUnlockUtils } from '@/types/favorability'

const props = defineProps<FavorabilityDrawerProps>()
const emit = defineEmits<FavorabilityDrawerEmits>()

const userStore = useUserStore()

// 添加调试信息
console.log('FavorabilityDrawer favorabilityState:', props.favorabilityState)

// 计算属性
const currentHeartValue = computed(() => props.favorabilityState.currentHeartValue)
const currentLevel = computed(() => {
  const current = props.favorabilityState.currentLevel
  if (!current) {
    console.warn('Current level is undefined, using default level0')
    return 'Lv0' // level0 格式化后显示为 Lv0
  }
  return SceneUnlockUtils.formatLevelText(current)
})

const nextLevel = computed(() => {
  const next = props.favorabilityState.nextLevel
  const current = props.favorabilityState.currentLevel
  const levelInfos = props.favorabilityState.levelInfos

  console.log('Computing nextLevel:', { current, next, levelInfos })

  // 如果没有下一等级，检查是否真的是最高等级
  if (!next) {
    // 如果有等级信息，检查当前是否是最高等级
    if (levelInfos && levelInfos.length > 0) {
      const sortedLevels = [...levelInfos].sort((a, b) => a.heart_value - b.heart_value)
      const highestLevel = sortedLevels[sortedLevels.length - 1]

      if (current === highestLevel?.level) {
        return 'Max Level'
      } else {
        // 如果不是最高等级但没有nextLevel，可能是数据问题
        console.warn('No next level but not at max level:', {
          current,
          highestLevel: highestLevel?.level
        })
        return 'Max Level'
      }
    }
    return 'Max Level'
  }

  // 如果下一等级和当前等级相同，说明数据有问题
  if (next === current) {
    console.warn('Next level is same as current level, treating as max level:', { current, next })
    return 'Max Level'
  }

  return SceneUnlockUtils.formatLevelText(next)
})
const nextLevelHeartValue = computed(() => props.favorabilityState.nextLevelHeartValue)
const heartValueLeft = computed(() => props.favorabilityState.heartValueLeft)
const currentCoins = computed(() => userStore.userInfo?.coins || 0)

// 计算下一等级需要的金币数量
const coinsRequiredForNextLevel = computed(() => {
  const { sceneConditions, nextLevel: nextLevelName } = props.favorabilityState
  if (!nextLevelName) return 0

  // 查找需要金币解锁且等级要求为下一等级的场景
  const nextLevelHeartValue = props.favorabilityState.nextLevelHeartValue || 0
  let maxCoinsRequired = 0

  sceneConditions.forEach((condition) => {
    if (condition.requirement === 'coins') {
      // 检查是否有场景需要这个等级的好感度
      const hasHeartRequirement = sceneConditions.some(
        (c) =>
          c.scene_id === condition.scene_id &&
          c.requirement === 'heart_value' &&
          c.value <= nextLevelHeartValue
      )

      if (hasHeartRequirement && condition.value > currentCoins.value) {
        maxCoinsRequired = Math.max(maxCoinsRequired, condition.value - currentCoins.value)
      }
    }
  })

  return maxCoinsRequired
})

// 计算好感度进度百分比
const progressPercentage = computed(() => {
  if (!nextLevelHeartValue.value) return 100
  const progress = (currentHeartValue.value / nextLevelHeartValue.value) * 100
  return Math.min(progress, 100)
})

// 计算金币进度百分比
const coinsProgressPercentage = computed(() => {
  if (coinsRequiredForNextLevel.value <= 0) return 100
  const totalRequired = currentCoins.value + coinsRequiredForNextLevel.value
  const progress = (currentCoins.value / totalRequired) * 100
  return Math.min(progress, 100)
})

// 计算最大金币奖励（显示在奖励区域）
const maxCoinReward = computed(() => {
  const { sceneConditions } = props.favorabilityState

  // 找到所有金币要求中的最大值
  let maxCoins = 0
  sceneConditions.forEach((condition) => {
    if (condition.requirement === 'coins') {
      maxCoins = Math.max(maxCoins, condition.value)
    }
  })

  return maxCoins
})

// 事件处理
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

const handleBackdropClick = () => {
  handleClose()
}

// 付费点击处理
const handlePaymentClick = () => {
  emit('payment', maxCoinReward.value)
}
</script>

<style lang="less" scoped>
.favorability-drawer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.drawer-content {
  width: 100%;
  height: 480px;
  max-height: 85vh; /* 确保在小屏幕上不会超出视窗 */
  background: #ffffff;
  border-radius: 20px 20px 0px 0px;
  padding: 0;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 如果内容过多，允许滚动 */
}

.drawer-header {
  padding: 40px 15px 0 15px;

  .level-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;

    .current-level,
    .next-level {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .level-label {
        font-family: 'Work Sans', sans-serif;
        font-weight: 500;
        font-size: 12px;
        line-height: 1.17;
        color: #777777;
        text-align: center;
      }

      .level-value {
        font-family: 'Work Sans', sans-serif;
        font-weight: 600;
        font-size: 18px;
        line-height: 1.17;
        text-align: center;
        margin: 0;
      }
    }

    .current-level .level-value {
      color: #333333;
    }

    .next-level .level-value {
      color: #ca93f2;
    }

    .level-arrow {
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0.6;
    }
  }
}

.drawer-body {
  flex: 1;
  padding: 0 15px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.progress-section {
  margin-top: 30px;

  .progress-background {
    background: #f5f5f5;
    border-radius: 10px;
    padding: 15px;
    position: relative;
  }

  .progress-items {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .progress-item {
    .progress-info {
      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        .progress-icon-text {
          display: flex;
          align-items: center;
          gap: 4px;

          .heart-icon,
          .comment-icon,
          .coin-icon {
            width: 12px;
            height: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .progress-label {
            font-family: 'Work Sans', sans-serif;
            font-weight: 500;
            font-size: 14px;
            line-height: 1.17;
            color: #333333;
          }
        }

        .progress-remaining {
          font-family: 'Work Sans', sans-serif;
          font-weight: 500;
          font-size: 12px;
          line-height: 1.17;
          color: #777777;
        }

        .progress-completed {
          font-family: 'Work Sans', sans-serif;
          font-weight: 600;
          font-size: 12px;
          line-height: 1.17;
          color: #4caf50;
        }
      }

      .progress-bar {
        width: 100%;
        height: 18px;
        background: #ffffff;
        border-radius: 20px;
        overflow: hidden;

        .progress-fill {
          height: 100%;
          background: #ca93f2;
          border-radius: 20px;
          transition: width 0.3s ease;
        }
      }
    }
  }

  .option-label {
    font-family: 'Work Sans', sans-serif;
    font-weight: 600;
    font-size: 12px;
    line-height: 1.17;
    color: #777777;
    margin-bottom: 10px;
  }
}

.reward-section {
  .reward-background {
    background: #f5f5f5;
    border-radius: 10px;
    padding: 20px 14px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .reward-description {
    .reward-text {
      font-family: 'Work Sans', sans-serif;
      font-weight: 500;
      font-size: 13px;
      line-height: 1.17;
      color: #555555;
      text-align: center;
      display: block;
    }
  }

  .reward-item {
    flex: 1;
    display: flex;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }
    .reward-badge {
      width: 89px;
      height: 28px;
      background: linear-gradient(180deg, #e2baff 0%, #f6eaff 100%);
      border-radius: 9px;
      box-shadow: inset 0px 4px 0px 0px rgba(214, 159, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .reward-amount {
        font-family: 'Work Sans', sans-serif;
        font-weight: 600;
        font-size: 16px;
        line-height: 1.17;
        color: #8e2ed4;
        margin-right: 8px;
      }

      .diamond-icon {
        width: 32px;
        height: 32px;
        position: absolute;
        left: -12px;
        top: -2px;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
  }

  .option-label {
    font-family: 'Work Sans', sans-serif;
    font-weight: 600;
    font-size: 12px;
    line-height: 1.17;
    color: #777777;
  }
}

.separator-text {
  font-family: 'Work Sans', sans-serif;
  font-weight: 600;
  font-size: 12px;
  line-height: 1.17;
  text-align: center;
  color: #aaaaaa;
  margin: 4px 0;
}

.drawer-footer {
  padding: 20px 15px 38px 15px;
  display: flex;
  justify-content: center;

  .close-button {
    width: 140px;
    height: 38px;
    background: #ca93f2;
    border-radius: 40px;
    border: none;
    font-family: 'Work Sans', sans-serif;
    font-weight: 600;
    font-size: 14px;
    line-height: 1.17;
    text-align: center;
    color: #1f0038;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #b088e8;
    }

    &:active {
      transform: scale(0.98);
    }

    // 响应式设计：在小屏幕上调整按钮宽度
    @media (max-width: 375px) {
      width: 120px;
    }

    @media (max-width: 320px) {
      width: 100px;
      font-size: 13px;
    }
  }
}
</style>
