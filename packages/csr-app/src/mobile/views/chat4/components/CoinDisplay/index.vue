<template>
  <button class="coin-display" @click="$emit('add-coins')">
    <div class="coin-icon">
      <svg width="13" height="13" viewBox="0 0 13 13" fill="none">
        <circle cx="6.5" cy="6.5" r="6.5" fill="#DAFF96"/>
        <path d="M6.5 3V10M3 6.5H10" stroke="#1F0038" stroke-width="1.5" stroke-linecap="round"/>
      </svg>
    </div>
    <span class="coin-amount">{{ formatAmount(amount) }}</span>
  </button>
</template>

<script setup lang="ts">
interface Props {
  amount: number
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'add-coins': []
}>()

// 格式化金币数量
const formatAmount = (amount: number): string => {
  if (amount >= 1000000) {
    return `${(amount / 1000000).toFixed(1)}M`
  } else if (amount >= 1000) {
    return `${(amount / 1000).toFixed(1)}K`
  }
  return amount.toString()
}
</script>

<style lang="less" scoped>
.coin-display {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-radius: 34px;
  border: 1px solid #DAFF96;
  background: #1F0038;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(31, 0, 56, 0.8);
    transform: scale(1.05);
  }
  
  &:active {
    transform: scale(0.95);
  }
}

.coin-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 13px;
  height: 13px;
}

.coin-amount {
  font-family: 'Work Sans', sans-serif;
  font-size: 11px;
  font-weight: 600;
  line-height: 1.17;
  color: #DAFF96;
}
</style>
