<template>
  <div class="coin-display-figma">
    <div class="coin-btn">
      <div class="diamond-icon">
        <svg width="28" height="28" viewBox="0 0 28 28" fill="none">
          <path d="M14 2L18 8H10L14 2Z" fill="#DAFF96"/>
          <path d="M10 8H18L22 14L14 26L6 14L10 8Z" fill="#DAFF96"/>
        </svg>
      </div>
      <span class="coin-text">30</span>
      <div class="add-icon">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M10 5V15M5 10H15" stroke="#1F0038" stroke-width="2" stroke-linecap="round"/>
          <circle cx="10" cy="10" r="5" fill="#DAFF96"/>
        </svg>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 金币显示组件
</script>

<style lang="less" scoped>
.coin-display-figma {
  width: 94px;
  height: 24px;
}

.coin-btn {
  position: relative;
  width: 80px;
  height: 24px;
  background: #ca93f2;
  border-radius: 0 34px 34px 0;
  margin-left: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 8px 0 0;

  .diamond-icon {
    position: absolute;
    left: -14px;
    top: -2px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
  }

  .coin-text {
    font-family: 'Work Sans', sans-serif;
    font-weight: 600;
    font-size: 13px;
    line-height: 1.173;
    color: #1f0038;
    margin-left: 20px;
  }

  .add-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
