<template>
  <div class="moment-skeleton">
    <!-- 朋友圈动态骨架屏 -->
    <div v-for="i in count" :key="i" class="skeleton-post">
      <div class="skeleton-header">
        <div class="skeleton-avatar"></div>
        <div class="skeleton-user-info">
          <div class="skeleton-name"></div>
          <div class="skeleton-time"></div>
        </div>
      </div>
      
      <div class="skeleton-content">
        <div class="skeleton-text-line"></div>
        <div class="skeleton-text-line short"></div>
        <div class="skeleton-text-line medium"></div>
      </div>
      
      <div class="skeleton-actions">
        <div class="skeleton-action-button"></div>
        <div class="skeleton-action-button"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  count?: number
}

withDefaults(defineProps<Props>(), {
  count: 3
})
</script>

<style scoped>
.moment-skeleton {
  padding: 16px;
}

.skeleton-post {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.skeleton-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  margin-right: 12px;
}

.skeleton-user-info {
  flex: 1;
}

.skeleton-name {
  width: 80px;
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 6px;
}

.skeleton-time {
  width: 60px;
  height: 12px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.skeleton-content {
  margin-bottom: 12px;
}

.skeleton-text-line {
  height: 14px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 8px;
}

.skeleton-text-line.short {
  width: 60%;
}

.skeleton-text-line.medium {
  width: 80%;
}

.skeleton-actions {
  display: flex;
  gap: 16px;
}

.skeleton-action-button {
  width: 60px;
  height: 24px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 12px;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>
