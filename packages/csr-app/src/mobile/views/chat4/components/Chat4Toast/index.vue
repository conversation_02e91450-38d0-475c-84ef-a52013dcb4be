<template>
  <Transition name="toast" @enter="onEnter" @leave="onLeave">
    <div v-if="visible" :class="['chat4-toast', `chat4-toast--${type}`]">
      <div class="chat4-toast__content">
        <div class="chat4-toast__text">
          {{ message }}
        </div>
      </div>
      <div class="chat4-toast__border-top"></div>
      <div class="chat4-toast__border-bottom"></div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'

export interface Chat4ToastProps {
  message: string
  type?: 'unlock' | 'success' | 'cooldown'
  duration?: number
  visible?: boolean
}

const props = withDefaults(defineProps<Chat4ToastProps>(), {
  type: 'unlock',
  duration: 3000,
  visible: false,
})

const emit = defineEmits<{
  close: []
}>()

const visible = ref(props.visible)

let timer: number | null = null

const showToast = () => {
  visible.value = true
  if (props.duration > 0) {
    timer = setTimeout(() => {
      hideToast()
    }, props.duration)
  }
}

const hideToast = () => {
  visible.value = false
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
  emit('close')
}

const onEnter = (el: Element) => {
  void el.offsetHeight // trigger reflow
}

const onLeave = () => {
  // Animation cleanup if needed
}

watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      showToast()
    } else {
      hideToast()
    }
  },
  { immediate: true },
)

defineExpose({
  show: showToast,
  hide: hideToast,
})
</script>

<style scoped>
.chat4-toast {
  position: relative;
  width: 329px;
  height: 40px;
  margin: 0 auto;
  z-index: 1000;
}

.chat4-toast__content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  border-radius: 0;
  overflow: hidden;
}

.chat4-toast__text {
  font-weight: 600;
  font-size: 13px;
  line-height: 1.4;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 245px;
}

.chat4-toast__border-top,
.chat4-toast__border-bottom {
  position: absolute;
  left: 0;
  width: 100%;
  height: 1px;
}

.chat4-toast__border-top {
  top: 0;
}

.chat4-toast__border-bottom {
  bottom: 0;
}

/* Unlock type - Red gradient background, white text */
.chat4-toast--unlock .chat4-toast__content {
  background: linear-gradient(
    90deg,
    rgba(254, 252, 214, 0) 0%,
    #daff96 50%,
    rgba(254, 252, 214, 0) 100%
  );
}

.chat4-toast--unlock .chat4-toast__text {
  color: #1f0038;
}

.chat4-toast--unlock .chat4-toast__border-top,
.chat4-toast--unlock .chat4-toast__border-bottom {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
}

/* Success type - Green gradient background, dark text */
.chat4-toast--success .chat4-toast__content {
  background: linear-gradient(
      90deg,
      rgba(31, 0, 56, 0) 0%,
      rgba(31, 0, 56, 1) 50%,
      rgba(31, 0, 56, 0) 100%
    ),
    linear-gradient(
      90deg,
      rgba(227, 248, 190, 0) 0%,
      rgba(227, 248, 190, 1) 29.33%,
      rgba(227, 248, 190, 1) 69.71%,
      rgba(227, 248, 190, 0) 100%
    ),
    linear-gradient(
      90deg,
      rgba(254, 252, 214, 0) 0%,
      rgba(218, 255, 150, 1) 50%,
      rgba(254, 252, 214, 0) 100%
    ),
    #ffffff;
}

.chat4-toast--success .chat4-toast__text {
  color: #1f0038;
}

/* Cooldown type - Blue gradient background, white text */
.chat4-toast--cooldown .chat4-toast__content {
  background: linear-gradient(
      90deg,
      rgba(31, 0, 56, 0) 0%,
      rgba(31, 0, 56, 1) 50%,
      rgba(31, 0, 56, 0) 100%
    ),
    linear-gradient(
      90deg,
      rgba(255, 91, 91, 0) 0%,
      rgba(255, 91, 91, 1) 50%,
      rgba(255, 91, 91, 0) 100%
    ),
    linear-gradient(
      90deg,
      rgba(227, 248, 190, 0.2) 0%,
      rgba(227, 248, 190, 1) 29.33%,
      rgba(227, 248, 190, 1) 69.71%,
      rgba(227, 248, 190, 0.2) 100%
    ),
    linear-gradient(
      90deg,
      rgba(91, 162, 255, 0) 0%,
      rgba(91, 162, 255, 1) 50%,
      rgba(91, 162, 255, 0) 100%
    ),
    #ffffff;
}

.chat4-toast--cooldown .chat4-toast__text {
  color: #ffffff;
}

/* Toast animations */
.toast-enter-active {
  transition: all 0.3s ease-out;
}

.toast-leave-active {
  transition: all 0.3s ease-in;
}

.toast-enter-from {
  opacity: 0;
  transform: translateY(-20px);
}

.toast-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.toast-enter-to,
.toast-leave-from {
  opacity: 1;
  transform: translateY(0);
}
</style>
