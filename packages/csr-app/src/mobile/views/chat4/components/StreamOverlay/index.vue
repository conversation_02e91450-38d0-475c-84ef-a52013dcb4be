<!-- 复用chat2的StreamOverlay组件 -->
<template>
  <div class="stream-overlay">
    <!-- 角色对话气泡 -->
    <div class="character-overlay" :class="overlay.position">
      <div class="overlay-content">
        <div class="overlay-text" v-html="overlay.text"></div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div v-if="overlay?.button" class="overlay-button-container">
      <button
        class="overlay-button"
        v-if="overlay.button.text !== sayHiText"
        @click="$emit('overlay-button-click')"
      >
        <img
          class="overlay-button-icon"
          src="https://cdn.magiclight.ai/assets/playshot/target.png"
          alt="task"
        />
        <span class="overlay-button-text" :data-content="overlay.button.text">
          {{ overlay.button.text }}
        </span>
      </button>
      <img
        class="overlay-button-say-hi"
        v-else
        src="https://cdn.magiclight.ai/assets/playshot/sayHi.png"
        alt="Play"
        @click="$emit('overlay-button-click')"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Overlay {
  position: 'top' | 'center' | 'bottom'
  text: string
  button?: {
    text: string
  }
}

interface Props {
  overlay: Overlay
  sayHiText: string
}

defineProps<Props>()

const emit = defineEmits<{
  'overlay-button-click': []
}>()
</script>

<style lang="less" scoped>
.stream-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 200;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  pointer-events: none; /* 默认不阻挡点击 */

  /* 只有实际的内容元素才能被点击 */
  .character-overlay,
  .overlay-button-container {
    pointer-events: auto;
  }
}

.character-overlay {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  display: flex;
  width: 315px;
  padding: 10px 20px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 18px;
  border-top: 2px solid #1f0038;
  border-right: 2px solid #1f0038;
  border-bottom: 6px solid #1f0038;
  border-left: 2px solid #1f0038;
  background: linear-gradient(180deg, #f0dcff 0%, #daff96 100%);
  box-shadow: 0px 1px 6px 0px #9e81fe;
  color: #1f0038;
  text-align: center;
  font-size: 14px;
  font-weight: 500;

  &.bottom {
    bottom: 160px;
  }

  &.top {
    top: 20px;
  }

  &.center {
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

.overlay-button-container {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 100px;
  z-index: 100;
  padding: 0 20px;
  display: flex;
  justify-content: center;
  opacity: 1;

  .overlay-button-icon {
    width: 20px;
    height: 20px;
  }

  .overlay-button-say-hi {
    position: absolute;
    top: 0px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 80px;
    animation: breathe 2s ease-in-out infinite;
    cursor: pointer;
    transition: transform 0.3s;

    &:hover {
      transform: translateX(-50%) scale(1.05);
    }
  }

  .overlay-button {
    width: fit-content;
    max-width: 340px;
    padding: 10px 20px;
    border-radius: 12px;
    border: none;
    color: #666;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);
    color: #daff96;
    -webkit-text-stroke-width: 1;
    -webkit-text-stroke-color: #542c74;
    font-family: 'Work Sans';
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;

    &-text {
      position: relative;
      z-index: 0;
      &::after {
        content: attr(data-content);
        -webkit-text-stroke: 2px #542c74;
        position: absolute;
        left: 0;
        top: 0;
        z-index: -1;
      }
    }

    &:active {
      background: rgba(128, 128, 128, 0.2);
    }
  }
}

@keyframes breathe {
  0%,
  100% {
    transform: translateX(-50%) scale(1);
  }
  50% {
    transform: translateX(-50%) scale(1.1);
  }
}
</style>
