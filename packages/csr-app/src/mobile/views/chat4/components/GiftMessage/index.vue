<template>
  <div class="gift-message">
    <!-- 背景装饰图 -->
    <div class="background-decoration"></div>

    <!-- 主要内容区域 -->
    <div class="gift-content">
      <!-- 左侧：礼物图片 -->
      <div class="gift-image">
        <img :src="giftData.image_url" :alt="giftData.title" />
      </div>

      <!-- 右侧：礼物信息 -->
      <div class="gift-info">
        <div class="gift-name">{{ giftData.title }}</div>
        <div class="gift-quantity">x {{ quantity }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Present } from '@/api/chat-multivariate'

interface Props {
  giftData: Present
  quantity: number
  userAvatar?: string
}

withDefaults(defineProps<Props>(), {
  userAvatar: 'https://cdn.magiclight.ai/assets/playshot/default-avatar.png',
})
</script>

<style scoped>
.gift-message {
  position: relative;
  width: fit-content;
  background: linear-gradient(135deg, #ca93f2 0%, #b084e8 100%);
  border-radius: 12px 12px 0 12px;
  padding: 12px 16px;
  margin: 0;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(202, 147, 242, 0.3);
}

.background-decoration {
  position: absolute;
  top: -6px;
  left: -12px;
  width: 250px;
  height: 100px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 250 100"><defs><radialGradient id="sparkle" cx="50%" cy="50%" r="50%"><stop offset="0%" style="stop-color:%23ffffff;stop-opacity:0.8"/><stop offset="100%" style="stop-color:%23ffffff;stop-opacity:0"/></radialGradient></defs><circle cx="50" cy="30" r="3" fill="url(%23sparkle)"/><circle cx="200" cy="20" r="2" fill="url(%23sparkle)"/><circle cx="180" cy="60" r="2.5" fill="url(%23sparkle)"/><circle cx="30" cy="70" r="2" fill="url(%23sparkle)"/></svg>')
    no-repeat;
  background-size: cover;
  pointer-events: none;
  opacity: 0.6;
}

.gift-content {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  z-index: 1;
}

.gift-image {
  width: 52px;
  height: 52px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gift-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.gift-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.gift-name {
  font-weight: 500;
  font-size: 14px;
  line-height: 1.4;
  color: #ffffff;
  text-transform: capitalize;
  text-align: center;
}

.gift-quantity {
  font-weight: 500;
  font-size: 16px;
  line-height: 1.4;
  color: #daff96;
  text-align: center;
}

/* 动画效果 */
.gift-message {
  animation: giftSlideIn 0.5s ease-out;
}

@keyframes giftSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* 响应式调整 */
@media (max-width: 375px) {
  .gift-message {
    width: 200px;
    padding: 10px 40px 10px 20px;
  }

  .gift-content {
    gap: 12px;
  }

  .user-avatar {
    width: 44px;
    height: 44px;
  }

  .user-avatar img {
    width: 40px;
    height: 40px;
  }

  .gift-image {
    width: 120px;
    height: 80px;
    right: -20px;
  }
}
</style>
