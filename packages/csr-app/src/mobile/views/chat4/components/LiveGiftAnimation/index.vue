<template>
  <div class="live-gift-animation">
    <!-- 新的礼物通知条 -->
    <div
      v-for="(animation, index) in activeAnimations"
      :key="animation.id"
      class="gift-notification"
      :data-animation-id="animation.id"
      :style="{
        animationDelay: animation.delay + 'ms',
        bottom: getNotificationPosition(index) + 'px',
      }"
    >
      <!-- 主要内容区域 -->
      <div class="notification-content">
        <!-- 左侧：用户信息和文字 -->
        <div class="user-section">
          <!-- 用户头像 -->
          <div class="user-avatar">
            <img
              :src="getUserAvatar()"
              alt="User Avatar"
              class="avatar-image"
            />
          </div>

          <!-- 用户信息 -->
          <div class="user-info">
            <div class="username">{{ animation.senderName }}</div>
            <div class="action-text">
              <span class="send-text">send</span>
              <span class="gift-name">{{ animation.gift.title }}</span>
            </div>
          </div>
        </div>

        <!-- 右侧：礼物图片 -->
        <div class="gift-section">
          <div class="gift-image-container">
            <img
              :src="animation.gift.image_url"
              :alt="animation.gift.title"
              class="gift-image"
            />
          </div>
        </div>
      </div>

      <!-- 数量显示 -->
      <div class="quantity-display"> x {{ animation.quantity }} </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { Present } from '@/api/chat-multivariate'

// 使用API中定义的Present类型
type Gift = Present

// 动画数据类型
interface GiftAnimation {
  id: string
  gift: Gift
  senderName: string
  quantity: number
  delay: number
  lastUpdate: number
  timeoutId?: ReturnType<typeof setTimeout>
}

// 活跃的动画列表
const activeAnimations = ref<GiftAnimation[]>([])

// 获取用户头像
const getUserAvatar = () => {
  // 使用指定的默认头像
  return 'https://static.reelplay.ai/static/images/icon/default-avatar.png'
}

// 检查是否可以合并礼物
const canCombineGift = (
  newGift: Gift,
  newSender: string,
  existingAnimation: GiftAnimation,
): boolean => {
  const timeDiff = Date.now() - existingAnimation.lastUpdate
  const COMBINE_WINDOW = 3000 // 3秒合并窗口

  return (
    newGift.id === existingAnimation.gift.id &&
    newSender === existingAnimation.senderName &&
    timeDiff < COMBINE_WINDOW
  )
}

// 触发数量变化动画
const triggerQuantityAnimation = (animationId: string) => {
  // 添加一个临时的CSS类来触发动画
  const element = document.querySelector(
    `[data-animation-id="${animationId}"] .quantity-display`,
  )
  if (element) {
    element.classList.add('quantity-update')
    setTimeout(() => {
      element.classList.remove('quantity-update')
    }, 300)
  }
}

// 计算礼物通知的位置
const getNotificationPosition = (index: number) => {
  const COMMENT_CONTAINER_BOTTOM = 74 // 弹幕容器的bottom位置
  const MAX_COMMENTS = 5 // 最多显示5条弹幕
  const COMMENT_HEIGHT = 50 // 每条弹幕的大约高度（包含padding和文字）
  const COMMENT_GAP = 8 // 弹幕之间的间距
  const NOTIFICATION_HEIGHT = 60 // 每个礼物通知的高度
  const SPACING = 10 // 礼物通知之间的间距
  const SAFETY_MARGIN = 20 // 安全边距，确保不与弹幕重叠

  // 计算弹幕区域的实际占用高度
  const commentAreaHeight = MAX_COMMENTS * (COMMENT_HEIGHT + COMMENT_GAP)

  // 礼物通知应该从弹幕区域的顶部再往上开始
  const giftStartPosition =
    COMMENT_CONTAINER_BOTTOM + commentAreaHeight + SAFETY_MARGIN

  // 从计算出的起始位置开始，每个新通知向上堆叠
  return giftStartPosition + index * (NOTIFICATION_HEIGHT + SPACING)
}

// 播放礼物动画
const playGiftAnimation = (
  gift: Gift,
  senderName: string = 'You',
  quantity: number = 1,
) => {
  console.log('LiveGiftAnimation: playGiftAnimation called', {
    gift,
    senderName,
    quantity,
  })

  // 查找是否有可以合并的现有动画
  const existingAnimation = activeAnimations.value.find((animation) =>
    canCombineGift(gift, senderName, animation),
  )

  if (existingAnimation) {
    // 合并到现有动画
    console.log('LiveGiftAnimation: Combining with existing animation', {
      existingQuantity: existingAnimation.quantity,
      newQuantity: quantity,
      totalQuantity: existingAnimation.quantity + quantity,
    })

    existingAnimation.quantity += quantity
    existingAnimation.lastUpdate = Date.now()

    // 清除之前的定时器
    if (existingAnimation.timeoutId) {
      clearTimeout(existingAnimation.timeoutId)
    }

    // 重新设置定时器
    existingAnimation.timeoutId = setTimeout(() => {
      const index = activeAnimations.value.findIndex(
        (a) => a.id === existingAnimation.id,
      )
      if (index > -1) {
        activeAnimations.value.splice(index, 1)
        console.log('LiveGiftAnimation: Combined animation removed')
      }
    }, 4000)

    // 触发数量变化动画
    triggerQuantityAnimation(existingAnimation.id)
  } else {
    // 创建新动画
    const animationId = `gift_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 9)}`

    const animation: GiftAnimation = {
      id: animationId,
      gift,
      senderName,
      quantity,
      delay: 0,
      lastUpdate: Date.now(),
    }

    // 添加到活跃动画列表
    activeAnimations.value.push(animation)
    console.log('LiveGiftAnimation: New animation created', {
      id: animationId,
      totalAnimations: activeAnimations.value.length,
    })

    // 4秒后移除动画
    animation.timeoutId = setTimeout(() => {
      const index = activeAnimations.value.findIndex(
        (a) => a.id === animationId,
      )
      if (index > -1) {
        activeAnimations.value.splice(index, 1)
        console.log('LiveGiftAnimation: Animation removed')
      }
    }, 4000)
  }
}

// 暴露方法给父组件
defineExpose({
  playGiftAnimation,
})
</script>

<style scoped lang="less">
.live-gift-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 20;
  overflow: hidden;
}

.gift-notification {
  position: absolute;
  left: 10px; /* 与弹幕左对齐 */
  display: flex;
  align-items: center;
  gap: 12px;
  animation: slideInOut 4s ease-out forwards;
  /* 动态计算bottom位置，基于弹幕位置和索引 */
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 80px;
}

.user-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 100px;
  overflow: hidden;
  background: #ffffff;

  .avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border: 1px solid #ca93f2;
    border-radius: 100px;
  }
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 0;

  .username {
    color: #ffffff;
    font-weight: 600;
    font-size: 12px;
    line-height: 1.4;
  }

  .action-text {
    display: flex;
    align-items: center;
    gap: 8px;

    .send-text {
      color: rgba(255, 255, 255, 0.7);
      font-weight: 400;
      font-size: 12px;
      line-height: 1.4;
    }

    .gift-name {
      color: #daff96;
      font-weight: 500;
      font-size: 16px;
      line-height: 1.4;
      text-transform: capitalize;
    }
  }
}

.gift-section {
  .gift-image-container {
    width: 42px;
    height: 42px;
    border-radius: 70px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;

    .gift-image {
      width: 46px;
      height: 46px;
      object-fit: cover;
      position: relative;
      left: -2px;
      top: -0.5px;
    }
  }
}

.quantity-display {
  color: #ffffff;
  font-weight: 500;
  font-size: 24px;
  line-height: 1.4;
  text-transform: capitalize;
  text-shadow: 0px 2px 0px rgba(0, 0, 0, 0.4);
  transition: all 0.3s ease;

  &.quantity-update {
    animation: quantityPulse 0.3s ease;
  }
}

// 主要的滑入滑出动画
@keyframes slideInOut {
  0% {
    opacity: 0;
    transform: translateX(-100%);
  }
  15% {
    opacity: 1;
    transform: translateX(0);
  }
  85% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-100%);
  }
}

// 数量变化脉冲动画
@keyframes quantityPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
    color: #daff96;
  }
  100% {
    transform: scale(1);
  }
}

.gift-effects {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
}

.sparkle-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.sparkle {
  position: absolute;
  font-size: 20px;
  animation: sparkleFloat 2s ease-out forwards;

  &.sparkle-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0.2s;
  }

  &.sparkle-2 {
    top: 30%;
    right: 15%;
    animation-delay: 0.4s;
  }

  &.sparkle-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 0.6s;
  }

  &.sparkle-4 {
    top: 60%;
    right: 10%;
    animation-delay: 0.8s;
  }

  &.sparkle-5 {
    bottom: 20%;
    right: 25%;
    animation-delay: 1s;
  }
}

.heart-burst {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
}

.heart-particle {
  position: absolute;
  font-size: 16px;
  animation: heartBurst 2.5s ease-out forwards;

  &.heart-1 {
    animation-delay: 0.5s;
    --x-offset: 80px;
    --y-offset: 0px;
  }

  &.heart-2 {
    animation-delay: 0.6s;
    --x-offset: 40px;
    --y-offset: -69px;
  }

  &.heart-3 {
    animation-delay: 0.7s;
    --x-offset: -40px;
    --y-offset: -69px;
  }

  &.heart-4 {
    animation-delay: 0.8s;
    --x-offset: -80px;
    --y-offset: 0px;
  }

  &.heart-5 {
    animation-delay: 0.9s;
    --x-offset: -40px;
    --y-offset: 69px;
  }

  &.heart-6 {
    animation-delay: 1s;
    --x-offset: 40px;
    --y-offset: 69px;
  }
}

@keyframes giftShowcase {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  15% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
  }
  25% {
    transform: translate(-50%, -50%) scale(1);
  }
  85% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
}

@keyframes bannerSlide {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  20% {
    transform: translateX(0);
    opacity: 1;
  }
  80% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes giftPulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes sparkleFloat {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
  100% {
    opacity: 0;
    transform: scale(0.5) rotate(360deg) translateY(-30px);
  }
}

@keyframes heartBurst {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0);
  }
  30% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5) translateX(var(--x-offset))
      translateY(var(--y-offset));
  }
}
</style>
