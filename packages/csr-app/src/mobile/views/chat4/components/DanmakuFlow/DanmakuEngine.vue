<template>
  <div class="danmaku-engine">
    <div ref="danmakuContainer" class="danmaku-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import <PERSON><PERSON><PERSON> from 'danmaku'

interface DanmakuItem {
  id: string
  content: string
  type: 'normal' | 'special' | 'system'
  avatar?: string
  timestamp: number
}

interface Props {
  danmaku: DanmakuItem[]
  isVisible: boolean
  maxVisible?: number
  autoRemove?: boolean
  removeDelay?: number
  speed?: number // 弹幕速度
}

const props = withDefaults(defineProps<Props>(), {
  maxVisible: 8,
  autoRemove: true,
  removeDelay: 6000,
  speed: 144 // 默认弹幕速度
})

const emit = defineEmits<{
  'danmaku-complete': [danmakuId: string]
}>()

// 引用
const danmakuContainer = ref<HTMLElement>()
let danmakuEngine: any = null

// 内部状态
const processedIds = ref<Set<string>>(new Set())

// roundRect polyfill for older browsers
const addRoundRectPolyfill = () => {
  if (!CanvasRenderingContext2D.prototype.roundRect) {
    CanvasRenderingContext2D.prototype.roundRect = function (
      x: number,
      y: number,
      width: number,
      height: number,
      radius: number
    ) {
      this.beginPath()
      this.moveTo(x + radius, y)
      this.lineTo(x + width - radius, y)
      this.quadraticCurveTo(x + width, y, x + width, y + radius)
      this.lineTo(x + width, y + height - radius)
      this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
      this.lineTo(x + radius, y + height)
      this.quadraticCurveTo(x, y + height, x, y + height - radius)
      this.lineTo(x, y + radius)
      this.quadraticCurveTo(x, y, x + radius, y)
      this.closePath()
    }
  }
}

// 初始化弹幕引擎
const initDanmakuEngine = () => {
  if (!danmakuContainer.value) return

  // 添加roundRect支持
  addRoundRectPolyfill()

  danmakuEngine = new Danmaku({
    container: danmakuContainer.value,
    media: null, // 不绑定视频
    comments: [],
    engine: 'canvas', // 使用Canvas渲染，性能更好
    speed: props.speed // 使用传入的弹幕速度
  })

  console.log('Danmaku engine initialized')
}

// 添加弹幕到引擎
const addDanmakuToEngine = (item: DanmakuItem) => {
  if (!danmakuEngine || processedIds.value.has(item.id)) return

  // 构建弹幕内容
  let content = item.content

  // 创建Canvas的render函数
  const renderDanmaku = () => {
    // 根据Figma设计稿的精确尺寸，Canvas渲染需要适中的字体
    const fontSize = 12 // Canvas中适中的字体大小，清晰且不会太大
    const fontWeight = '500' // Work Sans Medium
    const fontFamily = 'Work Sans, sans-serif'
    const lineHeight = 1.173 // Figma中的行高
    const borderRadius = 68 // Figma中的圆角
    const avatarSize = item.type === 'special' && item.avatar ? 22 : 0
    const avatarGap = avatarSize > 0 ? 4 : 0 // Figma中的间距

    // 根据弹幕类型设置内边距
    let paddingH, paddingV
    if (item.type === 'special') {
      paddingH = 5 // 特殊弹幕：10px 5px
      paddingV = 10
    } else {
      paddingH = 10 // 普通/系统弹幕：10px
      paddingV = 10
    }

    // 创建临时canvas来测量文字
    const tempCanvas = document.createElement('canvas')
    const tempCtx = tempCanvas.getContext('2d')!
    tempCtx.font = `${fontWeight} ${fontSize}px ${fontFamily}`
    const textMetrics = tempCtx.measureText(content)
    const textWidth = textMetrics.width

    // 计算总尺寸
    const canvasWidth = textWidth + paddingH * 2 + avatarSize + avatarGap
    const canvasHeight = Math.max(fontSize * lineHeight + paddingV * 2, avatarSize + paddingV * 2)

    // 创建实际canvas，考虑设备像素比
    const canvas = document.createElement('canvas')
    const dpr = window.devicePixelRatio || 1

    // 设置canvas实际尺寸（考虑设备像素比）
    canvas.width = canvasWidth * dpr
    canvas.height = canvasHeight * dpr

    // 设置canvas显示尺寸
    canvas.style.width = `${canvasWidth}px`
    canvas.style.height = `${canvasHeight}px`

    const ctx = canvas.getContext('2d')!

    // 缩放上下文以匹配设备像素比
    ctx.scale(dpr, dpr)

    // 绘制背景
    if (item.type === 'special') {
      // 特殊弹幕：根据Figma设计稿的渐变背景 + 金色边框 + 发光效果
      const gradient = ctx.createLinearGradient(0, 0, canvasWidth, canvasHeight)
      gradient.addColorStop(0, '#8574F3')
      gradient.addColorStop(1, '#DF7AEC')

      // 绘制发光效果（模拟box-shadow: 0px 0px 11px 0px rgba(255, 255, 255, 0.6)）
      ctx.shadowColor = 'rgba(255, 255, 255, 0.6)'
      ctx.shadowBlur = 11
      ctx.shadowOffsetX = 0
      ctx.shadowOffsetY = 0

      // 绘制背景
      ctx.fillStyle = gradient
      ctx.beginPath()
      ctx.roundRect(0, 0, canvasWidth, canvasHeight, borderRadius)
      ctx.fill()

      // 完全重置阴影，绘制清晰边框
      ctx.shadowColor = 'rgba(0, 0, 0, 0)'
      ctx.shadowBlur = 0
      ctx.shadowOffsetX = 0
      ctx.shadowOffsetY = 0
      ctx.strokeStyle = '#FFED74'
      ctx.lineWidth = 1
      ctx.beginPath()
      ctx.roundRect(0, 0, canvasWidth, canvasHeight, borderRadius)
      ctx.stroke()

      // 绘制头像（如果有）
      if (item.avatar && avatarSize > 0) {
        const img = new Image()
        img.crossOrigin = 'anonymous'
        img.onload = () => {
          ctx.save()
          ctx.beginPath()
          ctx.arc(paddingH + avatarSize / 2, canvasHeight / 2, avatarSize / 2, 0, Math.PI * 2)
          ctx.clip()
          ctx.drawImage(img, paddingH, (canvasHeight - avatarSize) / 2, avatarSize, avatarSize)
          ctx.restore()
        }
        img.src = item.avatar
      }

      // 绘制文字
      ctx.fillStyle = '#FFFFFF'
      ctx.font = `${fontWeight} ${fontSize}px ${fontFamily}`
      ctx.textAlign = 'left'
      ctx.textBaseline = 'middle'
      ctx.fillText(content, paddingH + avatarSize + avatarGap, canvasHeight / 2)
    } else if (item.type === 'system') {
      // 系统弹幕：绿色主题
      ctx.fillStyle = 'rgba(218, 255, 150, 0.2)'
      ctx.beginPath()
      ctx.roundRect(0, 0, canvasWidth, canvasHeight, borderRadius)
      ctx.fill()

      // 绘制边框
      ctx.strokeStyle = 'rgba(218, 255, 150, 0.5)'
      ctx.lineWidth = 1
      ctx.stroke()

      // 绘制文字
      ctx.fillStyle = '#DAFF96'
      ctx.font = `${fontWeight} ${fontSize}px ${fontFamily}`
      ctx.textAlign = 'left'
      ctx.textBaseline = 'middle'
      ctx.fillText(content, paddingH, canvasHeight / 2)
    } else {
      // 普通弹幕：根据Figma设计稿实现双层效果

      // 第一步：绘制渐变边框层（外层）
      const borderGradient = ctx.createLinearGradient(0, 0, canvasWidth, canvasHeight)
      borderGradient.addColorStop(0, '#FFFFFF')
      borderGradient.addColorStop(0.3606, '#FF95D8')
      borderGradient.addColorStop(0.7452, '#B476FF')
      borderGradient.addColorStop(1, '#FFE6E6')

      // 无阴影绘制渐变边框背景
      ctx.shadowColor = 'rgba(0, 0, 0, 0)'
      ctx.shadowBlur = 0
      ctx.fillStyle = borderGradient
      ctx.beginPath()
      ctx.roundRect(0, 0, canvasWidth, canvasHeight, borderRadius)
      ctx.fill()

      // 第二步：绘制内部半透明背景
      ctx.fillStyle = 'rgba(46, 23, 65, 0.58)'
      ctx.beginPath()
      ctx.roundRect(1, 1, canvasWidth - 2, canvasHeight - 2, borderRadius - 1)
      ctx.fill()

      // 绘制文字
      ctx.fillStyle = '#FFFFFF'
      ctx.font = `${fontWeight} ${fontSize}px ${fontFamily}`
      ctx.textAlign = 'left'
      ctx.textBaseline = 'middle'
      ctx.fillText(content, paddingH, canvasHeight / 2)
    }

    return canvas
  }

  // 添加弹幕
  const comment = {
    text: content, // 备用文本
    render: renderDanmaku,
    mode: 'rtl' as const // 从右到左
  }

  try {
    danmakuEngine.emit(comment)
    processedIds.value.add(item.id)
    console.log('Added danmaku:', content)

    // 自动移除处理
    if (props.autoRemove) {
      setTimeout(() => {
        processedIds.value.delete(item.id)
        emit('danmaku-complete', item.id)
      }, props.removeDelay)
    }
  } catch (error) {
    console.error('Failed to add danmaku:', error)
  }
}

// 监听弹幕数据变化
watch(
  () => props.danmaku,
  (newDanmaku, oldDanmaku) => {
    if (!danmakuEngine) return

    const oldLength = oldDanmaku?.length || 0
    const newLength = newDanmaku.length

    if (newLength > oldLength) {
      // 有新弹幕，添加到引擎
      const newItems = newDanmaku.slice(oldLength)

      newItems.forEach(addDanmakuToEngine)
    } else if (newLength === oldLength && newLength > 0) {
      // 长度相同但可能有新内容（旧的被移除，新的被添加）
      const latestItem = newDanmaku[newLength - 1]
      const oldLatestItem = oldDanmaku?.[oldLength - 1]

      if (latestItem && (!oldLatestItem || latestItem.id !== oldLatestItem.id)) {
        addDanmakuToEngine(latestItem)
      }
    }
  },
  { immediate: true }
)

// 监听可见性变化
watch(
  () => props.isVisible,
  (visible) => {
    if (danmakuEngine) {
      if (visible) {
        danmakuEngine.show()
      } else {
        danmakuEngine.hide()
      }
    }
  }
)

// 监听弹幕数组长度，如果变为0则清空引擎
watch(
  () => props.danmaku.length,
  (newLength) => {
    if (newLength === 0 && danmakuEngine) {
      danmakuEngine.clear()
      processedIds.value.clear()
    }
  }
)

// 清空所有弹幕
const clearAll = () => {
  if (danmakuEngine) {
    danmakuEngine.clear()
  }
  processedIds.value.clear()
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    initDanmakuEngine()
  })
})

onUnmounted(() => {
  if (danmakuEngine) {
    danmakuEngine.destroy()
    danmakuEngine = null
  }
  processedIds.value.clear()
})

// 暴露方法
defineExpose({
  clearAll,
  addDanmaku: addDanmakuToEngine
})
</script>

<style scoped>
.danmaku-engine {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  pointer-events: none;
}

.danmaku-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 确保弹幕容器不影响其他元素 */
.danmaku-container canvas {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  pointer-events: none !important;
  z-index: 5 !important;
}
</style>

<!-- 全局样式，用于弹幕样式 -->
<style>
/* 普通弹幕样式 - 根据Figma设计 */
.danmaku-normal {
  background: rgba(46, 23, 65, 0.58);
  border-radius: 68px;
  padding: 11px 17px;
  backdrop-filter: blur(8px);
  position: relative;
  display: inline-block;
}

.danmaku-normal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ffffff 0%, #ff95d8 36%, #b476ff 74%, #ffe6e6 100%);
  border-radius: 68px;
  z-index: -1;
}

.danmaku-normal::after {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  background: rgba(46, 23, 65, 0.58);
  border-radius: 67px;
  backdrop-filter: blur(8px);
  z-index: -1;
}

.danmaku-normal .danmaku-text {
  font-family: 'Work Sans', sans-serif;
  font-weight: 500;
  font-size: 10px;
  line-height: 1.17;
  color: #ffffff;
  text-align: center;
  position: relative;
  z-index: 1;
}

/* 特殊弹幕样式 - 渐变背景带头像 */
.danmaku-special {
  background: linear-gradient(135deg, #8574f3 0%, #df7aec 100%);
  border: 1px solid #ffed74;
  border-radius: 68px;
  padding: 10px 5px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0px 0px 11px 0px rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(8px);
}

.danmaku-special .danmaku-avatar {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.danmaku-special .danmaku-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.danmaku-special .danmaku-text {
  font-family: 'Work Sans', sans-serif;
  font-weight: 500;
  font-size: 10px;
  line-height: 1.17;
  color: #ffffff;
  text-align: center;
  padding-right: 11px;
  position: relative;
  z-index: 1;
}

/* 系统弹幕样式 */
.danmaku-system {
  background: rgba(218, 255, 150, 0.2);
  border: 1px solid rgba(218, 255, 150, 0.5);
  border-radius: 68px;
  padding: 10px 16px;
  backdrop-filter: blur(8px);
  display: inline-block;
}

.danmaku-system .danmaku-text {
  font-family: 'Work Sans', sans-serif;
  font-weight: 500;
  font-size: 10px;
  line-height: 1.17;
  color: #daff96;
  text-align: center;
  position: relative;
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .danmaku-normal,
  .danmaku-special,
  .danmaku-system {
    padding: 8px 12px;
  }

  .danmaku-normal .danmaku-text,
  .danmaku-special .danmaku-text,
  .danmaku-system .danmaku-text {
    font-size: 9px;
  }

  .danmaku-special {
    padding: 8px 4px;
  }

  .danmaku-special .danmaku-avatar {
    width: 20px;
    height: 20px;
  }

  .danmaku-special .danmaku-text {
    padding-right: 8px;
  }
}
</style>
