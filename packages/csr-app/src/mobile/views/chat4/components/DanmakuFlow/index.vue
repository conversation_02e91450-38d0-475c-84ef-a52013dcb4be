<template>
  <div class="danmaku-flow" v-if="isVisible">
    <div class="danmaku-container">
      <div
        v-for="(danmaku, index) in visibleDanmaku"
        :key="danmaku.id"
        class="danmaku-item"
        :style="getDanmakuStyle(index)"
      >
        <DanmakuBubble :danmaku="danmaku" @animation-complete="handleDanmakuComplete" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from 'vue'
import DanmakuBubble from './DanmakuBubble.vue'

interface Danmaku {
  id: string
  content: string
  type: 'normal' | 'special' | 'system'
  avatar?: string // 特殊弹幕可能有头像
  timestamp: number
}

interface Props {
  danmaku: Danmaku[]
  isVisible: boolean
  maxVisible?: number
  autoRemove?: boolean // 是否自动移除旧弹幕
  removeDelay?: number // 自动移除延迟（毫秒）
}

const props = withDefaults(defineProps<Props>(), {
  maxVisible: 8, // Concert场景显示更多弹幕
  autoRemove: true,
  removeDelay: 5000 // 5秒后自动移除
})

const emit = defineEmits<{
  'danmaku-complete': [danmakuId: string]
}>()

// 内部状态管理
const internalDanmaku = ref<Danmaku[]>([])
const removeTimers = ref<Map<string, number>>(new Map())

// 显示的弹幕列表
const visibleDanmaku = computed(() => {
  const visible = internalDanmaku.value.slice(-props.maxVisible)
  console.log('DanmakuFlow: visibleDanmaku computed', {
    internalCount: internalDanmaku.value.length,
    visibleCount: visible.length,
    propsCount: props.danmaku.length
  })
  return visible
})

// 生成弹幕样式（横向飘过效果）
const getDanmakuStyle = (index: number) => {
  // 简化的弹幕轨道配置
  const trackHeight = 40
  const totalTracks = 8 // 固定8个轨道
  const track = index % totalTracks

  // 简单的位置计算
  const top = track * trackHeight + 60 // 60px顶部边距

  // 固定的动画时长，先测试基本功能
  const duration = 8

  // 移除延迟，先让弹幕立即显示
  const delay = 0

  console.log('getDanmakuStyle:', {
    index,
    track,
    top,
    duration,
    delay
  })

  return {
    position: 'absolute' as const,
    top: `${top}px`,
    right: '0', // 从屏幕右边缘开始
    animation: `danmaku-slide ${duration}s linear ${delay}s forwards`,
    zIndex: 10 + index,
    animationFillMode: 'both'
  }
}

// 添加弹幕
const addDanmaku = (newDanmaku: Danmaku) => {
  internalDanmaku.value.push(newDanmaku)

  // 如果启用自动移除，设置定时器
  if (props.autoRemove) {
    const timer = window.setTimeout(() => {
      removeDanmaku(newDanmaku.id)
    }, props.removeDelay)

    removeTimers.value.set(newDanmaku.id, timer)
  }

  // 限制最大数量，移除最旧的
  if (internalDanmaku.value.length > props.maxVisible * 2) {
    const removed = internalDanmaku.value.shift()
    if (removed) {
      clearRemoveTimer(removed.id)
    }
  }
}

// 移除弹幕
const removeDanmaku = (danmakuId: string) => {
  const index = internalDanmaku.value.findIndex((d) => d.id === danmakuId)
  if (index !== -1) {
    internalDanmaku.value.splice(index, 1)
    clearRemoveTimer(danmakuId)
    emit('danmaku-complete', danmakuId)
  }
}

// 清理定时器
const clearRemoveTimer = (danmakuId: string) => {
  const timer = removeTimers.value.get(danmakuId)
  if (timer) {
    window.clearTimeout(timer)
    removeTimers.value.delete(danmakuId)
  }
}

// 处理弹幕动画完成
const handleDanmakuComplete = (_danmakuId: string) => {
  // 由定时器控制移除，这里不做处理
}

// 监听外部弹幕变化
watch(
  () => props.danmaku,
  (newDanmaku, oldDanmaku) => {
    console.log('DanmakuFlow: watch triggered', {
      newLength: newDanmaku.length,
      oldLength: oldDanmaku?.length || 0,
      internalLength: internalDanmaku.value.length
    })

    // 计算需要添加的新弹幕
    const oldLength = oldDanmaku?.length || 0
    const newLength = newDanmaku.length

    if (newLength > oldLength) {
      // 有新弹幕，直接添加到内部状态
      const newItems = newDanmaku.slice(oldLength)
      console.log('DanmakuFlow: Adding new items', newItems)

      newItems.forEach((item) => {
        internalDanmaku.value.push(item)

        // 如果启用自动移除，设置定时器
        if (props.autoRemove) {
          const timer = window.setTimeout(() => {
            removeDanmaku(item.id)
          }, props.removeDelay)

          removeTimers.value.set(item.id, timer)
        }
      })

      console.log('DanmakuFlow: Internal count after adding', internalDanmaku.value.length)

      // 限制最大数量，移除最旧的
      if (internalDanmaku.value.length > props.maxVisible * 2) {
        const toRemove = internalDanmaku.value.length - props.maxVisible * 2
        for (let i = 0; i < toRemove; i++) {
          const removed = internalDanmaku.value.shift()
          if (removed) {
            clearRemoveTimer(removed.id)
          }
        }
      }
    } else if (newLength === 0 && oldLength > 0) {
      // 外部弹幕被清空，清空内部状态
      internalDanmaku.value = []
      clearAllTimers()
    }
  },
  { immediate: true }
)

// 清理所有定时器
const clearAllTimers = () => {
  removeTimers.value.forEach((timer) => window.clearTimeout(timer))
  removeTimers.value.clear()
}

onUnmounted(() => {
  clearAllTimers()
})

// 暴露方法给父组件
defineExpose({
  addDanmaku,
  removeDanmaku,
  clearAll: () => {
    internalDanmaku.value = []
    clearAllTimers()
  }
})
</script>

<style lang="less" scoped>
.danmaku-flow {
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  position: relative;
}

.danmaku-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.danmaku-item {
  position: absolute;
  white-space: nowrap;
  pointer-events: none;
}

// 弹幕横向飘过动画
@keyframes danmaku-slide {
  0% {
    transform: translateX(100%); // 从右侧开始（完全在屏幕外）
    opacity: 1;
  }

  100% {
    transform: translateX(-100vw); // 移动到屏幕左侧外
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 375px) {
  .danmaku-item {
    font-size: 0.9em;
  }
}
</style>
