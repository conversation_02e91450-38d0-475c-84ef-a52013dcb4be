<template>
  <div class="video-call-container">
    <!-- 增强背景容器 -->
    <EnhancedChatContainer
      :is-playing-video="false"
      :background-video="backgroundVideo"
      :background-image="backgroundImage"
      :default-image="backgroundImage"
      class="background-container"
    >
      <template #chat-interface>
        <!-- 视频通话界面内容 -->
        <div class="video-call-content">
          <!-- 返回按钮 -->
          <button class="back-button" @click="$emit('back-click')">
            <component :is="BackArrowIcon" />
          </button>

          <!-- 静音按钮 -->
          <button class="mute-button" @click="$emit('toggle-mute')" :class="{ active: isMuted }">
            <component :is="MuteIcon" />
          </button>

          <!-- 通话时长 -->
          <div class="call-duration">
            {{ formattedDuration }}
          </div>

          <!-- 消息气泡 - 统一位置显示用户消息或角色消息 -->
          <div v-if="shouldShowAnyMessage" class="message-bubble">
            <!-- 用户消息 -->
            <div v-if="userMessage" class="user-message-content">
              {{ userMessage }}
            </div>

            <!-- 角色消息 -->
            <div v-else-if="shouldShowMessage" class="character-message-wrapper">
              <div class="avatar">
                <img :src="characterAvatar" :alt="characterName" />
              </div>
              <div class="message-content">
                <span v-if="isActorThinking" class="thinking-text">
                  {{ characterName }} is thinking
                  <span class="typing-dots"> <span>.</span><span>.</span><span>.</span> </span>
                </span>
                <span v-else class="typewriter-text">{{ displayMessage }}</span>
              </div>
            </div>
          </div>

          <!-- 底部控制栏 -->
          <div class="bottom-controls">
            <!-- 对话按钮 -->
            <button class="chat-button" @click="toggleChatInput" :class="{ pulse: hasNewMessage }">
              <component :is="ChatIcon" />
            </button>

            <!-- 挂断按钮 -->
            <button class="hangup-button" @click="$emit('hangup-click')">
              <component :is="HangupIcon" />
            </button>
          </div>

          <!-- 互动提示 -->
          <div v-if="showInteractionHint" class="interaction-hint">
            <div class="hint-bubble">
              💬 {{ characterName }} seems to be waiting for your response...
            </div>
          </div>

          <!-- 发送反馈 -->
          <div v-if="showSendFeedback" class="send-feedback">✓ Sent</div>

          <!-- 聊天输入框 -->
          <VideoCallInput
            v-if="showChatInput"
            :placeholder="inputPlaceholder"
            @send-message="handleSendMessage"
            @typing="handleUserTyping"
            @close="showChatInput = false"
          />
        </div>
      </template>
    </EnhancedChatContainer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue'
import { useChatMessagesStore } from '@/store/chat-messages'
import EnhancedChatContainer from '@/mobile/components/EnhancedChatContainer.vue'
import VideoCallInput from '../VideoCallInput/index.vue'
import BackArrowIcon from '@/assets/icon/back-arrow.svg'
import MuteIcon from '@/assets/icon/mute-icon.svg'
import ChatIcon from '@/assets/icon/chat-icon.svg'
import HangupIcon from '@/assets/icon/hangup-icon.svg'

interface Props {
  characterName?: string
  characterAvatar?: string
  backgroundImage?: string
  backgroundVideo?: string
  isMuted?: boolean
  latestMessage?: string
}

interface Emits {
  (e: 'back-click'): void
  (e: 'hangup-click'): void
  (e: 'toggle-mute'): void
  (e: 'send-message', message: string): void
}

const props = withDefaults(defineProps<Props>(), {
  characterName: 'Tsunade',
  characterAvatar: '',
  backgroundImage: '',
  backgroundVideo: '',
  isMuted: false,
  latestMessage: ''
})

const emit = defineEmits<Emits>()

// Store
const chatMessagesStore = useChatMessagesStore()

// 状态
const showChatInput = ref(false)
const callStartTime = ref(Date.now())
const currentTime = ref(Date.now())
const displayMessage = ref('')
const userMessage = ref('')
const messageTimer = ref<ReturnType<typeof setTimeout> | null>(null)
const userMessageTimer = ref<ReturnType<typeof setTimeout> | null>(null)

// 新增状态
const hasNewMessage = ref(false)
const showInteractionHint = ref(false)
const showSendFeedback = ref(false)
const interactionHintTimer = ref<ReturnType<typeof setTimeout> | null>(null)

// 固定的输入提示语
const inputPlaceholder = computed(() => `Tell ${props.characterName} what's on your mind...`)

// 计算属性
const isActorThinking = computed(() => chatMessagesStore.isActorThinking)

const shouldShowMessage = computed(() => {
  return displayMessage.value || isActorThinking.value
})

const shouldShowAnyMessage = computed(() => {
  // 优先显示用户消息，如果没有用户消息则显示角色消息
  return userMessage.value || shouldShowMessage.value
})

// 通话时长计算
const formattedDuration = computed(() => {
  const duration = Math.floor((currentTime.value - callStartTime.value) / 1000)
  const minutes = Math.floor(duration / 60)
  const seconds = duration % 60
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})

// 切换聊天输入框
const toggleChatInput = () => {
  showChatInput.value = !showChatInput.value

  if (showChatInput.value) {
    // 打开输入框时重置状态
    hasNewMessage.value = false
    hideInteractionHint()
  }
}

// 处理用户输入状态
const handleUserTyping = () => {
  // 用户开始输入时隐藏互动提示
  hideInteractionHint()
}

// 显示发送反馈
const showSendingFeedback = () => {
  showSendFeedback.value = true
  setTimeout(() => {
    showSendFeedback.value = false
  }, 2000)
}

// 显示互动提示
const showInteractionHintDelayed = () => {
  hideInteractionHint()
  interactionHintTimer.value = setTimeout(() => {
    showInteractionHint.value = true
  }, 10000) // 10秒后显示提示
}

// 隐藏互动提示
const hideInteractionHint = () => {
  showInteractionHint.value = false
  if (interactionHintTimer.value) {
    clearTimeout(interactionHintTimer.value)
    interactionHintTimer.value = null
  }
}

// 处理发送消息
const handleSendMessage = (message: string) => {
  // 显示发送反馈
  showSendingFeedback()

  // 隐藏互动提示
  hideInteractionHint()

  // 重置状态
  hasNewMessage.value = false

  emit('send-message', message)
  showChatInput.value = false
}

// 监听消息变化，实现3秒显示逻辑
watch(
  () => chatMessagesStore.messages,
  (newMessages, oldMessages) => {
    if (newMessages.length > 0) {
      const latestMessage = newMessages[newMessages.length - 1]

      // 检查是否有新消息
      if (oldMessages && newMessages.length > oldMessages.length) {
        if (latestMessage.sender_type === 'actor') {
          hasNewMessage.value = true

          // 如果聊天输入框没有打开，显示互动提示
          if (!showChatInput.value) {
            showInteractionHintDelayed()
          }
        }
      }

      if (latestMessage.sender_type === 'user' && latestMessage.content?.text) {
        // 处理用户消息
        if (userMessageTimer.value) {
          clearTimeout(userMessageTimer.value)
        }

        // 立即显示用户消息
        userMessage.value = latestMessage.content.text

        // 3秒后隐藏用户消息
        userMessageTimer.value = setTimeout(() => {
          userMessage.value = ''
          // 用户消息隐藏后，如果有角色消息，确保角色消息能显示
        }, 3000)
      } else if (latestMessage.sender_type === 'actor' && latestMessage.content?.text) {
        // 处理角色消息
        if (messageTimer.value) {
          clearTimeout(messageTimer.value)
        }

        // 立即显示角色消息，不设置自动隐藏
        displayMessage.value = latestMessage.content.text
      }
    }
  },
  { deep: true }
)

// 定时器更新时间
let timer: ReturnType<typeof setInterval> | null = null

onMounted(() => {
  timer = setInterval(() => {
    currentTime.value = Date.now()
  }, 1000)
})

onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer)
  }

  if (messageTimer.value) {
    clearTimeout(messageTimer.value)
  }

  if (userMessageTimer.value) {
    clearTimeout(userMessageTimer.value)
  }

  // 清理新增的定时器
  hideInteractionHint()
})
</script>

<style lang="less" scoped>
.video-call-container {
  position: relative;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  overflow: hidden;

  .background-container {
    width: 100%;
    height: 100%;
  }
}

.video-call-content {
  position: relative;
  width: 100%;
  height: 100%;
}

.back-button {
  position: absolute;
  top: 20px;
  left: 7px;
  width: 36px;
  height: 36px;
  background: rgba(0, 0, 0, 0.3);
  border: none;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  backdrop-filter: blur(20px);
  cursor: pointer;

  svg {
    color: white;
  }
}

.mute-button {
  position: absolute;
  top: 20px;
  right: 12px;
  width: 42px;
  height: 42px;
  background: rgba(31, 0, 56, 0.5);
  border: none;
  border-radius: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    background: rgba(255, 83, 83, 0.8);
  }

  svg {
    width: 20px;
    height: 20px;
    color: white;
  }
}

.call-duration {
  position: absolute;
  top: 28px;
  left: 50%;
  transform: translateX(-50%);
  background: #ff5353;
  color: white;
  padding: 4px 14px;
  border-radius: 100px;
  font-family: 'Work Sans', sans-serif;
  font-weight: 500;
  font-size: 11px;
  z-index: 10;
}

.message-bubble {
  position: absolute;
  bottom: 160px;
  left: 36px;
  right: 36px;
  z-index: 10;

  .user-message-content {
    background: rgba(202, 147, 242, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 12px;
    font-family: 'Work Sans', sans-serif;
    font-weight: 500;
    font-size: 15px;
    max-width: 70%;
    word-wrap: break-word;
    margin-left: auto;
    display: block;
    text-align: right;
    width: fit-content;
  }

  .character-message-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;

    .avatar {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      overflow: hidden;
      border: 1px solid rgba(0, 0, 0, 0.5);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .message-content {
      background: rgba(255, 255, 255, 0.9);
      color: #1f0038;
      padding: 8px 12px;
      border-radius: 12px;
      font-family: 'Work Sans', sans-serif;
      font-weight: 500;
      font-size: 15px;
      border: 1px solid #1f0038;
      max-width: calc(100% - 40px);

      .thinking-text {
        color: #666;
        font-style: italic;
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .typing-dots {
        display: inline-flex;
        gap: 2px;

        span {
          width: 3px;
          height: 3px;
          background: #666;
          border-radius: 50%;
          animation: typing 1.4s infinite;

          &:nth-child(2) {
            animation-delay: 0.2s;
          }

          &:nth-child(3) {
            animation-delay: 0.4s;
          }
        }
      }

      .typewriter-text {
        animation: fadeIn 0.5s ease-in;
      }
    }
  }
}

.bottom-controls {
  position: absolute;
  bottom: 30px;
  left: 50px;
  right: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10;
}

.chat-button {
  width: 60px;
  height: 60px;
  background: #4c3c59;
  border: none;
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    background: #5a4a67;
    transform: scale(1.05);
  }

  &.pulse {
    animation: pulse 2s infinite;
  }

  svg {
    color: #ca93f2;
  }
}

.hangup-button {
  width: 60px;
  height: 60px;
  background: #ff5353;
  border: none;
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #ff4040;
    transform: scale(1.05);
  }

  svg {
    color: white;
  }
}

// 互动提示样式
.interaction-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 15;
  animation: fadeInUp 0.5s ease-out;

  .hint-bubble {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 16px;
    border-radius: 20px;
    font-size: 14px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}

// 发送反馈样式
.send-feedback {
  position: absolute;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(76, 175, 80, 0.9);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  z-index: 15;
  animation: slideUpFade 2s ease-out forwards;
}

// 动画定义
@keyframes thinking {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes typing {
  0%,
  60%,
  100% {
    opacity: 0;
  }
  30% {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(202, 147, 242, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(202, 147, 242, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(202, 147, 242, 0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate(-50%, -40%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

@keyframes slideUpFade {
  0% {
    opacity: 0;
    transform: translate(-50%, 20px);
  }
  20% {
    opacity: 1;
    transform: translate(-50%, 0);
  }
  80% {
    opacity: 1;
    transform: translate(-50%, 0);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -20px);
  }
}
</style>
