<template>
  <div class="map-container">
    <!-- 通用头部组件 -->
    <CommonHeader
      :character-name="characterName"
      :character-avatar="characterAvatar"
      :show-viewer-count="false"
      @back-click="handleBackClick"
    >
      <template #back-button>
        <span class="back-icon"><icon-left /></span>
      </template>
    </CommonHeader>

    <!-- 地图背景 -->
    <div class="map-background">
      <img
        :src="mergedMapConfig.backgroundImage"
        alt="Map Background"
        class="background-image"
      />
    </div>

    <!-- 地图位置点 -->
    <div class="map-locations">
      <MapLocationCard
        v-for="location in mergedMapConfig.locations"
        :key="location.id"
        :location="location"
        :favorability-state="favorabilityState"
        @click="handleLocationClick"
      />
    </div>

    <!-- 场景解锁弹窗 -->
    <SceneUnlockModal
      v-if="showUnlockModal"
      :visible="showUnlockModal"
      :scene-name="lockedLocationName"
      :required-level="lockedLocationRequiredLevel"
      :required-coins="lockedLocationRequiredCoins"
      :required-heart-value="lockedLocationRequiredHeartValue"
      :current-level="currentLevelFormatted"
      :current-heart-value="currentHeartValue"
      @close="handleCloseUnlockModal"
      @boost-favorability="handleBoostFavorability"
    />

    <!-- 钻石消费确认弹窗 -->
    <PaymentConfirmModal
      ref="paymentModalRef"
      v-model:visible="showPaymentModal"
      :coins="totalCost"
      @confirm="handlePaymentConfirm"
      @cancel="handlePaymentCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { MapConfig, MapLocation, MapLocationClickEvent } from '@/types/map'
import type { FavorabilityState } from '@/types/favorability'
import { DEFAULT_MAP_CONFIG } from '@/types/map'
import { SceneUnlockUtils } from '@/types/favorability'
import { Chat4SceneId } from '@/types/chat4-scene'
import { useChat4Store } from '@/store/chat4'
import { useChatResourcesStore } from '@/store/chat-resources'
import { useStoryStore } from '@/store/story'
import { useChatEventsStore } from '@/store/chat-events'
import MapLocationCard from './MapLocationCard.vue'
import SceneUnlockModal from '../SceneUnlockModal/index.vue'
import CommonHeader from '../CommonHeader/index.vue'
import PaymentConfirmModal from '../PaymentConfirmModal/index.vue'
import IconLeft from '@arco-design/web-vue/es/icon/icon-left'
import { buyHeartValue, type BuyHeartValueParams } from '@/api/game'

interface Props {
  /** 地图配置 */
  mapConfig?: MapConfig
  /** 好感度状态 */
  favorabilityState: FavorabilityState
}

interface Emits {
  /** 位置点击事件 */
  (e: 'location-click', event: MapLocationClickEvent): void
  /** 返回事件 */
  (e: 'back-click'): void
}

const props = withDefaults(defineProps<Props>(), {
  mapConfig: () => DEFAULT_MAP_CONFIG,
})

const emit = defineEmits<Emits>()

// 使用stores
const chat4Store = useChat4Store()
const resourcesStore = useChatResourcesStore()
const storyStore = useStoryStore()
const chatEventsStore = useChatEventsStore()

// 角色信息
const characterName = computed(
  () => storyStore.currentActor?.name || 'Character',
)
const characterAvatar = computed(
  () => storyStore.currentActor?.avatar_url || '',
)

// 全局好感度数据
const currentHeartValue = computed(
  () => chatEventsStore.favorabilityState.currentHeartValue,
)
const currentLevel = computed(
  () => chatEventsStore.favorabilityState.currentLevel,
)
const currentLevelFormatted = computed(() => {
  const level = currentLevel.value
  if (!level) return 'Lv0'
  // 将 level0, level1, level2 格式化为 Lv0, Lv1, Lv2
  return level.replace('level', 'Lv')
})

// 动态地图配置 - 从chat4 store和resources store获取
const mergedMapConfig = computed<MapConfig>(() => {
  // 从resources store获取背景图片
  const backgroundImage = resourcesStore.currentActorId
    ? resourcesStore.backgroundImageMap[resourcesStore.currentActorId] || ''
    : ''

  // 从chat4 store获取地图位置数据，转换为可变数组并修正类型
  const locations: MapLocation[] =
    chat4Store.mapLocations.length > 0
      ? chat4Store.mapLocations.map((loc) => ({
          ...loc,
          meetupScene: loc.meetupScene as Chat4SceneId, // 类型断言
        }))
      : props.mapConfig.locations

  return {
    backgroundImage: backgroundImage || props.mapConfig.backgroundImage,
    locations,
  }
})

// 解锁弹窗状态
const showUnlockModal = ref(false)
const lockedLocationName = ref('')
const lockedLocationRequiredLevel = ref('')
const lockedLocationRequiredCoins = ref(0)
const lockedLocationRequiredHeartValue = ref(0)
const currentLockedLocation = ref<MapLocation | null>(null)

// 钻石消费确认相关状态
const showPaymentModal = ref(false)
const costPerHeart = 1 // 每个心值的钻石成本：1钻石 = 1心值
const savedRequiredHeartValue = ref(0) // 保存的需求心值，避免响应式更新问题
const paymentModalRef = ref<any>(null) // PaymentConfirmModal组件引用

// 计算总费用
const totalCost = computed(() => {
  // 使用保存的需求心值或当前的需求心值
  const requiredHeartValue =
    savedRequiredHeartValue.value || lockedLocationRequiredHeartValue.value || 0
  const currentHeart = currentHeartValue.value || 0
  const heartValueNeeded = Math.max(0, requiredHeartValue - currentHeart)
  const cost = heartValueNeeded * costPerHeart
  return cost
})

/**
 * 检查位置是否解锁
 */
const isLocationUnlocked = (location: MapLocation): boolean => {
  if (!location.isLocked) return true

  const condition = location.unlockCondition
  if (!condition) return true

  // 检查好感度等级
  if (condition.requiredLevel) {
    const isLevelUnlocked = SceneUnlockUtils.isLevelUnlocked(
      condition.requiredLevel,
      props.favorabilityState.currentLevel,
    )
    if (!isLevelUnlocked) return false
  }

  // 检查心动值
  if (condition.requiredHeartValue) {
    if (
      props.favorabilityState.currentHeartValue < condition.requiredHeartValue
    ) {
      return false
    }
  }

  return true
}

/**
 * 获取解锁条件文本
 */
const getUnlockConditionText = (location: MapLocation): string => {
  const condition = location.unlockCondition
  if (!condition) return ''

  if (condition.requiredLevel) {
    const levelNumber = condition.requiredLevel.replace('level', '')
    return `Lv${levelNumber}`
  }

  return ''
}

/**
 * 处理位置点击
 */
const handleLocationClick = (location: MapLocation) => {
  // 检查是否解锁
  if (!isLocationUnlocked(location)) {
    // 保存当前锁定的地点信息
    currentLockedLocation.value = location

    showUnlockModal.value = true
    lockedLocationName.value = location.name
    lockedLocationRequiredLevel.value = getUnlockConditionText(location)
    lockedLocationRequiredCoins.value =
      location.unlockCondition?.requiredCoins || 0
    lockedLocationRequiredHeartValue.value =
      location.unlockCondition?.requiredHeartValue || 10 // 默认10个心值

    return
  }

  // 发送位置点击事件
  const event: MapLocationClickEvent = {
    location,
    timestamp: Date.now(),
  }

  emit('location-click', event)
}

/**
 * 处理返回按钮点击
 */
const handleBackClick = () => {
  emit('back-click')
}

/**
 * 处理提升好感度按钮点击 - 直接显示钻石消费确认弹窗
 */
const handleBoostFavorability = () => {
  // 保存当前的需求心值，避免在关闭弹窗后丢失
  savedRequiredHeartValue.value = lockedLocationRequiredHeartValue.value

  // 关闭解锁弹窗
  handleCloseUnlockModal()

  // 直接打开钻石消费确认弹窗
  showPaymentModal.value = true
}

/**
 * 处理钻石消费确认
 */
const handlePaymentConfirm = async () => {
  if (!storyStore.currentStory?.id || !storyStore.currentActor?.id) {
    return
  }

  try {
    // 调用buyHeartValue API
    const params: BuyHeartValueParams = {
      story_id: storyStore.currentStory.id,
      actor_id: storyStore.currentActor.id,
      coins: totalCost.value,
    }

    const response = await buyHeartValue(params)

    if (response.code === '0' && response.data.heart_value !== undefined) {
      // 更新本地好感度状态
      chatEventsStore.favorabilityState.currentHeartValue =
        response.data.heart_value

      // 重新计算等级信息和场景解锁状态
      chatEventsStore.updateFavorabilityLevelInfo()

      // 重置loading状态
      if (paymentModalRef.value) {
        paymentModalRef.value.resetLoading()
      }

      // 关闭购买弹窗
      showPaymentModal.value = false

      // 清理保存的值
      savedRequiredHeartValue.value = 0

      // 可以触发地图刷新或其他后续操作
    } else {
      // 重置loading状态
      if (paymentModalRef.value) {
        paymentModalRef.value.resetLoading()
      }
      // 这里可以显示错误提示
    }
  } catch (error) {
    // 重置loading状态
    if (paymentModalRef.value) {
      paymentModalRef.value.resetLoading()
    }
    // 这里可以显示错误提示
  }
}

/**
 * 处理钻石消费取消
 */
const handlePaymentCancel = () => {
  showPaymentModal.value = false
  // 清理保存的值
  savedRequiredHeartValue.value = 0
}

/**
 * 关闭解锁弹窗
 */
const handleCloseUnlockModal = () => {
  showUnlockModal.value = false
  lockedLocationName.value = ''
  lockedLocationRequiredLevel.value = ''
  lockedLocationRequiredCoins.value = 0
  lockedLocationRequiredHeartValue.value = 0
  currentLockedLocation.value = null
}
</script>

<style lang="less" scoped>
.map-container {
  position: relative;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  margin: 0 auto; // 居中显示
  overflow: hidden;
  background: #000;
}

.map-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;

  .background-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;

  .top-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 121px;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0.6) 0%,
      rgba(255, 255, 255, 0) 100%
    );
  }

  .bottom-gradient {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 121px;
    background: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.6) 0%,
      rgba(255, 255, 255, 0) 100%
    );
  }
}

.map-locations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
}
</style>
