<template>
  <div class="stream-controls">
    <!-- 评论输入框 -->
    <div class="comment-input-container">
      <div class="input-wrapper">
        <input
          v-model="commentText"
          type="text"
          placeholder="Drop your comment here"
          class="comment-input"
          @keyup.enter="handleSendComment"
          maxlength="200"
        />
        <button class="send-button" @click="handleSendComment" :disabled="!commentText.trim()">
          <span>→</span>
        </button>
      </div>
    </div>

    <!-- 控制按钮组 -->
    <div class="controls-group">
      <!-- 礼物按钮 -->
      <button class="control-button gift-button" @click="handleGiftClick">
        <GiftIcon class="button-icon" />
      </button>

      <!-- 心形点赞按钮 -->
      <button
        class="control-button heart-button"
        @click="handleHeartClick"
        :class="{ clicking: isHeartClicking }"
      >
        <HeartIcon class="button-icon" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import GiftIcon from '@/assets/icon/gift2-icon.svg'
import HeartIcon from '@/assets/icon/heart-icon.svg'

interface Props {
  isMuted: boolean
}

defineProps<Props>()

const emit = defineEmits<{
  'send-comment': [comment: string]
  'open-gift-modal': []
  'heart-click': []
  'toggle-mute': []
}>()

const commentText = ref('')
const isHeartClicking = ref(false)

const handleSendComment = () => {
  if (commentText.value.trim()) {
    emit('send-comment', commentText.value.trim())
    commentText.value = ''
  }
}

const handleGiftClick = () => {
  // 打开礼物选择弹窗
  emit('open-gift-modal')
}

const handleHeartClick = () => {
  // 触发点击动画
  isHeartClicking.value = true
  setTimeout(() => {
    isHeartClicking.value = false
  }, 200)

  // 发送心形点赞事件
  emit('heart-click')
}
</script>

<style lang="less" scoped>
.stream-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0;
  width: 100%;
}

.comment-input-container {
  flex: 1;

  .input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    height: 36px;
    border-radius: 20px;
    background: #4c3c59;
    box-shadow: 0px 0px 10px rgba(218, 255, 150, 0.15);
    transition: box-shadow 0.2s ease;

    &:focus-within {
      box-shadow: 0px 0px 15px rgba(218, 255, 150, 0.25);
    }
  }

  .comment-input {
    flex: 1;
    height: 100%;
    padding: 0 15px;
    padding-right: 45px; /* 为发送按钮留出空间 */
    border: none;
    border-radius: 20px;
    background: transparent;
    color: white;
    font-family: 'Work Sans', sans-serif;
    font-weight: 400;
    font-size: 12px;
    line-height: 1.17;
    outline: none;

    &::placeholder {
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .send-button {
    position: absolute;
    right: 3px;
    width: 30px;
    height: 30px;
    background: #7c4dff;
    border: none;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
    font-size: 14px;
    font-weight: bold;

    &:hover:not(:disabled) {
      background: #6c3ce6;
      transform: scale(1.05);
    }

    &:active:not(:disabled) {
      transform: scale(0.95);
    }

    &:disabled {
      background: rgba(124, 77, 255, 0.3);
      cursor: not-allowed;
      transform: none;
    }
  }
}

.controls-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;

  &:active {
    transform: scale(0.95);
  }
}

.gift-button {
  background: #4c3c59;
  box-shadow: 0px 0px 10px rgba(218, 255, 150, 0.15);

  .button-icon {
    fill: #daff96;
  }

  &:hover {
    box-shadow: 0px 0px 15px rgba(218, 255, 150, 0.25);
  }
}

.heart-button {
  background: #4c3c59;
  box-shadow: 0px 0px 10px rgba(218, 255, 150, 0.15);
  position: relative;
  overflow: visible;

  .button-icon {
    fill: #ff004d;
  }

  &:hover {
    box-shadow: 0px 0px 15px rgba(218, 255, 150, 0.25);
  }

  &.clicking {
    animation: heartPulse 0.2s ease;
  }

  &:active .button-icon {
    transform: scale(1.2);
  }
}

@keyframes heartPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
</style>
