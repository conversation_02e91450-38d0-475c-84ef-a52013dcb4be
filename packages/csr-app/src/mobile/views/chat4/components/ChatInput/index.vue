<template>
  <div class="chat-input-figma" @click="handleInputClick">
    <input
      ref="inputRef"
      v-model="inputText"
      type="text"
      placeholder="Chat here"
      class="hidden-input"
      @keyup.enter="handleSend"
      @blur="handleBlur"
    />
    <span class="placeholder-text" :class="{ 'has-text': inputText }">
      {{ inputText || 'Chat here' }}
    </span>
    <button v-if="inputText.trim()" class="send-button" @click="handleSend">→</button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const emit = defineEmits<{
  'send-message': [message: string]
}>()

const inputText = ref('')
const inputRef = ref<HTMLInputElement>()

const handleSend = () => {
  const message = inputText.value.trim()
  if (message) {
    emit('send-message', message)
    inputText.value = ''
  }
}

const handleInputClick = () => {
  inputRef.value?.focus()
}

const handleBlur = () => {
  // 延迟失焦，避免点击发送按钮时输入框失焦
  setTimeout(() => {
    if (document.activeElement !== inputRef.value) {
      // 输入框真正失焦
    }
  }, 100)
}

// 暴露方法给父组件
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur()
})
</script>

<style lang="less" scoped>
.chat-input-figma {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  cursor: text;

  .hidden-input {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    border: none;
    outline: none;
    background: transparent;
    font-size: 12px;
    padding: 0 15px;
  }

  .placeholder-text {
    font-family: 'Work Sans', sans-serif;
    font-weight: 400;
    font-size: 12px;
    line-height: 1.173;
    color: rgba(255, 255, 255, 0.7);
    flex: 1;
    pointer-events: none;

    &.has-text {
      color: #ffffff;
    }
  }

  .send-button {
    width: 24px;
    height: 24px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}
</style>
