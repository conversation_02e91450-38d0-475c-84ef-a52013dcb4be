import { ref, reactive } from 'vue'

export type ToastType = 'unlock' | 'success' | 'cooldown'

export interface ToastOptions {
  message: string
  type?: ToastType
  duration?: number
}

interface ToastState {
  visible: boolean
  message: string
  type: ToastType
  duration: number
}

const toastState = reactive<ToastState>({
  visible: false,
  message: '',
  type: 'unlock',
  duration: 3000
})

let toastQueue: ToastOptions[] = []
let isShowing = false

export function useChat4Toast() {
  const showToast = (options: ToastOptions) => {
    if (isShowing) {
      // Add to queue if a toast is already showing
      toastQueue.push(options)
      return
    }

    isShowing = true
    toastState.message = options.message
    toastState.type = options.type || 'unlock'
    toastState.duration = options.duration || 3000
    toastState.visible = true
  }

  const hideToast = () => {
    toastState.visible = false
    isShowing = false
    
    // Show next toast in queue if any
    if (toastQueue.length > 0) {
      const nextToast = toastQueue.shift()!
      setTimeout(() => {
        showToast(nextToast)
      }, 100) // Small delay between toasts
    }
  }

  // Convenience methods for different toast types
  const showUnlockToast = (message: string, duration?: number) => {
    showToast({ message, type: 'unlock', duration })
  }

  const showSuccessToast = (message: string, duration?: number) => {
    showToast({ message, type: 'success', duration })
  }

  const showCooldownToast = (message: string, duration?: number) => {
    showToast({ message, type: 'cooldown', duration })
  }

  const clearQueue = () => {
    toastQueue = []
  }

  return {
    toastState,
    showToast,
    hideToast,
    showUnlockToast,
    showSuccessToast,
    showCooldownToast,
    clearQueue
  }
}
