<template>
  <div class="custom-node" :class="nodeClass">
    <div
      class="node-avatar"
      v-if="
        data.type === 'begin' ||
        data.type === 'begin-1' ||
        data.type === 'begin-2' ||
        data.type === 'begin-3'
      "
    >
      <img src="https://cdn.magiclight.ai/assets/playshot/default-avatar.png" alt="角色" />
    </div>
    <div class="node-content">
      <div class="node-icon" v-if="nodeIcon">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          width="16"
          height="16"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <template v-if="nodeIcon === 'play-circle'">
            <circle cx="12" cy="12" r="10" />
            <polygon points="10 8 16 12 10 16 10 8" />
          </template>
          <template v-else-if="nodeIcon === 'flag'">
            <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z" />
            <line x1="4" y1="22" x2="4" y2="15" />
          </template>
          <template v-else-if="nodeIcon === 'circle'">
            <circle cx="12" cy="12" r="10" />
          </template>
        </svg>
      </div>
      <div class="node-title">{{ data.name }}</div>
      <div class="node-id">{{ shortenId }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  node: any
  data: {
    name: string
    id: string
    type: string
  }
}>()

const nodeClass = computed(() => {
  const classes = ['node-type-' + props.data.type]
  if (props.data.type === 'begin') classes.push('node-special')
  if (props.data.type === 'end') classes.push('node-special')
  return classes
})

// 缩短 ID 显示
const shortenId = computed(() => {
  const id = props.data.id
  if (id === '_BEGIN_' || id === '_END_') return id
  return id.length > 8 ? id.substring(0, 8) + '...' : id
})

// 根据节点类型显示不同图标
const nodeIcon = computed(() => {
  const type = props.data.type
  if (type === 'begin') {
    return 'play-circle'
  } else if (type === 'end') {
    return 'flag'
  } else {
    return 'circle'
  }
})
</script>

<style lang="less" scoped>
.custom-node {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px;
  border-radius: 8px;
  background: #3a0066;
  border: 1px solid rgba(255, 255, 255, 0.1);
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);

  &:hover {
    background: #4a0085;
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
    z-index: 10;
  }

  .node-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .node-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 2px;

    .node-icon {
      width: 16px;
      height: 16px;
      margin-bottom: 4px;
      color: #ca93f2;
    }

    .node-title {
      font-size: 14px;
      font-weight: 500;
      color: #fff;
      margin-bottom: 4px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .node-id {
      font-size: 10px;
      color: rgba(255, 255, 255, 0.5);
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }

  &.node-type-begin {
    background: linear-gradient(90deg, #6d0fb8 0%, #9e24c7 100%);
    border-color: #ca93f2;
  }

  &.node-type-end {
    background: #2b0044;
    border-color: #7a5b91;
  }

  &.node-special {
    box-shadow: 0 2px 8px rgba(202, 147, 242, 0.3);
  }

  &.node-type-begin-1,
  &.node-type-begin-2,
  &.node-type-begin-3 {
    background: linear-gradient(90deg, #6d0fb8 0%, #9e24c7 100%);
    border-color: #ca93f2;
  }
}
</style>
