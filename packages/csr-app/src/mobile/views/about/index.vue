<template>
  <div class="legal-page">
    <div class="header">
      <div class="back-button" @click="router.back()">
        <icon-left />
      </div>
      <h1>About Us</h1>
    </div>

    <div class="content">
      <div class="section">
        <h2>Get in touch with {{ appName }}.AI</h2>
        <p>Welcome to {{ appName }}.AI - Your Global AI Friendship Hub</p>
        <p>
          At {{ appName }}.AI, we pioneer the future of social interaction through advanced AI
          technology. Our vision is a world connected through AI, where everyone has the opportunity
          to engage with intelligent and understanding AI friends.
        </p>
      </div>

      <div class="section">
        <h2>Our Philosophy</h2>
        <p>
          We believe in harnessing the power of AI to enrich human lives, offering a new dimension
          to socializing and the joy of discovering AI companionship. Our platform is a celebration
          of diversity and innovation, inviting users from all walks of life to meet and interact
          with an array of AI personalities.
        </p>
      </div>

      <div class="section">
        <h2>What We Offer</h2>
        <h3>Diverse AI Characters</h3>
        <p>
          Our platform hosts a multitude of AI friends from various backgrounds and interests, ready
          to engage in meaningful conversation and friendship.
        </p>

        <h3>Personalized Experiences</h3>
        <p>
          Customize your interactions and grow your relationships with AI that learn and remember,
          providing a truly personalized social experience.
        </p>

        <h3>Community Engagement</h3>
        <p>
          Join a global community of users who share your enthusiasm for AI interaction. Share your
          experiences and connect with others.
        </p>
      </div>

      <div class="section">
        <h2>Our Commitment to You</h2>
        <p>
          We are committed to creating a welcoming and inclusive space for all to explore the
          possibilities of AI companionship. We prioritize your privacy and ensure a safe
          environment for you to express yourself freely and form lasting AI friendships.
        </p>
      </div>

      <div class="section">
        <h2>Join the Community & Contact Us</h2>
        <p>
          Discover the joy of AI socialization at {{ appName }}.AI. Forge connections, enjoy
          continuous engagement, and be a part of our growing global community.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { IconLeft } from '@arco-design/web-vue/es/icon'
import { computed, onMounted } from 'vue'
import { useLegalSEO } from '@/composables/useSEO'

const router = useRouter()
const appName = computed(() => import.meta.env.VITE_APP_NAME || 'ReelPlay')
const { setLegalPageSEO } = useLegalSEO()

onMounted(() => {
  setLegalPageSEO('about')
})
</script>

<style lang="less" scoped>
.legal-page {
  height: calc(var(--vh, 1vh) * 100);
  background: #1f0038;
  color: rgba(255, 255, 255, 0.9);
  // padding-bottom: 40px;
}

.header {
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background: rgba(31, 0, 56, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .back-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all 0.3s;
    margin-right: 16px;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    svg {
      width: 20px;
      height: 20px;
      color: rgba(255, 255, 255, 0.9);
    }
  }

  h1 {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    color: #ca93f2;
  }
}

.content {
  background: #1f0038;
  margin: 0 auto;
  padding: 24px 20px;

  .section {
    margin-bottom: 32px;

    &.notice {
      background: rgba(202, 147, 242, 0.1);
      border-radius: 12px;
      padding: 20px;
      border: 1px solid rgba(202, 147, 242, 0.2);
    }

    h2 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 16px;
      color: #ca93f2;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 12px;
      color: #ca93f2;
    }

    p {
      margin: 0 0 16px;
      line-height: 1.6;
      color: rgba(255, 255, 255, 0.8);
      font-size: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    ul {
      margin: 12px 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        color: rgba(255, 255, 255, 0.8);
        font-size: 15px;
        line-height: 1.6;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
