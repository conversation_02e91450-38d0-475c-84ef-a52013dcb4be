<template>
  <div class="login-container">
    <div class="back-button" @click="router.push('/')">
      <icon-close />
    </div>

    <div class="login-content">
      <div class="logo-wrapper">
        <img :src="logoUrl" alt="ReelPlay" />
        <h2>Sign up to continue !</h2>
      </div>

      <div class="form-wrapper">
        <div class="social-buttons">
          <div class="social-row">
            <div class="social-item" v-if="!isAndroidWebView()">
              <button
                class="social-button google"
                @click="(e) => handleSocialLogin('google', e)"
                :disabled="loading"
              >
                <GoogleIcon />
              </button>
              <span class="social-name">Google</span>
            </div>
            <div class="social-item">
              <button
                class="social-button discord"
                @click="(e) => handleSocialLogin('discord', e)"
                :disabled="loading"
              >
                <DiscordIcon />
              </button>
              <span class="social-name">Discord</span>
            </div>
            <div class="social-item">
              <button
                class="social-button facebook"
                @click="(e) => handleSocialLogin('facebook', e)"
                :disabled="loading"
              >
                <FacebookIcon />
              </button>
              <span class="social-name">Facebook</span>
            </div>
          </div>
        </div>

        <div class="divider">
          <span>or</span>
        </div>

        <div class="input-group">
          <input
            v-model="form.email"
            type="email"
            placeholder="Enter your email"
            :class="{ error: errors.email }"
            @blur="validateEmail"
          />
          <span v-if="errors.email" class="error-text">{{ errors.email }}</span>
        </div>

        <div class="input-group verification-group">
          <input
            v-model="form.verificationCode"
            type="text"
            placeholder="Enter code"
            :class="{ error: errors.verificationCode }"
          />
          <button
            class="send-code-button"
            :class="{ 'is-inactive': !form.email }"
            @click="handleSendCode"
            :disabled="loading || countdown > 0"
          >
            {{ countdown > 0 ? `Resend (${countdown}s)` : 'Send' }}
          </button>
          <span v-if="errors.verificationCode" class="error-text">{{
            errors.verificationCode
          }}</span>
        </div>

        <button
          class="continue-button"
          :class="{ 'is-inactive': !form.email || !form.verificationCode }"
          @click="handleLogin"
          :disabled="loading"
        >
          <span v-if="!loading">Continue</span>
          <a-spin v-else />
        </button>

        <!-- <div class="register-link">
          Don't have an account?
          <span class="sign-up" @click="handleRegister">Sign Up</span>
        </div> -->
      </div>
    </div>

    <div class="terms-text">
      By signing in you agree with our<br />
      <a
        href="/terms"
        @click.prevent="!loading && router.push('/terms')"
        :class="{ disabled: loading }"
        >Terms of Service</a
      >
      ,
      <a
        href="/privacy"
        @click.prevent="!loading && router.push('/privacy')"
        :class="{ disabled: loading }"
        >Privacy Policy</a
      >,
      <a
        href="/complaints"
        @click.prevent="!loading && router.push('/complaints')"
        :class="{ disabled: loading }"
        >Complaints Policy</a
      >,
      <a
        href="/content-removal"
        @click.prevent="!loading && router.push('/content-removal')"
        :class="{ disabled: loading }"
        >Content Removal Policy</a
      >,
      <a
        href="/record-keeping"
        @click.prevent="!loading && router.push('/record-keeping')"
        :class="{ disabled: loading }"
        >18 U.S.C. 2257 Compliance</a
      >,
      <a
        href="/about"
        @click.prevent="!loading && router.push('/about')"
        :class="{ disabled: loading }"
        >About Us</a
      >,
      <a
        href="/refund"
        @click.prevent="!loading && router.push('/refund')"
        :class="{ disabled: loading }"
        >Refund and Returns Policy</a
      >.
    </div>

    <!-- <gender-modal v-model:visible="showGenderModal" @select="handleGenderSelect" /> -->
    <browser-guide-modal v-model:visible="showBrowserGuideModal" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@/mobile/components/Message'
import { useUserStore } from '@/store/user'
import { useUserSEO } from '@/composables/useSEO'
import GoogleIcon from '@/assets/icon/google.svg'
import DiscordIcon from '@/assets/icon/discord.svg'
import FacebookIcon from '@/assets/icon/facebook.svg'
// import GenderModal from '@/mobile/components/GenderModal.vue'
import { getSocialLoginUrl } from '@/api/social-login'
import type { SocialLoginType } from '@/api/social-login'
import { reportEvent, isInAppBrowser } from '@/utils'
import { ReportEvent } from '@/interface'
import { useThrottleFn } from '@vueuse/core'
import BrowserGuideModal from '@/mobile/components/BrowserGuideModal.vue'
import { sendVerificationCode } from '@/api/user'
import { isAndroidWebView } from '@/utils/isAndroidWebView'

const logoUrl = computed(
  () => import.meta.env.VITE_LOGO_URL || 'https://cdn.magiclight.ai/assets/playshot/logo-v2.png'
)

interface FormState {
  email: string
  verificationCode: string
}

interface FormErrors {
  email?: string
  verificationCode?: string
}

const router = useRouter()
const userStore = useUserStore()
const { setUserPageSEO } = useUserSEO()
const loading = ref(false)
const countdown = ref(0)

const form = reactive<FormState>({
  email: '',
  verificationCode: ''
})

const errors = reactive<FormErrors>({})

const showGenderModal = ref(false)
const showBrowserGuideModal = ref(false)
let pendingAction: (() => void) | null = null

const validateEmail = () => {
  if (!form.email) {
    errors.email = 'Email is required'
    return false
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(form.email)) {
    errors.email = 'Invalid email format'
    return false
  }

  errors.email = ''
  return true
}

const validateForm = (): boolean => {
  let isValid = true
  errors.verificationCode = ''

  // 验证邮箱
  if (!validateEmail()) {
    isValid = false
  }

  if (!form.verificationCode) {
    errors.verificationCode = 'Verification code is required'
    isValid = false
  }

  return isValid
}

const handleSocialLoginBase = async (type: SocialLoginType, event?: Event) => {
  if (event) {
    event.preventDefault()
  }

  if (loading.value) {
    return
  }

  if (type === 'google' && isInAppBrowser()) {
    showBrowserGuideModal.value = true
    return
  }

  reportEvent(ReportEvent.ClickLoginPageSignInWithGoogleOrDiscord, {
    userId: userStore.userInfo?.uuid,
    loginType: type
  })

  loading.value = true
  try {
    const origin = window.location.origin
    const redirect_url = `${origin}/user/social-callback?login_type=${type}`

    const response = await getSocialLoginUrl(type, redirect_url)
    if (!response.data?.isOk || !response.data?.data?.url) {
      throw new Error('Invalid response from server')
    }

    const loginUrl = response.data.data.url
    if (!loginUrl.startsWith('http')) {
      throw new Error('Invalid login URL')
    }

    // 使用 setTimeout 确保 loading 动画持续到跳转发生
    setTimeout(() => {
      window.location.href = loginUrl
    }, 0)
    return // 防止执行到 finally 块
  } catch (error: any) {
    console.error('Social login error:', error)
    Message.error(error.message || 'Failed to initiate social login')
    loading.value = false // 只在错误时才结束 loading
  }
}

const handleSocialLogin = useThrottleFn(handleSocialLoginBase, 2000)

const handleEmailLogin = () => {
  reportEvent(ReportEvent.ClickLoginPageSignInWithEmail, {
    userId: userStore.userInfo?.uuid
  })
  // showEmailForm.value = true
}

const handleFacebookLogin = () => {
  reportEvent(ReportEvent.ClickLoginPageSignInWithFacebook, {
    userId: userStore.userInfo?.uuid
  })
  Message.info('Facebook login is coming soon!')
}

const handleRegister = () => {
  reportEvent(ReportEvent.ClickLoginPageSignUp, {
    userId: userStore.userInfo?.uuid
  })
  router.push('/user/register')
}

const handleLogin = async () => {
  if (!validateForm()) return

  try {
    loading.value = true
    const isLogin = await userStore.loginWithCode({
      email: form.email,
      code: form.verificationCode,
      code_type: 'login',
      gender: 'male',
      user_id: userStore.userInfo?.uuid
    })
    if (!isLogin) return
    reportEvent(ReportEvent.LoginSuccess, {
      userId: userStore.userInfo?.uuid,
      type: 'email'
    })
    Message.success('Verification code login successful')
    router.push('/')
    return
  } catch (error: any) {
    console.error(error)
    Message.error(error.message || 'Login failed')
  } finally {
    loading.value = false
  }
}

const handleGenderSelect = async () => {
  if (pendingAction) {
    await pendingAction()
    pendingAction = null
  }
}

const handleSendCode = async () => {
  if (!validateEmail()) {
    Message.error(errors.email || 'Invalid email')
    return
  }
  try {
    loading.value = true
    const response = await sendVerificationCode({
      email: form.email,
      code_type: 'login'
    })
    if (!response.data.isOk) {
      Message.error(response.data.message)
      return
    }

    // Start countdown
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)

    Message.success('Verification code sent')
  } catch (error: any) {
    console.error(error)
    Message.error(error.message || 'Failed to send verification code')
  } finally {
    loading.value = false
  }
}

defineOptions({
  name: 'LoginView'
})

onMounted(() => {
  reportEvent(ReportEvent.LoginPageView, {
    userId: userStore.userInfo?.uuid
  })

  // 设置登录页面SEO
  setUserPageSEO('login')
})
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.login-container {
  height: calc(var(--vh, 1vh) * 100);
  background: var(--mobile-bg-primary);
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  position: relative;
  padding-bottom: 80px;
  transition: background 0.3s ease;
}

.back-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-secondary);
  transition:
    color 0.2s,
    background 0.3s ease;
  background: var(--bg-tertiary);
  border-radius: 50%;
  flex-shrink: 0;

  &:hover {
    color: var(--text-primary);
    background: var(--bg-hover);
  }

  :deep(.arco-icon) {
    font-size: 18px;
  }
}

.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-width: 320px;
  margin: 0 auto;
  width: 100%;
}

.logo-wrapper {
  margin-bottom: 48px;
  text-align: center;

  img {
    height: 35px;
    margin-bottom: 24px;
  }

  h2 {
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 700;
    margin: 0;
    line-height: 1.5;
    background: linear-gradient(90deg, var(--accent-color) 0%, var(--coins-color) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 8px rgba(202, 147, 242, 0.3);
    animation: shine 2s infinite;

    .bonus-text {
      display: block;
      font-size: 14px;
      color: var(--text-secondary);
      font-weight: 500;
      margin-top: 8px;
      background: none;
      -webkit-text-fill-color: var(--text-secondary);
      text-shadow: none;
    }

    @keyframes shine {
      0% {
        opacity: 1;
      }
      50% {
        opacity: 0.8;
      }
      100% {
        opacity: 1;
      }
    }
  }
}

.form-wrapper {
  width: 100%;

  .social-buttons {
    margin-bottom: 24px;
    animation: slideUp 0.5s ease;

    .social-row {
      display: flex;
      justify-content: space-between;
    }

    .social-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .social-name {
        color: var(--text-secondary);
        font-size: 12px;
        font-weight: 500;
      }
    }

    .social-button {
      width: 64px;
      height: 64px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      color: white;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: none;
      padding: 0;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: 0.5s;
      }

      &:hover::before {
        left: 100%;
      }

      &:hover {
        transform: translateY(-2px);
      }

      &:active {
        transform: translateY(1px);
      }

      &.google {
        background: #fff;
        box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
      }

      &.discord {
        background: #6563ff;
        box-shadow: 0 2px 8px rgba(88, 101, 242, 0.2);
      }

      &.facebook {
        background: #0866ff;
        box-shadow: 0 2px 8px rgba(24, 119, 242, 0.2);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        pointer-events: none;
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        pointer-events: none;
      }

      :deep(svg) {
        width: 24px;
        height: 24px;
      }
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .divider {
    display: flex;
    align-items: center;
    margin: 24px 0;

    &::before,
    &::after {
      content: '';
      flex: 1;
      height: 1px;
      background: var(--divider-color);
    }

    span {
      padding: 0 16px;
      color: var(--text-tertiary);
      font-size: 15px;
      white-space: nowrap;
      text-transform: lowercase;
    }
  }

  .email-button {
    width: 100%;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    border-radius: 24px;
    color: white;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    border: none;
    background: #ca93f2;
    color: #241d49;
    :deep(.arco-icon) {
      font-size: 20px;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.15);
    }

    &:active {
      transform: scale(0.98);
    }
  }

  .input-group {
    margin-bottom: 16px;

    input {
      width: 100%;
      height: 48px;
      background: var(--mobile-input-bg);
      border: 1px solid var(--mobile-input-border);
      border-radius: 24px;
      padding: 0 20px;
      color: var(--text-primary);
      font-size: 15px;
      font-weight: 600;
      transition: all 0.3s;

      &::placeholder {
        color: var(--text-primary);
        font-size: 15px;
        font-weight: 600;
        line-height: normal;
        opacity: 0.3;
      }

      &:focus {
        outline: none;
        border-color: var(--accent-color);
        background: var(--mobile-input-bg);
      }

      &.error {
        border-color: rgba(255, 77, 77, 0.5);
      }
    }

    .error-text {
      color: rgba(255, 77, 77, 0.9);
      font-size: 12px;
      margin-top: 6px;
      margin-left: 4px;
      display: block;
    }
  }

  .continue-button {
    width: 100%;
    height: 48px;
    border: none;
    border-radius: 24px;
    background: var(--accent-color);
    color: var(--bg-primary);
    font-size: 15px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s;

    &.is-inactive {
      opacity: 0.5;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }

    &:not(:disabled):hover {
      opacity: 0.9;
    }

    &:not(:disabled):active {
      transform: scale(0.98);
    }
  }

  .register-link {
    margin-top: 24px;
    color: var(--text-secondary);
    font-size: 14px;
    text-align: center;

    .sign-up {
      color: var(--accent-color);
      font-weight: 500;
      margin-left: 4px;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.terms-text {
  position: relative;
  margin-top: auto;
  padding: 24px 20px;
  color: var(--text-tertiary);
  font-size: 12px;
  line-height: 1.5;
  text-align: center;

  a {
    color: var(--accent-color);
    text-decoration: none;
    transition: opacity 0.3s;

    &:hover {
      text-decoration: underline;
    }

    &.disabled {
      opacity: 0.5;
      pointer-events: none;
      cursor: not-allowed;
    }
  }
}

.verification-group {
  position: relative;

  .send-code-button {
    position: absolute;
    right: 8px;
    top: 24px; /* 固定位置，不再使用相对定位 */
    transform: translateY(-50%);
    background: var(--accent-color);
    border: none;
    border-radius: 16px;
    padding: 6px 12px;
    color: var(--bg-primary);
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s;
    z-index: 2; /* 确保按钮在最上层 */

    &.is-inactive {
      opacity: 0.5;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }

  /* 确保错误文本不会影响按钮位置 */
  .error-text {
    margin-top: 4px;
    display: block;
    position: relative;
    z-index: 1;
  }
}

.login-options {
  text-align: right;
  margin-top: 8px;

  .switch-mode {
    color: var(--accent-color);
    font-size: 14px;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
}

/* 亮色主题特定样式 */
body.light-theme {
  .login-container {
    .social-button {
      &.google {
        background: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(0, 0, 0, 0.1);
      }

      &.discord {
        background: #6563ff;
        box-shadow: 0 2px 8px rgba(101, 99, 255, 0.3);
      }

      &.facebook {
        background: #0866ff;
        box-shadow: 0 2px 8px rgba(8, 102, 255, 0.3);
      }
    }

    .logo-wrapper h2 {
      text-shadow: 0 2px 8px rgba(202, 147, 242, 0.2);
    }

    .input-group input {
      background: var(--mobile-input-bg);
      border-color: var(--mobile-input-border);

      &:focus {
        border-color: var(--accent-color);
        box-shadow: 0 0 0 2px rgba(202, 147, 242, 0.1);
      }
    }

    .continue-button {
      box-shadow: 0 2px 8px rgba(202, 147, 242, 0.2);

      &:hover:not(:disabled) {
        box-shadow: 0 4px 12px rgba(202, 147, 242, 0.3);
      }
    }

    .send-code-button {
      box-shadow: 0 2px 4px rgba(202, 147, 242, 0.2);
    }
  }
}

/* 暗色主题特定样式 */
body.dark-theme {
  .login-container {
    .social-button {
      &.google {
        background: #fff;
        box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
      }

      &.discord {
        background: #6563ff;
        box-shadow: 0 2px 8px rgba(88, 101, 242, 0.2);
      }

      &.facebook {
        background: #0866ff;
        box-shadow: 0 2px 8px rgba(24, 119, 242, 0.2);
      }
    }
  }
}
</style>
