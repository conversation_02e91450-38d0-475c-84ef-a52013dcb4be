<template>
  <div class="settings-page">
    <div class="header">
      <div class="back-button" @click="router.back()">
        <icon-left />
      </div>
      <h1>Settings</h1>
    </div>

    <div class="settings-content">
      <div class="avatar-section">
        <div class="avatar-wrapper" @click="handleAvatarClick">
          <img
            :src="
              userStore.userInfo?.avatar_url ||
              'https://cdn.magiclight.ai/assets/playshot/default-avatar.png'
            "
            alt="avatar"
          />
          <div class="camera-icon">
            <icon-camera />
          </div>
        </div>
      </div>

      <div class="settings-list">
        <div class="settings-item" @click="router.push('/user/coins')">
          <div class="item-label">
            <div class="item-label-text">Coins</div>
            <img src="https://cdn.magiclight.ai/assets/mobile/diamond.png" alt="diamond" />
            <div class="item-label-text">
              {{ userStore.userInfo?.coins }}
            </div>
          </div>
          <div class="item-value">
            <span>Details</span>
            <icon-right />
          </div>
        </div>

        <div class="settings-item" @click="showUsernameModal = true">
          <div class="item-label">Username</div>
          <div class="item-value">
            <span>{{ userStore.userInfo?.name }}</span>
            <icon-right />
          </div>
        </div>

        <div class="settings-item">
          <div class="item-label">UID</div>
          <div class="item-value">
            <span>{{ userStore.userInfo?.uuid?.slice(0, 7) }}</span>
          </div>
        </div>

        <div class="settings-item">
          <div class="item-label">Account</div>
          <div class="item-value">
            <span>{{ userStore.userInfo?.email }}</span>
          </div>
        </div>

        <div class="settings-item" @click="showGenderModal = true">
          <div class="item-label">Gender</div>
          <div class="item-value">
            <span>{{ userStore.userInfo?.gender }}</span>
            <icon-right />
          </div>
        </div>

        <div class="settings-item theme-item">
          <div class="item-label">Theme</div>
          <div class="item-value">
            <span>{{ themeStore.isDarkTheme ? 'Dark' : 'Light' }}</span>
            <div class="theme-toggle" @click="toggleTheme">
              <div class="toggle-track" :class="{ active: !themeStore.isDarkTheme }">
                <div class="toggle-thumb" :class="{ active: !themeStore.isDarkTheme }">
                  <SunIcon v-if="!themeStore.isDarkTheme" />
                  <MoonIcon v-else />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <button class="logout-button" @click="handleLogout">Log out</button>
    </div>

    <!-- Username Modal -->
    <div v-if="showUsernameModal" class="modal-overlay" @click="showUsernameModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>Change Username</h3>
          <button class="close-button" @click="showUsernameModal = false">
            <icon-close />
          </button>
        </div>
        <div class="modal-body">
          <input
            v-model="newUsername"
            type="text"
            placeholder="Enter new username"
            maxlength="20"
            class="modal-input"
          />
          <div class="modal-buttons">
            <button class="cancel-button" @click="handleCancelUsername">Cancel</button>
            <button class="confirm-button" @click="handleUpdateUsername" :disabled="updating">
              <span v-if="!updating">Confirm</span>
              <span v-else class="loading-dots"> <span>.</span><span>.</span><span>.</span> </span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Gender Modal -->
    <div v-if="showGenderModal" class="modal-overlay" @click="showGenderModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>Change Gender</h3>
          <button class="close-button" @click="showGenderModal = false">
            <icon-close />
          </button>
        </div>
        <div class="modal-body">
          <div class="gender-options">
            <button
              class="gender-option"
              :class="{ active: newGender === 'male' }"
              @click="newGender = 'male'"
            >
              Male
            </button>
            <button
              class="gender-option"
              :class="{ active: newGender === 'female' }"
              @click="newGender = 'female'"
            >
              Female
            </button>
          </div>
          <div class="modal-buttons">
            <button class="cancel-button" @click="showGenderModal = false">Cancel</button>
            <button class="confirm-button" @click="handleUpdateGender" :disabled="updating">
              <span v-if="!updating">Confirm</span>
              <span v-else class="loading-dots"> <span>.</span><span>.</span><span>.</span> </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineComponent, h, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store'
import { useThemeStore } from '@/store/theme'
import { useABTestStore } from '@/store/abtest'
import { Message } from '@/mobile/components/Message'
import { IconLeft, IconRight, IconCamera, IconClose } from '@arco-design/web-vue/es/icon'
import { updateUserInfo } from '@/api/user'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { trackABTestConversion } from '@/utils/abtest'

const router = useRouter()
const userStore = useUserStore()
const themeStore = useThemeStore()
const abtestStore = useABTestStore()

// AB测试：移动端默认主题
const defaultThemeTest = abtestStore.mobileDefaultTheme

// 页面加载时上报AB测试曝光
onMounted(() => {
  abtestStore.trackExposure('mobile_default_theme', {
    page: 'settings',
    defaultTheme: defaultThemeTest.variant === 'A' ? 'light' : 'dark',
    currentTheme: themeStore.isDarkTheme ? 'dark' : 'light'
  })
})

const showUsernameModal = ref(false)
const showGenderModal = ref(false)
const newUsername = ref(userStore.userInfo?.name || '')
const newGender = ref(userStore.userInfo?.gender || '')
const updating = ref(false)

const handleAvatarClick = () => {
  // TODO: Implement avatar upload
  Message.info('Avatar upload feature coming soon')
}

const handleUpdateUsername = async () => {
  if (!newUsername.value.trim()) {
    Message.error('Please enter a username')
    return
  }

  try {
    updating.value = true
    await updateUserInfo({ name: newUsername.value.trim() })
    userStore.handleUpdateUserInfo({ name: newUsername.value.trim() })
    Message.success('Username updated successfully')
    showUsernameModal.value = false
  } catch (error: any) {
    Message.error(error.message || 'Failed to update username')
  } finally {
    updating.value = false
  }
}

const handleUpdateGender = async () => {
  if (!newGender.value) {
    Message.error('Please select a gender')
    return
  }

  try {
    updating.value = true
    await updateUserInfo({ gender: newGender.value })
    userStore.handleUpdateUserInfo({ gender: newGender.value })
    Message.success('Gender updated successfully')
    showGenderModal.value = false
  } catch (error: any) {
    Message.error(error.message || 'Failed to update gender')
  } finally {
    updating.value = false
  }
}

const handleCancelUsername = () => {
  showUsernameModal.value = false
  newUsername.value = userStore.userInfo?.name || ''
}

const handleCancelGender = () => {
  showGenderModal.value = false
  newGender.value = userStore.userInfo?.gender || ''
}

const toggleTheme = () => {
  themeStore.toggleTheme()

  // 上报AB测试转化事件
  trackABTestConversion('mobile_default_theme', 'theme_toggle', 1)
}

const handleLogout = async () => {
  try {
    userStore.logout()
    reportEvent(ReportEvent.ClickSettingsPageSignOut, {
      userId: userStore.userInfo?.uuid
    })
    router.push('/user/login')
  } catch (error: any) {
    Message.error(error.message || 'Failed to logout')
  }
}

// 太阳图标组件
const SunIcon = defineComponent({
  name: 'SunIcon',
  render() {
    return h(
      'svg',
      {
        viewBox: '0 0 24 24',
        fill: 'none',
        stroke: 'currentColor',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        class: 'theme-icon'
      },
      [
        h('circle', { cx: '12', cy: '12', r: '5' }),
        h('path', {
          d: 'M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42'
        })
      ]
    )
  }
})

// 月亮图标组件
const MoonIcon = defineComponent({
  name: 'MoonIcon',
  render() {
    return h(
      'svg',
      {
        viewBox: '0 0 24 24',
        fill: 'none',
        stroke: 'currentColor',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        class: 'theme-icon'
      },
      [h('path', { d: 'M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z' })]
    )
  }
})
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.settings-page {
  height: calc(var(--vh, 1vh) * 100);
  background: var(--mobile-app-bg);
  color: var(--text-primary);
  padding: 20px;
  transition:
    background 0.3s ease,
    color 0.3s ease;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 32px;

  .back-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-tertiary);
    border-radius: 50%;
    margin-right: 16px;
    cursor: pointer;
    transition: background 0.3s ease;

    &:active {
      background: var(--bg-hover);
    }
  }

  h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    color: var(--text-primary);
  }
}

.avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: 32px;

  .avatar-wrapper {
    position: relative;
    width: 96px;
    height: 96px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .camera-icon {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 32px;
      background: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }
  }
}

.settings-list {
  background: var(--bg-tertiary);
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 32px;
  transition: background 0.3s ease;
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background 0.3s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: var(--bg-hover);
  }

  &.theme-item {
    cursor: default;

    &:active {
      background: transparent;
    }
  }

  .item-label {
    color: var(--text-secondary);
    font-size: 15px;
    display: flex;
    align-items: center;
    gap: 4px;

    img {
      width: 20px;
      height: 20px;
    }
  }

  .item-value {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-tertiary);
    font-size: 15px;
    max-width: 60%;

    > span:first-child {
      flex: 1;
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// 主题切换器样式
.theme-toggle {
  cursor: pointer;
  padding: 4px;
  border-radius: 20px;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }

  .toggle-track {
    width: 48px;
    height: 24px;
    background: var(--bg-secondary);
    border-radius: 12px;
    position: relative;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);

    &.active {
      background: var(--accent-bg);
      border-color: var(--accent-color);
    }

    .toggle-thumb {
      width: 20px;
      height: 20px;
      background: var(--text-primary);
      border-radius: 50%;
      position: absolute;
      top: 1px;
      left: 1px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 4px var(--shadow-color);

      &.active {
        transform: translateX(24px);
        background: var(--accent-color);
      }

      .theme-icon {
        width: 12px;
        height: 12px;
        color: var(--bg-primary);
      }
    }
  }
}

.logout-button {
  width: 100%;
  height: 48px;
  background: var(--bg-tertiary);
  border: none;
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: var(--bg-hover);
  }

  &:active {
    background: var(--bg-hover);
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

.modal-content {
  width: 100%;
  background: var(--bg-secondary);
  border-radius: 24px 24px 0 0;
  padding: 20px;
  position: relative;
  animation: slideUp 0.3s ease-out;
  transition: background 0.3s ease;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h3 {
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    margin: 0;
  }

  .close-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-tertiary);
    border: none;
    border-radius: 50%;
    color: var(--text-primary);
    cursor: pointer;
    transition: background 0.3s ease;

    &:active {
      background: var(--bg-hover);
    }
  }
}

.modal-body {
  .modal-input {
    width: 100%;
    height: 48px;
    background: var(--mobile-input-bg);
    border: 1px solid var(--mobile-input-border);
    border-radius: 12px;
    padding: 0 16px;
    color: var(--text-primary);
    font-size: 15px;
    margin-bottom: 20px;
    transition: all 0.3s ease;

    &::placeholder {
      color: var(--text-tertiary);
    }

    &:focus {
      outline: none;
      border-color: var(--accent-color);
      background: var(--bg-hover);
    }
  }

  .gender-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;

    .gender-option {
      height: 48px;
      background: var(--mobile-input-bg);
      border: 1px solid var(--mobile-input-border);
      border-radius: 12px;
      color: var(--text-primary);
      font-size: 15px;
      cursor: pointer;
      transition: all 0.2s ease;

      &.active {
        background: var(--accent-color);
        border-color: var(--accent-color);
        color: var(--bg-primary);
        font-weight: 600;
      }

      &:active {
        transform: scale(0.98);
      }
    }
  }

  .modal-buttons {
    display: flex;
    gap: 12px;

    button {
      flex: 1;
      height: 48px;
      border-radius: 24px;
      font-size: 15px;
      font-weight: 600;
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.98);
      }
    }

    .cancel-button {
      background: var(--bg-tertiary);
      color: var(--text-primary);

      &:active {
        background: var(--bg-hover);
      }
    }

    .confirm-button {
      background: var(--accent-color);
      color: var(--bg-primary);

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &:not(:disabled):active {
        background: var(--accent-hover);
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.loading-dots {
  display: inline-flex;
  gap: 2px;

  span {
    animation: dots 1.4s infinite;

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
}

@keyframes dots {
  0%,
  20% {
    opacity: 0;
    transform: translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-4px);
  }
  80%,
  100% {
    opacity: 0;
    transform: translateY(0);
  }
}
</style>
