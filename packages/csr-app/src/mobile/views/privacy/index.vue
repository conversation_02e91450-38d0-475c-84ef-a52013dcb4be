<template>
  <div class="legal-page">
    <div class="header">
      <div class="back-button" @click="router.back()">
        <icon-left />
      </div>
      <h1>Privacy Policy</h1>
    </div>

    <div class="content">
      <div class="section">
        <h2>Privacy at {{ appName }}</h2>
        <p>
          {{ appName }} Technology Co., Ltd, together with its affiliates ("{{ appName }}", "we",
          "us") values the privacy of individuals who use our animations generation service, and any
          of our other websites, applications, or services that link to this Privacy Policy
          (collectively, the "Service").
        </p>
      </div>

      <div class="section notice">
        <h3>Important Notice</h3>
        <p>
          This Privacy Policy is designed to explain how we collect, use, and share information from
          Users of the Service. This Policy is incorporated by reference into our Terms of Service.
          By agreeing to this Policy in your account setup or by using the Service, you agree to the
          terms and conditions of this Policy.
        </p>
      </div>

      <div class="section">
        <h3>Information We Collect</h3>
        <h4>Information You Provide Us</h4>
        <p>
          We collect any information you provide to us when you use the Service. You may provide us
          with information in various ways:
        </p>
        <ul>
          <li>
            <strong>Registration:</strong> When you register for an account, you are required to
            provide us with certain personal information and to designate a password.
          </li>
          <li>
            <strong>Inputs:</strong> In order to create images and animations, you must submit
            texts, images, radio, videos, and other modalities of data to us.
          </li>
          <li>
            <strong>Communications:</strong> If you contact us directly, we may receive additional
            information about you.
          </li>
          <li>
            <strong>Transactions:</strong> Details of transactions you carry out through our website
            and of the fulfillment of your orders.
          </li>
        </ul>
      </div>

      <div class="section">
        <h3>How We Use Your Information</h3>
        <p>We may use the information we collect:</p>
        <ul>
          <li>
            To facilitate the provision of the Service to you, including the creation of animations
          </li>
          <li>To operate, maintain, enhance and provide features of the Service</li>
          <li>To understand and analyze the usage trends and preferences of our users</li>
          <li>To contact you for administrative or informational purposes</li>
          <li>For marketing purposes and to personalize your experience</li>
        </ul>
      </div>

      <div class="section">
        <h3>Security</h3>
        <p>
          We use certain physical, organizational, and technical safeguards that are designed to
          maintain the integrity and security of information that we collect. Please be aware that
          no security measures are perfect or impenetrable and thus we cannot and do not guarantee
          the security of your data.
        </p>
      </div>

      <div class="section">
        <h3>Contact Us</h3>
        <p>
          If you have any questions about this Policy, please contact us at {{ appName }} Discord
          Server.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { IconLeft } from '@arco-design/web-vue/es/icon'
import { computed } from 'vue'

const appName = computed(() => import.meta.env.VITE_APP_NAME || 'Playshot')

const router = useRouter()
</script>

<style lang="less" scoped>
.legal-page {
  height: calc(var(--vh, 1vh) * 100);
  background: #1f0038;
  color: rgba(255, 255, 255, 0.9);
  padding-bottom: 40px;
}

.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #1f0038;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .back-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.6);
    transition: all 0.3s;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;

    &:hover {
      color: white;
      background: rgba(255, 255, 255, 0.15);
    }

    :deep(.arco-icon) {
      font-size: 18px;
    }
  }

  h1 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
  }
}

.content {
  margin: 0 auto;
  padding: 24px 20px;
  background: #1f0038;
  .section {
    margin-bottom: 32px;

    &.notice {
      background: rgba(202, 147, 242, 0.1);
      border-radius: 12px;
      padding: 20px;
      border: 1px solid rgba(202, 147, 242, 0.2);
    }

    h2 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 16px;
      color: #ca93f2;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 12px;
      color: #ca93f2;
    }

    h4 {
      font-size: 16px;
      font-weight: 600;
      margin: 16px 0 8px;
      color: rgba(255, 255, 255, 0.9);
    }

    p {
      margin: 0 0 16px;
      line-height: 1.6;
      color: rgba(255, 255, 255, 0.8);
      font-size: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    ul {
      margin: 12px 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        color: rgba(255, 255, 255, 0.8);
        font-size: 15px;
        line-height: 1.6;

        strong {
          color: rgba(255, 255, 255, 0.95);
          font-weight: 600;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
