import { ref, computed } from 'vue'
import type { Scene, UserInputs } from '../types'

export function useGameState() {
  const currentScene = ref(0)
  const isShowStartBtn = ref(true)
  const isLoading = ref(false)
  const currentPreviewIndex = ref(0)
  const scenes = ref<Scene[]>([])
  
  const userInputs = ref<UserInputs>({
    name: '',
    pose: '',
    time: '',
    clothes: ''
  })

  const nextBtnText = computed(() => {
    const sceneTexts: Record<number, string> = {
      0: '怎么体验？',
      1: '就这么叫我吧',
      2: '',
      3: '这么穿一定很好看',
      4: '快点哦',
      5: '快点，我等不及了',
      6: '你指路我来开',
      7: '这个姿势一定很棒',
      8: '',
      9: '前面就是了',
      10: '',
      11: '好看',
      12: '好大',
      13: '好白',
      14: '这个姿势太棒了',
      15: '日期你还能忘了',
      16: '写什么呢',
      17: '写什么呢',
      18: '你真好看～'
    }
    return sceneTexts[currentScene.value]
  })

  return {
    currentScene,
    isShowStartBtn,
    isLoading,
    currentPreviewIndex,
    scenes,
    userInputs,
    nextBtnText
  }
} 