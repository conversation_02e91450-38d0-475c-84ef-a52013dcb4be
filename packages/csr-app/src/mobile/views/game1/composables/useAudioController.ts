import { Howl } from 'howler'
import { Ref } from 'vue'
import type { Scene, AudioController } from '../types'

export function useAudioController(
  scenes: Ref<Scene[]>,
  currentScene: Ref<number>,
  isShowNextButton: Ref<boolean>
): AudioController {
  let sound: Howl | null = null
  let ambientSound: Howl | null = null
  let backgroundMusic: Howl | null = null

  const audioController: AudioController = {
    playVoice: () => {
      audioController.stopVoice()
      const scene = scenes.value[currentScene.value]
      const url = scene?.sound_url
      if (!url) return
      isShowNextButton.value = false
      sound = new Howl({ src: [url], html5: true })
      sound.play()
      sound.on('end', () => {
        isShowNextButton.value = true
      })
    },

    playAmbient: (index = 0, loop = true) => {
      audioController.stopAmbient()
      const url = scenes.value[currentScene.value]?.ambient_url?.split(',')[index]
      if (!url) return
      ambientSound = new Howl({ src: [url], html5: true, loop })
      ambientSound.play()
    },

    playBackgroundMusic: () => {
      audioController.stopBackgroundMusic()
      const url = scenes.value[currentScene.value]?.bgm_url
      if (!url) return
      backgroundMusic = new Howl({ src: [url], html5: true, loop: true })
      backgroundMusic.play()
    },

    stopVoice: () => {
      if (sound) sound.stop()
    },

    stopAmbient: () => {
      if (ambientSound) ambientSound.stop()
    },

    stopBackgroundMusic: () => {
      if (backgroundMusic) backgroundMusic.stop()
    },

    stopAll: () => {
      audioController.stopVoice()
      audioController.stopAmbient()
      audioController.stopBackgroundMusic()
    },

    playAll: () => {
      audioController.playVoice()
      audioController.playAmbient()
      audioController.playBackgroundMusic()
    }
  }

  return audioController
}
