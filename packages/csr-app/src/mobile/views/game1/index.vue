<template>
  <div class="game-container">
    <div v-if="isShowStartBtn" class="game-container-start">
      <a-button @click="startGame">开始游戏</a-button>
    </div>

    <div v-if="currentScene === scenes.length" class="game-container-restart">
      <a-button @click="restartGame">重新开始</a-button>
    </div>

    <transition name="scene" mode="out-in">
      <GameScene
        :scene="scenes[currentScene]"
        :current-scene="currentScene"
        :current-preview-index="currentPreviewIndex"
      >
        <GameInputBubble
          v-if="isTextInputScene"
          :input-type="scenes[currentScene]?.input_type || 'name'"
          v-model="userInputs[scenes[currentScene]?.input_type ?? 'name']"
          :position="scenes[currentScene]?.inputPosition"
          :is-text-input-scene="isTextInputScene"
        />

        <a-button
          class="next-step"
          v-if="currentScene < scenes.length - 1 && isShowNextButton && nextBtnText"
          @click="nextScene"
          :loading="isLoading"
        >
          {{ nextBtnText }}
        </a-button>
      </GameScene>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import GameScene from './components/GameScene.vue'
import GameInputBubble from './components/GameInputBubble.vue'
import { useGameState } from './composables/useGameState'
import { useAudioController } from './composables/useAudioController'
import {
  getStoryTemplate,
  batchTextToSpeech,
  postScenceImageTasks,
  getScenceImageTasks,
  Scene,
  ImageTask
} from '@/api/game1'
// import type { Scene, ImageTask, ImageTasksResponse, ImageGenerationResponse } from './types'

const {
  currentScene,
  isShowStartBtn,
  isLoading,
  currentPreviewIndex,
  scenes,
  userInputs,
  nextBtnText
} = useGameState()

const isShowNextButton = ref(false)
const isTextInputScene = computed(() => scenes.value[currentScene.value]?.type === 'interaction')

const audioController = useAudioController(scenes, currentScene, isShowNextButton)

// 场景管理方法
const fetchScenes = async () => {
  try {
    const response = await getStoryTemplate()
    scenes.value = response.data.data.templates.sort((a, b) => a.sort - b.sort)
    console.log('获取场景成功:', scenes.value)
  } catch (error) {
    console.log('获取场景失败', error)
  }
}

// 语音生成方法
const generateVoiceOvers = async (startIndex = 0, endIndex) => {
  const userName = userInputs.value.name
  scenes.value.slice(startIndex, endIndex).forEach((scene) => {
    scene.caption = scene.caption.replace('{user}', userName)
  })

  const captions = scenes.value.slice(startIndex, endIndex).map((scene) => scene.caption)
  const batchSize = 10
  const batches: { texts: string[]; startIndex: number }[] = []

  for (let i = 0; i < captions.length; i += batchSize) {
    batches.push({ texts: captions.slice(i, i + batchSize), startIndex: i + startIndex })
  }

  try {
    for (let batch of batches) {
      const res = await batchTextToSpeech(batch.texts)
      batch.texts.forEach((caption, index) => {
        const sceneIndex = batch.startIndex + index
        const scene = scenes.value[sceneIndex]
        if (scene) {
          scene.sound_url = res.data.data[index] || ''
        }
      })
    }
  } catch (error) {
    console.error('生成语音失败:', error)
  }
}

const generateInitialVoiceOvers = async () => {
  await generateVoiceOvers(0, 2)
}

// 游戏控制方法
const startGame = () => {
  isShowStartBtn.value = false
  audioController.playAll()
}

const restartGame = async () => {
  isLoading.value = true
  await fetchScenes()
  await generateInitialVoiceOvers()
  isLoading.value = false
  isShowStartBtn.value = false
  currentScene.value = 0
  audioController.playAll()
}

// 图片生成任务管理
const createPollingHandler = (
  generateImgListRef: string[],
  updateSceneCallback: (templateId: string, imageUrl: string) => void,
  interval = 15000
) => {
  let polling: number | null = null
  return {
    start: async () => {
      polling = window.setInterval(async () => {
        try {
          if (generateImgListRef.length === 0) {
            if (polling) window.clearInterval(polling)
            return
          }
          const response = await getScenceImageTasks(generateImgListRef)
          let allFinished = true

          const tasks = response.data.data.images
          tasks.forEach((task: ImageTask) => {
            if (['finish', 'failed', 'error'].includes(task.status)) {
              generateImgListRef = generateImgListRef.filter((id) => id !== task.uuid)
              if (task.image_url) {
                updateSceneCallback(task.template_id, task.image_url)
              }
            } else {
              allFinished = false
            }
          })

          if (allFinished && polling) {
            window.clearInterval(polling)
            polling = null
          }
        } catch (error) {
          console.error('检查任务状态失败:', error)
          if (polling) window.clearInterval(polling)
          polling = null
          isLoading.value = false
        }
      }, interval)
    },
    stop: () => {
      if (polling) window.clearInterval(polling)
    }
  }
}

const updateScene = (template_id, image_url) => {
  const scene = scenes.value.find((scene) => scene.uuid === template_id)
  if (!scene || !image_url) return

  scene.status = 'finish'
  scene.preview_url ||= image_url

  if (scene.sort === 8) {
    const scene9 = scenes.value.find((scene) => scene.sort === 9)
    if (scene9) scene9.preview_url ||= image_url
  }
}

// 场景切换方法
const nextScene = async () => {
  if (
    isLoading.value ||
    (isTextInputScene.value &&
      !userInputs.value[scenes.value[currentScene.value]?.input_type ?? ''])
  ) {
    return alert('请输入内容')
  }

  const previews = scenes.value[currentScene.value]?.preview_url.split(',')
  if (previews && currentPreviewIndex.value < previews.length - 1) {
    currentPreviewIndex.value++
    return
  }

  if (
    currentScene.value + 1 &&
    scenes.value[currentScene.value + 1] &&
    !scenes.value[currentScene.value + 1].preview_url
  ) {
    return alert('哥哥, 不要心急嘛～')
  }

  audioController.stopAmbient()

  // 处理特殊场景
  if (currentScene.value === 1) {
    isLoading.value = true
    await generateVoiceOvers(2, scenes.value.length)
    isLoading.value = false
  }

  // 处理图片生成任务
  await handleImageGeneration()

  if (currentScene.value < scenes.value.length - 1) {
    currentScene.value++
  }
}

const handleImageGeneration = async () => {
  const prompt = userInputs.value

  if (currentScene.value === 3) {
    await generateImages([6, 7, 11, 12, 13])
  } else if (currentScene.value === 7) {
    await generateImages([14, 15, 17])
  } else if (currentScene.value === 15) {
    await generateImages([18])
  }
}

const generateImages = async (sceneIndexes: number[]) => {
  isLoading.value = true
  try {
    const tasks = sceneIndexes.map((index) => ({
      template_id: scenes.value[index].uuid,
      ...userInputs.value
    }))

    const res = await postScenceImageTasks(tasks)
    if (res.data.code !== 0) throw new Error('提交任务失败')

    const generateImgList = res.data.data.image_ids
    const taskPolling = createPollingHandler(generateImgList, updateScene)
    taskPolling.start()
  } catch (error) {
    console.error('生成图片失败:', error)
    alert('提交任务失败')
  } finally {
    isLoading.value = false
  }
}

onMounted(async () => {
  isLoading.value = true
  await fetchScenes()
  await generateInitialVoiceOvers()
  isLoading.value = false
  audioController.playAll()
})
</script>

<style lang="less" scoped>
.game-container {
  position: relative;
  width: 100vw;
  height: calc(var(--vh, 1vh) * 100);
  overflow: hidden;

  &-start,
  &-restart {
    width: 100px;
    height: 100px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
  }
}

.scene-enter-active,
.scene-leave-active {
  opacity: 1;
  button {
    display: block;
  }
}

.scene-enter-from,
.scene-leave-to {
  opacity: 0;
  button {
    display: none;
  }
}

.next-step {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 10px 20px;
  font-size: 18px;
  cursor: pointer;
  border: none;
  border-radius: 8px;
  background-color: #007bff;
  color: white;
}
</style>
