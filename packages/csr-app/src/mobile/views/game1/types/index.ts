export interface Scene {
  uuid: string
  sort: number
  type: 'image' | 'video' | 'interaction'
  input_type?: InputType
  preview_url: string
  caption: string
  sound_url?: string
  status?: string
  ambient_url?: string
  bgm_url?: string
  inputPosition?: {
    x: number
    y: number
  }
}

export interface AudioController {
  playVoice: () => void
  playAmbient: (index?: number, loop?: boolean) => void
  playBackgroundMusic: () => void
  stopVoice: () => void
  stopAmbient: () => void
  stopBackgroundMusic: () => void
  stopAll: () => void
  playAll: () => void
}

export interface UserInputs {
  name: string
  pose: string
  time: string
  clothes: string
  [key: string]: string
}

export type InputType = 'name' | 'pose' | 'time' | 'clothes'

// API 响应类型
export interface ApiResponse<T> {
  code: number // 改为 number 类型
  data: T
  message?: string
  isOk: boolean
}

export interface ImageTask {
  uuid: string
  template_id: string
  status: string
  image_url?: string
}

export interface ImageTasksResponse {
  data: {
    images: ImageTask[]
  }
}

export interface ImageGenerationResponse {
  data: {
    image_ids: string[]
  }
}

export interface VoiceResponse {
  data: {
    audio_urls: string[]
  }
}

export interface TemplateResponse {
  data: {
    templates: Scene[]
  }
}
