<template>
  <div class="legal-page">
    <div class="header">
      <div class="back-button" @click="router.back()">
        <icon-left />
      </div>
      <h1>Refund and Returns Policy</h1>
    </div>

    <div class="content">
      <div class="section">
        <h2>{{ appName }} Refund and Returns Policy</h2>
        <p>
          <strong>Last updated: July 1st, 2025</strong>
        </p>
      </div>

      <div class="section notice">
        <h3>Important Notice</h3>
        <p>
          By using our services and making purchases, you acknowledge that you have read,
          understood, and agree to be bound by this Refund and Returns Policy.
        </p>
      </div>

      <div class="section">
        <h3>Non-Refundable Fees</h3>
        <p>
          Fees are non-refundable once the Login details have been used on Portal. Non-use of a
          Membership or inability of User to access the Website through no fault of {{ appName }}.AI
          shall not be grounds for a refund of Fees. Company does not provide refunds or credits for
          any partial-month Membership periods.
        </p>
      </div>

      <div class="section">
        <h3>Refund Process</h3>
        <p>
          In the event a refund is issued, ALL refunds will be made by {{ appName }}.AI who will
          credit the Payment Method used to make the original purchase. NO refunds will be made by
          cash or paper check. ALL refunds will be issued within ten (10) days of communication
          between the User and the Company's Client Relations department.
        </p>
      </div>

      <div class="section">
        <h3>Pricing Errors</h3>
        <p>
          In the event a product and/or service is listed at an incorrect price or with incorrect
          information due to typographical error, we shall have the right to refuse or cancel any
          orders placed for the product and/or service listed at the incorrect price. We shall have
          the right to refuse or cancel any such order whether or not the order has been confirmed
          and your credit card charged. If your credit card has already been charged for the
          purchase and your order is canceled, we shall immediately issue a credit to your credit
          card account or other payment account in the amount of the charge.
        </p>
      </div>

      <div class="section">
        <h3>Payment Method Limitations</h3>
        <p>
          This refund policy does not apply when the payment method You used to purchase a
          membership does not offer the possibility to issue such a refund. This includes, but is
          not limited to, payment in cryptocurrency and/or through payment provider OnlyPay.
        </p>
      </div>

      <div class="section">
        <h3>Third-Party Payment Providers</h3>
        <p>
          Refunds through some payment providers, are processed by the payment provider and
          {{ appName }}.AI doesn't have complete control on the finality and therefore can't
          guarantee how the refund will be issued.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { IconLeft } from '@arco-design/web-vue/es/icon'
import { computed, onMounted } from 'vue'
import { useLegalSEO } from '@/composables/useSEO'

const router = useRouter()
const appName = computed(() => import.meta.env.VITE_APP_NAME || 'ReelPlay')
const { setLegalPageSEO } = useLegalSEO()

onMounted(() => {
  setLegalPageSEO('refund')
})
</script>

<style lang="less" scoped>
.legal-page {
  height: calc(var(--vh, 1vh) * 100);
  background: #1f0038;
  color: rgba(255, 255, 255, 0.9);
  padding-bottom: 40px;
}

.header {
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background: rgba(31, 0, 56, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .back-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all 0.3s;
    margin-right: 16px;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    svg {
      width: 20px;
      height: 20px;
      color: rgba(255, 255, 255, 0.9);
    }
  }

  h1 {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    color: #ca93f2;
  }
}

.content {
  margin: 0 auto;
  padding: 24px 20px;
  background: #1f0038;
  .section {
    margin-bottom: 32px;

    &.notice {
      background: rgba(202, 147, 242, 0.1);
      border-radius: 12px;
      padding: 20px;
      border: 1px solid rgba(202, 147, 242, 0.2);
    }

    h2 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 16px;
      color: #ca93f2;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 12px;
      color: #ca93f2;
    }

    p {
      margin: 0 0 16px;
      line-height: 1.6;
      color: rgba(255, 255, 255, 0.8);
      font-size: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    ul {
      margin: 12px 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        color: rgba(255, 255, 255, 0.8);
        font-size: 15px;
        line-height: 1.6;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
