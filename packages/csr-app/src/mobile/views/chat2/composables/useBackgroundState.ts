import { ref, watch, toRef, type Ref } from 'vue'

interface BackgroundStateOptions {
  initialImage?: string | Ref<string>
}

export const useBackgroundState = (options: BackgroundStateOptions = {}) => {
  // 将 initialImage 转换为响应式引用
  const imageRef = toRef(options, 'initialImage')
  const currentBgImage = ref<string>(
    (typeof imageRef.value === 'string' ? imageRef.value : '') || ''
  )

  // 监听初始图片变化
  watch(
    imageRef,
    (newImage) => {
      const value = typeof newImage === 'string' ? newImage : ''
      if (value) {
        currentBgImage.value = value
      }
    },
    {
      immediate: true
    }
  )

  return {
    currentBgImage,
    updateBackgroundImage: (newImage: string) => {
      currentBgImage.value = newImage
    }
  }
}
