<template>
  <BaseDrawer
    :visible="visible"
    mask-color="transparent"
    mask-blur="0"
    height="65vh"
    background="#1F0038"
    border-radius="16px 16px 0 0"
    padding="0"
    @update:visible="handleVisibilityChange"
  >
    <div class="history-drawer-content">
      <div class="drawer-header">
        <h3>Chat History</h3>
        <button class="close-button" @click="handleClose">
          <icon-close />
        </button>
      </div>
      <div ref="messagesContainer" class="messages-container" @scroll="handleScroll">
        <div class="messages-history">
          <div v-for="message in messages" :key="message.id">
            <template v-if="message.content.text">
              <div class="message-item">
                <div class="message-avatar" v-if="message.sender_type !== 'system'">
                  <img
                    :src="
                      message.sender_type === 'user'
                        ? 'https://cdn.magiclight.ai/assets/playshot/default-avatar.png'
                        : message.sender?.avatar_url || storyStore.currentActor?.avatar_url
                    "
                    :alt="
                      message.sender_type === 'user'
                        ? 'You'
                        : message.sender?.name || storyStore.currentActor?.name
                    "
                  />
                </div>
                <div class="message-content-wrapper">
                  <div class="sender-name" v-if="message.sender_type !== 'system'">
                    {{
                      message.sender_type === 'user'
                        ? 'You'
                        : message.sender?.name || storyStore.currentActor?.name
                    }}
                  </div>
                  <!-- <SystemMessage
                v-if="message.msg_type === 'system'"
                :message="message"
                :visible-content="visibleContent[message.id]"
              /> -->
                  <UserMessage
                    v-if="message.sender_type === 'user'"
                    :message="message"
                    :visible-content="visibleContent[message.id]"
                    :is-in-history="true"
                  />
                  <ActorMessage
                    v-else
                    :message="message"
                    :visible-content="visibleContent[message.id]"
                    :is-playing="isMessagePlaying(message)"
                    :is-in-history="true"
                    @play-tts="$emit('play-tts', message)"
                  />
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      <div v-show="showScrollBottom" class="scroll-bottom-button" @click="scrollToBottom">
        <icon-arrow-down />
      </div>
    </div>
  </BaseDrawer>
</template>

<script setup lang="ts">
import { watch, nextTick } from 'vue'
import { useStoryStore } from '@/store/story'
import { useHistoryState } from '../../composables/useHistoryState'
import type { Message } from '../../composables/useMessageState'
import BaseDrawer from '@/mobile/components/BaseDrawer.vue'
import IconClose from '@/assets/icon/close.svg'
import UserMessage from '../UserMessage.vue'
import ActorMessage from '../ActorMessage.vue'

interface Props {
  messages: Message[]
  visibleContent: Record<string, { text: string; mood?: number }>
  isMessagePlaying: (message: Message) => boolean
}

const props = defineProps<Props>()
const emit = defineEmits(['play-tts'])
const storyStore = useStoryStore()

const visible = defineModel<boolean>('visible')

const { showScrollBottom, messagesContainer, handleScroll, scrollToBottom } = useHistoryState()

const handleVisibilityChange = (value: boolean) => {
  visible.value = value
}

const handleClose = () => {
  visible.value = false
}

watch(
  () => visible.value,
  async (newValue) => {
    if (newValue) {
      await nextTick()
      // Ensure we scroll to bottom after the drawer animation completes
      setTimeout(() => {
        scrollToBottom()
      }, 300)
    }
  }
)

watch(
  () => props.messages,
  async () => {
    if (visible.value) {
      await nextTick()
      // Add a small delay to ensure DOM is updated
      setTimeout(() => {
        scrollToBottom()
      }, 100)
    }
  },
  { deep: true }
)
</script>

<style lang="less" scoped>
.history-drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;

  .drawer-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    position: sticky;
    top: 0;
    z-index: 10;
    background: #1f0038;
    // border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    h3 {
      color: #fff;
      font-size: 16px;
      font-weight: 500;
      margin: 0;
    }

    .close-button {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      :deep(svg) {
        flex-shrink: 0;
        width: 16px;
        height: 16px;
        color: #fff;
      }
    }
  }

  .messages-container {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    height: calc(100% - 56px); /* Subtract header height */
  }

  .messages-history {
    padding: 16px;

    .message-item {
      display: flex;
      gap: 12px;
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .message-avatar {
        margin-top: 8px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        overflow: hidden;
        flex-shrink: 0;
        border: 1px solid rgba(255, 255, 255, 0.2);
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .message-content-wrapper {
        flex: 1;

        .sender-name {
          margin-top: 8px;
          color: rgba(255, 255, 255, 0.6);
          font-size: 14px;
          margin-bottom: 8px;
        }
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        :deep(.message-content) {
          background: transparent;
          border: none;
          backdrop-filter: none;
          color: #fff;
          padding: 0;
          font-size: 15px;
          line-height: 1.5;

          .text {
            white-space: pre-wrap;
          }
        }

        :deep(.user-message .message-content) {
          background: transparent;
          border: none;
        }

        :deep(.system-message .message-content) {
          background: transparent;
          border: none;
          color: rgba(255, 255, 255, 0.6);
          padding: 0;
        }
      }

      &::after {
        content: '';
        display: block;
        height: 1px;
        background: rgba(255, 255, 255, 0.1);
        margin-top: 24px;
      }

      &:last-child {
        margin-bottom: 0;

        &::after {
          display: none;
        }
      }
    }
  }

  .scroll-bottom-button {
    position: fixed;
    bottom: 24px;
    right: 24px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 10;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    :deep(svg) {
      width: 24px;
      height: 24px;
      color: #fff;
    }
  }
}
</style>
