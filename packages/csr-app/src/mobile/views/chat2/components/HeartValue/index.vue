<template>
  <div
    v-show="showWrapper"
    class="telepathy-wrapper"
    :class="{ 'is-full': showFullEffect }"
    @click="handleClick"
  >
    <img
      class="circle-bg"
      :class="{ hide: showFullEffect }"
      src="https://static.playshot.ai/static/images/chat/telepathy-bg2.png"
      alt="telepathy circle"
    />
    <img
      class="circle-bg full"
      :class="{ show: showFullEffect && !messagesStore.isActorThinking }"
      src="https://static.playshot.ai/static/images/chat/telepathy-100-v3.gif"
      alt="telepathy full"
    />
    <div class="telepathy-container" :class="{ hide: showFullEffect }">
      <div class="wave-container" ref="waveContainer">
        <div class="waves" :style="{ transform: `translateY(${100 - value}%)` }">
          <div class="wave wave1"></div>
          <div class="wave wave2"></div>
        </div>
      </div>
      <div class="value-text" :data-content="`${value}%`">{{ value }}%</div>
      <div class="value-unit" :data-content="`MindControl`">MindControl</div>
    </div>
    <ConfirmDialog
      v-model:visible="showTelepathyTip"
      title="Mind Control"
      :title-style="{
        color: '#FFF'
      }"
      :content-style="{
        fontSize: '13px',
        color: '#CA93F2',
        fontWeight: '600'
      }"
      :confirm-button-style="{
        borderRadius: '26px',
        borderTop: ' 2px solid #1F0038',
        borderRight: '2px solid #1F0038',
        borderBottom: '6px solid #1F0038',
        borderLeft: '2px solid #1F0038',
        background: 'linear-gradient(180deg, #D6CAFE 0%, #CA93F2 100%)',
        boxShadow: '0px 2px 12px 0px #B098FF'
      }"
      content="In this game, you possess a mysterious power: Mind Control. Strengthen it through conversations, and upon reaching 100%, special plots will be unlocked."
      :show-cancel="false"
      :show-icon="false"
      confirm-text="Ok, I get it."
    />
  </div>
</template>

<script setup lang="ts">
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import { ref, computed, watch } from 'vue'
import { useChatMessagesStore } from '@/store/chat-messages'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface'

const props = defineProps<{
  value: number
}>()

const emit = defineEmits<{
  (e: 'click'): void
}>()

const messagesStore = useChatMessagesStore()
const showTelepathyTip = ref(false)
const canShowFullEffect = ref(false)

// 监听心值和打字状态的变化
watch(
  [
    () => props.value,
    () => messagesStore.messageTypingPromise,
    () => messagesStore.isActorThinking
  ],
  async ([value, typingPromise]) => {
    // 如果心值未达到100，不显示效果
    if (value < 100) {
      canShowFullEffect.value = false
      return
    }

    // 如果没有正在进行的打字效果，直接显示
    if (!typingPromise) {
      canShowFullEffect.value = true
      return
    }

    // 如果有打字效果，等待打字完成
    try {
      canShowFullEffect.value = false
      await typingPromise
      canShowFullEffect.value = true
    } catch {
      // 如果打字效果出错，也允许显示
      canShowFullEffect.value = true
    }
  },
  { immediate: true }
)

const showFullEffect = computed(() => {
  return canShowFullEffect.value
})

const showWrapper = computed(() => {
  return props.value < 100 || canShowFullEffect.value
})

const handleClick = () => {
  reportEvent(ReportEvent.HeartValueClick, {
    value: props.value
  })
  if (showFullEffect.value) {
    canShowFullEffect.value = false
    emit('click')
  } else {
    showTelepathyTip.value = true
  }
}
</script>

<style lang="less" scoped>
@import url('https://fonts.googleapis.com/css2?family=Oleo+Script+Swash+Caps:wght@400;700&display=swap');

.telepathy-wrapper {
  position: relative;
  width: 90px;
  height: 90px;
  flex-shrink: 0;

  &.is-full {
    width: 210px;
    height: 210px;
    right: -60px;
    cursor: pointer;
    animation: pulse 2s ease-in-out infinite;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.circle-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  // transition: opacity 0.3s ease-in-out;
  opacity: 1;

  &.hide {
    opacity: 0;
  }

  &.full {
    opacity: 0;

    &.show {
      opacity: 1;
    }
  }
}

.telepathy-container {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 82px;
  height: 82px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  overflow: hidden;
  // backdrop-filter: blur(10px);
  transition: opacity 0.3s ease-in-out;
  opacity: 1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

  &.hide {
    opacity: 0;
  }
}

.wave-container {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  overflow: hidden;
  border-radius: 50%;
}

.waves {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  bottom: 0;
  transition: transform 0.5s ease-out;
}

.wave {
  position: absolute;
  width: 200%;
  height: 200%;
  left: -50%;
  top: 0;
  background: linear-gradient(0deg, #daff96 0%, transparent 80%);
  border-radius: 38%;

  &.wave1 {
    animation: rotate 7s linear infinite;
  }

  &.wave2 {
    animation: rotate 5s linear infinite;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.value-text {
  position: absolute;
  top: 25%;
  left: 0;
  right: 0;
  text-align: center;
  color: #fff;
  font-size: 20px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  z-index: 3;
  font-weight: 700;
  font-family: 'Oleo Script Swash Caps';
  &::before {
    content: attr(data-content);
    position: absolute;
    left: 0;
    width: 100%;
    color: #000;
    z-index: -1;
    -webkit-text-stroke: 1px #000;
    font-family: 'Oleo Script Swash Caps';
  }
}

.value-unit {
  position: absolute;
  bottom: 25%;
  left: 0;
  right: 0;
  color: #fff;

  text-align: center;
  font-size: 11px;
  font-weight: 700;
  line-height: normal;
  font-family: 'Oleo Script Swash Caps';
  &::before {
    content: attr(data-content);
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    color: #000;
    z-index: -1;
    -webkit-text-stroke: 1px #000;
    font-family: 'Oleo Script Swash Caps';
  }
}
</style>
