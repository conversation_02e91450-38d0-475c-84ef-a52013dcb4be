<template>
  <div class="chat-interface">
    <!-- 聊天区域 -->
    <div class="chat-section" v-show="shouldShowChatSection">
      <!-- 消息列表 -->
      <MessageList
        ref="messageListRef"
        :messages="chatMessagesStore.messages"
        :visible-content="visibleContent"
        :is-message-playing="isMessagePlaying"
        @continue-click="$emit('continue-click')"
      />

      <!-- Chat Options -->
      <ChatOptionsComponent
        v-if="shouldShowChatOptions"
        :heart-value="chatMessagesStore.heartValue"
        @select="$emit('option-select', $event)"
      />

      <!-- 输入区域 -->
      <template v-if="chatUIStore.isLegacyVersion">
        <ChatInputVersion1
          ref="chatInputRefVersion1"
          :collapse-task-tip="() => $emit('collapse-task-tip')"
          @add-credit="$emit('add-credit', $event)"
          @scroll-to-bottom="$emit('scroll-to-bottom')"
          @toggle-history="$emit('toggle-history')"
        />
      </template>
      <template v-else>
        <ChatInputVersion2
          v-show="!isTelepathyComplete"
          ref="chatInputRefVersion2"
          @collapse-task-tip="$emit('collapse-task-tip')"
          @add-credit="$emit('add-credit', $event)"
          @scroll-to-bottom="$emit('scroll-to-bottom')"
          @toggle-history="$emit('toggle-history')"
        />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useChatMessagesStore } from '@/store/chat-messages'
import { useChatResourcesStore } from '@/store/chat-resources'
import { useChatUIStore } from '@/store/chat-ui'
import MessageList from '../MessageList/index.vue'
import ChatOptionsComponent from '../ChatOptions/index.vue'
import ChatInputVersion2 from '../ChatInput/index.vue'
import ChatInputVersion1 from '@/mobile/views/chat/components/ChatInput.vue'

interface Props {
  contentReady: boolean
  visibleContent: Record<string, { text: string; mood?: number }>
  showChatOptions: boolean
  isTelepathyComplete: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'collapse-task-tip': []
  'add-credit': [type: string]
  'scroll-to-bottom': []
  'toggle-history': []
  'continue-click': []
  'option-select': [option: any]
  'telepathy-complete': []
}>()

// Store
const chatMessagesStore = useChatMessagesStore()
const chatResourcesStore = useChatResourcesStore()
const chatUIStore = useChatUIStore()

// Refs
const messageListRef = ref()
const chatInputRefVersion1 = ref()
const chatInputRefVersion2 = ref()

// 计算属性
const shouldShowChatSection = computed(() => {
  return (
    !chatUIStore.overlay &&
    !chatUIStore.isEnding &&
    !chatResourcesStore.animatedImages.length &&
    props.contentReady
  )
})

const shouldShowChatOptions = computed(() => {
  return (
    chatResourcesStore.chatCard2?.length &&
    chatMessagesStore.heartValue >= 100 &&
    props.showChatOptions
  )
})

// 方法
const isMessagePlaying = (message: any) => {
  return message.content.audio_url && chatMessagesStore.currentPlayingMessageId === message.id
}

const skipCurrentTyping = async () => {
  await messageListRef.value?.skipCurrentTyping()
}

const showAttentionAnimation = () => {
  if (chatInputRefVersion2.value && !props.isTelepathyComplete && !chatUIStore.isLegacyVersion) {
    chatInputRefVersion2.value.showAttentionAnimation()
  }
}

const toggleHistory = () => {
  messageListRef.value?.toggleHistory()
}

// 暴露方法给父组件
defineExpose({
  skipCurrentTyping,
  showAttentionAnimation,
  toggleHistory,
  messageListRef,
  chatInputRefVersion1,
  chatInputRefVersion2
})
</script>

<style lang="less" scoped>
.chat-interface {
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: 0;
}

.chat-section {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  z-index: 2;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%);
}
</style>
