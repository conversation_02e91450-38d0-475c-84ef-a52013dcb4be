<template>
  <div
    class="user-message"
    v-if="message.content.text || message.content.media_url"
    :class="{ 'in-history': isInHistory }"
  >
    <div class="message-content">
      <div v-if="!isInHistory" class="avatar">
        <img src="https://cdn.magiclight.ai/assets/playshot/default-avatar.png" alt="User Avatar" />
      </div>
      <div class="content">
        <div class="text" :data-content="message.content.text" v-html="message.content.text"></div>
        <div v-if="message.content.media_url" class="image-container">
          <img :src="message.content.media_url" alt="User Image" @load="handleImageLoad" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Message } from '../composables/useMessageState'

interface Props {
  message: Message
  visibleContent?: {
    text: string
    mood?: number
  }
  isInHistory?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isInHistory: false
})

const handleImageLoad = (event: Event) => {
  const img = event.target as HTMLImageElement
  if (img.naturalWidth > img.naturalHeight) {
    img.classList.add('landscape')
  } else {
    img.classList.add('portrait')
  }
}
</script>

<style lang="less" scoped>
.user-message {
  display: flex;
  margin-bottom: 16px;

  .message-content {
    display: flex;
    gap: 12px;
    align-items: center;

    .avatar {
      width: 32px;
      height: 32px;
      flex-shrink: 0;
      border-radius: 50%;
      overflow: hidden;
      margin-top: 2px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .content {
      font-size: 15px;
      line-height: 1.4;
      word-break: break-word;
      min-height: 32px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .text {
        white-space: pre-wrap;
      }

      .image-container {
        margin-top: 8px;
        border-radius: 8px;
        overflow: hidden;

        img {
          width: 100%;
          height: auto;
          display: block;

          &.landscape {
            max-height: 200px;
            object-fit: cover;
          }

          &.portrait {
            max-height: 300px;
            object-fit: contain;
          }
        }
      }
    }
  }

  &:not(.in-history) {
    .message-content {
      color: #333;
      background: rgba(31, 0, 56, 0.56);
      border-radius: 15px;
      padding: 10px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      max-width: 70%;
      .text {
        position: relative;
        font-size: 15px;
        color: #dcacff;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;

        &::after {
          font-size: 15px;
          content: attr(data-content);
          -webkit-text-stroke: 1px #000;
          position: absolute;
          left: 0;
          top: 0;
          z-index: -1;
        }
      }
    }
  }

  &.in-history {
    .message-content {
      color: #666;
      padding: 0 16px;

      .text {
        white-space: pre-wrap;
      }
    }
  }
}
</style>
