<template>
  <div v-if="showOptions" class="chat-options" :class="{ 'horizontal-layout': isHorizontalLayout }">
    <button
      v-for="(option, index) in options"
      :key="option.option_id"
      class="chat-option-button"
      :class="{
        unlocked: option.is_purchased,
        'first-option': index === 0,
        'second-option': index === 1,
        'third-option': index === 2,
        'highlight-animation': option.is_highlight
      }"
      @click="handleSelect(option)"
    >
      <img
        class="diamond-icon"
        src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
        alt="diamond"
      />
      <span v-html="option.text"></span>
      <!-- <div class="cursor-icon">
        <icon-cursor />
      </div> -->
      <div class="coins-container" v-if="option.paid_required && !option.is_purchased">
        <img src="https://cdn.magiclight.ai/assets/mobile/diamond.png" alt="coins" />
        <span class="coins-text">{{ option?.coins || 0 }}</span>
      </div>
      <div class="unlock-container" v-if="option.paid_required && option.is_purchased">
        <icon-unlock />
      </div>
    </button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { IconUnlock } from '@arco-design/web-vue/es/icon'
import { useChatResourcesStore } from '@/store/chat-resources'
import IconCursor from '@/assets/icon/cursor.svg'

interface ChatOption {
  option_id: string
  text: string
  paid_required: boolean
  is_purchased?: boolean
  coins?: number
  is_highlight?: boolean
}

const props = defineProps<{
  heartValue: number
}>()

const emit = defineEmits<{
  (e: 'select', option: ChatOption): void
}>()

const chatResourcesStore = useChatResourcesStore()

const showOptions = computed(() => {
  return chatResourcesStore.chatCard2?.length && props.heartValue >= 100
})

const options = computed(() => chatResourcesStore.chatCard2 || [])

const handleSelect = (option: ChatOption) => {
  emit('select', option)
}

const isHorizontalLayout = computed(() => {
  return options.value.length <= 2
})
</script>

<style lang="less" scoped>
.chat-options {
  position: fixed;
  left: 50%;
  top: 65%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  z-index: 11;

  &.horizontal-layout {
    flex-direction: row;
    justify-content: center;
    gap: 16px;

    .chat-option-button {
      flex: 1;
      min-width: 0; // 防止按钮溢出
      min-height: 130px; // 横向布局时的最低高度

      .diamond-icon {
        top: 16px;
        left: 50%;
        transform: translateX(-50%);
      }

      .cursor-icon {
        bottom: 26px;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }

  .chat-option-button {
    position: relative;
    padding: 16px;
    border-radius: 20px;
    background: linear-gradient(151deg, #ffdbd3 0%, #ffb762 96.71%);
    border: 2px solid #1f0038;
    color: #1f0038;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    overflow: hidden; // 确保背景图片不会溢出圆角

    // 添加背景图片
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: url('https://static.playshot.ai/static/images/chat/chat-option-bg.png');
      background-repeat: repeat; // 设置背景图片重复
      background-size: 18px; // 设置背景图片的大小
      z-index: 0;
    }

    // 确保内容在背景之上
    span,
    .coins-container,
    .unlock-container {
      position: relative;
      z-index: 1;
    }

    &.second-option {
      background: linear-gradient(151deg, #ca93f2 0%, #b168e6 96.71%);
    }

    &.third-option {
      background: linear-gradient(151deg, #bcc2ff 0%, #68a7ff 96.71%);
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      filter: brightness(1.05);
    }

    &:active {
      transform: translateY(1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    span {
      line-height: 1.4;
    }

    &.unlocked {
      position: relative;
      &::before {
        display: inline-block;
        content: 'Unlocked';
        position: absolute;
        top: 0;
        left: 0;
        color: #1f0038;
        font-size: 10px;
        font-weight: 500;
        border-radius: 0px 0px 8px 0px;
        background: #daff96;
        padding: 3px 6px 2px 8px;
      }
    }

    .coins-container {
      position: absolute;
      right: -6px;
      top: -6px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      border-radius: 34px;
      border: 1px solid #daff96;
      padding: 2px 10px 2px 7px;
      background: #1f0038;
      height: 20px;

      img {
        width: 13px;
        height: 13px;
      }

      .coins-text {
        color: #daff96;
        font-size: 11px;
        font-weight: 600;
      }
    }

    .unlock-container {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      color: rgba(31, 0, 56, 0.4);
    }

    .diamond-icon {
      width: 24px;
      height: 24px;
      z-index: 1;
    }

    .cursor-icon {
      position: absolute;
      width: 24px;
      height: 24px;
      z-index: 1;
      right: 16px;
      // transform: translateY(-50%);
    }

    // Add highlight animation styles
    &.highlight-animation {
      position: relative;
      overflow: visible;
      border: none;

      // 原有背景和边框
      &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: 20px;
        background: linear-gradient(151deg, #ffdbd3 0%, #ffb762 96.71%);
        border: 2px solid #1f0038;
        z-index: -1;
      }

      // 动画边框
      &::after {
        content: '';
        position: absolute;
        inset: -5px;
        border-radius: 22px;
        background: linear-gradient(
          90deg,
          rgba(255, 215, 0, 0.2) 10%,
          rgba(255, 215, 0, 0.7) 25%,
          rgba(255, 215, 0, 1) 40%,
          rgba(255, 180, 0, 1) 50%,
          rgba(255, 215, 0, 1) 60%,
          rgba(255, 215, 0, 0.7) 75%,
          rgba(255, 215, 0, 0.2) 90%
        );
        background-size: 70% 100%;
        background-repeat: no-repeat;
        mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        mask-composite: exclude;
        -webkit-mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        padding: 10px;
        animation: borderMove 1.5s linear infinite;
        pointer-events: none;
        z-index: -2;
      }

      &.second-option::before {
        background: linear-gradient(151deg, #ca93f2 0%, #b168e6 96.71%);
      }

      &.third-option::before {
        background: linear-gradient(151deg, #bcc2ff 0%, #68a7ff 96.71%);
      }
    }
  }
}

@keyframes borderMove {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes rotateGradient {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes moveGradient {
  0% {
    background-position: 100% 0;
  }
  100% {
    background-position: -300% 0;
  }
}
</style>
