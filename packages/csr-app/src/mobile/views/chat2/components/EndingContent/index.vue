<template>
  <div class="ending-content">
    <div class="ending-text" v-html="endings?.html"></div>
    <div class="ending-buttons">
      <button class="ending-button restart" @click="$emit('restart')">
        Play Again
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Ending {
  html: string
}

interface Props {
  endings: Ending | null
}

defineProps<Props>()

const emit = defineEmits<{
  restart: []
}>()
</script>

<style lang="less" scoped>
.ending-content {
  z-index: 1000;
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translate(-50%, 0);

  .ending-text {
    width: 315px;
    padding: 20px;
    text-align: center;
    border-radius: 8px;
    border-top: 2px solid #1f0038;
    border-right: 2px solid #1f0038;
    border-bottom: 6px solid #1f0038;
    border-left: 2px solid #1f0038;
    background: linear-gradient(180deg, #f0dcff 0%, #ca93f2 100%);
    box-shadow: 0px 1px 6px 0px #9e81fe;
    z-index: 100;
  }
}

.ending-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 50px;

  .ending-button {
    border-radius: 40px;
    background: #ca93f2;
    color: #241d49;
    font-family: 'Work Sans';
    font-size: 15px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    display: flex;
    width: 315px;
    height: 42px;
    justify-content: center;
    align-items: center;
    gap: 5px;
    flex-shrink: 0;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      opacity: 0.9;
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }
  }
}
</style>
