<template>
  <div class="chat-container-wrapper">
    <div
      class="chat-wrapper"
      :class="{
        'video-playing': isPlayingVideo,
        'character-topmost': isCharacterTopmost,
        'telepathy-complete': isTelepathyComplete
      }"
      @click="handleContainerClick"
    >
      <!-- 顶部导航栏插槽 -->
      <slot name="top-bar" />

      <!-- 主要内容容器 -->
      <ChatContainer
        :is-playing-video="isPlayingVideo"
        :background-video="backgroundVideo"
        :background-image="backgroundImage"
        :default-image="defaultImage"
        :animated-images="animatedImages"
      >
        <!-- 视频播放器插槽 -->
        <template #video-player>
          <slot name="video-player" />
        </template>

        <!-- 聊天界面插槽 -->
        <template #chat-interface>
          <div v-show="shouldShowContent" class="content-wrapper">
            <!-- 聊天UI控件插槽 -->
            <slot name="chat-ui-controls" />

            <!-- 聊天界面插槽 -->
            <slot name="chat-interface" />

            <!-- 覆盖层插槽 -->
            <slot name="overlay" />

            <!-- 结束内容插槽 -->
            <slot name="ending" />
          </div>
        </template>
      </ChatContainer>
    </div>

    <!-- 背景加载覆盖层 -->
    <div v-if="!shouldShowContent && !shouldShowResourceLoader" class="background-loading-overlay">
      <div class="loading-spinner-center"></div>
    </div>

    <!-- 首次加载使用ResourceLoader -->
    <ResourceLoader
      v-if="shouldShowResourceLoader"
      :visible="shouldShowResourceLoader"
      :progress="loadingProgress"
      :loading-text="loadingText"
      :logo-url="logoUrl"
    />

    <!-- 错误状态 -->
    <div v-show="isError" class="error-wrapper">
      <div class="error-container" :style="{ backgroundImage: `url(${defaultImage})` }">
        <div class="error-content">
          <div class="error-message">
            <div class="error-title">Oops!</div>
            <div class="error-desc">This story seems unstable</div>
          </div>
          <button class="overlay-button" @click="$emit('back-home')">
            <span class="overlay-button-text" data-content="Back to Index">Back to Index</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useChatUIStore } from '@/store/chat-ui'
import ChatContainer from '@/mobile/components/ChatContainer.vue'
import ResourceLoader from '@/shared/components/ResourceLoader.vue'

interface Props {
  isPlayingVideo: boolean
  backgroundVideo: string | null
  backgroundImage: string | null
  defaultImage: string | null
  animatedImages: string[]
  contentReady: boolean
  loadingProgress?: number
  loadingText?: string
  shouldShowResourceLoader?: boolean
  isError?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loadingProgress: 0,
  loadingText: 'Initializing...',
  shouldShowResourceLoader: false,
  isError: false
})

const emit = defineEmits<{
  'container-click': [event: MouseEvent]
  'back-home': []
}>()

const chatUIStore = useChatUIStore()

// 本地状态
const isCharacterTopmost = ref(false)

// 计算属性
const isTelepathyComplete = computed(() => {
  // 这里可以根据实际需求调整逻辑
  return false
})

const shouldShowContent = computed(() => {
  return !props.isError && props.contentReady && !props.shouldShowResourceLoader
})

const logoUrl = computed(
  () => import.meta.env.VITE_LOGO_URL || 'https://cdn.magiclight.ai/assets/playshot/logo-v2.png'
)

// 方法
const handleContainerClick = (event: MouseEvent) => {
  emit('container-click', event)
}
</script>

<style lang="less" scoped>
.chat-container-wrapper {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  position: relative;
  overflow: hidden;
}

.chat-wrapper {
  position: relative;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  overflow: hidden;
  background-color: #000;
  transition: opacity 0.3s ease;

  &.video-playing {
    background-color: transparent;
  }

  &.character-topmost {
    :deep(.chat-background) {
      transform: scale(1.1);
    }
  }

  &.telepathy-complete {
    :deep(.chat-section) {
      background: none;
    }
  }
}

.content-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

// 背景上的loading覆盖层
.background-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  z-index: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;

  .loading-spinner-center {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top-color: #fff;
    animation: spin 1s linear infinite;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(4px);
  }
}

.error-wrapper {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  position: relative;
}

.error-container {
  position: relative;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
  }

  .error-content {
    position: relative;
    z-index: 1;
    text-align: center;
    color: #fff;

    .error-message {
      margin-bottom: 24px;

      .error-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 8px;
      }

      .error-desc {
        font-size: 16px;
        opacity: 0.8;
      }
    }

    .overlay-button {
      background: linear-gradient(180deg, #ff70df 0%, #f64c9c 100%);
      border: none;
      border-radius: 999px;
      padding: 12px 32px;
      color: #fff;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      transition: opacity 0.3s;

      &:hover {
        opacity: 0.8;
      }

      .overlay-button-text {
        position: relative;

        &::before {
          content: attr(data-content);
          position: absolute;
          left: 0;
          width: 100%;
          color: rgba(255, 255, 255, 0.4);
        }
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
