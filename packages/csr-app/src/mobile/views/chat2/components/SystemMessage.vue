<template>
  <div class="system-message">
    <div class="message-content">
      <div class="text">{{ message.content.text }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Message } from '../composables/useMessageState'

interface Props {
  message: Message
  visibleContent?: {
    text: string
    mood?: number
  }
}

defineProps<Props>()
</script>

<style lang="less" scoped>
.system-message {
  display: flex;
  justify-content: center;
  margin: 16px 0;

  .message-content {
    background: rgba(0, 0, 0, 0.6);
    border-radius: 999px;
    padding: 6px 16px;
    color: #fff;
    font-size: 13px;
    line-height: 1.4;
    backdrop-filter: blur(10px);
  }
}
</style>
