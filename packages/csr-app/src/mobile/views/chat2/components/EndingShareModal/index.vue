<template>
  <div class="ending-share-modal" v-if="chatUIStore.showEndingShare" @click="handleClose">
    <div class="share-content" @click.stop>
      <div class="share-header">
        <div class="share-title">Game Conclusion</div>
        <button class="close-button" @click="handleClose">
          <icon-close />
        </button>
      </div>

      <div class="share-body">
        <div class="poster-container" v-if="posterUrl">
          <img :src="posterUrl" alt="Share Poster" class="poster-image" />
        </div>
        <div v-else class="poster-loading">
          <a-spin />
        </div>

        <!-- <div class="ending-title">{{ storyStore.currentStory?.title }}</div>
        <div class="ending-subtitle">{{ chatStore.endings?.html }}</div> -->

        <div class="action-buttons">
          <button class="leave-button" @click="handleLeave">Leave</button>
          <button class="try-again-button" @click="handleTryAgain">Try again</button>
        </div>
      </div>
    </div>
    <div class="share-button-container">
      <button class="share-button" @click="handleShareClick">
        <icon-share />
      </button>
      <div class="share-button-text">Share</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useShareFeature } from '../../composables/useShare'
import IconShare from '@/assets/icon/share.svg'
import IconClose from '@/assets/icon/close.svg'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface/report'
import { useStoryStore } from '@/store/story'
import { useRouter } from 'vue-router'
import { useChatUIStore } from '@/store/chat-ui'

const router = useRouter()
const storyStore = useStoryStore()
const chatUIStore = useChatUIStore()

const { handleShare, getPoster, posterUrl, isLoading, isSupported, downloadImage } =
  useShareFeature()

const handleClose = () => {
  chatUIStore.showEndingShare = false
}

const handleLeave = () => {
  router.push('/')
}

const handleTryAgain = () => {
  router.push({
    name: 'StoryIntro',
    params: {
      storyId: storyStore.currentStory?.id
    }
  })
}

const handleShareClick = async () => {
  reportEvent(ReportEvent.EndingShareButtonClick, {
    story_id: storyStore.currentStory?.id,
    actor_id: storyStore.currentActor?.id,
    from: 'ending'
  })

  if (isSupported.value) {
    await handleShare(true)
  } else {
    await downloadImage(true)
  }
}

watch(
  () => chatUIStore.showEndingShare,
  async (newVal) => {
    if (newVal) {
      reportEvent(ReportEvent.EndingShareButtonExpose, {
        story_id: storyStore.currentStory?.id,
        actor_id: storyStore.currentActor?.id,
        from: 'ending'
      })
      await getPoster(true)
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.ending-share-modal {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  flex-direction: column;

  .share-content {
    width: 90%;
    max-width: 375px;
    background: #1f0038;
    border-radius: 24px;
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 16px;
    box-sizing: border-box;
  }

  .share-header {
    text-align: center;
    margin-bottom: 16px;
    position: relative;

    .share-title {
      font-size: 18px;
      font-weight: 600;
      color: #fff;
    }

    .close-button {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 24px;
      height: 24px;
      background: transparent;
      border: none;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      :deep(svg) {
        width: 20px;
        height: 20px;
        path {
          fill: rgba(255, 255, 255, 0.8);
        }
      }

      &:active {
        transform: translateY(-50%) scale(0.95);
      }
    }
  }

  .share-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;

    .poster-container {
      width: 100%;
      aspect-ratio: 1;
      border-radius: 16px;
      overflow: hidden;
      background: #1f0038;

      .poster-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .poster-loading {
      width: 100%;
      aspect-ratio: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #1f0038;
      border-radius: 16px;
    }

    .ending-title {
      font-size: 16px;
      font-weight: 600;
      color: #fff;
      text-align: center;
    }

    .ending-subtitle {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      text-align: center;
    }

    .action-buttons {
      display: flex;
      gap: 12px;
      width: 100%;
      margin-top: 8px;

      button {
        flex: 1;
        height: 42px;
        border-radius: 40px;
        font-size: 15px;
        font-weight: 600;
        cursor: pointer;
        border: none;
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.98);
        }
      }

      .leave-button {
        border: 1px solid rgba(255, 255, 255, 0.5);
        background: transparent;
        color: rgba(255, 255, 255, 0.8);
      }

      .try-again-button {
        background: #ca93f2;
        color: #241d49;
      }
    }
  }

  .share-button {
    margin-top: 20px;
    width: 42px;
    height: 42px;
    border-radius: 25px;
    background: #ca93f2;
    border: none;
    &:active {
      transform: scale(0.98);
      background: rgba(255, 255, 255, 0.1);
    }

    :deep(svg) {
      width: 24px;
      height: 24px;
      path {
        fill: #241d49;
      }
    }
  }

  .share-button-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 5px;

    .share-button-text {
      font-size: 12px;
      font-weight: 600;
      color: #fff;
    }
  }
}
</style>
