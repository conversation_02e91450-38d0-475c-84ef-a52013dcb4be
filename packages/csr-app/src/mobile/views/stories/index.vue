<template>
  <div class="stories-page" ref="storiesPage">
    <!-- Header section remains the same -->

    <header class="header">
      <div class="logo">
        <img :src="logoUrl" :alt="websiteTitle" loading="eager" width="117" height="24" />
      </div>
      <div class="header-buttons">
        <button
          class="checkin-btn"
          @click="
            checkinStore.canShowCheckin
              ? handleDailyTasks()
              : showAuthDrawer('Sign up to claim your rewards!')
          "
        >
          <!-- 有奖励可领取时显示摇摆的emoji -->
          <span :class="{ 'shake-animation': hasRewards }">🎁</span>
        </button>
        <a
          v-if="appName === 'ReelPlay'"
          href="https://x.com/Reelplay197835"
          target="_blank"
          rel="noopener noreferrer"
          class="twitter-btn"
          @click="handleTwitter"
        >
          <TwitterIcon />
          <span class="twitter-text">Follow</span>
        </a>
        <a
          v-if="appName === 'PlayShot'"
          href="https://t.me/+bnRxPQGjVPM5MzM1 "
          target="_blank"
          rel="noopener noreferrer"
          class="telegram-btn"
          @click="handleTelegram"
        >
          <TelegramIcon />
          <span class="telegram-text">Join</span>
        </a>
        <button
          v-if="!userStore.userInfo?.uuid || userStore.isGuest"
          class="sign-in-btn"
          @click="handleSignIn"
        >
          Sign in
        </button>
        <CreditDisplay
          v-else
          :amount="userStore.userInfo?.coins || 0"
          @add="handleRecharge"
          :show-add-button="true"
        />
      </div>
    </header>

    <!-- <div class="header-background">
      <img
        src="https://cdn.magiclight.ai/assets/playshot/banner-2.png"
        alt="background"
        loading="lazy"
        decoding="async"
      />
    </div> -->

    <div class="download-banner" v-if="appName === 'ReelPlay'">
      <div class="banner-content">
        <img
          src="https://static.playshot.ai/static/images/banner/reelplay_download_logo.png"
          alt="App Logo"
          class="app-logo"
          width="32"
          height="32"
        />
        <div class="banner-text">
          <h3>Join Community</h3>
          <p>Share your thoughts and claim your reward!</p>
        </div>
      </div>
      <a
        href="https://discord.gg/FdZJmU4a8x"
        target="_blank"
        rel="noopener noreferrer"
        class="discord-btn"
        @click="handleDiscord"
      >
        <DiscordIcon />
        <span class="discord-text">Join</span>
      </a>
    </div>

    <main class="main-content">
      <section class="section">
        <div class="section-header">
          <h2 class="section-title">
            <span class="icon">🔥</span>
            Hottest
          </h2>
          <p class="section-subtitle">Most popular stories for you</p>
        </div>
        <div class="filter-buttons">
          <button class="filter-btn" @click="showPopularDrawer = true">
            {{ selectedPopularLabel() }}
            <DropdownIcon />
          </button>
          <!-- <button class="filter-btn" @click="showGenderDrawer = true">
            {{ selectedGenderLabel() }}
            <DropdownIcon />
          </button> -->
          <button
            :class="{
              'filter-btn': true,
              'tags-btn': selectedTags.length > 0 && !isLoading,
              loading: isLoading
            }"
            @click="showTagsDrawer = true"
          >
            {{ tagsButtonText() }}
          </button>
        </div>
        <div class="story-grid-container" ref="storyGridContainer">
          <!-- 优化：始终显示VirtualStoryGrid，避免条件渲染导致的延迟 -->
          <VirtualStoryGrid
            :stories="
              storyStore.hotStories.length > 0
                ? storyStore.hotStories
                : storyStore.loading || isLoading
                ? Array(6).fill({})
                : []
            "
            :loading="storyStore.loading || isLoading"
            :container-element="storyGridContainer"
            @story-click="handleStoryClick"
            @image-loaded="handleImageLoaded"
            @subscription-change="handleSubscriptionChange"
            @need-login="showAuthDrawer('Sign up to subscribe!')"
          />
          <!-- 只有在确实没有数据且不在加载时才显示无数据提示 -->
          <div
            v-if="!storyStore.loading && !isLoading && storyStore.hotStories.length === 0"
            class="no-data"
          >
            <p>No characters available under current conditions.</p>
          </div>
        </div>
      </section>
    </main>

    <footer class="footer">
      <a href="mailto:<EMAIL>" class="support-email">
        Support Email: <EMAIL>
      </a>
    </footer>

    <AuthDrawer
      :visible="authDrawerVisible"
      :is-in-landing-page="false"
      :title="authDrawerTitle"
      @update:visible="authDrawerVisible = $event"
      @login="handleAuthSuccess"
      @register="handleAuthSuccess"
    />

    <CategoryDrawer
      :visible="showPopularDrawer"
      type="popular"
      :initial-popular="selectedPopular"
      @update:visible="showPopularDrawer = $event"
      @popular-change="handlePopularChange"
    />

    <CategoryDrawer
      :visible="showGenderDrawer"
      type="gender"
      :initial-gender="selectedGender"
      @update:visible="showGenderDrawer = $event"
      @gender-change="handleGenderChange"
    />

    <CategoryDrawer
      :visible="showTagsDrawer"
      type="tags"
      title="Tags"
      :initial-tags="selectedTags"
      @update:visible="showTagsDrawer = $event"
      @tags-change="handleTagsChange"
    />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'Stories'
})
import {
  onMounted,
  onUnmounted,
  getCurrentInstance,
  defineAsyncComponent,
  onActivated,
  onDeactivated,
  ref,
  computed
} from 'vue'
import { useRouter, onBeforeRouteLeave } from 'vue-router'
import { useStoryStore, useUserStore, useRechargeStore } from '@/store'
import { useCheckinStore } from '@/store/checkin'
import { useTasksStore } from '@/store/tasks'
import { useTagsFilter } from '@/composables/useTagsFilter'
import { setDynamicSEO } from '@/router/seo-guard'
import type { Story } from '@/api/stories'
import { reportEvent, getDeviceId } from '@/utils'
import { ReportEvent } from '@/interface'

import DiscordIcon from '@/assets/icon/discord-black.svg'
import TwitterIcon from '@/assets/icon/twitter.svg'
import TelegramIcon from '@/assets/icon/telegram.svg'
import AuthDrawer from '@/mobile/components/AuthDrawer.vue'
import { isAndroidWebView } from '@/utils/isAndroidWebView'
import CategoryDrawer from '@/mobile/components/CategoryDrawer.vue'
import DropdownIcon from '@/assets/icon/dropdown.svg'

import VirtualStoryGrid from '@/mobile/components/VirtualStoryGrid.vue'

// Lazy load components
const CreditDisplay = defineAsyncComponent(() => import('@/shared/components/CreditDisplay.vue'))

const router = useRouter()
const storyStore = useStoryStore()
const userStore = useUserStore()
const rechargeStore = useRechargeStore()
const checkinStore = useCheckinStore()
const tasksStore = useTasksStore()

// 检查是否有未完成的任务或可以签到
const hasRewards = computed(() => {
  // 如果可以签到，返回 true
  if (checkinStore.canClaim) return true

  // 如果有未完成的任务，返回 true
  if (tasksStore.pendingTasks.length > 0) return true

  return false
})

// 从环境变量获取网站标题和Logo URL
const websiteTitle = computed(() => import.meta.env.VITE_WEBSITE_TITLE || 'Playshot')
const logoUrl = computed(
  () => import.meta.env.VITE_LOGO_URL || 'https://cdn.magiclight.ai/assets/playshot/logo-v2.png'
)
const appName = computed(() => import.meta.env.VITE_APP_NAME || 'Playshot')
const authDrawerVisible = ref(false)
const authDrawerTitle = ref('')

// 移除未使用的isIOS变量

const showPopularDrawer = ref(false)
const showGenderDrawer = ref(false)
const showTagsDrawer = ref(false)

// 使用标签筛选composable
const {
  selectedPopular,
  selectedGender,
  selectedTags,
  selectedPopularLabel,
  selectedGenderLabel,
  tagsButtonText,
  handlePopularChange,
  handleGenderChange,
  handleTagsChange,
  initFromUrlParams,
  isLoading
} = useTagsFilter()

// 更新SEO信息
const updateSEO = () => {
  setDynamicSEO('Stories', {
    filters: {
      selectedTags: selectedTags.value,
      selectedPopular: selectedPopular.value,
      totalStories: storyStore.hotStories.length
    }
  })
}

const handleStoryClick = async (story: Story) => {
  reportEvent(ReportEvent.ClickIndexPageStoryCard, {
    storyId: story.id
  })
  storyStore.setCurrentStory(story)
  storyStore.setCurrentActors(story.actors)
  router.push(`/story/${story.id}`)
  // router.push(`/chat/${story.id}/${story.actors[0].id}`)
}

const handleImageLoaded = (story: Story) => {
  reportEvent(ReportEvent.IndexPageStoryCardExposure, {
    storyId: story.id
  })
}

const handleSignIn = () => {
  reportEvent(ReportEvent.ClickIndexPageSignIn)
  router.push('/user/login')
}

const handleRecharge = () => {
  rechargeStore.toggleRechargeModal()
}

const handleDiscord = () => {
  reportEvent(ReportEvent.SocialButtonJoinClick, {
    type: 'discord'
  })
}

const handleTwitter = () => {
  reportEvent(ReportEvent.SocialButtonJoinClick, {
    type: 'twitter'
  })
}

const handleTelegram = () => {
  reportEvent(ReportEvent.SocialButtonJoinClick, {
    type: 'telegram'
  })
}

const scrollPosition = ref(0)
const storiesPage = ref<HTMLElement | null>(null)
const storyGridContainer = ref<HTMLElement | null>(null)

// 获取实际的滚动容器
const getScrollContainer = () => {
  // 使用 stories-page 作为滚动容器（它有 overflow-y: auto）
  return storiesPage.value || (document.querySelector('.stories-page') as HTMLElement)
}

onBeforeRouteLeave(() => {
  // 在路由离开前保存滚动位置
  const scrollContainer = getScrollContainer()
  if (scrollContainer) {
    scrollPosition.value = scrollContainer.scrollTop
    console.log('💾 保存滚动位置:', scrollPosition.value, '容器:', scrollContainer.className)
  }
})

onActivated(() => {
  console.log('🔄 Stories组件 onActivated - 组件被激活，当前滚动位置:', scrollPosition.value)

  // 恢复滚动位置
  const restoreScroll = () => {
    const scrollContainer = getScrollContainer()
    if (scrollContainer && scrollPosition.value > 0) {
      scrollContainer.scrollTo({
        top: scrollPosition.value,
        behavior: 'instant'
      })
      console.log('🔄 恢复滚动位置:', scrollPosition.value, '容器:', scrollContainer.className)
    } else if (scrollPosition.value > 0) {
      console.warn('⚠️ 无法恢复滚动位置 - 容器未找到，保存的位置:', scrollPosition.value)
    }
  }

  // 立即尝试恢复
  restoreScroll()

  // 延迟重试，确保 VirtualStoryGrid 已经渲染
  setTimeout(restoreScroll, 100)
  setTimeout(restoreScroll, 300)
})

// 标记是否已经初始化
const isInitialized = ref(false)

// 使用onMounted进行一次性初始化
onMounted(() => {
  console.log('🔄 Stories组件 onMounted - 组件被挂载')

  // 确保Stories页面立即显示
  const checkAndShowStories = () => {
    const storiesElement = document.querySelector('.stories-page') as HTMLElement
    if (storiesElement) {
      storiesElement.classList.add('loaded')
      storiesElement.style.opacity = '1'
      console.log('✅ Stories页面已显示')
    }
  }

  // 立即检查一次
  setTimeout(checkAndShowStories, 10)

  // 备用方案
  setTimeout(checkAndShowStories, 100)

  reportEvent(ReportEvent.VisitIndexPage)
  const { proxy } = getCurrentInstance()

  // @ts-ignore
  proxy?.$tracker?.start()
  // @ts-ignore
  proxy?.$tracker?.setUserID(userStore.userInfo?.uuid)
})

// 使用onActivated处理每次组件激活时的逻辑
onActivated(async () => {
  // 如果已经初始化过，只处理URL参数变化
  if (isInitialized.value) {
    // 只处理URL参数变化，不重新加载所有数据
    initFromUrlParams() // 不等待，避免阻塞页面显示

    // 更新SEO
    setTimeout(() => updateSEO(), 100)

    // 恢复滚动位置（在内容更新后）
    setTimeout(() => {
      const restoreScroll = () => {
        const scrollContainer = getScrollContainer()
        if (scrollContainer && scrollPosition.value > 0) {
          scrollContainer.scrollTo({
            top: scrollPosition.value,
            behavior: 'instant'
          })
          console.log('🔄 [重新激活] 恢复滚动位置:', scrollPosition.value)
        }
      }
      restoreScroll()
      setTimeout(restoreScroll, 100)
    }, 50)

    return
  }

  // 首次加载时，优先初始化标签筛选以显示内容
  initFromUrlParams() // 不等待，让页面先显示

  // 后台执行其他非关键初始化任务
  Promise.all([
    userStore.isAuthenticated ? userStore.getUserInfo() : Promise.resolve(),
    // Only fetch check-in info if user is logged in and not a guest
    userStore.isAuthenticated && !userStore.isGuest
      ? checkinStore.fetchCheckinInfo()
      : Promise.resolve(),
    // Fetch tasks list if user is logged in and not a guest
    userStore.isAuthenticated && !userStore.isGuest ? tasksStore.fetchTasks() : Promise.resolve()
  ])
    .then(() => {
      // 后台任务完成后的处理
      if (isAndroidWebView()) {
        reportEvent(ReportEvent.OpenInAndroidAPKWebView, {
          userId: userStore.userInfo?.uuid,
          deviceId: getDeviceId()
        })
      }
    })
    .catch((error) => {
      console.warn('Background initialization failed:', error)
    })

  // 标记为已初始化
  isInitialized.value = true

  // 初始化SEO
  updateSEO()

  // // Show check-in modal if user can claim and check-in rewards are configured
  // if (
  //   userStore.isAuthenticated &&
  //   !userStore.isGuest &&
  //   sysConfigStore.checkInCoinsPerDay &&
  //   checkinStore.canClaim
  // ) {
  //   checkinStore.showModal()
  // }
})

const showAuthDrawer = (title?: string) => {
  authDrawerTitle.value = title || 'Sign in to continue !'
  authDrawerVisible.value = true
}

const handleAuthSuccess = async () => {
  authDrawerVisible.value = false
  // After successful login, fetch check-in info and tasks, then redirect to daily tasks page if needed
  if (userStore.isGuest) return

  // 并行获取签到信息和任务列表
  await Promise.all([checkinStore.fetchCheckinInfo(), tasksStore.fetchTasks()])

  // 如果有可领取的奖励，跳转到每日任务页面
  if (hasRewards.value) {
    handleDailyTasks()
  }
}

const handleSubscriptionChange = (updatedStory: Story) => {
  // 更新 store 中的故事订阅状态
  const index = storyStore.hotStories.findIndex((s) => s.id === updatedStory.id)
  if (index !== -1) {
    storyStore.hotStories[index] = updatedStory
  }
}

const handleDailyTasks = () => {
  reportEvent(ReportEvent.ClickProfilePageDailyTasks, {
    userId: userStore.userInfo?.uuid
  })
  router.push('/daily-tasks')
}

// 添加生命周期钩子来调试 keep-alive 状态
onUnmounted(() => {
  console.log('❌ Stories组件 onUnmounted - 组件被销毁（keep-alive 失效）')
})

onDeactivated(() => {
  console.log('😴 Stories组件 onDeactivated - 组件被缓存')
})
</script>

<style lang="less" scoped>
@import '@/assets/style/mixin.less';
@import '@/assets/style/theme.less';

.stories-page {
  height: 100%;
  height: calc(var(--vh, 1vh) * 100);
  background-color: var(--mobile-bg-primary);
  position: relative;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  will-change: transform;
  -webkit-overflow-scrolling: touch;
  padding-bottom: calc(env(safe-area-inset-bottom) + 80px);
  transition: background-color 0.3s ease;

  // 确保Stories页面始终可见
  opacity: 1 !important;

  // 如果有 loaded 类，添加过渡效果
  &.loaded {
    opacity: 1;
    transition: opacity 0.2s ease;
  }
}

.header-background {
  position: relative;
  height: fit-content;
  margin-bottom: 0;
  overflow: hidden;
  flex-shrink: 0;
  padding: 6px 0 26px 0;
  margin-bottom: 30px;
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100%;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    // position: absolute;
    top: 0;
    left: 0;
  }
}

.header {
  position: relative;
  top: 0;
  left: 0;
  right: 0;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 0 16px;
  background-color: transparent;

  .logo {
    flex-shrink: 0;
    img {
      width: 117px;
      height: 24px;
      object-fit: cover;
    }
  }

  .header-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: flex-end;
    min-width: 0;

    // 在小屏幕上减少间距
    @media screen and (max-width: 480px) {
      gap: 6px;
    }

    // 在超小屏幕上进一步优化
    @media screen and (max-width: 360px) {
      gap: 4px;
    }
  }
  :deep(.credit-display) {
    margin-left: 8px;
  }
  .checkin-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: transparent;
    border: none;
    transition: all 0.2s ease;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    flex-shrink: 0;

    &:active {
      transform: scale(0.95);
    }

    // 摇摆动画 - 摇一次停2秒
    .shake-animation {
      animation: shake 3s ease-in-out infinite;
    }
  }

  .discord-btn,
  .twitter-btn,
  .telegram-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 28px;
    padding: 0 10px;
    transition: all 0.2s ease;
    gap: 4px;
    width: auto;
    min-width: 60px;
    border-radius: 14px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    text-decoration: none;
    flex-shrink: 0;

    svg {
      width: 14px;
      height: 14px;
      display: flex;
    }

    // 在小屏幕上进一步压缩
    @media screen and (max-width: 480px) {
      min-width: 50px;
      padding: 0 8px;
      height: 26px;

      svg {
        width: 12px;
        height: 12px;
      }
    }

    // 在超小屏幕上只显示图标
    @media screen and (max-width: 360px) {
      min-width: 28px;
      width: 28px;
      padding: 0;

      span {
        display: none;
      }
    }
  }

  .discord-btn {
    background: #b48ded;
    box-shadow: 0 2px 4px rgba(88, 101, 242, 0.3);

    .discord-text {
      color: #1f0038;
      font-weight: 600;
      font-size: 13px;
      line-height: 1;
      display: flex;
      align-items: center;
    }
  }

  .twitter-btn {
    background: #222222; /* 使用深灰色而不是纯黑色，增加对比度 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2); /* 添加细微的白色边框 */

    .twitter-text {
      color: #ffffff;
      font-weight: 600;
      font-size: 13px;
      line-height: 1;
      display: flex;
      align-items: center;
    }

    svg {
      width: 18px; /* 稍微增大图标尺寸 */
      height: 18px;
      margin-right: 2px; /* 调整间距 */
    }
  }

  .telegram-btn {
    background: #0088cc; /* Telegram品牌色 */
    box-shadow: 0 2px 4px rgba(0, 136, 204, 0.3);
    border: 1px solid transparent;

    .telegram-text {
      color: #ffffff;
      font-weight: 600;
      font-size: 13px;
      line-height: 1;
      display: flex;
      align-items: center;
    }

    svg {
      width: 18px;
      height: 18px;
      margin-right: 2px;
    }
  }
  .sign-in-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 24px;
    // padding: 15px 0;
    white-space: nowrap;
    border-radius: 34px;
    background: #daff96;
    color: #1f0038;
    border: none;
    font-weight: 600;
    font-size: 12px;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      opacity: 0.9;
    }

    &:active {
      transform: translateY(0);
    }
  }
}

.main-content {
  position: relative;
  z-index: 2;
  padding: 0 16px 16px 16px;
  margin-top: 0px;
  flex: 1;
  margin-bottom: 20px;
  width: 100%;

  @media screen and (min-width: 768px) {
    padding: 16px 32px;
  }
}

.section {
  margin-bottom: 32px;

  .section-header {
    margin-bottom: 16px;
  }

  .section-title {
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 0;
  }

  .section-subtitle {
    color: var(--text-secondary);
    font-size: 12px;
    margin-top: 2px;
  }
}

.footer {
  display: flex;
  justify-content: center;
  padding: 20px 16px;
  position: relative;
  width: 100%;
  background-color: var(--mobile-bg-primary);
  margin-bottom: env(safe-area-inset-bottom);

  .support-email {
    color: var(--text-primary);
    text-align: center;
    font-size: 11px;
    font-weight: 500;
    text-decoration: none;
    opacity: 0.5;

    &:hover {
      opacity: 0.8;
    }
  }
}

.story-grid-container {
  // 优化：让容器能够自适应高度，同时确保有最小高度
  min-height: 300px;
  flex: 1; // 占用剩余空间
  position: relative;
  display: flex;
  flex-direction: column;
  height: auto;
  overflow: visible;

  // 确保在不同屏幕尺寸下都有合适的最小高度
  @media screen and (max-height: 600px) {
    min-height: 250px;
  }

  @media screen and (max-height: 500px) {
    min-height: 200px;
  }

  // 确保虚拟滚动组件能够正确显示
  > .virtual-story-grid-mobile {
    flex: 1;
    width: 100%;
  }
}

.story-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  padding: 16px;

  /* 确保在小屏幕上至少显示两个卡片 */
  @media screen and (max-width: 360px) {
    grid-template-columns: repeat(2, 1fr);
  }

  .no-data {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 0;
    color: #ca93f2;
    font-size: 16px;
    font-weight: 500;
  }
}

.no-data {
  text-align: center;
  padding: 40px 0;
  color: #ca93f2;
  font-size: 16px;
  font-weight: 500;
}

.category-list {
  display: none;
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin: 0 0 16px;
  padding: 0;
  width: 100%;

  .filter-btn {
    display: flex;
    height: 30px;
    padding: 10px 16px;
    justify-content: center;
    align-items: center;
    gap: 12px;
    border-radius: 36px;
    border: 1px solid #ca93f2;
    color: #ca93f2;
    font-size: 12px;
    font-weight: 700;
    background: transparent;
    transition: all 0.2s ease;
    white-space: nowrap;
    margin-bottom: 4px;

    svg {
      width: 12px;
      height: 6px;
    }

    &:hover {
      background: rgba(202, 147, 242, 0.2);
      color: #ca93f2;
    }

    &:active {
      transform: scale(0.95);
    }

    &.tags-btn {
      background: #ca93f2;
      color: #1f0038;
      border-color: #ca93f2;
      box-shadow: 0 2px 8px rgba(202, 147, 242, 0.3);

      &:hover {
        background: #d4a8f7;
        color: #1f0038;
      }
    }

    &.loading {
      opacity: 0.7;
      pointer-events: none;
    }
  }

  /* 小屏幕适配 - 保持按钮大小，但确保它们能够正确换行 */
  @media screen and (max-width: 360px) {
    justify-content: space-between; /* 在小屏幕上均匀分布按钮 */

    .filter-btn {
      flex-grow: 0;
      flex-shrink: 0;
    }
  }
}

// Loading & Error states
.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--text-secondary);
  gap: 16px;
}

.download-banner {
  margin-top: 8px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #daff96;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 2;
  // height: 66px;
  .banner-content {
    display: flex;
    // align-items: center;
    gap: 12px;
  }

  .app-logo {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    object-fit: cover;
  }

  .banner-text {
    h3 {
      color: #1f0038;
      font-size: 14px;
      font-weight: 600;
      margin: 0;
    }

    p {
      color: #1e1e1e;
      font-size: 12px;
      margin: 5px 0 0;
    }
  }

  .discord-btn {
    background: #ca93f2;
    color: #1f0038;
    border: none;
    padding: 8px 16px;
    border-radius: 15px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    text-decoration: none;

    svg {
      width: 16px;
      height: 16px;
    }

    .discord-text {
      color: #1f0038;
      font-weight: 600;
      font-size: 13px;
    }

    &:hover {
      transform: translateY(-1px);
      opacity: 0.9;
    }

    &:active {
      transform: translateY(0);
    }
  }
}

// 摇摆动画关键帧
@keyframes shake {
  0% {
    transform: rotate(0deg);
  }
  3.33%,
  10%,
  16.67%,
  23.33%,
  30% {
    transform: rotate(-10deg);
  }
  6.67%,
  13.33%,
  20%,
  26.67% {
    transform: rotate(10deg);
  }
  33.33%,
  100% {
    transform: rotate(0deg);
  }
}
</style>
