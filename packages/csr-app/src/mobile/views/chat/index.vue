<template>
  <div
    class="chat-container"
    v-show="
      !chatStore.isInitializing &&
      !chatStore.isError &&
      !chatStore.isResourceLoading
    "
    :class="{
      'video-playing': chatStore.isPlayingVideo,
      'character-topmost': isCharacterTopmost,
    }"
    @click="handleContainerClick"
  >
    <!-- Progress Bar -->
    <div v-if="!chatStore.isPlayingVideo" class="progress-container">
      <ChatProgress :value="chatStore.totalProgress" />
    </div>

    <!-- Audio Control -->
    <div class="audio-control" v-if="!chatStore.isPlayingVideo">
      <button class="audio-button" @click.stop="audioManager.toggleMute">
        <icon-volume-notice v-if="!audioManager.isMuted" />
        <icon-volume-mute v-else />
      </button>
    </div>

    <!-- Credit Display -->
    <div class="credit-display-container" v-if="!chatStore.isPlayingVideo">
      <CreditDisplay
        :amount="userStore.userInfo?.coins || 0"
        :show-add-button="true"
        @add="handleAddCredit('top')"
      />
    </div>

    <!-- Tap to Show Button -->
    <img
      v-show="isCharacterTopmost && !chatStore.isPlayingVideo"
      :class="{ 'button-topmost': isCharacterTopmost }"
      src="https://cdn.magiclight.ai/assets/playshot/tap2show.png"
      alt="tap2show"
    />

    <!-- Video Player -->
    <VideoPlayer
      ref="videoManager.videoPlayerRef"
      :storyId="storyStore.currentStory?.id || ''"
      :actorId="storyStore.currentActor?.id || ''"
      :video-url="chatStore.videoUrl"
      :is-playing="chatStore.isPlayingVideo"
      :muted="chatStore.isMuted"
      :loading-background="storyStore.currentActor?.preview_url"
      :force-loading="false"
      @update:is-playing="(val) => (chatStore.isPlayingVideo = val)"
      @update:muted="(val) => (chatStore.isMuted = val)"
      @video-ended="videoManager.onVideoEnded"
      @video-loaded="videoManager.onVideoLoaded"
      @video-skipped="videoManager.handleVideoSkipped"
    />

    <!-- Background Layers -->
    <div class="background-layers" v-show="!chatStore.isPlayingVideo">
      <VideoBackground
        v-if="chatStore.backgroundVideo"
        :video-url="chatStore.backgroundVideo"
        :muted="true"
        :loop="true"
        :autoplay="true"
      />
      <div
        v-else
        class="background-image"
        :class="{ active: true }"
        :style="{
          backgroundImage: `url(${
            currentBgImage || storyStore.currentActor?.preview_url
          })`,
        }"
      />
    </div>

    <!-- Chat Interface -->
    <div v-show="!chatStore.isPlayingVideo">
      <!-- Character Background -->
      <div class="character-background">
        <div
          v-if="!props.characterId"
          class="back-button"
          @click="handleBackHome"
        >
          <icon-left />
        </div>
      </div>
      <!-- Chat Section -->
      <ChatSection
        v-show="
          !chatStore.overlay &&
          chatStore.isChatSectionVisible &&
          !chatStore.isEnding
        "
        :visible="!chatStore.overlay && chatStore.isChatSectionVisible"
        class="chat-section-container"
        ref="chatSectionRef"
        :height="chatSectionHeight"
        :has-task-tip="hasTaskTip"
        @height-change="handleHeightChange"
      >
        <!-- Task Tip -->
        <template #task-tip>
          <TaskTip
            v-show="
              hasTaskTip &&
              !chatStore.isPlayingVideo &&
              chatStore.isChatSectionVisible &&
              !chatStore.isEnding
            "
          />
        </template>
        <template #messages>
          <MessageList
            ref="messageListRef"
            :messages="messages"
            :visible-content="visibleContent"
            :is-message-playing="isMessagePlaying"
            @scroll-to-bottom="scrollToBottom"
            @add-credit="handleAddCredit"
            @update:visible-content="(content) => (visibleContent = content)"
          />
        </template>
        <template #input>
          <ChatInput
            ref="chatInputRef"
            @add-credit="handleAddCredit"
            @scroll-to-bottom="scrollToBottom"
          />
        </template>
      </ChatSection>
    </div>

    <!-- Ending Content -->
    <div v-if="chatStore.isEnding && chatStore.endings" class="ending-content">
      <div class="ending-text" v-html="chatStore.endings?.html"></div>
      <div class="ending-buttons">
        <button class="ending-button restart" @click="handleRestart">
          <!-- <icon-restart /> -->
          Play Again
        </button>
      </div>
    </div>

    <!-- Overlay -->
    <div
      class="overlay-container"
      v-if="chatStore.overlay"
      @click="handleOverlayButton"
    >
      <div class="character-overlay" :class="chatStore.overlay.position">
        <div class="overlay-content">
          <div class="overlay-text" v-html="chatStore.overlay.text"></div>
        </div>
      </div>

      <div v-if="chatStore.overlay?.button" class="overlay-button-container">
        <button
          class="overlay-button"
          v-if="chatStore.overlay.button.text !== sayHiText"
        >
          <img
            class="overlay-button-icon"
            src="https://cdn.magiclight.ai/assets/playshot/target.png"
            alt="task"
          />
          <span
            class="overlay-button-text"
            :data-content="chatStore.overlay.button.text"
          >
            {{ chatStore.overlay.button.text }}
          </span>
        </button>
        <img
          class="overlay-button-say-hi"
          v-else
          src="https://cdn.magiclight.ai/assets/playshot/sayHi.png"
          alt="Play"
        />
      </div>
    </div>

    <!-- Animated Images -->
    <div
      v-if="chatStore.animatedImages.length > 0"
      class="animated-images-container"
      @click="handleAnimatedImageClick"
    >
      <img
        class="tap2continue"
        src="https://cdn.magiclight.ai/assets/playshot/tap2continue-v2.png"
        alt="tap2continue"
      />
      <img
        v-for="(image, index) in filteredAnimatedImages"
        :key="image"
        :src="image"
        :class="{
          'animated-image': true,
          'active': index === currentImageIndex,
          'previous': index === previousImageIndex,
        }"
        alt="Animated Image"
        @error="
          (e: Event) => ((e.target as HTMLImageElement).style.display = 'none')
        "
      />
    </div>

    <!-- Modals -->
    <LeaveConfirmModal
      v-model:visible="showLeaveConfirmModal"
      @confirm="handleLeaveConfirm"
      @cancel="handleLeaveCancel"
    />
    <AuthDrawer
      :isInLandingPage="!!props.characterId"
      v-model:visible="showAuthDrawer"
      @login="handleLoginSuccess"
      @register="handleRegisterSuccess"
    />
    <RatingModal
      v-model:visible="storyStore.isShowRatingModal"
      @close="storyStore.isShowRatingModal = false"
      @submit="handleRatingSubmit"
    />

    <!-- 付费场景提醒对话框 -->
    <ConfirmDialog
      v-model:visible="chatEventsStore.showPaidSceneDialog"
      title="Spicy Level"
      :title-style="{ color: '#FFF' }"
      :content-style="{
        color: '#CA93F2',
        fontWeight: '600',
        textAlign: 'left',
      }"
      confirm-text="I Understand"
      :show-cancel="false"
      :show-icon="false"
      :close-on-click-overlay="false"
      @confirm="chatEventsStore.showPaidSceneDialog = false"
    >
      <template #content>
        <p>You have entered the spicy level. Enjoy your spicy time.</p>
        <p>
          Note: This level offers 5 minutes of free time, and the subsequent
          content is priced at 5 diamonds per 5 minutes.
        </p>
      </template>
    </ConfirmDialog>
  </div>
  <div v-show="chatStore.isInitializing || chatStore.isResourceLoading">
    <LoadingOverlay :background-image="storyStore.currentActor?.preview_url" />
  </div>
  <div v-show="chatStore.isError">
    <div
      class="error-container"
      :style="{
        backgroundImage: `url(${storyStore.currentActor?.preview_url})`,
      }"
    >
      <div class="error-content">
        <!-- <img
          class="error-emoji"
          src="https://cdn.magiclight.ai/assets/playshot/error-emoji.png"
          alt="Oops"
        /> -->
        <div class="error-message">
          <div class="error-title">Oops!</div>
          <div class="error-desc">This story seems unstable</div>
        </div>
        <button class="overlay-button" @click="handleBackHome">
          <span class="overlay-button-text" data-content="Back to Index"
            >Back to Index</span
          >
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    ref,
    onMounted,
    watch,
    computed,
    onBeforeMount,
    onBeforeUnmount,
    onUnmounted,
    nextTick,
    getCurrentInstance,
  } from 'vue'
  import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router'
  import { useChatStore } from '@/store/chat'
  import { useActorStore } from '@/store/character'
  import { useUserStore } from '@/store/user'
  import { useStoryStore } from '@/store/story'
  import { useRechargeStore } from '@/store/recharge'
  import { useChatEventsStore } from '@/store/chat-events'
  import { ReportEvent } from '@/interface'
  import { reportEvent } from '@/utils'
  // Components
  import VideoPlayer from './components/VideoPlayer.vue'
  import LeaveConfirmModal from '@/mobile/components/LeaveConfirmModal.vue'
  import AuthDrawer from '@/mobile/components/AuthDrawer.vue'
  import ChatProgress from '@/mobile/views/chat/components/ChatProgress.vue'
  import CreditDisplay from '@/shared/components/CreditDisplay.vue'
  import MessageList from './components/MessageList.vue'
  import ChatInput from './components/ChatInput.vue'
  import ChatSection from './components/ChatSection.vue'
  import TaskTip from './components/TaskTip.vue'
  import VideoBackground from '@/mobile/components/VideoBackground.vue'
  import LoadingOverlay from '@/mobile/components/LoadingOverlay.vue'
  import RatingModal from '@/mobile/components/RatingModal.vue'
  import ConfirmDialog from '@/components/ConfirmDialog.vue'
  import { Message } from '@/mobile/components/Message'
  // Icons
  // import IconRestart from '@/assets/icon/restart.svg'
  // import IconLeft from '@/assets/icon/left.svg'
  import IconVolumeNotice from '@/assets/icon/volume-notice.svg'
  import IconVolumeMute from '@/assets/icon/volume-mute.svg'

  // Composables
  import { useTypingEffect } from '@/mobile/composables/useTypingEffect'
  import { useAudioManager } from '@/mobile/composables/useAudioManager'
  import { useVideoManager } from '@/mobile/composables/useVideoManager'
  import { useSmartNavigation } from '@/composables/useSmartNavigation'

  const props = defineProps<{
    characterId?: string
  }>()

  // Store
  const route = useRoute()
  const router = useRouter()
  const chatStore = useChatStore()
  const actorStore = useActorStore()
  const userStore = useUserStore()
  const storyStore = useStoryStore()
  const rechargeStore = useRechargeStore()
  const chatEventsStore = useChatEventsStore()

  // Composables
  const { typeText, cleanup: typingCleanup } = useTypingEffect()
  const audioManager = useAudioManager()
  const videoManager = useVideoManager()

  // Refs
  const messagesContainer = ref<HTMLElement | null>(null)
  const messageListRef = ref<InstanceType<typeof MessageList> | null>(null)
  const messages = ref<any[]>([])
  const visibleContent = ref<Record<string, { text: string; mood: number }>>({})
  const currentTypingIndex = ref(0)
  const isTypingInProgress = ref(false)
  const showLeaveConfirmModal = ref(false)
  const showAuthDrawer = ref(false)
  const currentBgImage = ref('')
  const isCharacterTopmost = ref(false)
  const currentImageIndex = ref(0)
  const previousImageIndex = ref(-1)
  const startTime = ref<number>(0)
  const totalHiddenTime = ref<number>(0)
  const lastHiddenTime = ref<number>(0)
  const chatSectionRef = ref<HTMLElement | null>(null)
  const chatInputRef = ref<HTMLElement | null>(null)
  const MIN_INPUT_HEIGHT = 200 // 预估的输入区域高度（包含选项）
  const MIN_HEIGHT = 100 // 最小高度为 100px
  const MAX_HEIGHT = window.innerHeight * 0.98 // Maximum height (98% of viewport)
  const currentHeight = ref(window.innerHeight * 0.4) // 初始高度设为视口高度的 40%
  let startY = 0
  let startHeight = 0

  // Constants
  const sayHiText = 'Say "Hi"'

  // Computed
  const isMessagePlaying = (message: any) => {
    return (
      audioManager.state.isPlaying &&
      audioManager.state.currentPlayingMessageId === message.id
    )
  }

  const hasTaskTip = computed(() => {
    return chatStore.tasks.length > 0
  })

  const filteredAnimatedImages = computed(() => {
    return chatStore.animatedImages.filter((image) => image)
  })

  // 添加计算属性来决定 ChatSection 的高度
  const chatSectionHeight = computed(() => {
    const baseHeight = currentHeight.value
    // 每个选项 44px 高度 + 12px padding + 8px gap = 64px
    const optionsHeight = chatStore.chatOptions?.length
      ? chatStore.chatOptions.length * 64 + 16
      : 0
    // 如果有选项，额外增加底部padding
    const extraPadding = chatStore.chatOptions?.length ? 16 : 0
    return Math.min(MAX_HEIGHT, baseHeight + optionsHeight + extraPadding)
  })

  // 添加计算输入区域高度的逻辑
  const calculateInputHeight = computed(() => {
    const baseInputHeight = 68 // 基础输入框高度
    const optionsHeight = chatStore.chatOptions?.length
      ? chatStore.chatOptions.length * 64 + 24
      : 0
    const actionOptionsHeight = chatStore.actionOptions?.length ? 88 : 0 // action options 的高度
    const uploadImageHeight = chatStore.isShowUploadImage ? 120 : 0 // 上传图片组件的高度

    return Math.max(
      baseInputHeight,
      optionsHeight,
      actionOptionsHeight,
      uploadImageHeight,
    )
  })

  // 监听 chatOptions 变化
  watch(
    () => chatStore.chatOptions,
    (newOptions) => {
      // 不需要手动设置样式，computed 属性会自动处理高度变化
      scrollToBottom()
    },
    { deep: true, immediate: false },
  )

  // Methods
  const scrollToBottom = () => {
    if (chatStore.isPlayingVideo) return
    setTimeout(() => {
      const messagesList = messageListRef.value?.messagesListRef
      if (messagesList) {
        messagesList.scrollTo({
          top: messagesList.scrollHeight,
          behavior: 'smooth',
        })
      }
    }, 500)
  }

  const startGame = async () => {
    if (chatStore.initialized) return

    const actorId = (route.params.actorId as string) || props.characterId
    const storyId = route.params.storyId as string
    if (actorId) {
      // 优化：并行执行初始化任务
      const initTasks = [
        chatStore.initializeChat(actorId, storyId),
        Promise.resolve(audioManager.init()),
        storyStore.getStoreDetail(storyId),
      ]

      await Promise.allSettled(initTasks)

      // 在获取故事详情后，根据 actorId 设置当前角色
      if (actorId && storyStore.currentActors.length > 0) {
        const currentActor = storyStore.currentActors.find(
          (actor) => actor.id === actorId,
        )
        if (currentActor) {
          storyStore.setCurrentActor(currentActor)
          console.log(
            '✅ Chat页面设置当前角色:',
            currentActor.name,
            currentActor.avatar_url,
          )
        } else {
          console.warn('⚠️ Chat页面未找到指定的角色:', actorId)
        }
      }

      scrollToBottom()
      startTime.value = Date.now()
    }
    videoManager.setVideoRef(videoManager.videoPlayerRef.value)
    audioManager.playBgm()
    chatStore.initialized = true
  }

  const handleBackHome = () => {
    reportEvent(ReportEvent.BackHome, {
      userId: userStore.userInfo?.uuid,
      recentMessages: chatStore.messages.slice(-3),
      actorId: props.characterId || route.params.actorId,
    })

    // 使用智能导航，在 iframe 环境中通过 postMessage 返回主应用，否则使用正常路由
    const { smartGoHome } = useSmartNavigation()
    smartGoHome()
  }

  const handleRestart = async () => {
    reportEvent(ReportEvent.GameEndClickRestart, {
      storyId: (route.params?.storyId as string) || storyStore.currentStory?.id,
      actorId: props.characterId || route.params.actorId,
    })
    chatStore.clearChat()
    const actorId = (route.params.actorId as string) || props.characterId
    const storyId = route.params.storyId as string
    if (actorId) {
      chatStore.isFirstVideo = false
      await chatStore.initializeChat(actorId, storyId)
    }
  }

  const handleOverlayButton = () => {
    if (chatStore.overlay?.button?.action === 'continue') {
      chatStore.handleOverlayButtonClick()
    }
    if (chatStore.overlay?.button?.text !== sayHiText) {
      reportEvent(ReportEvent.ClickSayHiButton, {
        storyId: storyStore.currentStory?.id,
        actorId: props.characterId || route.params.actorId,
      })
    }
  }

  const handleContainerClick = (event: MouseEvent) => {
    if (isCharacterTopmost.value) {
      isCharacterTopmost.value = false
      return
    }
    if (!chatStore.backgroundImageCouldBeFullScreen) {
      return
    }
    const backgroundLayers = document.querySelector('.background-layers')
    const characterBackground = document.querySelector('.character-background')
    const messagesWrapper = document.querySelector('.messages-wrapper')
    const target = event.target as HTMLElement

    if (target.closest('.back-button')) {
      return
    }

    if (
      characterBackground?.contains(target) ||
      messagesWrapper?.contains(target) ||
      backgroundLayers?.contains(target)
    ) {
      if (isCharacterTopmost.value) {
        isCharacterTopmost.value = false
      } else {
        const isExcludedElement =
          target.closest('.play-button') ||
          target.closest('.action-option-button') ||
          target.closest('.chat-option-button') ||
          target.closest('.input-container') ||
          target.closest('.back-button') ||
          target.closest('.message-content') ||
          target.closest('.system-message') ||
          target.closest('.task-tip')

        if (!isExcludedElement) {
          isCharacterTopmost.value = true
        }
      }
    } else {
      isCharacterTopmost.value = false
    }
  }

  const handleAnimatedImageClick = () => {
    if (currentImageIndex.value < chatStore.animatedImages.length - 1) {
      previousImageIndex.value = currentImageIndex.value
      currentImageIndex.value++
      return
    }

    const { currentActor } = storyStore
    const actorId = currentActor?.id

    if (!actorId) return

    chatStore.backgroundVideoMap[actorId] = ''
    const actorImages = chatStore.animatedImagesMap[actorId] || []
    currentBgImage.value = actorImages[actorImages.length - 1] || ''
    chatStore.backgroundImageCouldBeFullScreen = true
    chatStore.backgroundImageMap[actorId] =
      actorImages[actorImages.length - 1] || ''
    chatStore.animatedImagesMap[actorId] = []
    currentImageIndex.value = 0
    previousImageIndex.value = -1
  }

  const handleAddCredit = (actionType: 'pay_now' | 'option' | 'top') => {
    reportEvent(ReportEvent.ClickAddCreditInChat, {
      userId: userStore.userInfo?.uuid,
      actorId: props.characterId || route.params.actorId,
      storyId: storyStore.currentStory?.id,
      actionType,
    })
    if (userStore.isGuest) {
      showAuthDrawer.value = true
    } else {
      rechargeStore.toggleRechargeModal()
    }
  }

  const handleLoginSuccess = async () => {
    await userStore.getUserInfo()
    showAuthDrawer.value = false
  }

  const handleRegisterSuccess = () => {
    showAuthDrawer.value = false
  }

  // Navigation Guards
  let nextCallback: any = null

  const handleLeaveConfirm = () => {
    showLeaveConfirmModal.value = false
    if (nextCallback) {
      chatStore.clearChat()
      nextCallback()
      if (document.hidden && lastHiddenTime.value > 0) {
        totalHiddenTime.value += Date.now() - lastHiddenTime.value
      }

      const totalDuration = Date.now() - startTime.value
      const visibleDuration = Math.max(
        0,
        Math.floor((totalDuration - totalHiddenTime.value) / 1000),
      )

      reportEvent(ReportEvent.ChatDuration, {
        characterId: props.characterId,
        duration: visibleDuration,
        userId: userStore.userInfo?.uuid,
        isInChat: !props.characterId,
        actorId: props.characterId || route.params.actorId,
      })
    }
  }

  const handleLeaveCancel = () => {
    showLeaveConfirmModal.value = false
    nextCallback = null
  }

  onBeforeRouteLeave((to, from, next) => {
    // 如果是前往登录页面、发生错误或已经通过钻石用完模态框确认离开，则直接离开
    if (
      to.path === '/user/login' ||
      chatStore.isError ||
      chatEventsStore.isConfirmedLeave
    ) {
      // 如果是通过钻石用完模态框确认离开，重置标记
      if (chatEventsStore.isConfirmedLeave) {
        chatEventsStore.isConfirmedLeave = false
      }
      next()
      return
    }
    nextCallback = next
    showLeaveConfirmModal.value = true
  })

  // Typing Effect
  const startTyping = async (message: any) => {
    if (!message) return
    // if (message.msg_type === 'task_tip') {
    //   processNextMessage()
    //   return
    // }
    if (
      (message.sender_type === 'user' &&
        !messages.value.find((m) => m.uuid === message.id)) ||
      (message.sender_type !== 'user' &&
        messages.value.find((m) => m.id === message.id))
    ) {
      messages.value.push(message)
      currentTypingIndex.value++
      processNextMessage()
      return
    }

    if (message.msg_type !== 'text' || message.sender_type !== 'actor') {
      messages.value.push(message)
      currentTypingIndex.value++
      processNextMessage()
      return
    }

    if (isTypingInProgress.value) return

    const text = message.content.text
    if (!text) {
      currentTypingIndex.value++
      processNextMessage()
      return
    }

    isTypingInProgress.value = true
    messages.value.push(message)
    visibleContent.value[message.id] = {
      text: '',
      mood: undefined,
    }

    if (!chatStore.isMuted && text.trim()) {
      handlePlayTTS(message)
    }

    await typeText(text, message.content.addition?.actions, (currentText) => {
      visibleContent.value[message.id].text = currentText
    })
    chatStore.completeMessageTyping()
    const mood = message.content.addition?.mood
    if (mood) {
      await new Promise((resolve) => setTimeout(resolve, 1000))
      visibleContent.value[message.id].mood = mood
    }

    scrollToBottom()
    currentTypingIndex.value++
    isTypingInProgress.value = false
    processNextMessage()
  }

  const processNextMessage = () => {
    if (currentTypingIndex.value < chatStore.messages.length) {
      const nextMessage = chatStore.messages[currentTypingIndex.value]
      startTyping(nextMessage)
    }
  }

  // Watchers
  watch(
    () => chatStore.messages,
    (newMessages) => {
      if (!newMessages.length) return
      for (let i = currentTypingIndex.value; i < newMessages.length; i++) {
        const message = newMessages[i]
        if (!isTypingInProgress.value) {
          startTyping(message)
          break
        }
      }
    },
    { deep: true },
  )

  watch(
    () => chatStore.messages.length,
    (newLength) => {
      if (newLength === 0) {
        messages.value = []
        currentTypingIndex.value = 0
        visibleContent.value = {}
      }
    },
  )

  watch(
    () => chatStore.backgroundImage,
    async (newImage) => {
      if (newImage) {
        currentBgImage.value = newImage
      }
    },
    { immediate: true },
  )

  watch(
    () => chatStore.isChatSectionVisible,
    () => {
      if (chatStore.isChatSectionVisible) {
        scrollToBottom()
      }
    },
  )

  // Lifecycle Hooks
  onBeforeMount(async () => {
    chatStore.$onInit()
    await storyStore.getStoreDetail()
  })

  onMounted(() => {
    const { proxy } = getCurrentInstance()
    // @ts-ignore
    proxy?.$tracker?.setUserID(userStore.userInfo?.uuid)
    if (!props.characterId) {
      reportEvent(ReportEvent.StartChatFromIndexPage, {
        actorId: route.params.actorId,
      })
    }
    startGame()
    audioManager.init()
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 设置初始高度，确保输入区域完全可见
    const viewportHeight = window.innerHeight
    const initialHeight = Math.max(MIN_HEIGHT, viewportHeight * 0.4)
    currentHeight.value = initialHeight
  })

  onBeforeUnmount(() => {
    chatStore.videoElementRef = null
    chatStore.clearChat()
    chatStore.initialized = false
    typingCleanup()
    audioManager.cleanup()
    isTypingInProgress.value = false
    currentTypingIndex.value = 0
    visibleContent.value = {}
    document.removeEventListener('visibilitychange', handleVisibilityChange)
  })

  onUnmounted(() => {
    chatStore.pauseBgm()
  })

  // Visibility Change Handler
  const handleVisibilityChange = () => {
    if (document.hidden) {
      lastHiddenTime.value = Date.now()
    } else {
      if (lastHiddenTime.value > 0) {
        totalHiddenTime.value += Date.now() - lastHiddenTime.value
        lastHiddenTime.value = 0
      }
    }
  }

  const handlePlayTTS = async (message: any) => {
    if (!messageListRef.value) return
    messageListRef.value.handlePlayTTS(
      message,
      chatStore.currentVoiceId,
      chatStore.currentVoiceProvider,
    )
  }

  // 添加处理高度变化的方法
  const handleHeightChange = (height: number) => {
    currentHeight.value = height
    scrollToBottom()
  }

  const handleRatingSubmit = async (
    ratings: { story: number; character: number; image: number },
    comment: string,
  ) => {
    try {
      const formData = {
        story_id: storyStore.currentStory?.id,
        actor_id: storyStore.currentActor?.id,
        story_rating: ratings.story,
        character_rating: ratings.character,
        image_rating: ratings.image,
        comment,
      }
      const isOk = await chatStore.submitRatingForm(formData)
      if (isOk) {
        storyStore.isShowRatingModal = false
        Message.success('Thank you for your feedback!')
      } else {
        Message.error('Failed to submit feedback')
      }
    } catch (error) {
      Message.error('Failed to submit feedback')
    }
  }

  defineOptions({
    name: 'ChatView',
  })
</script>

<style lang="less" src="./styles/index.less" />
