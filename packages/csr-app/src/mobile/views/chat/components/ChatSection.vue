<template>
  <div
    class="chat-section"
    v-show="visible"
    ref="chatSectionRef"
    :class="{ 'is-dragging': isDragging }"
    :style="{
      '--sheet-height': `${currentHeight}px`,
      '--sheet-y': `${translateY}px`,
      '--input-height': `${inputSectionHeight}px`,
      height: `${currentHeight}px`
    }"
  >
    <div class="task-tip-container">
      <slot name="task-tip" />
    </div>
    <div class="sheet-container" :style="{ transform: `translateY(${translateY}px)` }">
      <div
        class="drag-area"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
        @touchcancel="handleTouchEnd"
        @click="handleDragAreaClick"
      >
        <div class="drag-indicator" />
      </div>
      <div class="sheet-content" @click.stop>
        <div class="messages-container" ref="messagesContainer">
          <div class="messages-wrapper">
            <slot name="messages" />
          </div>
        </div>
        <div class="input-section">
          <slot name="input" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'

const props = defineProps<{
  visible: boolean
  minHeight?: number
  maxHeight?: number
  height?: number
  hasTaskTip?: boolean
}>()

const emit = defineEmits<{
  (e: 'height-change', height: number): void
}>()

const chatSectionRef = ref<HTMLElement | null>(null)
const messagesContainer = ref<HTMLElement | null>(null)
const isDragging = ref(false)
const translateY = ref(0)
const currentHeight = ref(0)
const inputSectionHeight = ref(0)

// 定义消息区域的最小高度
const MIN_MESSAGES_HEIGHT = 145 // 给消息区域一个最小高度

// 更新 SNAP_POINTS 为只有最大和最小两个点
const SNAP_POINTS = computed(() => ({
  // 最小高度需要包含：输入区域高度 + 拖拽区域高度 + 消息区域最小高度
  MIN: Math.max(props.minHeight || 100, inputSectionHeight.value + MIN_MESSAGES_HEIGHT),
  MAX: Math.min(props.maxHeight || window.innerHeight * 0.85, MAX_AVAILABLE_HEIGHT.value)
}))

// 是否处于展开状态
const isExpanded = ref(false)

// 定义关键位置
const TASK_TIP_HEIGHT = 112 // TaskTip 的 top 位置
const TASK_TIP_MARGIN = 20 // 与 TaskTip 保持的最小间距

// 计算最大可用高度
const MAX_AVAILABLE_HEIGHT = computed(() => {
  const baseHeight = window.innerHeight * 0.85
  // 如果有 TaskTip，减去其高度和间距
  return props.hasTaskTip ? baseHeight - TASK_TIP_HEIGHT - TASK_TIP_MARGIN : baseHeight
})

const VELOCITY_THRESHOLD = 0.15 // 速度阈值
const SNAP_THRESHOLD = 0.5 // 吸附阈值，增大以使状态更容易确定

// 触摸相关状态
let startY = 0
let startHeight = 0
let lastY = 0
let velocity = 0
let lastTime = 0
let animationFrame: number | null = null

// 处理触摸开始
const handleTouchStart = (e: TouchEvent) => {
  isDragging.value = true
  startY = e.touches[0].clientY
  lastY = startY
  startHeight = currentHeight.value
  velocity = 0
  lastTime = Date.now()

  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
  }
}

// 处理触摸移动
const handleTouchMove = (e: TouchEvent) => {
  if (!isDragging.value) return

  const currentY = e.touches[0].clientY
  const deltaY = currentY - startY

  // 计算速度
  const now = Date.now()
  const dt = now - lastTime
  if (dt > 0) {
    velocity = (lastY - currentY) / dt
  }
  lastTime = now
  lastY = currentY

  // 简化滑动逻辑，使用线性阻尼
  const adjustedDeltaY = deltaY * 0.8

  // 计算新的高度，但限制在 MIN 和 MAX 之间
  const newHeight = Math.max(
    SNAP_POINTS.value.MIN,
    Math.min(SNAP_POINTS.value.MAX, startHeight - adjustedDeltaY)
  )

  currentHeight.value = newHeight
  // 更新 CSS 变量
  document.documentElement.style.setProperty('--sheet-height', `${newHeight}px`)
  emit('height-change', newHeight)
}

// 处理触摸结束
const handleTouchEnd = () => {
  if (!isDragging.value) return
  isDragging.value = false

  const currentPosition = currentHeight.value
  const midPoint = (SNAP_POINTS.value.MAX + SNAP_POINTS.value.MIN) / 2

  // 根据速度和位置综合判断
  if (Math.abs(velocity) > VELOCITY_THRESHOLD) {
    // 速度足够大时，根据速度方向决定
    isExpanded.value = velocity >= 0
    animateToHeight(velocity < 0 ? SNAP_POINTS.value.MIN : SNAP_POINTS.value.MAX)
  } else {
    // 速度不够大时，根据位置决定
    isExpanded.value = currentPosition >= midPoint
    animateToHeight(currentPosition < midPoint ? SNAP_POINTS.value.MIN : SNAP_POINTS.value.MAX)
  }
}

// 平滑动画到目标高度
const animateToHeight = (targetHeight: number) => {
  // 确保目标高度只能是 MIN 或 MAX
  const safeTargetHeight =
    targetHeight >= (SNAP_POINTS.value.MAX + SNAP_POINTS.value.MIN) / 2
      ? SNAP_POINTS.value.MAX
      : SNAP_POINTS.value.MIN

  const startHeight = currentHeight.value
  const startTime = performance.now()
  const duration = 300

  const animate = (currentTime: number) => {
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / duration, 1)

    // 使用 easeOutExpo 缓动函数
    const easeProgress = progress === 1 ? 1 : 1 - Math.pow(2, -10 * progress)

    // 在动画过程中也只使用 MIN 和 MAX 两个值
    const newHeight = easeProgress >= 0.5 ? safeTargetHeight : startHeight
    currentHeight.value = newHeight
    // 更新 CSS 变量
    document.documentElement.style.setProperty('--sheet-height', `${newHeight}px`)
    emit('height-change', newHeight)

    if (progress < 1) {
      animationFrame = requestAnimationFrame(animate)
    }
  }

  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
  }
  animationFrame = requestAnimationFrame(animate)
}

// 处理拖拽区域点击
const handleDragAreaClick = (event: MouseEvent) => {
  // 只有当点击的是拖拽区域或其指示器时才触发
  const target = event.target as HTMLElement
  if (!target.closest('.drag-area')) {
    return
  }

  // 强制只在 MIN 和 MAX 之间切换
  const newHeight =
    Math.abs(currentHeight.value - SNAP_POINTS.value.MIN) < 10
      ? SNAP_POINTS.value.MAX
      : SNAP_POINTS.value.MIN

  isExpanded.value = newHeight === SNAP_POINTS.value.MAX
  animateToHeight(newHeight)
}

// 监听输入区域高度变化
const observeInputSection = () => {
  if (!chatSectionRef.value) return

  const inputSection = chatSectionRef.value.querySelector('.input-section')
  if (!inputSection) return

  // 初始化输入区域高度
  inputSectionHeight.value = inputSection.getBoundingClientRect().height

  // 初始化完成后立即设置为最小高度
  currentHeight.value = SNAP_POINTS.value.MIN
  document.documentElement.style.setProperty('--sheet-height', `${SNAP_POINTS.value.MIN}px`)

  const handleHeightChange = (newHeight: number) => {
    if (newHeight === inputSectionHeight.value) return

    const oldInputHeight = inputSectionHeight.value
    inputSectionHeight.value = newHeight

    // 如果是从隐藏到显示的变化（比如ChatInput刚显示出来），强制保持在最小高度
    if (oldInputHeight === 0 || !isExpanded.value) {
      currentHeight.value = SNAP_POINTS.value.MIN
      document.documentElement.style.setProperty('--sheet-height', `${SNAP_POINTS.value.MIN}px`)
      return
    }

    // 只有在展开状态下才调整高度
    if (isExpanded.value) {
      const heightDiff = newHeight - oldInputHeight
      const adjustedHeight = Math.min(currentHeight.value + heightDiff, SNAP_POINTS.value.MAX)
      currentHeight.value = adjustedHeight
      document.documentElement.style.setProperty('--sheet-height', `${adjustedHeight}px`)
    }
  }

  // 创建 MutationObserver 监听子元素变化
  const mutationObserver = new MutationObserver(() => {
    nextTick(() => {
      const newHeight = inputSection.getBoundingClientRect().height
      handleHeightChange(newHeight)
    })
  })

  mutationObserver.observe(inputSection, {
    childList: true,
    subtree: true,
    attributes: true
  })

  // 创建 ResizeObserver 监听尺寸变化
  const resizeObserver = new ResizeObserver((entries) => {
    for (const entry of entries) {
      handleHeightChange(entry.contentRect.height)
    }
  })

  resizeObserver.observe(inputSection)
}

// 初始化
onMounted(() => {
  // 等待 DOM 和子组件完全加载后初始化
  nextTick(async () => {
    // 先初始化观察者，这会设置正确的 inputSectionHeight
    observeInputSection()
  })
})

// 监听外部高度变化
watch(
  () => props.height,
  (newHeight) => {
    // 只在必要时更新高度，不强制收缩
    if (newHeight && !isDragging.value) {
      const targetHeight = Math.min(newHeight, SNAP_POINTS.value.MAX)
      if (Math.abs(currentHeight.value - targetHeight) > 1) {
        currentHeight.value = targetHeight
      }
    }
  }
)

// 监听可见性变化
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      nextTick(() => {
        if (chatSectionRef.value) {
          const inputSection = chatSectionRef.value.querySelector('.input-section')
          if (inputSection) {
            inputSectionHeight.value = inputSection.getBoundingClientRect().height
            isExpanded.value = false
            currentHeight.value = SNAP_POINTS.value.MIN
            document.documentElement.style.setProperty(
              '--sheet-height',
              `${SNAP_POINTS.value.MIN}px`
            )
          }
        }
      })
    }
  },
  { immediate: true }
)

// 监听 hasTaskTip 变化
watch(
  () => props.hasTaskTip,
  (newHasTaskTip) => {
    // 如果当前高度超过新的最大可用高度，调整到新的最大高度
    if (currentHeight.value > SNAP_POINTS.value.MAX) {
      animateToHeight(SNAP_POINTS.value.MAX)
    }
  }
)
</script>

<style lang="less" scoped>
.chat-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  pointer-events: none;
  height: var(--sheet-height);

  .task-tip-container {
    position: absolute;
    top: -84px; // TaskTip 高度 + 间距
    left: 0;
    right: 0;
    pointer-events: auto;
  }

  // 添加全局 CSS 变量
  &:root {
    --sheet-height: 0px;
    --sheet-y: 0px;
    --input-height: 0px;
  }

  .sheet-backdrop {
    position: absolute;
    inset: 0;
    background-color: #000;
    transition: opacity 0.15s ease-out;
    pointer-events: none;
  }

  .sheet-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 20px 20px 0 0;
    max-height: var(--sheet-height);
    transform: translateY(var(--sheet-y));
    transition: transform 0.15s ease-out;
    pointer-events: auto;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 -8px 20px rgba(0, 0, 0, 0.1);
    height: 100%;
  }

  .drag-area {
    position: relative;
    height: 24px;
    padding: 10px;
    cursor: grab;
    touch-action: none;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .drag-indicator {
    width: 36px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin: 0 auto;
  }

  .sheet-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }

  .messages-container {
    flex: 1;
    overflow: hidden;
    position: relative;
  }

  .messages-wrapper {
    height: auto;
    min-height: 145px; // 与 MIN_MESSAGES_HEIGHT 保持一致
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0 16px; // 移除底部 padding

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .input-section {
    flex-shrink: 0;
    padding: 0 10px 10px 10px; // 保持原有的 padding
  }
}

// 添加拖动时的样式
.is-dragging {
  .sheet-container {
    transition: none;
  }

  .sheet-backdrop {
    transition: none;
  }
}
</style>
