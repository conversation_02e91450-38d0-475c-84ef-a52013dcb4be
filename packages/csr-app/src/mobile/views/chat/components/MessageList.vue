<template>
  <div class="messages-list" ref="messagesListRef">
    <template v-for="message in messages.filter((m) => m.sender_type !== 'tips')" :key="message.id">
      <!-- 系统消息 -->
      <SystemMessage v-if="message.sender_type === 'system'" :message="message" />

      <!-- 提示消息 -->
      <!-- <TaskTip v-else-if="message.sender_type === 'tips'" :message="message" /> -->

      <!-- 普通消息 -->
      <ChatMessage
        v-else
        :message="message"
        :visible-content="visibleContent"
        :is-message-playing="isMessagePlaying"
        :is-tts-loading="chatStore.ttsLoading"
        :current-playing-message-id="chatStore.currentPlayingMessageId"
        @play-tts="handlePlayTTS"
        @message-click="handleMessageClick"
      />
    </template>

    <!-- Loading Message -->
    <ActorMessage
      v-if="chatStore.streaming || chatStore.waitSeconds || chatStore.isActorThinking"
      :message="{
        sender: { name: storyStore.currentActor?.name },
        msg_type: 'text',
        content: { text: '' }
      }"
      :visible-content="{}"
      :is-message-playing="isMessagePlaying"
      :is-tts-loading="false"
      :current-playing-message-id="null"
      :loading="true"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue'
import { useChatStore, useStoryStore } from '@/store'
import SystemMessage from './SystemMessage.vue'
import TaskTip from './TaskTip.vue'
import ChatMessage from './ChatMessage.vue'
import ActorMessage from './ActorMessage.vue'
import { ReportEvent } from '@/interface'
import { reportEvent } from '@/utils'
import { useAudioManager } from '@/mobile/composables/useAudioManager'

const props = defineProps<{
  messages: any[]
  visibleContent: Record<string, { text: string; mood: number }>
  isMessagePlaying: (message: any) => boolean
}>()

const emit = defineEmits<{
  (e: 'scroll-to-bottom'): void
  (e: 'add-credit', type: 'pay_now' | 'option' | 'top'): void
  (e: 'update:visible-content', content: Record<string, { text: string; mood: number }>): void
}>()

const chatStore = useChatStore()
const storyStore = useStoryStore()
const audioManager = useAudioManager()
const messagesListRef = ref<HTMLElement | null>(null)

const handlePlayTTS = async (message: any, voiceId?: string, voiceProvider?: string) => {
  if (message.msg_type !== 'text') return

  // 如果当前消息正在播放，则停止
  if (props.isMessagePlaying(message)) {
    audioManager.stopTTS()
    return
  }

  reportEvent(ReportEvent.PlayTTS, {
    messageId: message.id,
    messageText: message.content.text,
    actorId: storyStore.currentActor?.id
  })

  await audioManager.playTTS(message.content.text, message.id, voiceId, voiceProvider)
}

const handleMessageClick = async (event: MouseEvent) => {
  const target = event.target as HTMLElement
  const button = target.closest('button')
  if (!button) return

  const action = button.getAttribute('data-action')
  if (!action) return

  if (action === 'pay_now') {
    emit('add-credit', 'pay_now')
  } else if (action === 'try_again') {
    const optionId = button.getAttribute('data-option-id')
    if (optionId) {
      // 找到包含这个按钮的消息
      const messageElement = button.closest('.message')
      if (messageElement) {
        const messageId = messageElement.getAttribute('data-message-id')
        if (messageId) {
          // 通过事件通知父组件更新 visibleContent
          const newVisibleContent = { ...props.visibleContent }
          delete newVisibleContent[messageId]
          emit('update:visible-content', newVisibleContent)
        }
      }
      await chatStore.sendMessage('try again', optionId)
    }
  }
}

defineExpose({
  messagesListRef,
  handlePlayTTS
})
</script>

<style lang="less" scoped>
.messages-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
  height: calc(var(--sheet-height) - 44px - var(--input-height, 58px));
  padding: 0;
  mask-image: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(0, 0, 0, 0.2) 1%,
    rgba(0, 0, 0, 0.4) 2%,
    rgba(0, 0, 0, 0.6) 3%,
    rgba(0, 0, 0, 0.8) 4%,
    rgba(0, 0, 0, 1) 5%
  );
  -webkit-mask-image: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(0, 0, 0, 0.2) 1%,
    rgba(0, 0, 0, 0.4) 2%,
    rgba(0, 0, 0, 0.6) 3%,
    rgba(0, 0, 0, 0.8) 4%,
    rgba(0, 0, 0, 1) 5%
  );

  scroll-behavior: smooth;

  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
