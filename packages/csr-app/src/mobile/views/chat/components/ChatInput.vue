<template>
  <div
    class="input-section"
    v-show="
      !chatStore.overlay && !chatStore.loading && canShowChatInput && !chatStore.isActorThinking
    "
  >
    <!-- Action Options -->
    <div v-if="chatStore.actionOptions?.length" class="action-options">
      <button
        v-for="option in reversedActionOptions"
        :key="option.option_id"
        class="action-option-button"
        :class="{ 'paid-required': option.paid_required }"
        @click="handleOptionSelect(option)"
      >
        <img
          src="https://cdn.magiclight.ai/assets/playshot/pay-now-4x.png"
          alt="pay now"
          v-if="option.paid_required"
        />
        <img
          v-else
          src="https://cdn.magiclight.ai/assets/playshot/try-again-4x.png"
          alt="try again"
        />
      </button>
    </div>

    <!-- 普通输入框，当有 actionOptions 时隐藏 -->
    <div v-else-if="!chatStore.isEnding && !uploadImageUrl" class="input-container">
      <!-- <button class="history-button" @click="toggleHistory">
        <icon-history />
      </button> -->
      <div
        class="input-wrapper"
        :class="{ disabled: chatStore.disableInput }"
        v-if="!chatStore.hideInput"
      >
        <input
          v-model="inputMessage"
          :placeholder="placeholder"
          :disabled="isInputDisabled"
          @keyup.enter="sendMessage"
        />
        <button
          class="send-button"
          :disabled="isInputDisabled || !inputMessage.trim()"
          @click="sendMessage"
        >
          <icon-loading v-if="chatStore.streaming" />
          <icon-send v-else></icon-send>
        </button>
      </div>
    </div>

    <!-- Chat Options -->
    <div v-if="chatStore.chatOptions?.length" class="chat-options">
      <button
        v-for="option in chatStore.chatOptions"
        :key="option.option_id"
        class="chat-option-button"
        :class="{ 'paid-required': option.paid_required }"
        @click="handleOptionSelect(option)"
      >
        <span v-html="option.text"></span>
        <div class="coins-container" v-if="option.paid_required && !option.is_purchased">
          <img
            src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
            alt="coins"
            v-if="option.paid_required && !option.is_purchased"
          />
          <span class="coins-text">{{ option?.coins || 0 }}</span>
        </div>
        <div class="unlock-container" v-if="option.paid_required && option.is_purchased">
          <icon-unlock />
        </div>
      </button>
    </div>

    <div v-if="chatStore.isShowUploadImage" class="upload-image-container">
      <UploadImage
        :visible="chatStore.isShowUploadImage"
        :face-detect="chatStore.onFaceDetect"
        @upload-success="handleUploadSuccess"
        @upload-error="handleUploadError"
        @update:clear="handleClearUploadImage"
        @update:send-image="handleSendImage(uploadImageUrl)"
        @update:skip-upload="handleSendImage('__CANCEL__')"
        :button-text="!uploadImageUrl ? 'Upload Image' : 'Send Image'"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onBeforeUnmount } from 'vue'
import { useChatStore } from '@/store/chat'
import { useStoryStore } from '@/store/story'
import { useUserStore } from '@/store/user'
import { ChatOptions } from '@/types/chat'
import { ReportEvent } from '@/interface'
import { reportEvent } from '@/utils'
import IconUnlock from '@/assets/icon/unlock.svg'
// import IconLoading from '@/assets/icon/loading.svg'
import IconSend from '@/assets/icon/send-message.svg'
import IconHistory from '@/assets/icon/history.svg'
import UploadImage from '@/mobile/components/UploadImage/index.vue'

const emit = defineEmits<{
  (e: 'add-credit', type: 'pay_now' | 'option' | 'top'): void
  (e: 'scroll-to-bottom'): void
  (e: 'toggle-history'): void
}>()

const props = defineProps<{
  collapseTaskTip?: () => void
}>()

const chatStore = useChatStore()
const storyStore = useStoryStore()
const userStore = useUserStore()

const inputMessage = ref('')
const uploadImageUrl = ref('')
const remainingTime = ref(0)

// 添加倒计时逻辑
let countdownInterval: ReturnType<typeof setInterval> | null = null

const startCountdown = (initialTime: number) => {
  clearCountdown()
  remainingTime.value = initialTime
  countdownInterval = setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value -= 1000
    } else {
      clearCountdown()
    }
  }, 1000)
}

const clearCountdown = () => {
  if (countdownInterval) {
    clearInterval(countdownInterval)
    countdownInterval = null
  }
}

// 监听 messageDelayTime 的变化
watch(
  () => chatStore.messageDelayTime,
  (newDelay) => {
    if (
      chatStore.messageTypingPromise !== null &&
      (chatStore.hasNextVideo || chatStore.hasNextMessage)
    ) {
      startCountdown(newDelay)
    }
  }
)

// 监听 messageTypingPromise 的变化
watch(
  () => chatStore.messageTypingPromise,
  (newPromise) => {
    if (!newPromise) {
      clearCountdown()
    } else if (chatStore.hasNextVideo || chatStore.hasNextMessage) {
      startCountdown(chatStore.messageDelayTime)
    }
  }
)

// 组件卸载时清理
onBeforeUnmount(() => {
  clearCountdown()
})

const reversedActionOptions = computed(() => {
  return chatStore.actionOptions ? [...chatStore.actionOptions].reverse() : []
})

const placeholder = computed(() => {
  if (chatStore.loading || chatStore.isActorThinking) return 'Please wait...'
  if (chatStore.disableInput && (chatStore.actionOptions?.length || chatStore.chatOptions?.length))
    return 'Please select an option'
  if (chatStore.messageTypingPromise !== null) {
    if (chatStore.hasNextVideo) {
      return `Video will auto-play in ${Math.ceil(remainingTime.value / 1000)}s...`
    }
    if (chatStore.hasNextMessage) {
      return `Next message in ${Math.ceil(remainingTime.value / 1000)}s...`
    }
    return 'Please wait...'
  }
  return 'Type your message...'
})

const isInputDisabled = computed(() => {
  return (
    chatStore.disableInput ||
    chatStore.isActorThinking ||
    chatStore.messageTypingPromise !== null ||
    chatStore.hasNextVideo ||
    chatStore.hasNextMessage
  )
})

const canShowChatInput = ref(false)
watch(
  () => chatStore.messageTypingPromise,
  async (typingPromise) => {
    // 如果没有正在进行的打字效果，直接显示
    if (!typingPromise) {
      canShowChatInput.value = true
      return
    }
    // 如果有打字效果，等待打字完成
    try {
      canShowChatInput.value = false
      await typingPromise
      canShowChatInput.value = true
    } catch {
      // 如果打字效果出错，也允许显示
      canShowChatInput.value = true
    }
  },
  { immediate: true }
)

const handleOptionSelect = async (option: ChatOptions) => {
  reportEvent(ReportEvent.ClickChatOption, {
    optionId: option.option_id,
    optionText: option.text,
    storyId: storyStore.currentStory?.id,
    actorId: storyStore.currentActor?.id,
    isPaidRequired: option.paid_required,
    isPurchased: option.is_purchased
  })
  const unlockedOption = option.paid_required && !option.is_purchased
  const notEnoughCoins = userStore.userInfo?.coins < option.coins
  if (unlockedOption && notEnoughCoins) {
    emit('add-credit', 'option')
    return
  }

  chatStore.disableInput = false
  await chatStore.sendMessage(option.text, option.option_id)
  props.collapseTaskTip?.()
}

const sendMessage = async () => {
  // 如果用户不上传图片，输入文字消息点击发送也要关闭上传组件
  if (chatStore.isShowUploadImage) {
    chatStore.isShowUploadImage = false
  }

  if (!inputMessage.value.trim() || chatStore.streaming) return

  const message = inputMessage.value
  inputMessage.value = ''
  await chatStore.sendMessage(message)
  emit('scroll-to-bottom')
  props.collapseTaskTip?.()
}

const handleUploadSuccess = (download_url: string) => {
  uploadImageUrl.value = download_url
}

const handleUploadError = () => {
  console.log('Upload failed')
}

const handleClearUploadImage = () => {
  uploadImageUrl.value = ''
}

const handleSendImage = async (imageUrl: string) => {
  if (!imageUrl) return
  await chatStore.sendMessage(imageUrl, null, 'image')
  uploadImageUrl.value = ''
  chatStore.isShowUploadImage = false
  chatStore.onFaceDetect = false
}
const toggleHistory = () => {
  reportEvent(ReportEvent.ClickChatHistoryButton)
  emit('toggle-history')
}
</script>

<style lang="less" scoped>
.input-section {
  background: rgba(0, 0, 0, 0.7);
  padding: 5px 15px 25px 15px;
}

.input-container {
  display: flex;
  align-items: center;
  &.disabled {
    opacity: 0.8;
    pointer-events: none;
  }
  .input-wrapper {
    display: flex;
    gap: 8px;
    flex: 1;
    align-items: center;
    background: #4c3c59;
    border-radius: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    height: 36px;
    input {
      background: #4c3c59;
      color: #fff;
      font-size: 14px;
      font-weight: 400;
      height: 36px;
      padding: 0;
      width: 100%;
      height: 100%;
      border: none;
      outline: none;
      padding: 0 10px;
      border-radius: 30px;
      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:hover,
      &:focus {
        background-color: transparent;
        border: none;
        box-shadow: none;
      }
    }
  }
  .history-button {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: #ca93f2;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: rgba(255, 255, 255, 0.6);
    flex-shrink: 0;
    margin-right: 15px;
    &:hover {
      color: rgba(255, 255, 255, 0.9);
    }

    :deep(svg) {
      flex-shrink: 0;
      width: 16px;
      height: 16px;
    }
  }
  .send-button {
    min-width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #ca93f2;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    padding: 0;
    transition: all 0.2s ease;
    margin-right: 2px;
    &:hover:not(:disabled) {
      background: #b48ded;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    :deep(svg) {
      width: 30px;
      height: 30px;
    }

    .icon-send {
      font-size: 20px;
      transform: rotate(45deg);
    }
  }
}

.chat-options,
.action-options {
  margin-top: 8px;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.chat-option-button {
  padding: 10px 20px;
  border-radius: 8px;
  background: #3d2f54;
  border: 1px solid transparent;
  color: white;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  // backdrop-filter: blur(10px);
  transition: all 0.2s ease;
  position: relative;

  span {
    flex: 1;
    text-align: center;
  }

  .coins-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    position: absolute;
    right: -6px;
    top: -6px;
    border-radius: 34px;
    border: 1px solid #daff96;
    padding: 2px 10px 2px 7px;
    background: #1f0038;
    color: #daff96;
    font-size: 11px;
    font-weight: 600;
    height: 20px;
    img {
      width: 13px;
      height: 13px;
    }
  }
  .unlock-container {
    position: absolute;
    right: 0px;
    top: -6px;
  }
}

.action-options {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 12px 16px;
  margin: 0 -16px;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.action-option-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: fit-content;
  background: transparent;
  border: transparent;
  position: relative;
  transition: all 0.15s ease-in-out;
  transform: translateY(0);
  cursor: pointer;
  user-select: none;
  padding: 0;
  margin: 0;

  img {
    height: 64px;
    width: auto;
    max-width: none;
    filter: drop-shadow(0 4px 4px rgba(0, 0, 0, 0.2));
    transition: all 0.15s ease-in-out;
    user-select: none;
    pointer-events: none;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    transform: translateZ(0);
    will-change: transform;
  }

  &:hover {
    transform: translateY(-1px);
    img {
      filter: drop-shadow(0 6px 6px rgba(0, 0, 0, 0.25));
    }
  }

  &:active {
    transform: translateY(2px);
    img {
      filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.15));
    }
  }
}

@media screen and (max-width: 360px) {
  .action-option-button {
    min-width: 140px;
    img {
      height: 52px;
    }
  }
}
</style>
