import { ref } from 'vue'

interface ApiResponse<T> {
  isOk: boolean
  message?: string
  data: T
}

export function useApi() {
  const loading = ref(false)
  const error = ref<string | null>(null)

  const handleApiResponse = <T>(response: ApiResponse<T>) => {
    if (response.isOk) {
      return response.data
    }
    throw new Error(response.message || 'API request failed')
  }

  const withLoading = async <T>(operation: () => Promise<T>) => {
    loading.value = true
    error.value = null
    try {
      return await operation()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'An unexpected error occurred'
      console.log('withLoading', error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    error,
    handleApiResponse,
    withLoading
  }
}
