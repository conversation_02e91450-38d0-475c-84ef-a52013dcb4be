import { ref, onMounted, onUnmounted } from 'vue'
import <PERSON> from 'shepherd.js'
import 'shepherd.js/dist/css/shepherd.css'
import { animate } from 'motion'

// 扩展Shepherd类型定义
declare module 'shepherd.js' {
  interface Step {
    next(): void
    complete(): void
    hide(): void
  }

  interface Tour {
    next(): void
    complete(): void
    destroy?(): void
    start(): void
    getCurrentStep(): Step | null
  }
}

// 导入轮播相关的类型
import type { PropType } from 'vue'

// Add proper type definition for Shepherd
type ShepherdTour = InstanceType<typeof Shepherd.Tour>

const PointerIconUrl = 'https://static.playshot.ai/static/guide/pointer.png'

interface TalentGuideOptions {
  onComplete?: () => void
  onSkip?: () => void
  isFirstTime?: boolean
}

export const useTalentGuide = (options: TalentGuideOptions = {}) => {
  const tour = ref<ShepherdTour | null>(null)
  const hasSeenGuide = ref(false)
  const isGuideActive = ref(false)

  // 清理tour相关资源的函数
  const cleanupTourResources = () => {
    if (tour.value && (tour.value as any)._overlay) {
      // 已经由 Shepherd 本身管理，不需要我们手动清理
    }

    // 移除事件监听器，避免重复添加
    window.removeEventListener('scroll', updateHighlightMaskPosition)
    window.removeEventListener('resize', updateHighlightMaskPosition)

    // 移除所有临时元素
    document
      .querySelectorAll('.guide-hand-pointer, .swipe-trail, .card-highlight-mask')
      .forEach((el) => el.remove())

    // 移除所有高亮样式
    document
      .querySelectorAll(
        '.guide-highlight, .shepherd-highlighted, .highlighted, .guide-card-highlight'
      )
      .forEach((el) => {
        el.classList.remove(
          'guide-highlight',
          'shepherd-highlighted',
          'highlighted',
          'guide-card-highlight'
        )
        if (el instanceof HTMLElement) {
          // 清除可能添加的内联样式
          el.style.removeProperty('z-index')
          el.style.removeProperty('position')
          el.style.removeProperty('box-shadow')
          el.style.removeProperty('pointer-events')
          el.style.removeProperty('clip-path')
          el.style.removeProperty('-webkit-clip-path')
          el.style.removeProperty('isolation')
          // 移除可能影响布局的属性
          el.style.removeProperty('transform')
          el.style.removeProperty('translate')
          el.style.removeProperty('animation')
        }
      })

    // 确保轮播卡片位置不受影响
    document.querySelectorAll('.carousel .skill-card').forEach((card) => {
      if (card instanceof HTMLElement) {
        // 不修改可能影响位置的属性
        card.style.removeProperty('z-index')
        card.style.removeProperty('visibility')
        card.style.removeProperty('opacity')
        card.style.removeProperty('transform')
        card.style.removeProperty('translate')
        card.style.removeProperty('animation')
        card.style.removeProperty('position')
      }
    })

    // 修复导航按钮
    document.querySelectorAll('.touch-nav, .carousel-navigation').forEach((el) => {
      if (el instanceof HTMLElement) {
        el.style.removeProperty('z-index')
      }
    })

    // 移除可能添加的内联样式
    const styleElements = document.querySelectorAll('style[id^="hand-pointer-"]')
    styleElements.forEach((style) => {
      if (style.parentNode) {
        style.parentNode.removeChild(style)
      }
    })
  }

  // 创建下一步按钮
  const createNextButton = (text: string) => {
    const buttons = document.querySelectorAll('.shepherd-button')
    if (buttons && buttons.length > 0) {
      const nextButton = buttons[0] as HTMLElement
      if (nextButton) {
        nextButton.textContent = text
      }
    }
  }

  // Add a function to create and animate hand pointer
  const createHandPointer = (animationType: string, targetSelector: string | HTMLElement) => {
    // 先清除可能存在的其他手指元素
    document.querySelectorAll('.guide-hand-pointer').forEach((el) => el.remove())

    // 获取目标元素
    let targetElement: HTMLElement | null = null

    if (typeof targetSelector === 'string') {
      targetElement = document.querySelector(targetSelector) as HTMLElement
    } else {
      targetElement = targetSelector
    }

    if (!targetElement) {
      console.error(`找不到目标元素: ${targetSelector}`)
      return
    }

    // 创建指针元素
    const pointer = document.createElement('div')
    pointer.className = 'guide-hand-pointer'

    // 添加内联样式确保可见性，但不干扰蒙层
    pointer.style.cssText = `
      position: fixed !important;
      width: 40px;
      height: 40px;
      z-index: 10001 !important;
      pointer-events: none;
      visibility: visible !important;
      display: flex !important;
      align-items: center;
      justify-content: center;
      opacity: 1 !important;
      transition: top 0.2s ease-out, left 0.2s ease-out;
    `

    // 创建图像元素，使用导入的SVG
    const img = document.createElement('img')
    img.src = PointerIconUrl
    img.alt = 'Hand pointer'
    img.style.cssText = `
      width: 100%;
      height: 100%;
      object-fit: contain;
      filter: drop-shadow(0 0 8px rgba(202, 147, 242, 0.8));
    `
    pointer.appendChild(img)

    // 确保添加到body最后，使其在最上层
    document.body.appendChild(pointer)

    console.log('创建的手指指针元素:', pointer)
    switch (animationType) {
      case 'tap':
      case 'click': {
        console.log('执行点击动画')

        // 获取目标元素的位置
        const targetRect = targetElement.getBoundingClientRect()
        console.log('目标元素位置:', targetRect)

        // 应用高亮效果
        targetElement.classList.add('guide-highlight')
        console.log('已添加高亮样式到点击目标')

        // 放置指针在按钮上方居中，但更靠近按钮
        const centerX = targetRect.left + targetRect.width / 2
        const centerY = targetRect.top + targetRect.height / 2

        // 调整位置，让指针更靠近按钮
        pointer.style.left = `${centerX - 20}px`
        pointer.style.top = `${centerY}px` // 从之前的60px降低到35px，使指针更靠近按钮

        // 提高指针可见性
        const pointerImg = pointer.querySelector('img')
        if (pointerImg) {
          const pointerImgElement = pointerImg as HTMLElement
          pointerImgElement.style.filter =
            'drop-shadow(0 0 10px rgba(255, 255, 255, 0.8)) drop-shadow(0 0 15px rgba(202, 147, 242, 0.9))'
        }

        // 判断是否是确认按钮
        const isConfirmButton = targetElement.classList.contains('confirm-btn')

        // 创建点击动画 - 对确认按钮使用更明显的动画
        const keyframes = `
          @keyframes tap-animation {
            0%, 100% { 
              transform: translateY(0) scale(1); 
              filter: drop-shadow(0 0 8px rgba(202, 147, 242, 0.8));
            }
            50% { 
              transform: translateY(${isConfirmButton ? '8px' : '10px'}) scale(0.9); 
              filter: drop-shadow(0 0 15px rgba(202, 147, 242, 1.0));
            }
          }
        `
        const styleElement = document.createElement('style')
        styleElement.textContent = keyframes
        document.head.appendChild(styleElement)

        // 应用动画
        pointer.style.animation = 'tap-animation 1.2s infinite'

        // 延迟一段时间后移除动画和元素，但保留高亮效果
        setTimeout(() => {
          pointer.remove()
          styleElement.remove()
        }, 7000)

        break
      }

      case 'swipe': {
        console.log('执行滑动动画')

        // 使用正确的选择器匹配 TalentSelect.vue 中的轮播结构
        const carousel = document.querySelector('.carousel')
        if (!carousel) {
          console.error('找不到轮播容器')
          return
        }

        // 找到活动卡片 - 直接使用.active类，不要使用备选
        const activeCard = document.querySelector('.skill-card.active')

        if (!activeCard) {
          console.error('找不到活动卡片，无法创建滑动动画')
          return
        }

        console.log('找到活动卡片，创建滑动动画')
        const cardRect = activeCard.getBoundingClientRect()
        console.log('卡片位置信息:', cardRect)

        // 确保没有其他卡片有高亮效果
        document.querySelectorAll('.skill-card').forEach((card) => {
          card.classList.remove('guide-card-highlight', 'highlighted')
          // 确保不会添加任何会影响位置的内联样式
          if (card instanceof HTMLElement) {
            card.style.removeProperty('transform')
            card.style.removeProperty('translate')
            card.style.removeProperty('animation')
          }
        })

        // 计算起始和结束位置 - 从左向右滑动
        const startPos = {
          // 起始位置在左侧，靠近卡片左边缘
          left: cardRect.left + cardRect.width * 0.25,
          // 垂直位置在卡片中部偏下
          top: cardRect.top + cardRect.height * 0.6
        }

        const endPos = {
          // 结束位置在右侧，靠近卡片右边缘
          left: cardRect.right - cardRect.width * 0.25,
          // 保持相同的垂直位置
          top: startPos.top
        }

        // 调整以确保在视窗内
        if (startPos.left < 10) startPos.left = 10
        if (startPos.top < 10) startPos.top = 10
        if (endPos.left > window.innerWidth - 10) endPos.left = window.innerWidth - 40

        console.log('手指动画位置:', startPos, endPos)

        // 创建一个新的轨迹元素来显示滑动路径
        const trail = document.createElement('div')
        trail.className = 'swipe-trail'
        trail.style.cssText = `
          position: fixed;
          left: ${startPos.left}px;
          top: ${startPos.top + 20}px;
          width: ${endPos.left - startPos.left}px;
          height: 4px;
          background: linear-gradient(90deg, 
            rgba(202, 147, 242, 0.2), 
            rgba(202, 147, 242, 0.8), 
            rgba(202, 147, 242, 0.2)
          );
          border-radius: 2px;
          opacity: 0;
          z-index: 10000;
          pointer-events: none;
          box-shadow: 0 0 8px rgba(202, 147, 242, 0.6);
        `
        document.body.appendChild(trail)

        // 使用轻量级高亮方法，创建一个围绕卡片的高亮遮罩，而不是直接修改卡片
        const highlightMask = document.createElement('div')
        highlightMask.className = 'card-highlight-mask'
        highlightMask.style.cssText = `
          position: fixed;
          left: ${cardRect.left - 5}px;
          top: ${cardRect.top - 5}px;
          width: ${cardRect.width + 10}px;
          height: ${cardRect.height + 10}px;
          border-radius: 16px;
          box-shadow: 0 0 0 2px rgba(218, 255, 150, 0.7),
                      0 0 20px 5px rgba(218, 255, 150, 0.5);
          z-index: 9997;
          pointer-events: none;
          animation: highlight-outer-glow 2s infinite;
        `
        document.body.appendChild(highlightMask)

        // 确保轮播导航也是可见的
        const touchNavs = document.querySelectorAll('.touch-nav')
        touchNavs.forEach((nav) => {
          if (nav instanceof HTMLElement) {
            nav.style.zIndex = '9998'
          }
        })

        // 设置初始位置
        pointer.style.left = `${startPos.left}px`
        pointer.style.top = `${startPos.top}px`

        // 提高手指指针的可见性
        const pointerImg = pointer.querySelector('img') as HTMLElement
        if (pointerImg) {
          pointerImg.style.filter =
            'drop-shadow(0 0 10px rgba(255, 255, 255, 1)) drop-shadow(0 0 15px rgba(202, 147, 242, 1))'
          pointerImg.style.transform = 'scale(1.2)'
        }

        // 确保所有元素准备好后再开始动画
        setTimeout(() => {
          // 先显示轨迹
          trail.style.transition = 'opacity 0.5s ease-in-out'
          trail.style.opacity = '0.9'

          // 等轨迹显示后再开始手指动画
          setTimeout(() => {
            // 添加手指滑动动画
            pointer.style.transition = 'left 1.2s cubic-bezier(0.23, 1, 0.32, 1)'

            // 定义重复动画函数
            const runAnimation = () => {
              // 设置起始位置
              pointer.style.left = `${startPos.left}px`

              // 稍微延迟确保起始位置已生效
              setTimeout(() => {
                // 开始滑动动画
                pointer.style.left = `${endPos.left}px`

                // 动画结束后重置
                setTimeout(() => {
                  // 重置手指位置，无过渡效果
                  pointer.style.transition = 'none'
                  pointer.style.left = `${startPos.left}px`

                  // 短暂延迟后再次开始动画
                  setTimeout(() => {
                    pointer.style.transition = 'left 1.2s cubic-bezier(0.23, 1, 0.32, 1)'
                    // 递归调用自身以无限循环
                    runAnimation()
                  }, 100)
                }, 1300)
              }, 100)
            }

            // 开始执行动画
            runAnimation()
          }, 500)

          // 一段时间后停止动画并移除元素
          setTimeout(() => {
            trail.remove()
            pointer.remove()
            highlightMask.remove() // 移除高亮遮罩
            // 重置导航z-index
            touchNavs.forEach((nav) => {
              if (nav instanceof HTMLElement) {
                nav.style.removeProperty('z-index')
              }
            })
          }, 10000) // 动画总时长
        }, 300) // 初始延迟

        break
      }

      case 'allocate': {
        console.log('执行属性分配动画')

        // 查找属性卡片中的加号按钮
        // 获取可用的加号按钮 - 提供多种选择器以提高成功率
        let plusButtons = targetElement.querySelectorAll('.control-btn.plus:not(.disabled)')

        // 如果没找到，可能是因为targetElement不是属性卡片本身，尝试查找其他选择器
        if (!plusButtons || plusButtons.length === 0) {
          console.log('在目标元素中未找到加号按钮，尝试在整个文档中搜索')
          plusButtons = document.querySelectorAll('.control-btn.plus:not(.disabled)')
        }

        // 如果还没找到，尝试不限制disabled的状态
        if (!plusButtons || plusButtons.length === 0) {
          console.log('尝试查找任意加号按钮，不考虑禁用状态')
          plusButtons =
            document.querySelectorAll('.control-btn.plus') ||
            document.querySelectorAll('.control-btn')
        }

        if (!plusButtons || plusButtons.length === 0) {
          console.error('找不到可用的加号按钮，无法创建操作动画')
          return
        }

        console.log(`找到 ${plusButtons.length} 个加号按钮`)

        // 选择第一个可用的加号按钮
        const plusBtn = plusButtons[0] as HTMLElement

        // 应用高亮效果
        plusBtn.classList.add('guide-highlight')
        console.log('已添加高亮样式到加点按钮')

        const btnRect = plusBtn.getBoundingClientRect()
        console.log('加号按钮位置:', btnRect)

        // 调整指针位置，使其位于按钮上方，且更靠近按钮
        pointer.style.left = `${btnRect.left + btnRect.width / 2 - 40}px`
        pointer.style.top = `${btnRect.top + 5}px` // 将距离从40px减少到20px，让指针更靠近按钮

        // 旋转手指指针，使其指向下方
        const pointerImg = pointer.querySelector('img')
        if (pointerImg) {
          // 应用旋转和指向下方的样式
          const pointerImgElement = pointerImg as HTMLElement
          pointerImgElement.style.transform = 'rotate(90deg) scale(1.1)' // 旋转90度并稍微放大
          pointerImgElement.style.filter =
            'drop-shadow(0 0 8px rgba(255, 255, 255, 0.8)) drop-shadow(0 0 12px rgba(202, 147, 242, 0.9))'
          pointerImgElement.style.transformOrigin = 'center center' // 确保从中心旋转
        }

        // 创建上下移动的动画（指向下方移动）
        const keyframes = `
          @keyframes pointer-tap-down {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(6px); } // 减少移动距离，从10px减少到6px
          }
        `
        const styleElement = document.createElement('style')
        styleElement.textContent = keyframes
        document.head.appendChild(styleElement)

        // 保持指针元素本身不动，只让里面的图像进行动画
        pointer.style.animation = 'none' // 清除之前可能的动画

        // 应用新的动画到图像元素
        if (pointerImg) {
          // 使用 CSS 自定义属性简化复杂的 transform 动画处理
          const pointerImgElement = pointerImg as HTMLElement
          pointerImgElement.style.setProperty('--rotate-scale', 'rotate(90deg) scale(1.1)')
          pointerImgElement.style.transform = 'var(--rotate-scale)'

          // 移除之前的动画样式，重新添加
          if (styleElement.parentNode) {
            styleElement.parentNode.removeChild(styleElement)
          }

          const newKeyframes = `
            @keyframes pointer-tap-down {
              0%, 100% { transform: var(--rotate-scale) translateY(0); }
              50% { transform: var(--rotate-scale) translateY(6px); }
            }
          `
          styleElement.textContent = newKeyframes
          document.head.appendChild(styleElement)

          // 应用动画
          if (pointerImg) {
            const pointerImgElement = pointerImg as HTMLElement
            pointerImgElement.style.animation = 'pointer-tap-down 1s infinite'
          }
        }

        // 一段时间后移除动画和元素，但保留高亮效果
        setTimeout(() => {
          pointer.remove()
          styleElement.remove()
        }, 7000)

        break
      }
    }

    // 移除之前添加的CSS样式（如果存在）
    const existingStyle = document.getElementById('hand-pointer-style')
    if (existingStyle) {
      existingStyle.remove()
    }

    // 添加简化的CSS
    const style = document.createElement('style')
    style.id = 'hand-pointer-style'
    style.textContent = `
      .guide-hand-pointer {
        position: fixed;
        width: 40px;
        height: 40px;
        pointer-events: none;
        z-index: 10001; /* 在蒙层和提示上方 */
        transform-origin: center center;
      }
      .swipe-trail {
        position: fixed;
        z-index: 10000; /* 在蒙层上方但在手指下方 */
        pointer-events: none;
      }
    `
    document.head.appendChild(style)

    return pointer
  }

  const initTour = () => {
    if (tour.value) {
      // 需要销毁并重新初始化
      cleanupTourResources()

      try {
        // 安全检查：确保destroy方法存在且是函数才调用它
        if (tour.value.destroy && typeof tour.value.destroy === 'function') {
          tour.value.destroy()
        } else {
          console.log('Tour实例存在但没有destroy方法，使用备用清理方式')
          // 如果没有destroy方法，尝试使用其他方法终止tour
          if (tour.value.complete && typeof tour.value.complete === 'function') {
            tour.value.complete()
          }

          // 移除可能存在的tour元素
          document
            .querySelectorAll(
              '.shepherd-element, .shepherd-content, .shepherd-text, .shepherd-header, .shepherd-footer, .shepherd-cancel-icon'
            )
            .forEach((el) => {
              el.remove()
            })

          // 移除蒙层
          document.querySelectorAll('.shepherd-modal-overlay-container').forEach((el) => {
            el.remove()
          })
        }
      } catch (e) {
        console.warn('清理旧tour实例时发生错误:', e)
        // 继续创建新tour
      }

      // 无论如何都重置tour引用
      tour.value = null
    }

    const localTour = new Shepherd.Tour({
      useModalOverlay: true, // 恢复使用内置蒙层
      defaultStepOptions: {
        cancelIcon: {
          enabled: false
        },
        classes: 'talent-guide-step',
        // 我们不使用Shepherd的自动滚动，而是实现自己的
        scrollTo: false,
        canClickTarget: true, // 确保可以点击目标元素
        modalOverlayOpeningPadding: 10, // 增加高亮区域的内边距
        modalOverlayOpeningRadius: 4, // 高亮区域的圆角
        buttons: [
          {
            text: 'Next',
            action: function () {
              // 在进入下一步之前，清除所有高亮效果和动画元素
              document.querySelectorAll('.card-highlight-mask').forEach((mask) => mask.remove())
              document
                .querySelectorAll('.guide-hand-pointer, .swipe-trail')
                .forEach((el) => el.remove())

              return this.next()
            },
            classes: 'talent-guide-button talent-guide-next'
          }
        ]
      },
      exitOnEsc: false,
      keyboardNavigation: false
    })

    // 设置 tour.value 为 localTour
    tour.value = localTour

    // 实现自定义的滚动功能
    const scrollToTargetElement = (stepId: string, elementSelector: string | HTMLElement) => {
      try {
        let targetElement: HTMLElement | null = null

        if (typeof elementSelector === 'string') {
          targetElement = document.querySelector(elementSelector) as HTMLElement
        } else {
          targetElement = elementSelector
        }

        if (!targetElement) {
          console.warn(`找不到目标元素进行滚动: ${elementSelector}`)
          return
        }

        // 获取元素位置
        const rect = targetElement.getBoundingClientRect()
        const isInViewport =
          rect.top >= 0 &&
          rect.left >= 0 &&
          rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
          rect.right <= (window.innerWidth || document.documentElement.clientWidth)

        // 只有当元素不在视口内时才滚动
        if (!isInViewport) {
          console.log(`滚动到步骤 ${stepId} 的目标元素`)

          // 为不同的步骤计算最佳滚动位置
          const scrollOptions: ScrollIntoViewOptions = { behavior: 'smooth' }

          if (stepId === 'welcome' || stepId === 'drag-explain') {
            // 技能卡片应该在视图中居中
            scrollOptions.block = 'center'
          } else if (stepId === 'allocate-attributes') {
            // 属性分配应该在视图顶部
            scrollOptions.block = 'start'
            // 额外偏移，避免被UI元素遮挡
            const topOffset = 50
            const currentScroll = window.pageYOffset || document.documentElement.scrollTop
            window.scrollTo({
              top: rect.top + currentScroll - topOffset,
              behavior: 'smooth'
            })
            return // 使用自定义滚动，不再执行下面的scrollIntoView
          } else if (stepId === 'confirm-selection') {
            // 确认按钮应该在视图底部
            scrollOptions.block = 'end'
          }

          targetElement.scrollIntoView(scrollOptions)
        }
      } catch (e) {
        console.error('滚动到目标元素时出错:', e)
      }
    }

    // 每一步变化时，简化高亮处理
    localTour.on('show', (evt) => {
      // 短暂延迟，确保DOM已更新
      setTimeout(() => {
        try {
          // 获取当前步骤和ID
          const step = evt.step
          const currentStepId = step.id

          // 确保清理不再需要的高亮效果
          if (currentStepId !== 'welcome' && currentStepId !== 'drag-explain') {
            // 清理所有卡片相关的高亮效果
            document.querySelectorAll('.card-highlight-mask').forEach((mask) => mask.remove())
            document.querySelectorAll('.skill-card').forEach((card) => {
              card.classList.remove('guide-card-highlight', 'highlighted')
              if (card instanceof HTMLElement) {
                card.style.removeProperty('transform')
                card.style.removeProperty('translate')
                card.style.removeProperty('animation')
              }
            })
          }

          // 获取当前步骤的attachTo元素并滚动到该位置
          if (step.options && step.options.attachTo && step.options.attachTo.element) {
            const selector = step.options.attachTo.element
            const targetElement =
              typeof selector === 'string' ? document.querySelector(selector) : selector

            if (targetElement) {
              // 添加高亮类，但不要是skill-card类型的元素，那些有特殊处理
              if (selector !== '.carousel' && !targetElement.classList.contains('skill-card')) {
                targetElement.classList.add('guide-highlight')
                console.log(`已高亮步骤目标元素: ${selector}`)
              }

              // 滚动到当前步骤的目标元素
              scrollToTargetElement(currentStepId, targetElement)
            }
          }

          // 更新高亮遮罩位置，确保滚动后遮罩位置正确
          setTimeout(() => {
            updateHighlightMaskPosition()
          }, 300) // 在滚动动画完成后更新位置
        } catch (e) {
          console.error('处理步骤显示事件时出错:', e)
        }
      }, 100)
    })

    // 辅助函数：监听用户操作并在操作后自动进入下一步
    const listenForUserAction = (
      selector: string,
      eventType: string | string[],
      timeout = 15000
    ) => {
      return new Promise<void>((resolve) => {
        const elements = document.querySelectorAll(selector)
        let isResolved = false

        if (elements.length === 0) {
          // 提供更详细的警告信息
          console.warn(
            `监听目标未找到: ${selector}，元素可能不存在或已被隐藏。事件类型: ${
              Array.isArray(eventType) ? eventType.join(',') : eventType
            }`
          )

          // 如果没找到元素，设置一个超时后自动进入下一步
          setTimeout(() => {
            if (!isResolved) {
              console.log(`监听${selector}超时，自动继续`)
              isResolved = true
              resolve()
            }
          }, timeout)
          return
        }

        // 添加更详细的日志
        console.log(
          `开始监听用户操作: ${selector}, 找到 ${elements.length} 个元素, 事件类型:`,
          eventType
        )

        // 创建一个事件处理函数
        const handleEvent = () => {
          if (!isResolved) {
            console.log(
              `用户已执行操作: ${selector}, 事件类型: ${
                Array.isArray(eventType) ? eventType.join() : eventType
              }`
            )
            isResolved = true

            // 移除所有事件监听器
            const events = Array.isArray(eventType) ? eventType : [eventType]
            elements.forEach((el) => {
              events.forEach((evt) => {
                el.removeEventListener(evt, handleEvent)
              })
            })

            resolve()
          }
        }

        // 给所有匹配的元素添加事件监听
        const events = Array.isArray(eventType) ? eventType : [eventType]
        elements.forEach((el) => {
          events.forEach((evt) => {
            el.addEventListener(evt, handleEvent)
          })
        })

        // 设置超时，避免用户一直不操作
        setTimeout(() => {
          if (!isResolved) {
            console.log(`监听${selector}超时，自动继续`)
            isResolved = true
            resolve()
          }
        }, timeout)
      })
    }

    // 监听轮播交互的特殊函数，包括触摸滑动和导航按钮点击
    const listenForCarouselNavigation = () => {
      // 同时监听多种导航方式
      return Promise.race([
        // 监听触摸滑动
        listenForUserAction('.carousel', ['touchmove', 'touchend'], 10000),
        // 监听导航按钮点击
        listenForUserAction('.touch-nav', ['click', 'mousedown'], 10000),
        // 移除对不存在或隐藏元素的监听
        // listenForUserAction('.carousel-navigation', ['click', 'mousedown'], 10000),
        // 卡片直接点击
        listenForUserAction('.skill-card:not(.active)', ['click', 'mousedown'], 10000),
        // 监听键盘左右箭头
        new Promise<void>((resolve) => {
          const handler = (e: KeyboardEvent) => {
            if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
              console.log('检测到键盘箭头导航')
              document.removeEventListener('keydown', handler)
              resolve()
            }
          }
          document.addEventListener('keydown', handler)
          // 设置超时自动解决
          setTimeout(() => {
            document.removeEventListener('keydown', handler)
          }, 10000)
        })
      ])
    }

    // 监听加点按钮的特殊函数
    const listenForAttributeChanges = () => {
      return Promise.race([
        // 监听加号按钮
        listenForUserAction('.control-btn.plus', ['click', 'mousedown'], 10000),
        // 监听减号按钮
        listenForUserAction('.control-btn.minus', ['click', 'mousedown'], 10000),
        // 不指定加减，监听所有控制按钮
        listenForUserAction('.control-btn', ['click', 'mousedown'], 10000),
        // 监听数值直接变化
        new Promise<void>((resolve) => {
          // 创建一个MutationObserver来监听点数变化
          const observer = new MutationObserver((mutations) => {
            for (const mutation of mutations) {
              if (mutation.type === 'characterData' || mutation.type === 'childList') {
                console.log('检测到属性点数变化')
                observer.disconnect()
                resolve()
                break
              }
            }
          })

          // 查找属性点数显示元素
          const attributeValues = document.querySelectorAll('.attribute-value, .point-value')
          if (attributeValues.length > 0) {
            attributeValues.forEach((el) => {
              observer.observe(el, {
                characterData: true,
                childList: true,
                subtree: true
              })
            })

            // 设置超时自动解决
            setTimeout(() => {
              observer.disconnect()
            }, 10000)
          } else {
            // 如果找不到监听目标，设置一个超时
            setTimeout(resolve, 10000)
          }
        })
      ])
    }

    // Add steps to the tour
    localTour.addStep({
      id: 'welcome',
      title: 'Welcome to Talent Select',
      text: 'In this step, you can select the talent for your story.',
      attachTo: {
        // 直接附加到当前活动卡片
        element: '.skill-card.active',
        on: 'bottom'
      },
      buttons: [
        {
          text: 'Next',
          action() {
            return this.next()
          }
        }
      ],
      when: {
        show: () => {
          // 延迟一点以确保DOM完全渲染和定位
          setTimeout(() => {
            // 当第一步显示时，找到当前活动卡片
            const activeCard = document.querySelector('.skill-card.active')

            if (activeCard) {
              console.log('找到活动卡片:', activeCard)

              // 尝试滚动到活动卡片
              scrollToTargetElement('welcome', activeCard as HTMLElement)

              // 移除可能存在的任何高亮类，以避免多个卡片被高亮
              document.querySelectorAll('.skill-card').forEach((card) => {
                card.classList.remove('highlighted', 'guide-card-highlight')

                // 确保不会添加任何会影响位置的内联样式
                if (card instanceof HTMLElement) {
                  card.style.removeProperty('transform')
                  card.style.removeProperty('translate')
                  card.style.removeProperty('animation')
                }
              })

              // 移除任何已存在的高亮遮罩
              document.querySelectorAll('.card-highlight-mask').forEach((mask) => mask.remove())

              // 获取最新的位置信息
              const cardRect = activeCard.getBoundingClientRect()

              // 使用外部遮罩高亮，而不是直接修改卡片
              const highlightMask = document.createElement('div')
              highlightMask.className = 'card-highlight-mask'
              highlightMask.style.cssText = `
                position: fixed;
                left: ${cardRect.left - 5}px;
                top: ${cardRect.top - 5}px;
                width: ${cardRect.width + 10}px;
                height: ${cardRect.height + 10}px;
                border-radius: 16px;
                box-shadow: 0 0 0 2px rgba(218, 255, 150, 0.7),
                            0 0 20px 5px rgba(218, 255, 150, 0.5);
                z-index: 9997;
                pointer-events: none;
                animation: highlight-outer-glow 2s infinite;
                transition: left 0.2s, top 0.2s, width 0.2s, height 0.2s;
              `
              document.body.appendChild(highlightMask)

              console.log('已创建外部高亮遮罩，位置:', {
                left: cardRect.left,
                top: cardRect.top,
                width: cardRect.width,
                height: cardRect.height
              })
            } else {
              console.warn('无法找到活动卡片，请检查选择器')
            }

            // 在步骤显示后，确保导航器UI元素可见，但不影响卡片位置
            const carouselNavs = document.querySelectorAll('.touch-nav, .carousel-navigation')
            carouselNavs.forEach((nav) => {
              if (nav instanceof HTMLElement) {
                nav.style.zIndex = '9998'
              }
            })
          }, 50) // 短延迟确保DOM完全更新
        },
        hide: () => {
          // 移除高亮遮罩
          document.querySelectorAll('.card-highlight-mask').forEach((mask) => mask.remove())

          // 移除高亮类
          document.querySelectorAll('.skill-card').forEach((card) => {
            card.classList.remove('guide-card-highlight', 'highlighted')

            // 确保恢复任何被修改的样式
            if (card instanceof HTMLElement) {
              card.style.removeProperty('transform')
              card.style.removeProperty('translate')
              card.style.removeProperty('animation')
            }
          })
        }
      }
    })

    localTour.addStep({
      id: 'drag-explain',
      title: 'Swipe to explore more skills',
      text: 'Swipe left or right to switch',
      attachTo: {
        element: '.carousel',
        on: 'bottom'
      },
      buttons: [
        {
          text: 'Next',
          action() {
            return this.next()
          }
        }
      ],
      when: {
        show: function () {
          console.log('展示滑动教程步骤')

          // 更新按钮文字
          createNextButton('Next')

          // 保存一些需要在Promise中使用的方法
          const tourNext = this.next?.bind(this)

          return new Promise<void>((resolve) => {
            // 延迟一段时间确保UI加载完成
            setTimeout(() => {
              // 查找轮播容器
              const carousel = document.querySelector('.carousel')
              if (carousel) {
                console.log('找到轮播容器:', carousel)

                // 滚动到轮播容器
                scrollToTargetElement('drag-explain', carousel as HTMLElement)

                // 查找活动卡片（当前选中的卡片）- 更新选择器
                const activeCard = document.querySelector('.skill-card.active')
                if (activeCard) {
                  console.log('找到活动卡片:', activeCard)
                  createHandPointer('swipe', activeCard as HTMLElement)
                  console.log('滑动动画已触发')

                  // 立即更新位置一次，确保手指和高亮遮罩位置正确
                  updateHighlightMaskPosition()

                  // 监听用户滑动操作
                  listenForCarouselNavigation().then(() => {
                    console.log('检测到用户滑动操作，准备进入下一步')
                    // 短暂延迟后自动进入下一步
                    setTimeout(() => {
                      if (typeof tourNext === 'function') {
                        tourNext()
                      }
                      resolve()
                    }, 1000)
                  })
                } else {
                  console.error('找不到活动卡片，尝试使用轮播容器')
                  // 回退到轮播容器
                  createHandPointer('swipe', carousel as HTMLElement)
                  resolve()
                }
              } else {
                console.error('找不到轮播容器')
                resolve()
              }
            }, 500)
          })
        },
        hide: () => {
          // 移除所有指针及动画元素
          document
            .querySelectorAll('.guide-hand-pointer, .swipe-trail, .card-highlight-mask')
            .forEach((el) => el.remove())

          // 移除所有卡片高亮效果
          document.querySelectorAll('.skill-card').forEach((card) => {
            card.classList.remove('guide-card-highlight', 'highlighted')
            if (card instanceof HTMLElement) {
              card.style.removeProperty('transform')
              card.style.removeProperty('translate')
              card.style.removeProperty('animation')
            }
          })

          console.log('滑动动画及相关高亮效果已清除')
        }
      }
    })

    localTour.addStep({
      id: 'allocate-attributes',
      title: 'Allocate Attribute Points',
      text: 'Click to allocate attributes (total 20 points available for allocation)',
      attachTo: {
        element: '.attribute-grid',
        on: 'top'
      },
      buttons: [
        {
          text: 'Next',
          action() {
            return this.next()
          }
        }
      ],
      when: {
        show: function () {
          console.log('展示加点教程步骤')

          // 清理之前步骤的高亮效果
          document.querySelectorAll('.card-highlight-mask').forEach((mask) => mask.remove())
          document.querySelectorAll('.skill-card').forEach((card) => {
            card.classList.remove('guide-card-highlight', 'highlighted')
            if (card instanceof HTMLElement) {
              card.style.removeProperty('transform')
              card.style.removeProperty('translate')
              card.style.removeProperty('animation')
            }
          })

          // 更新按钮文字
          createNextButton('我知道了')

          // 保存一些需要在Promise中使用的方法
          const tourNext = this.next?.bind(this)

          return new Promise<void>((resolve) => {
            setTimeout(() => {
              // 查找属性网格并滚动到其位置
              const attributeGrid = document.querySelector('.attribute-grid')
              if (attributeGrid) {
                scrollToTargetElement('allocate-attributes', attributeGrid as HTMLElement)
              }

              // 创建一个向下指向的手指动画，指向加点按钮
              createHandPointer('allocate', '.attribute-card .control-btn')

              // 监听用户点击加点按钮的操作
              listenForAttributeChanges().then(() => {
                console.log('检测到用户点击加点按钮，准备进入下一步')
                // 短暂延迟后自动进入下一步
                setTimeout(() => {
                  if (typeof tourNext === 'function') {
                    tourNext()
                  }
                  resolve()
                }, 1000)
              })
            }, 500)
          })
        },
        hide: () => {
          // 移除动画元素和高亮效果
          document.querySelectorAll('.guide-hand-pointer').forEach((el) => el.remove())
          document.querySelectorAll('.guide-highlight').forEach((el) => {
            el.classList.remove('guide-highlight')
            if (el instanceof HTMLElement) {
              el.style.boxShadow = ''
            }
          })
        }
      }
    })

    localTour.addStep({
      id: 'confirm-selection',
      title: 'Confirm Selection',
      text: 'Please allocate all 20 attribute points before clicking the confirm button to complete the talent selection',
      attachTo: { element: '.confirm-btn', on: 'top' },
      canClickTarget: true,
      advanceOn: { selector: '.confirm-btn', event: 'click' },
      buttons: [
        {
          text: 'Complete',
          action() {
            return this.complete()
          }
        }
      ],
      when: {
        show: function () {
          console.log('展示确认选择步骤')

          // 更新按钮文字
          createNextButton('Complete')

          // 保存一些需要在Promise中使用的方法
          const tourComplete = this.complete?.bind(this)

          return new Promise<void>((resolve) => {
            // 查找确认按钮并滚动到其位置
            const confirmBtn = document.querySelector('.confirm-btn')
            if (confirmBtn) {
              scrollToTargetElement('confirm-selection', confirmBtn as HTMLElement)
            }

            // 创建一个指向确认按钮的指针，位置更靠近按钮
            createHandPointer('click', '.confirm-btn')

            // 监听用户点击确认按钮的操作
            listenForUserAction('.confirm-btn', ['click', 'mousedown'], 10000).then(() => {
              console.log('检测到用户点击确认按钮，结束向导')
              // 短暂延迟后结束向导
              setTimeout(() => {
                if (typeof tourComplete === 'function') {
                  tourComplete()
                }
                resolve()
              }, 500)
            })
          })
        }
      }
    })

    // 为tour添加通用的beforeHide回调，在每个步骤隐藏前清理相关资源
    localTour.on('hide', () => {
      // 移除所有临时元素，但保留高亮遮罩直到下一步显示
      document.querySelectorAll('.guide-hand-pointer, .swipe-trail').forEach((el) => el.remove())
    })

    // 当步骤完成时也清理资源
    localTour.on('complete', () => {
      try {
        // 标记为已完成
        isGuideActive.value = false
        hasSeenGuide.value = true

        // Save to local storage that user has seen the guide - but don't prevent reopening
        try {
          // We'll still store that they've seen it (for first-time detection)
          // but we won't prevent manual reopening
          localStorage.setItem('talent-guide-seen', 'true')
        } catch (e) {
          console.error('Could not save guide status to localStorage', e)
        }

        // Call the completion callback if provided
        if (typeof options.onComplete === 'function') {
          options.onComplete()
        }
      } catch (e) {
        console.error('处理tour完成事件时出错:', e)
      } finally {
        // 确保清除所有高亮效果和遮罩，即使上面的代码出错
        cleanupTourResources()
      }
    })

    // 当取消tour时也清理资源
    localTour.on('cancel', () => {
      try {
        isGuideActive.value = false
      } catch (e) {
        console.error('处理tour取消事件时出错:', e)
      } finally {
        cleanupTourResources()
      }
    })
  }

  const startTour = () => {
    // 先清理任何现有的资源
    cleanupTourResources()

    // 重置导览相关状态
    resetGuideState()

    try {
      // 初始化导览
      initTour()

      // 启动导览
      if (tour.value) {
        isGuideActive.value = true

        // 添加滚动和调整大小事件监听，当页面滚动或窗口大小改变时更新高亮遮罩位置
        window.addEventListener('scroll', updateHighlightMaskPosition, { passive: true })
        window.addEventListener('resize', updateHighlightMaskPosition, { passive: true })

        // 监听动画帧以更可靠地更新位置
        let frameId: number | null = null
        const updatePositionsOnAnimation = () => {
          updateHighlightMaskPosition()
          frameId = requestAnimationFrame(updatePositionsOnAnimation)
        }

        // 启动动画帧更新
        frameId = requestAnimationFrame(updatePositionsOnAnimation)

        // 保存frameId以便后续清除
        const cleanupAnimationFrame = () => {
          if (frameId !== null) {
            cancelAnimationFrame(frameId)
            frameId = null
          }
        }

        // 监听tour完成事件以清除动画帧
        tour.value.on('complete', cleanupAnimationFrame)
        tour.value.on('cancel', cleanupAnimationFrame)

        // 启动前先主动触发一次updateHighlightMaskPosition，确保即使在滚动后点击开始也能正确定位
        setTimeout(() => {
          if (tour.value && typeof tour.value.start === 'function') {
            tour.value.start()
          } else {
            console.error('Tour实例缺少start方法')
            isGuideActive.value = false
            cleanupAnimationFrame()
          }
        }, 10)
      } else {
        console.error('Tour未能正确初始化')
        isGuideActive.value = false
      }
    } catch (e) {
      console.error('启动导览失败:', e)
      isGuideActive.value = false
    }
  }

  // 更新高亮遮罩位置的函数 - 修改为更可靠的实现
  const updateHighlightMaskPosition = () => {
    // 检查当前步骤
    if (!tour.value || !isGuideActive.value) return

    let currentStep: any = null

    try {
      // 安全地获取当前步骤
      if (typeof tour.value.getCurrentStep === 'function') {
        currentStep = tour.value.getCurrentStep()
      }

      if (!currentStep) return

      // 获取当前步骤ID
      const currentStepId = currentStep.id

      // 如果是欢迎步骤，更新卡片高亮遮罩位置
      if (currentStepId === 'drag-explain') {
        // 滑动步骤也需要更新高亮遮罩和手指指针位置
        const activeCard = document.querySelector('.skill-card.active')
        if (activeCard) {
          const cardRect = activeCard.getBoundingClientRect()

          // 更新高亮遮罩
          const highlightMask = document.querySelector('.card-highlight-mask')
          if (highlightMask && highlightMask instanceof HTMLElement) {
            highlightMask.style.left = `${cardRect.left - 5}px`
            highlightMask.style.top = `${cardRect.top - 5}px`
            highlightMask.style.width = `${cardRect.width + 10}px`
            highlightMask.style.height = `${cardRect.height + 10}px`
          }

          // 更新手指指针和轨迹位置
          const pointer = document.querySelector('.guide-hand-pointer')
          const trail = document.querySelector('.swipe-trail')

          if (pointer instanceof HTMLElement && trail instanceof HTMLElement) {
            // 计算起始和结束位置 - 从左向右滑动
            const startPos = {
              left: cardRect.left + cardRect.width * 0.25,
              top: cardRect.top + cardRect.height * 0.6
            }

            const endPos = {
              left: cardRect.right - cardRect.width * 0.25,
              top: startPos.top
            }

            // 调整轨迹位置
            trail.style.left = `${startPos.left}px`
            trail.style.top = `${startPos.top + 20}px`
            trail.style.width = `${endPos.left - startPos.left}px`

            // 如果指针正在动画中，仅更新垂直位置
            pointer.style.top = `${startPos.top}px`
          }
        }
      } else if (currentStepId === 'allocate-attributes') {
        // 更新加点按钮指针位置
        const pointer = document.querySelector('.guide-hand-pointer')
        if (pointer && pointer instanceof HTMLElement) {
          // 找到当前高亮的加点按钮
          const plusBtn = document.querySelector(
            '.guide-highlight.control-btn, .control-btn.plus:not(.disabled)'
          )
          if (plusBtn) {
            const btnRect = plusBtn.getBoundingClientRect()
            // 调整指针位置，使其位于按钮上方，且更靠近按钮
            pointer.style.left = `${btnRect.left + btnRect.width / 2 - 40}px`
            pointer.style.top = `${btnRect.top + 5}px`
          } else {
            // 如果找不到特定的按钮，尝试找到整个属性卡片区域
            const attributeGrid = document.querySelector('.attribute-grid, .attribute-card')
            if (attributeGrid) {
              const gridRect = attributeGrid.getBoundingClientRect()
              pointer.style.top = `${gridRect.top + 50}px` // 放在属性区域的上方位置
            }
          }
        }
      } else if (currentStepId === 'confirm-selection') {
        // 更新确认按钮指针位置
        const pointer = document.querySelector('.guide-hand-pointer')
        if (pointer && pointer instanceof HTMLElement) {
          const confirmBtn = document.querySelector('.confirm-btn')
          if (confirmBtn) {
            const btnRect = confirmBtn.getBoundingClientRect()
            // 放置指针在按钮上方居中
            const centerX = btnRect.left + btnRect.width / 2
            const centerY = btnRect.top + btnRect.height / 2
            pointer.style.left = `${centerX - 20}px`
            pointer.style.top = `${centerY}px`
          }
        }
      }

      // 处理通用的点击动画情况
      if (
        !['welcome', 'drag-explain', 'allocate-attributes', 'confirm-selection'].includes(
          currentStepId
        )
      ) {
        // 对于其他任何步骤，尝试找到通用的高亮元素和指针
        const pointer = document.querySelector('.guide-hand-pointer')
        if (pointer && pointer instanceof HTMLElement) {
          // 找到高亮的元素
          const highlightedElement = document.querySelector('.guide-highlight')
          if (highlightedElement) {
            const rect = highlightedElement.getBoundingClientRect()
            // 更新指针位置到高亮元素位置
            const centerX = rect.left + rect.width / 2
            const centerY = rect.top + rect.height / 2
            pointer.style.left = `${centerX - 20}px`
            pointer.style.top = `${centerY}px`
          }
        }
      }
    } catch (e) {
      console.error('更新高亮遮罩位置时出错:', e)
    }
  }

  // 添加重置导览状态的函数
  const resetGuideState = () => {
    // 重置引导标志状态
    isGuideActive.value = true

    // 移除所有高亮类
    document
      .querySelectorAll(
        '.guide-highlight, .shepherd-highlighted, .highlighted, .guide-card-highlight'
      )
      .forEach((el) => {
        el.classList.remove(
          'guide-highlight',
          'shepherd-highlighted',
          'highlighted',
          'guide-card-highlight'
        )

        // 确保移除任何可能影响位置的样式
        if (el instanceof HTMLElement && el.classList.contains('skill-card')) {
          el.style.removeProperty('transform')
          el.style.removeProperty('translate')
          el.style.removeProperty('animation')
        }
      })

    // 移除所有临时元素
    document.querySelectorAll('.guide-hand-pointer, .swipe-trail').forEach((el) => {
      el.remove()
    })

    // 重置任何导览相关的本地DOM修改，但不要修改原有的样式属性
    document.querySelectorAll('.skill-card').forEach((card) => {
      if (card instanceof HTMLElement) {
        // 移除我们可能添加的内联样式，但保留原有样式
        card.style.removeProperty('z-index')
        card.style.removeProperty('visibility')
        card.style.removeProperty('opacity')
        card.style.removeProperty('transform')
        card.style.removeProperty('translate')
        card.style.removeProperty('animation')
        // 确保不修改transform，以免影响卡片原有的位置
      }
    })

    // 移除任何导览添加的内联样式
    const guideStyles = document.querySelectorAll('style[id^="hand-pointer-"]')
    guideStyles.forEach((style) => style.remove())
  }

  const checkIfFirstTime = (): boolean => {
    try {
      return localStorage.getItem('talent-guide-seen') !== 'true'
    } catch (e) {
      console.error('Could not access localStorage', e)
      return false
    }
  }

  onMounted(() => {
    // Start tour automatically if it's the first time
    if (options.isFirstTime === true || (options.isFirstTime === undefined && checkIfFirstTime())) {
      // Small delay to ensure component is mounted
      setTimeout(() => {
        startTour()
      }, 500)
    }
  })

  onUnmounted(() => {
    try {
      if (tour.value) {
        // 安全地调用complete方法
        if (
          isGuideActive.value &&
          tour.value.complete &&
          typeof tour.value.complete === 'function'
        ) {
          tour.value.complete()
        }

        // 确保无论如何都重置状态
        isGuideActive.value = false
      }
    } catch (e) {
      console.error('卸载组件时清理tour失败:', e)
    } finally {
      // 确保清理所有资源，即使上面的代码出错
      cleanupTourResources()

      // 移除事件监听器
      window.removeEventListener('scroll', updateHighlightMaskPosition)
      window.removeEventListener('resize', updateHighlightMaskPosition)
    }
  })

  return {
    startTour,
    isGuideActive,
    hasSeenGuide
  }
}
