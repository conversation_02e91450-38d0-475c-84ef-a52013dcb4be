import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useDeviceDetection } from './useDeviceDetection'

/**
 * 自适应路由 Composable
 * 根据设备类型动态适配路由和组件
 */
export function useAdaptiveRoute() {
  const route = useRoute()
  const router = useRouter()
  const { deviceType } = useDeviceDetection()
  
  // 当前路由是否有对应的PC端组件
  const hasPCComponent = ref(false)
  
  // 当前路由是否有对应的移动端组件
  const hasMobileComponent = ref(false)
  
  // 当前应该显示PC端还是移动端组件
  const shouldShowPCComponent = computed(() => {
    // 如果设备类型是桌面，且当前路由有对应的PC端组件，则显示PC端组件
    return deviceType.value === 'desktop' && hasPCComponent.value
  })
  
  // 当前应该显示的组件类型
  const currentComponentType = computed(() => {
    return shouldShowPCComponent.value ? 'pc' : 'mobile'
  })
  
  // 检查当前路由是否有对应的PC端和移动端组件
  const checkRouteComponents = () => {
    const currentPath = route.path
    
    // 检查是否有对应的PC端组件
    // 这里需要根据实际项目结构进行调整
    try {
      // 尝试动态导入PC端组件
      const pcComponentPath = `@/pc/views${currentPath}.vue`
      import(pcComponentPath)
        .then(() => {
          hasPCComponent.value = true
        })
        .catch(() => {
          hasPCComponent.value = false
        })
    } catch (error) {
      hasPCComponent.value = false
    }
    
    // 检查是否有对应的移动端组件
    try {
      // 尝试动态导入移动端组件
      const mobileComponentPath = `@/mobile/views${currentPath}.vue`
      import(mobileComponentPath)
        .then(() => {
          hasMobileComponent.value = true
        })
        .catch(() => {
          hasMobileComponent.value = false
        })
    } catch (error) {
      hasMobileComponent.value = false
    }
  }
  
  // 监听路由变化
  watch(
    () => route.path,
    () => {
      checkRouteComponents()
    },
    { immediate: true }
  )
  
  // 监听设备类型变化
  watch(
    () => deviceType.value,
    () => {
      // 当设备类型变化时，重新检查路由组件
      checkRouteComponents()
    }
  )
  
  return {
    shouldShowPCComponent,
    currentComponentType,
    deviceType
  }
}
