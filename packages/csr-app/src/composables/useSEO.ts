import { useHead, useSeoMeta } from '@unhead/vue'
import { computed } from 'vue'
import type { Story } from '@/api/stories'
import type { RouteLocationNormalized } from 'vue-router'

// 默认SEO配置
const DEFAULT_SEO = {
  siteName: computed(() => import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'),
  defaultTitle: computed(
    () =>
      `${
        import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
      }.AI - NSFW Character AI CrushOn Chat - Spicy AI`
  ),
  defaultDescription: `${
    import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
  } AI is an virtual roleplay platform where you interact with uncensored, lifelike AI characters. Inspired by leading names like CrushOn AI, Spicy AI, and crush No Filter AI, ${
    import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
  } AI takes immersive storytelling to the next level with bold Character AI NSFW experiences and customizable sexting chat pron scenarios. Explore your fantasies with intelligent AI designed for deep, unfiltered connections.`,
  defaultImage: 'https://cdn.magiclight.ai/assets/playshot/og-image.png',
  defaultKeywords: 'Spicy AI, CrushOn AI, AI characters, Character AI NSFW, AI sex chat',
  twitterHandle: '@playshot_ai',
  domain: computed(() => import.meta.env.VITE_DOMAIN || 'https://playshot.ai')
}

// 从故事描述中提取关键词
function extractKeywordsFromDescription(description: string, count: number = 3): string {
  if (!description) return DEFAULT_SEO.defaultKeywords

  // 移除标点符号，转换为小写，分割成单词
  const words = description
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter((word) => word.length > 3) // 只保留长度大于3的单词
    .filter(
      (word) =>
        ![
          'this',
          'that',
          'with',
          'from',
          'they',
          'have',
          'been',
          'will',
          'your',
          'what',
          'when',
          'where',
          'would',
          'could',
          'should'
        ].includes(word)
    ) // 过滤常见停用词

  // 去重并取前几个
  const uniqueWords = [...new Set(words)].slice(0, count)

  // 如果提取的关键词不够，补充默认关键词
  const keywords =
    uniqueWords.length > 0
      ? `${uniqueWords.join(', ')}, ${DEFAULT_SEO.defaultKeywords}`
      : DEFAULT_SEO.defaultKeywords

  return keywords
}

// 路由级别的SEO管理器
export function useRouteSEO() {
  const { setBaseSEO } = useBaseSEO()
  const { setStoryDetailSEO } = useStoryDetailSEO()
  const { setChatSEO } = useChatSEO()
  const { setStoriesPageSEO } = useStoriesSEO()

  const setRouteSEO = (route: RouteLocationNormalized, dynamicData?: any) => {
    const seoConfig = route.meta?.seo
    if (!seoConfig) return

    // 如果是动态SEO，处理特殊逻辑
    if (seoConfig.dynamic && dynamicData) {
      handleDynamicSEO(route, dynamicData)
      return
    }

    // 处理特定页面类型的SEO
    if (seoConfig.pageType) {
      handlePageTypeSEO(route, seoConfig.pageType)
      return
    }

    // 静态SEO配置
    const title = seoConfig.title || DEFAULT_SEO.defaultTitle.value
    const description = seoConfig.description || DEFAULT_SEO.defaultDescription
    const keywords = seoConfig.keywords || DEFAULT_SEO.defaultKeywords
    const image = seoConfig.image || DEFAULT_SEO.defaultImage
    const type = seoConfig.type || 'website'

    setBaseSEO({
      title,
      description,
      keywords,
      image,
      url: `${DEFAULT_SEO.domain.value}${route.fullPath}`,
      type
    })
  }

  const handleDynamicSEO = (route: RouteLocationNormalized, data: any) => {
    // 故事详情页面的动态SEO
    if (route.name === 'StoryIntro' && data.story) {
      console.log('setStoryDetailSEO', data.story)
      setStoryDetailSEO(data.story)
      return
    }

    // 聊天页面的动态SEO
    if (['Chat', 'Chat2', 'Chat3'].includes(route.name as string) && data.story) {
      setChatSEO(data.story, data.actorName)
      return
    }

    // Stories页面的动态SEO
    if (route.name === 'Stories' && data.filters) {
      setStoriesPageSEO(data.filters)
      return
    }
  }

  const handlePageTypeSEO = (route: RouteLocationNormalized, pageType: string) => {
    const { setUserPageSEO } = useUserSEO()
    const { setLegalPageSEO } = useLegalSEO()

    // 根据页面类型设置相应的SEO
    switch (pageType) {
      case 'login':
        setUserPageSEO('login')
        break
      case 'signup':
        setUserPageSEO('signup')
        break
      case 'profile':
        setUserPageSEO('profile')
        break
      case 'settings':
        setUserPageSEO('settings')
        break
      case 'terms':
        setLegalPageSEO('terms')
        break
      case 'privacy':
        setLegalPageSEO('privacy')
        break
      case 'complaints':
        setLegalPageSEO('complaints')
        break
      case 'content-removal':
        setLegalPageSEO('content-removal')
        break
      case 'record-keeping':
        setLegalPageSEO('record-keeping')
        break
      default:
        // 使用默认SEO
        setBaseSEO({
          url: `${DEFAULT_SEO.domain.value}${route.fullPath}`
        })
    }
  }

  return { setRouteSEO }
}

// 基础SEO配置函数（保持向后兼容）
export function useBaseSEO() {
  const setBaseSEO = (
    options: {
      title?: string
      description?: string
      keywords?: string
      image?: string
      url?: string
      type?: 'website' | 'article'
    } = {}
  ) => {
    const {
      title = DEFAULT_SEO.defaultTitle.value,
      description = DEFAULT_SEO.defaultDescription,
      keywords = DEFAULT_SEO.defaultKeywords,
      image = DEFAULT_SEO.defaultImage,
      url = DEFAULT_SEO.domain.value,
      type = 'website'
    } = options

    // 设置基础head标签
    useHead({
      title,
      meta: [
        { name: 'description', content: description },
        { name: 'keywords', content: keywords }
      ],
      link: [{ rel: 'canonical', href: url }]
    })

    // 设置SEO meta标签
    useSeoMeta({
      title,
      description,
      ogTitle: title,
      ogDescription: description,
      ogImage: image,
      ogUrl: url,
      ogType: type,
      ogSiteName: DEFAULT_SEO.siteName.value,
      twitterCard: 'summary_large_image',
      twitterSite: DEFAULT_SEO.twitterHandle,
      twitterTitle: title,
      twitterDescription: description,
      twitterImage: image
    })
  }

  return { setBaseSEO }
}

// Stories页面SEO
export function useStoriesSEO() {
  const { setBaseSEO } = useBaseSEO()

  const setStoriesPageSEO = (
    options: {
      selectedTags?: string[]
      selectedPopular?: string
      totalStories?: number
    } = {}
  ) => {
    const { selectedTags = [], selectedPopular = 'popular', totalStories = 0 } = options

    let title = 'Hottest AI Stories & Characters'
    let description =
      'Discover the most popular AI-powered interactive stories and characters. Chat, roleplay, and create your own adventures.'

    // 根据筛选条件动态调整标题和描述
    if (selectedTags.length > 0) {
      const tagsText = selectedTags.join(', ')
      title = `${tagsText} AI Stories & Characters`
      description = `Explore ${tagsText} themed AI stories and characters. Interactive fiction with immersive roleplay experiences.`
    }

    if (selectedPopular === 'newest') {
      title = title.replace('Hottest', 'Newest')
      description = description.replace('most popular', 'latest')
    }

    if (totalStories > 0) {
      description += ` Browse ${totalStories}+ unique characters and stories.`
    }

    setBaseSEO({
      title: `${title} | Playshot`,
      description,
      url: `${DEFAULT_SEO.domain}/stories`,
      type: 'website'
    })
  }

  return { setStoriesPageSEO }
}

// 单个故事详情SEO
export function useStoryDetailSEO() {
  const { setBaseSEO } = useBaseSEO()

  const setStoryDetailSEO = (story: Story) => {
    if (!story) return

    // 标题格式：故事名 - ReelPlay/PlayShot AI
    const appName = DEFAULT_SEO.siteName.value
    const title = `${story.title} - ${appName} AI`

    // 描述使用故事的实际描述
    const description =
      story.description ||
      `Chat and roleplay with ${story.title}. An immersive AI-powered interactive story experience.`

    // 从故事描述中提取关键词（3个随机单词）
    const keywords = extractKeywordsFromDescription(description, 3)

    const image = story.preview_url || story.actors?.[0]?.avatar_url || DEFAULT_SEO.defaultImage
    const url = `${DEFAULT_SEO.domain.value}/story/${story.id}`

    setBaseSEO({
      title,
      description,
      keywords,
      image,
      url,
      type: 'article'
    })

    // 添加额外的结构化数据
    useHead({
      script: [
        {
          type: 'application/ld+json',
          innerHTML: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'CreativeWork',
            name: story.title,
            description: description,
            image: image,
            url: url,
            author: {
              '@type': 'Organization',
              name: import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
            },
            publisher: {
              '@type': 'Organization',
              name: import.meta.env.VITE_WEBSITE_TITLE || 'Playshot',
              logo: {
                '@type': 'ImageObject',
                url: DEFAULT_SEO.defaultImage
              }
            }
          })
        }
      ]
    })
  }

  return { setStoryDetailSEO }
}

// 聊天页面SEO
export function useChatSEO() {
  const { setBaseSEO } = useBaseSEO()

  const setChatSEO = (story: Story, actorName?: string) => {
    if (!story) return

    const characterText = actorName ? ` with ${actorName}` : ''
    const title = `Chat${characterText} - ${story.title}`
    const description = `Interactive AI chat experience${characterText} in ${story.title}. Immersive roleplay and storytelling.`
    const image = story.preview_url || story.actors?.[0]?.avatar_url || DEFAULT_SEO.defaultImage

    setBaseSEO({
      title: `${title} | ${import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'}`,
      description,
      image,
      type: 'website'
    })
  }

  return { setChatSEO }
}

// 用户页面SEO
export function useUserSEO() {
  const { setBaseSEO } = useBaseSEO()

  const setUserPageSEO = (pageType: 'profile' | 'settings' | 'login' | 'signup') => {
    const seoConfig = {
      profile: {
        title: `My Profile | ${import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'}`,
        description: `Manage your ${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        } profile, view your story history, and customize your AI storytelling experience.`
      },
      settings: {
        title: `Account Settings | ${import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'}`,
        description: `Customize your ${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        } account settings, preferences, and privacy options.`
      },
      login: {
        title: `Sign In | ${import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'}`,
        description: `Sign in to ${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        } to access your AI stories, characters, and personalized content.`
      },
      signup: {
        title: `Sign Up | ${import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'}`,
        description: `Join ${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        } to create and explore AI-powered interactive stories and characters.`
      }
    }

    const config = seoConfig[pageType]
    setBaseSEO({
      title: config.title,
      description: config.description,
      type: 'website'
    })
  }

  return { setUserPageSEO }
}

// 法律页面SEO
export function useLegalSEO() {
  const { setBaseSEO } = useBaseSEO()

  const setLegalPageSEO = (
    pageType:
      | 'terms'
      | 'privacy'
      | 'complaints'
      | 'content-removal'
      | 'record-keeping'
      | 'about'
      | 'refund'
  ) => {
    const seoConfig = {
      terms: {
        title: `Terms of Service | ${import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'}`,
        description: `Read ${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        }'s Terms of Service. Learn about our service terms, user eligibility, payment terms, and legal agreements for using our AI-powered interactive storytelling platform.`,
        keywords: `terms of service, legal, user agreement, ${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        }, AI stories, terms and conditions`
      },
      privacy: {
        title: `Privacy Policy | ${import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'}`,
        description: `${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        }'s Privacy Policy explains how we collect, use, and protect your personal information when using our AI storytelling platform. Learn about data security and your privacy rights.`,

        keywords: `privacy policy, data protection, personal information, privacy rights, ${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        }, data security`
      },
      complaints: {
        title: `Complaints Policy | ${import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'}`,
        description: `Learn about ${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        }'s complaints policy and how to file a complaint. We are committed to addressing user concerns in a fair and timely manner.`,
        keywords: `complaints policy, user complaints, customer service, support, ${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        }, dispute resolution`
      },
      'content-removal': {
        title: `Content Removal Policy | ${import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'}`,
        description: `${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        }'s Content Removal Policy outlines our procedures for handling DMCA takedown requests and removing prohibited content from our platform.`,
        keywords: `content removal, DMCA, copyright, takedown, intellectual property, ${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        }, content policy`
      },
      'record-keeping': {
        title: `18 U.S.C. 2257 Compliance | ${import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'}`,
        description: `${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        }'s compliance statement regarding 18 U.S.C. 2257 record-keeping requirements. All content is AI-generated with no real persons depicted.`,
        keywords: `2257 compliance, record keeping, AI generated content, legal compliance, ${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        }, adult content`
      },
      about: {
        title: `About Us | ${import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'}`,
        description: `Learn about ${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        }.AI - Your Global AI Friendship Hub. Discover our mission to pioneer the future of social interaction through advanced AI technology and diverse AI characters.`,
        keywords: `about us, AI friendship, AI characters, social interaction, AI technology, ${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        }, company information`
      },
      refund: {
        title: `Refund and Returns Policy | ${import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'}`,
        description: `${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        }'s Refund and Returns Policy. Learn about our refund eligibility, process, and terms for digital services and virtual currency purchases.`,
        keywords: `refund policy, returns, digital services, virtual currency, customer service, ${
          import.meta.env.VITE_WEBSITE_TITLE || 'Playshot'
        }, payment terms`
      }
    }

    const config = seoConfig[pageType]
    setBaseSEO({
      title: config.title,
      description: config.description,
      keywords: config.keywords,
      type: 'website'
    })
  }

  return { setLegalPageSEO }
}

export { DEFAULT_SEO }
