import { ref, onMounted, onUnmounted, computed } from 'vue'
import isMobile from 'ismobilejs'

/**
 * 设备检测 Composable
 * 提供设备类型检测功能，包括PC/移动设备识别和开发者工具模拟设备检测
 */
export function useDeviceDetection() {
  // 设备类型状态
  const isDesktop = ref(false)
  const isMobileDevice = ref(false)
  const isTablet = ref(false)
  const isPhone = ref(false)
  const isEmulated = ref(false) // 是否为开发者工具模拟的移动设备

  // 检测设备类型
  const detectDeviceType = () => {
    const mobileInfo = isMobile()

    // 检测是否为移动设备
    isMobileDevice.value = mobileInfo.any
    isTablet.value = mobileInfo.tablet
    isPhone.value = mobileInfo.phone

    // 检测是否为桌面设备 (非移动设备或屏幕宽度大于1024px)
    isDesktop.value = !isMobileDevice.value || window.innerWidth >= 1024

    // 检测是否为开发者工具模拟的移动设备
    // 通常开发者工具模拟移动设备时，userAgent会包含移动设备特征，但实际屏幕尺寸较大
    isEmulated.value = detectEmulatedDevice()
  }

  // 检测是否为开发者工具模拟的移动设备
  const detectEmulatedDevice = () => {
    const mobileInfo = isMobile()

    // 如果userAgent表明是移动设备，但屏幕宽度大于768px，可能是开发者工具模拟的
    if (mobileInfo.any && window.innerWidth >= 768) {
      // 额外检查是否存在开发者工具特有的属性
      return true
    }

    // 检查是否存在常见的开发者工具模拟标志
    const ua = navigator.userAgent.toLowerCase()
    return ua.includes('emulator') || ua.includes('responsive')
  }

  // 计算最终的设备类型
  const deviceType = computed(() => {
    if (isEmulated.value) return 'mobile' // 如果是模拟的移动设备，按移动设备处理
    if (isTablet.value) return 'tablet'
    if (isPhone.value) return 'mobile'
    return 'desktop'
  })

  // 监听窗口大小变化
  const handleResize = () => {
    detectDeviceType()
  }

  onMounted(() => {
    detectDeviceType()
    window.addEventListener('resize', handleResize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })

  return {
    isDesktop,
    isMobileDevice,
    isTablet,
    isPhone,
    isEmulated,
    deviceType
  }
}
