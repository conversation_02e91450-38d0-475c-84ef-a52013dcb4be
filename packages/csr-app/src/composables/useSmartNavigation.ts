/**
 * 智能导航组合式函数
 * 在微前端环境中自动处理导航
 */

import { useRouter } from 'vue-router'
import { 
  isInMicroFrontend, 
  navigateToHome, 
  navigateToStories, 
  navigateToStory,
  goBack 
} from '@/utils/iframeNavigation'

export function useSmartNavigation() {
  const router = useRouter()

  /**
   * 智能导航到首页
   */
  const smartGoHome = () => {
    console.log('🏠 智能导航: 尝试返回首页')
    
    if (navigateToHome()) {
      console.log('✅ 通过微前端导航到主应用首页')
      return true
    }
    
    // 降级到正常路由
    console.log('⬇️ 降级到CSR应用内部导航')
    router.push('/')
    return false
  }

  /**
   * 智能导航到故事列表
   */
  const smartGoToStories = () => {
    console.log('📚 智能导航: 尝试前往故事列表')
    
    if (navigateToStories()) {
      console.log('✅ 通过微前端导航到主应用故事列表')
      return true
    }
    
    // 降级到正常路由
    console.log('⬇️ 降级到CSR应用内部导航')
    router.push('/stories')
    return false
  }

  /**
   * 智能导航到特定故事
   */
  const smartGoToStory = (storyId: string) => {
    console.log('📖 智能导航: 尝试前往故事详情', storyId)
    
    if (navigateToStory(storyId)) {
      console.log('✅ 通过微前端导航到主应用故事详情')
      return true
    }
    
    // 降级到正常路由
    console.log('⬇️ 降级到CSR应用内部导航')
    router.push(`/story/${storyId}`)
    return false
  }

  /**
   * 智能返回上一页
   */
  const smartGoBack = () => {
    console.log('⬅️ 智能导航: 尝试返回上一页')
    
    if (goBack()) {
      console.log('✅ 通过微前端导航返回')
      return true
    }
    
    // 降级到正常路由
    console.log('⬇️ 降级到浏览器历史记录')
    window.history.back()
    return false
  }

  /**
   * 通用智能导航
   */
  const smartNavigate = (path: string) => {
    console.log('🧭 智能导航: 尝试导航到', path)
    
    // 根据路径选择合适的导航方法
    if (path === '/' || path === '/home') {
      return smartGoHome()
    } else if (path === '/stories') {
      return smartGoToStories()
    } else if (path.startsWith('/story/')) {
      const storyId = path.split('/')[2]
      if (storyId) {
        return smartGoToStory(storyId)
      }
    }
    
    // 其他路径使用正常路由
    console.log('⬇️ 使用正常路由导航到', path)
    router.push(path)
    return false
  }

  /**
   * 检查是否在微前端环境
   */
  const inMicroFrontend = isInMicroFrontend()

  return {
    // 智能导航方法
    smartGoHome,
    smartGoToStories,
    smartGoToStory,
    smartGoBack,
    smartNavigate,
    
    // 状态
    inMicroFrontend,
    
    // 原始路由器（用于复杂导航）
    router
  }
}
