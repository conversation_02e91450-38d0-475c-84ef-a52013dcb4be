// 性能监控工具
export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: Map<string, number> = new Map()
  private observers: PerformanceObserver[] = []

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  constructor() {
    this.initObservers()
  }

  private initObservers() {
    // 监控 LCP (Largest Contentful Paint)
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1] as any
          this.metrics.set('LCP', lastEntry.startTime)
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        this.observers.push(lcpObserver)
      } catch (e) {
        console.warn('LCP observer not supported')
      }

      // 监控 FID (First Input Delay)
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            this.metrics.set('FID', entry.processingStart - entry.startTime)
          })
        })
        fidObserver.observe({ entryTypes: ['first-input'] })
        this.observers.push(fidObserver)
      } catch (e) {
        console.warn('FID observer not supported')
      }

      // 监控 CLS (Cumulative Layout Shift)
      try {
        let clsValue = 0
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
              this.metrics.set('CLS', clsValue)
            }
          })
        })
        clsObserver.observe({ entryTypes: ['layout-shift'] })
        this.observers.push(clsObserver)
      } catch (e) {
        console.warn('CLS observer not supported')
      }
    }
  }

  // 测量自定义性能指标
  mark(name: string) {
    if ('performance' in window && performance.mark) {
      performance.mark(name)
    }
  }

  measure(name: string, startMark: string, endMark?: string) {
    if ('performance' in window && performance.measure) {
      try {
        if (endMark) {
          performance.measure(name, startMark, endMark)
        } else {
          performance.measure(name, startMark)
        }
        const measure = performance.getEntriesByName(name, 'measure')[0]
        this.metrics.set(name, measure.duration)
        return measure.duration
      } catch (e) {
        console.warn(`Failed to measure ${name}:`, e)
      }
    }
    return 0
  }

  // 获取页面加载性能
  getPageLoadMetrics() {
    if (!('performance' in window) || !performance.timing) {
      return {}
    }

    const timing = performance.timing
    const navigation = performance.navigation

    return {
      // DNS查询时间
      dnsLookup: timing.domainLookupEnd - timing.domainLookupStart,
      // TCP连接时间
      tcpConnect: timing.connectEnd - timing.connectStart,
      // 请求响应时间
      request: timing.responseEnd - timing.requestStart,
      // DOM解析时间
      domParse: timing.domContentLoadedEventStart - timing.responseEnd,
      // 资源加载时间
      resourceLoad: timing.loadEventStart - timing.domContentLoadedEventStart,
      // 总加载时间
      totalLoad: timing.loadEventEnd - timing.navigationStart,
      // 页面类型
      navigationType: navigation.type,
      // 重定向次数
      redirectCount: navigation.redirectCount
    }
  }

  // 获取资源加载性能
  getResourceMetrics() {
    if (!('performance' in window) || !performance.getEntriesByType) {
      return []
    }

    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
    return resources.map((resource) => ({
      name: resource.name,
      type: this.getResourceType(resource.name),
      duration: resource.duration,
      size: resource.transferSize || 0,
      cached: resource.transferSize === 0 && resource.decodedBodySize > 0
    }))
  }

  private getResourceType(url: string): string {
    if (url.match(/\.(css)$/i)) return 'css'
    if (url.match(/\.(js)$/i)) return 'js'
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/i)) return 'image'
    if (url.match(/\.(woff|woff2|ttf|eot)$/i)) return 'font'
    return 'other'
  }

  // 获取内存使用情况
  getMemoryMetrics() {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit
      }
    }
    return null
  }

  // 获取所有指标
  getAllMetrics() {
    return {
      coreWebVitals: {
        LCP: this.metrics.get('LCP'),
        FID: this.metrics.get('FID'),
        CLS: this.metrics.get('CLS')
      },
      pageLoad: this.getPageLoadMetrics(),
      resources: this.getResourceMetrics(),
      memory: this.getMemoryMetrics(),
      custom: Object.fromEntries(this.metrics)
    }
  }

  // 发送性能数据到分析服务
  sendMetrics(endpoint?: string) {
    const metrics = this.getAllMetrics()

    if (endpoint) {
      fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(metrics)
      }).catch((e) => console.warn('Failed to send metrics:', e))
    }

    return metrics
  }

  // 清理观察者
  cleanup() {
    this.observers.forEach((observer) => observer.disconnect())
    this.observers = []
  }
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance()

// 自动在页面加载完成后发送指标
if (typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    setTimeout(() => {
      performanceMonitor.sendMetrics()
    }, 5000) // 5秒后发送，确保所有指标都已收集
  })
}
