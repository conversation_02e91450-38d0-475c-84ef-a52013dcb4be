export function isAndroidWebView() {
  const userAgent = window.navigator.userAgent.toLowerCase()
  const android = /android/.test(userAgent)
  const wv = /wv/.test(userAgent)

  // 排除 Facebook WebView
  const isFacebookWebView = /fban|fbav|fb_iab/.test(userAgent)

  // 排除 Opera Mini
  const isOperaMini = /opera mini|op_mini/.test(userAgent)

  // 排除微信、微博、QQ 等常见 WebView
  const isWeChat = /micromessenger/.test(userAgent)
  const isWeibo = /weibo/.test(userAgent)
  const isQQ = /qq\//.test(userAgent)

  // 排除国外常见 App WebView
  const isTikTok = /musical\.ly|tiktok/.test(userAgent) // TikTok
  const isInstagram = /instagram/.test(userAgent) // Instagram
  const isTwitter = /twitter/.test(userAgent) // Twitter
  const isSnapchat = /snapchat/.test(userAgent) // Snapchat
  const isReddit = /reddit/.test(userAgent) // Reddit
  const isPinterest = /pinterest/.test(userAgent) // Pinterest
  const isLinkedIn = /linkedin/.test(userAgent) // LinkedIn
  const isDiscord = /discord/.test(userAgent) // Discord
  const isTelegram = /telegram/.test(userAgent) // Telegram

  // 如果是 Android WebView 但不属于这些 App，基本可以确定是 APK 里的 WebView
  return (
    android &&
    wv &&
    !isFacebookWebView &&
    !isOperaMini &&
    !isWeChat &&
    !isWeibo &&
    !isQQ &&
    !isTikTok &&
    !isInstagram &&
    !isTwitter &&
    !isSnapchat &&
    !isReddit &&
    !isPinterest &&
    !isLinkedIn &&
    !isDiscord &&
    !isTelegram
  )
}
