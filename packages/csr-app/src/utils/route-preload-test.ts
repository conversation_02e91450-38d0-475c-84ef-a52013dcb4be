/**
 * 路由预加载功能测试工具
 * 用于验证路由懒加载和预加载策略的效果
 */

import { getCacheInfo, resetLoadStats } from './advanced-route-loader'
import { getPreloadStatus, preloadRouteGroup } from './route-preload-init'

/**
 * 测试结果接口
 */
interface TestResult {
  name: string
  success: boolean
  duration: number
  details?: any
  error?: string
}

/**
 * 路由预加载测试套件
 */
export class RoutePreloadTestSuite {
  private results: TestResult[] = []

  /**
   * 运行所有测试
   */
  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 开始路由预加载功能测试')
    this.results = []

    // 重置统计数据
    resetLoadStats()

    // 测试列表
    const tests = [
      this.testCacheBasicFunctionality,
      this.testPreloadInitialization,
      this.testRouteGroupPreload,
      this.testCachePerformance,
      this.testErrorHandling,
      this.testMemoryUsage
    ]

    // 依次执行测试
    for (const test of tests) {
      try {
        await test.call(this)
      } catch (error) {
        console.error('测试执行失败:', error)
      }
    }

    // 输出测试报告
    this.generateReport()
    return this.results
  }

  /**
   * 测试缓存基本功能
   */
  private async testCacheBasicFunctionality(): Promise<void> {
    const startTime = performance.now()

    try {
      const initialCacheInfo = getCacheInfo()

      // 验证缓存信息结构
      const hasValidStructure =
        typeof initialCacheInfo.size === 'number' &&
        Array.isArray(initialCacheInfo.keys) &&
        typeof initialCacheInfo.stats === 'object'

      const duration = performance.now() - startTime

      this.results.push({
        name: '缓存基本功能测试',
        success: hasValidStructure,
        duration,
        details: {
          cacheSize: initialCacheInfo.size,
          keysCount: initialCacheInfo.keys.length,
          stats: initialCacheInfo.stats
        }
      })
    } catch (error) {
      this.results.push({
        name: '缓存基本功能测试',
        success: false,
        duration: performance.now() - startTime,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 测试预加载初始化
   */
  private async testPreloadInitialization(): Promise<void> {
    const startTime = performance.now()

    try {
      const preloadStatus = getPreloadStatus()

      // 验证预加载状态
      const isInitialized = preloadStatus.isInitialized
      const hasDeviceType = typeof preloadStatus.isMobile === 'boolean'

      const duration = performance.now() - startTime

      this.results.push({
        name: '预加载初始化测试',
        success: isInitialized && hasDeviceType,
        duration,
        details: {
          isInitialized,
          isMobile: preloadStatus.isMobile,
          cacheInfo: preloadStatus.cacheInfo
        }
      })
    } catch (error) {
      this.results.push({
        name: '预加载初始化测试',
        success: false,
        duration: performance.now() - startTime,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 测试路由组预加载
   */
  private async testRouteGroupPreload(): Promise<void> {
    const startTime = performance.now()

    try {
      const beforeCache = getCacheInfo()

      // 尝试预加载一个路由组
      await preloadRouteGroup('core')

      // 等待一小段时间让预加载完成
      await new Promise((resolve) => setTimeout(resolve, 100))

      const afterCache = getCacheInfo()

      // 验证缓存是否增加
      const cacheIncreased = afterCache.size >= beforeCache.size

      const duration = performance.now() - startTime

      this.results.push({
        name: '路由组预加载测试',
        success: cacheIncreased,
        duration,
        details: {
          beforeSize: beforeCache.size,
          afterSize: afterCache.size,
          newKeys: afterCache.keys.filter((key) => !beforeCache.keys.includes(key))
        }
      })
    } catch (error) {
      this.results.push({
        name: '路由组预加载测试',
        success: false,
        duration: performance.now() - startTime,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 测试缓存性能
   */
  private async testCachePerformance(): Promise<void> {
    const startTime = performance.now()

    try {
      const iterations = 100
      const times: number[] = []

      // 多次调用getCacheInfo测试性能
      for (let i = 0; i < iterations; i++) {
        const start = performance.now()
        getCacheInfo()
        times.push(performance.now() - start)
      }

      const avgTime = times.reduce((a, b) => a + b, 0) / times.length
      const maxTime = Math.max(...times)

      // 性能要求：平均时间 < 1ms，最大时间 < 5ms
      const performanceGood = avgTime < 1 && maxTime < 5

      const duration = performance.now() - startTime

      this.results.push({
        name: '缓存性能测试',
        success: performanceGood,
        duration,
        details: {
          iterations,
          avgTime: avgTime.toFixed(3),
          maxTime: maxTime.toFixed(3),
          minTime: Math.min(...times).toFixed(3)
        }
      })
    } catch (error) {
      this.results.push({
        name: '缓存性能测试',
        success: false,
        duration: performance.now() - startTime,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 测试错误处理
   */
  private async testErrorHandling(): Promise<void> {
    const startTime = performance.now()

    try {
      // 尝试预加载不存在的路由组
      let errorCaught = false

      try {
        await preloadRouteGroup('non-existent-group')
      } catch (error) {
        errorCaught = true
      }

      // 错误处理应该优雅，不应该抛出未捕获的异常
      const duration = performance.now() - startTime

      this.results.push({
        name: '错误处理测试',
        success: true, // 只要没有未捕获的异常就算成功
        duration,
        details: {
          errorCaught,
          message: '错误处理正常'
        }
      })
    } catch (error) {
      this.results.push({
        name: '错误处理测试',
        success: false,
        duration: performance.now() - startTime,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 测试内存使用
   */
  private async testMemoryUsage(): Promise<void> {
    const startTime = performance.now()

    try {
      // 获取内存信息（如果可用）
      const memoryInfo = (performance as any).memory

      if (memoryInfo) {
        const usedJSHeapSize = memoryInfo.usedJSHeapSize
        const totalJSHeapSize = memoryInfo.totalJSHeapSize
        const jsHeapSizeLimit = memoryInfo.jsHeapSizeLimit

        // 检查内存使用是否合理（使用率 < 80%）
        const memoryUsageRatio = usedJSHeapSize / jsHeapSizeLimit
        const memoryUsageGood = memoryUsageRatio < 0.8

        const duration = performance.now() - startTime

        this.results.push({
          name: '内存使用测试',
          success: memoryUsageGood,
          duration,
          details: {
            usedJSHeapSize: Math.round(usedJSHeapSize / 1024 / 1024) + 'MB',
            totalJSHeapSize: Math.round(totalJSHeapSize / 1024 / 1024) + 'MB',
            jsHeapSizeLimit: Math.round(jsHeapSizeLimit / 1024 / 1024) + 'MB',
            usageRatio: (memoryUsageRatio * 100).toFixed(2) + '%'
          }
        })
      } else {
        this.results.push({
          name: '内存使用测试',
          success: true,
          duration: performance.now() - startTime,
          details: {
            message: '内存信息不可用（非Chrome浏览器）'
          }
        })
      }
    } catch (error) {
      this.results.push({
        name: '内存使用测试',
        success: false,
        duration: performance.now() - startTime,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 生成测试报告
   */
  private generateReport(): void {
    const totalTests = this.results.length
    const passedTests = this.results.filter((r) => r.success).length
    const failedTests = totalTests - passedTests
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0)

    console.log('\n📊 路由预加载测试报告')
    console.log('='.repeat(50))
    console.log(`总测试数: ${totalTests}`)
    console.log(`通过: ${passedTests} ✅`)
    console.log(`失败: ${failedTests} ❌`)
    console.log(`总耗时: ${totalDuration.toFixed(2)}ms`)
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`)
    console.log('='.repeat(50))

    // 详细结果
    this.results.forEach((result, index) => {
      const status = result.success ? '✅' : '❌'
      console.log(`${index + 1}. ${status} ${result.name} (${result.duration.toFixed(2)}ms)`)

      if (result.details) {
        console.log('   详情:', result.details)
      }

      if (result.error) {
        console.log('   错误:', result.error)
      }
    })

    console.log('\n')
  }
}

/**
 * 运行路由预加载测试
 */
export async function runRoutePreloadTests(): Promise<TestResult[]> {
  const testSuite = new RoutePreloadTestSuite()
  return await testSuite.runAllTests()
}

// 开发环境下自动暴露测试工具
// if (import.meta.env.DEV) {
//   const testTools = {
//     runTests: runRoutePreloadTests,
//     getCacheInfo,
//     getPreloadStatus,
//     preloadRouteGroup
//   }

//   const windowAny = window as any
//   windowAny.__routePreloadTest = testTools

//   console.log('🔧 路由预加载测试工具已挂载到 window.__routePreloadTest')
// }
