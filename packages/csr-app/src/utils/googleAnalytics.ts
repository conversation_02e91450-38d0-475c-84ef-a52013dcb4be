/**
 * Google Analytics 工具函数
 * 使用 vue-gtag-next 库
 */

import { useGtag } from 'vue-gtag-next'

/**
 * 获取 gtag 实例
 */
const getGtag = () => {
  try {
    // 只在 prod.southeastAsia 环境中使用 gtag
    if (import.meta.env.MODE !== 'prod.southeastAsia') {
      return null
    }
    return useGtag()
  } catch (error) {
    console.warn('Failed to get gtag instance:', error)
    return null
  }
}

/**
 * 发送 Google Analytics 事件
 * @param eventName 事件名称
 * @param parameters 事件参数
 */
export const sendGAEvent = (eventName: string, parameters: Record<string, any> = {}) => {
  try {
    const gtag = getGtag()
    if (gtag) {
      console.log(`Sending GA4 event: ${eventName}`, parameters)
      gtag.event(eventName, parameters)
    } else {
      console.warn('gtag not available')
    }
  } catch (error) {
    console.warn('Failed to send GA event:', error)
  }
}

/**
 * 发送用户登录状态事件
 * @param isLoggedIn 是否已登录
 */
export const sendUserLoginStatusEvent = (isLoggedIn: boolean) => {
  sendGAEvent('user_login_status', {
    event_category: 'authentication',
    event_label: 'is_logged_in',
    value: isLoggedIn ? 1 : 0
  })
}

/**
 * 发送用户游戏开始事件
 */
export const sendUserPlayedGameEvent = () => {
  sendGAEvent('userplayedgame', {
    event_category: 'game',
    event_label: 'userplayedgame',
    value: 1
  })
}

/**
 * 发送用户点击封面事件
 */
export const sendUserClickCoverEvent = () => {
  sendGAEvent('userclickcover', {
    event_category: 'story_card_click',
    event_label: 'story_card_click',
    value: 1
  })
}
