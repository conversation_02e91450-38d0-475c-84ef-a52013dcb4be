/**
 * 获取动态API地址的工具函数
 * 根据当前域名动态构造API地址，支持多域名部署
 */

// 声明全局类型
declare global {
  interface Window {
    __DYNAMIC_API_HOST__?: string
  }
}

/**
 * 获取动态API地址
 * 优先级：
 * 1. 全局变量 window.__DYNAMIC_API_HOST__（由Vite插件注入）
 * 2. meta标签中的api-host（由Vite插件注入）
 * 3. 环境变量 VITE_API_HOST（回退方案）
 */
export function getDynamicApiHost(): string {
  // 直接使用环境变量，简化逻辑
  if (typeof window !== 'undefined' && window.__DYNAMIC_API_HOST__) {
    return window.__DYNAMIC_API_HOST__
  }

  // 如果没有动态地址，尝试从meta标签获取
  if (typeof window !== 'undefined') {
    const metaTag = document.querySelector('meta[name="api-host"]')
    if (metaTag) {
      return metaTag.getAttribute('content') || import.meta.env.VITE_API_HOST
    }
  }

  // 最终回退到环境变量
  return import.meta.env.VITE_API_HOST
}

/**
 * 构造完整的API URL
 * @param path API路径，如 '/api/v1/user.whoami'
 * @returns 完整的API URL
 */
export function buildApiUrl(path: string): string {
  const apiHost = getDynamicApiHost()
  // 确保路径以 / 开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`
  return `${apiHost}${normalizedPath}`
}

/**
 * 获取当前使用的API域名（不包含协议）
 * @returns API域名，如 'api.playshot.ai'
 */
export function getApiDomain(): string {
  const apiHost = getDynamicApiHost()
  try {
    const url = new URL(apiHost)
    return url.hostname
  } catch {
    return apiHost.replace(/^https?:\/\//, '')
  }
}
