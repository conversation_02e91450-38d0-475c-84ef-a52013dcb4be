import { createPreloadManager } from './route-preload-strategy'
import { getCacheInfo, resetLoadStats } from './advanced-route-loader'

/**
 * 路由预加载初始化器
 */
export class RoutePreloadInitializer {
  private preloadManager: any = null
  private isInitialized = false
  private isMobile = false

  /**
   * 初始化路由预加载
   * @param isMobile 是否为移动端
   */
  async initialize(isMobile: boolean): Promise<void> {
    if (this.isInitialized) return

    this.isMobile = isMobile
    console.log(`🚀 初始化${isMobile ? '移动端' : 'PC端'}路由预加载策略`)

    try {
      // 创建预加载管理器
      this.preloadManager = createPreloadManager(isMobile)

      // 开始预加载
      await this.preloadManager.startPreload()

      // 设置性能监控
      this.setupPerformanceMonitoring()

      // 设置路由变化监听
      this.setupRouteChangeListener()

      this.isInitialized = true
      console.log('✅ 路由预加载初始化完成')
    } catch (error) {
      console.error('❌ 路由预加载初始化失败:', error)
    }
  }

  /**
   * 设置性能监控
   */
  private setupPerformanceMonitoring(): void {
    // 每30秒输出一次缓存状态
    setInterval(() => {
      const cacheInfo = getCacheInfo()
      console.log('📊 路由缓存状态:', {
        缓存组件数: cacheInfo.size,
        加载统计: cacheInfo.stats,
        缓存键: cacheInfo.keys.slice(0, 5) // 只显示前5个
      })
    }, 30000)

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        console.log('👀 页面重新可见，检查预加载状态')
        this.checkPreloadHealth()
      }
    })
  }

  /**
   * 设置路由变化监听
   */
  private setupRouteChangeListener(): void {
    // 监听路由变化，智能预加载相关路由
    if (typeof window !== 'undefined' && window.addEventListener) {
      window.addEventListener('popstate', () => {
        this.onRouteChange()
      })
    }
  }

  /**
   * 路由变化处理
   */
  private onRouteChange(): void {
    const currentPath = window.location.pathname
    console.log('🔄 路由变化:', currentPath)

    // 根据当前路由智能预加载相关路由组
    this.smartPreloadByRoute(currentPath)
  }

  /**
   * 根据路由智能预加载
   */
  private smartPreloadByRoute(path: string): void {
    if (!this.preloadManager) return

    // 聊天相关路由
    if (path.includes('/chat') || path.includes('/story/')) {
      this.preloadManager.preloadGroup('chat')
    }

    // 用户相关路由
    if (path.includes('/user/')) {
      this.preloadManager.preloadGroup('user')
    }

    // 编辑器相关路由
    if (path.includes('/creation/') || path.includes('/editor/')) {
      this.preloadManager.preloadGroup('editor')
    }
  }

  /**
   * 检查预加载健康状态
   */
  private checkPreloadHealth(): void {
    const cacheInfo = getCacheInfo()
    const { stats } = cacheInfo

    // 如果失败率过高，重置统计
    if (stats.total > 10 && stats.failed / stats.total > 0.3) {
      console.warn('⚠️ 路由加载失败率过高，重置统计')
      resetLoadStats()
    }

    // 如果缓存过多，清理一些低优先级的缓存
    if (cacheInfo.size > 20) {
      console.log('🧹 缓存数量过多，进行清理')
      this.cleanupCache()
    }
  }

  /**
   * 清理缓存
   */
  private cleanupCache(): void {
    // 这里可以实现更智能的缓存清理策略
    // 比如清理最久未使用的、低优先级的组件
    console.log('🧹 执行缓存清理')
  }

  /**
   * 手动预加载路由组
   */
  async preloadGroup(groupName: string): Promise<void> {
    if (!this.preloadManager) {
      console.warn('预加载管理器未初始化')
      return
    }

    await this.preloadManager.preloadGroup(groupName)
  }

  /**
   * 获取预加载状态
   */
  getStatus(): {
    isInitialized: boolean
    isMobile: boolean
    cacheInfo: ReturnType<typeof getCacheInfo>
  } {
    return {
      isInitialized: this.isInitialized,
      isMobile: this.isMobile,
      cacheInfo: getCacheInfo()
    }
  }

  /**
   * 销毁预加载器
   */
  destroy(): void {
    this.preloadManager = null
    this.isInitialized = false
    console.log('🗑️ 路由预加载器已销毁')
  }
}

// 创建全局实例
export const routePreloadInitializer = new RoutePreloadInitializer()

/**
 * 便捷的初始化函数
 */
export async function initializeRoutePreload(isMobile: boolean): Promise<void> {
  await routePreloadInitializer.initialize(isMobile)
}

/**
 * 获取预加载状态
 */
export function getPreloadStatus() {
  return routePreloadInitializer.getStatus()
}

/**
 * 手动预加载路由组
 */
export async function preloadRouteGroup(groupName: string): Promise<void> {
  await routePreloadInitializer.preloadGroup(groupName)
}

// 开发环境下的调试工具
// if (import.meta.env.DEV) {
//   // 将调试工具挂载到全局对象
//   const debugTools = {
//     getStatus: getPreloadStatus,
//     preloadGroup: preloadRouteGroup,
//     getCacheInfo,
//     resetLoadStats
//   }

//   const windowAny = window as any
//   windowAny.__routePreloadDebug = debugTools

//   console.log('🔧 路由预加载调试工具已挂载到 window.__routePreloadDebug')
// }
