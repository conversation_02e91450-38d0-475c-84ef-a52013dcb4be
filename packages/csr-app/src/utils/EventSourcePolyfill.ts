import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface/report'
import { useUserStore } from '@/store'
import { useRechargeStore } from '@/store/recharge'
import router from '@/router'
import { Message } from '@/mobile/components/Message'
import { useStoryStore } from '@/store'
import { ErrorHandler } from './errorHandler'

// 全局 SSE 连接管理
const activeSSEConnections: Set<EventSourcePolyfill> = new Set()

/**
 * 跟踪所有正在进行的 fetch 请求
 * 用于存储所有待处理的请求的 AbortController 实例
 */
const pendingFetchRequests: Set<AbortController> = new Set()

/**
 * 注册一个 fetch 请求的 AbortController
 * @param controller - 要注册的 AbortController 实例
 * @returns 返回传入的 AbortController 实例，方便链式调用
 */
export function registerPendingFetchRequest(controller: AbortController) {
  pendingFetchRequests.add(controller)
  return controller
}

/**
 * 从注册表中移除一个 fetch 请求的 AbortController
 * @param controller - 要移除的 AbortController 实例
 */
export function unregisterPendingFetchRequest(controller: AbortController) {
  pendingFetchRequests.delete(controller)
}

/**
 * 取消所有活跃的 SSE 连接和正在进行的 fetch 请求
 * 这个函数会同时取消：
 * 1. 所有已建立的 SSE 连接
 * 2. 所有正在进行中的 fetch 请求（包括那些还未收到响应的）
 */
export function cancelAllSSEConnections() {
  console.log(
    `Cancelling ${activeSSEConnections.size} active SSE connections and ${pendingFetchRequests.size} pending fetch requests`
  )

  // 取消所有活跃的 SSE 连接
  activeSSEConnections.forEach((connection) => {
    connection.close()
  })
  activeSSEConnections.clear()

  // 取消所有正在进行的 fetch 请求
  pendingFetchRequests.forEach((controller) => {
    controller.abort()
  })
  pendingFetchRequests.clear()
}

// 上报故事和角色付费成功
export function reportPaymentSuccessForStoryAndCharacter() {
  const storyStore = useStoryStore()
  // @ts-ignore
  if (window.fbq) {
    // @ts-ignore
    window.fbq('trackCustom', 'PaymentSuccessForStoryAndCharacter', {
      storyId: storyStore.currentStory?.id,
      actorId: storyStore.currentActor?.id,
      amount: storyStore.currentActor?.coins
    })
  }
  reportEvent(ReportEvent.PaymentSuccessForStoryAndCharacter, {
    storyId: storyStore.currentStory?.id,
    actorId: storyStore.currentActor?.id,
    amount: storyStore.currentActor?.coins
  })
}

// EventSource 的 Polyfill 实现
export class EventSourcePolyfill {
  private url: string
  private headers: Record<string, string>
  private method: string
  private body?: string
  private abortController: AbortController
  private isConnected: boolean = true
  private isStartChat: boolean = false
  private onStreamClose?: () => Promise<void> | void
  private maxRetries: number = 3
  private retryDelay: number = 1000 // Initial retry delay in ms
  onmessage: ((event: MessageEvent) => void) | null = null
  onerror: ((error: any) => void) | null = null

  constructor(
    url: string,
    options: {
      headers: Record<string, string>
      method?: 'GET' | 'POST'
      body?: string
      isStartChat?: boolean
      onStreamClose?: () => Promise<void> | void
      maxRetries?: number
    }
  ) {
    this.url = url
    this.headers = options.headers
    this.method = options.method || 'GET'
    this.body = options.body
    this.isStartChat = options.isStartChat || false
    this.onStreamClose = options.onStreamClose
    this.maxRetries = options.maxRetries || 3
    this.abortController = new AbortController()

    // 将当前连接添加到活跃连接集合中
    activeSSEConnections.add(this)

    this.connect()
  }
  // 因为polyfill实现无法在浏览器控制台的EventStream子标签中显示事件数据。
  // 所以这里使用console来打印事件数据以方便调试。
  private logEventStream(data: any, description: string = 'SSE Event') {
    const EventStreamTag = [
      `%cEventStream`,
      'color: white;background:#C5C5C5;font-weight: bold; font-size:8px; padding:2px 6px; border-radius: 5px'
    ]

    console.groupCollapsed(...EventStreamTag, description)
    if (Array.isArray(data)) {
      console.table(data)
    } else {
      console.log(data)
    }
    console.groupEnd()
  }

  private async connect(retryCount = 0) {
    try {
      // Set up timeout controller
      const timeoutDuration = 30000 // 30 seconds timeout
      const timeoutController = new AbortController()
      const timeoutId = setTimeout(() => {
        timeoutController.abort()
      }, timeoutDuration)

      // Combine timeout signal with existing abort signal
      const combinedController = new AbortController()
      Promise.race([
        this.abortController.signal.addEventListener('abort', () => combinedController.abort()),
        timeoutController.signal.addEventListener('abort', () => combinedController.abort())
      ])

      // 注册这个请求到全局跟踪器
      registerPendingFetchRequest(combinedController)

      // Record start time for API metrics
      const startTime = Date.now()

      const response = await fetch(this.url, {
        method: this.method,
        headers: {
          Accept: 'text/event-stream',
          'Cache-Control': 'no-cache',
          ...this.headers
        },
        body: this.body,
        signal: combinedController.signal
      })

      // 请求完成后从跟踪器中移除
      unregisterPendingFetchRequest(combinedController)

      // Calculate request duration
      const duration = Date.now() - startTime

      // Report API metrics
      reportEvent(ReportEvent.ApiRequest, {
        url: this.url,
        method: this.method,
        status: response.status,
        duration,
        route: window.location.pathname
      })

      console.log(
        '%c[DEBUG] Request Body:',
        'background: #2196F3; color: white; padding: 2px 6px; border-radius: 4px; font-weight: bold',
        this.body
      )
      // Clear timeout if request succeeds
      clearTimeout(timeoutId)

      if (!response.ok) {
        // 尝试解析错误响应
        const errorData = await response.json()

        // Report API error using enhanced error handler
        ErrorHandler.handleApiError(
          new Error(`EventSource API Error: ${response.status} ${response.statusText}`),
          {
            url: this.url,
            method: this.method,
            status: response.status,
            duration,
            route: window.location.pathname,
            error: errorData
          }
        )

        if (errorData?.code === '99001') {
          // 如果用户为游客，跳转到登录页
          const userStore = useUserStore()
          const storyStore = useStoryStore()
          storyStore.isNeedRecharge = true
          if (userStore.userInfo?.role === 'guest') {
            sessionStorage.setItem('login_redirect', window.location.href)
            router.push('/user/login')
            return
          }
          const rechargeStore = useRechargeStore()
          rechargeStore.toggleRechargeModal()
          if (this.isStartChat) {
            router.replace({
              name: 'StoryIntro',
              params: {
                storyId: storyStore.currentStory.id
              }
            })
          }
          return
        }

        if (response.status === 401 && retryCount < 1) {
          // 处理token过期逻辑，只尝试刷新一次
          const userStore = useUserStore()
          const refreshToken = userStore.storedRefreshToken
          if (refreshToken) {
            const refreshSuccess = await userStore.handleRefreshToken(refreshToken)
            if (refreshSuccess) {
              // 更新 headers 中的 token
              const newToken = localStorage.getItem('token')
              if (newToken) {
                this.headers.Authorization = `Bearer ${newToken}`
                return this.connect(retryCount + 1)
              }
            }
            // 如果刷新失败或无法获取新token，抛出错误
            throw new Error('Failed to refresh token')
          }
          // 重新自动注册（添加错误处理，避免死循环）
          try {
            const signupSuccess = await userStore.signUpAsGuest()
            if (!signupSuccess) {
              throw new Error('Guest signup failed')
            }
            // 更新 headers 中的 token
            const newToken = localStorage.getItem('token')
            if (newToken) {
              this.headers.Authorization = `Bearer ${newToken}`
              return this.connect(retryCount + 1)
            }
          } catch (signupError) {
            console.error('Failed to re-register as guest:', signupError)
            // 如果重新注册失败，不要继续重试，避免死循环
            throw new Error('Failed to re-authenticate')
          }
        }
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      if (!response.body) {
        throw new Error('Response body is null')
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''
      // 如果请求是成功购买并初始化对话，则上报故事和角色付费成功
      if (this.url.includes('game.start')) {
        const storyStore = useStoryStore()
        const story = storyStore.currentStory
        if (
          story &&
          !story?.is_purchased &&
          storyStore.currentActor?.coins > 0 &&
          !storyStore.currentActor?.is_purchased
        ) {
          storyStore.isNeedRecharge = false
          reportPaymentSuccessForStoryAndCharacter()
        }
      }
      const processStream = async () => {
        while (this.isConnected) {
          const { value, done } = await reader.read()
          if (done) {
            this.logEventStream('Stream completed', 'SSE Stream End')
            if (this.onStreamClose) {
              await this.onStreamClose()
            }
            break
          }

          const chunk = decoder.decode(value, { stream: true })
          try {
            // Parse JSON data
            const jsonData = JSON.parse(chunk)
            this.logEventStream(jsonData, 'SSE JSON Message')

            if (this.onmessage) {
              this.onmessage(new MessageEvent('message', { data: jsonData }))
            }
          } catch (e) {
            // Handle non-JSON data
            buffer += chunk
            let currentMessage = ''
            let braceCount = 0
            const messages = []

            // Parse character by character to properly split JSON objects
            for (const char of buffer) {
              currentMessage += char
              if (char === '{') braceCount++
              if (char === '}') {
                braceCount--
                if (braceCount === 0) {
                  // Complete JSON object found
                  try {
                    const parsedMessage = JSON.parse(currentMessage)
                    messages.push(parsedMessage)
                    currentMessage = ''
                  } catch (e) {
                    this.logEventStream(`Failed to parse message: ${currentMessage}`, 'Error')
                  }
                }
              }
            }

            // Keep any incomplete message in buffer
            buffer = currentMessage

            // Process each complete message
            for (const message of messages) {
              this.logEventStream(JSON.stringify(message), 'SSE Parsed Message')

              if (this.onmessage) {
                this.onmessage(
                  new MessageEvent('message', {
                    data: JSON.stringify(message)
                  })
                )
              }
            }
          }
        }

        if (!this.isConnected) {
          this.logEventStream('Connection closed by client', 'SSE Connection Status')
          if (this.onStreamClose) {
            await this.onStreamClose()
          }
        }
      }

      await processStream()
    } catch (error) {
      console.error('SSE connection error:', error)
      this.logEventStream(error, 'SSE Error')

      // Report connection error
      reportEvent(ReportEvent.ApiUnknownError, {
        url: this.url,
        method: this.method,
        code: error.name || 'CONNECTION_ERROR',
        message: error.message || 'SSE connection error',
        route: window.location.pathname
      })

      // Implement retry logic
      if (retryCount < this.maxRetries && this.isConnected) {
        const currentDelay = this.retryDelay * Math.pow(2, retryCount)
        console.log(
          `Retrying connection in ${currentDelay}ms (attempt ${retryCount + 1}/${this.maxRetries})`
        )

        await new Promise((resolve) => setTimeout(resolve, currentDelay))
        return this.connect(retryCount + 1)
      }

      // If max retries reached or connection manually closed, show error message
      if (this.isConnected) {
        Message.error(
          'Sorry, the network is unstable, please try again or switch to another story to continue'
        )
      }

      // Dispatch error event
      const errorEvent = new Event('error')
      Object.defineProperty(errorEvent, 'error', { value: error })
      this.dispatchEvent(errorEvent)
    }
  }

  private dispatchEvent(event: Event) {
    if (event.type === 'error' && this.onerror) {
      this.onerror(event)
    }
  }

  close() {
    this.logEventStream('Connection closing', 'SSE Close Request')
    this.isConnected = false
    this.abortController.abort()

    // 从活跃连接集合中移除当前连接
    activeSSEConnections.delete(this)

    this.logEventStream('Connection aborted', 'SSE Close Complete')
  }
}
