/**
 * 智能资源预加载器
 * 根据用户行为和网络状况智能预加载资源
 */

interface PreloadResource {
  url: string
  type: 'script' | 'style' | 'image' | 'font' | 'video' | 'audio'
  priority: 'high' | 'medium' | 'low'
  condition?: () => boolean
}

class ResourcePreloader {
  private preloadedResources = new Set<string>()
  private networkInfo: any = null
  private isSlowNetwork = false

  constructor() {
    this.detectNetworkCondition()
  }

  /**
   * 检测网络状况
   */
  private detectNetworkCondition(): void {
    // @ts-ignore
    this.networkInfo = navigator.connection || navigator.mozConnection || navigator.webkitConnection

    if (this.networkInfo) {
      this.isSlowNetwork =
        this.networkInfo.effectiveType === 'slow-2g' ||
        this.networkInfo.effectiveType === '2g' ||
        this.networkInfo.saveData === true

      console.log(`📶 网络状况: ${this.networkInfo.effectiveType}, 慢网络: ${this.isSlowNetwork}`)
    }
  }

  /**
   * 预加载单个资源
   */
  async preloadResource(resource: PreloadResource): Promise<void> {
    // 检查条件
    if (resource.condition && !resource.condition()) {
      return
    }

    // 避免重复预加载
    if (this.preloadedResources.has(resource.url)) {
      return
    }

    // 慢网络下只预加载高优先级资源
    if (this.isSlowNetwork && resource.priority !== 'high') {
      return
    }

    try {
      const link = document.createElement('link')

      switch (resource.type) {
        case 'script':
          link.rel = 'modulepreload'
          link.href = resource.url
          break
        case 'style':
          link.rel = 'preload'
          link.as = 'style'
          link.href = resource.url
          break
        case 'image':
          link.rel = 'preload'
          link.as = 'image'
          link.href = resource.url
          break
        case 'font':
          link.rel = 'preload'
          link.as = 'font'
          link.type = 'font/woff2'
          link.crossOrigin = 'anonymous'
          link.href = resource.url
          break
        case 'video':
          link.rel = 'preload'
          link.as = 'video'
          link.href = resource.url
          break
        case 'audio':
          link.rel = 'preload'
          link.as = 'audio'
          link.href = resource.url
          break
      }

      document.head.appendChild(link)
      this.preloadedResources.add(resource.url)

      console.log(`✅ 预加载资源: ${resource.url} (${resource.type}, ${resource.priority})`)
    } catch (error) {
      console.warn(`❌ 预加载失败: ${resource.url}`, error)
    }
  }

  /**
   * 批量预加载资源
   */
  async preloadResources(resources: PreloadResource[]): Promise<void> {
    // 按优先级排序
    const sortedResources = resources.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })

    // 分批预加载，避免阻塞主线程
    const batchSize = this.isSlowNetwork ? 2 : 5
    for (let i = 0; i < sortedResources.length; i += batchSize) {
      const batch = sortedResources.slice(i, i + batchSize)
      await Promise.all(batch.map((resource) => this.preloadResource(resource)))

      // 给主线程一些时间
      await new Promise((resolve) => setTimeout(resolve, 10))
    }
  }

  /**
   * 预加载关键路由组件
   */
  preloadCriticalRoutes(): void {
    const criticalRoutes: PreloadResource[] = [
      {
        url: '/src/mobile/views/stories/index.vue',
        type: 'script',
        priority: 'high',
        condition: () => window.innerWidth <= 768
      },
      {
        url: '/src/pc/views/stories/index.vue',
        type: 'script',
        priority: 'high',
        condition: () => window.innerWidth > 768
      },
      {
        url: '/src/shared/components/StoryCard.vue',
        type: 'script',
        priority: 'high'
      },
      {
        url: '/src/mobile/components/VirtualStoryGrid.vue',
        type: 'script',
        priority: 'medium',
        condition: () => window.innerWidth <= 768
      },
      {
        url: '/src/pc/components/VirtualStoryGrid.vue',
        type: 'script',
        priority: 'medium',
        condition: () => window.innerWidth > 768
      }
    ]

    this.preloadResources(criticalRoutes)
  }

  /**
   * 预加载关键图片
   */
  preloadCriticalImages(): void {
    const criticalImages: PreloadResource[] = [
      {
        url: 'https://cdn.magiclight.ai/assets/playshot/logo-v2.png',
        type: 'image',
        priority: 'high'
      },
      {
        url: 'https://static.playshot.ai/static/images/icon/playshot-icon.png',
        type: 'image',
        priority: 'medium'
      }
    ]

    this.preloadResources(criticalImages)
  }

  /**
   * 智能预加载 - 根据用户行为预测
   */
  smartPreload(): void {
    // 监听用户交互，预测可能访问的资源
    let hoverTimer: number

    document.addEventListener('mouseover', (event) => {
      const target = event.target as HTMLElement

      // 鼠标悬停在故事卡片上时预加载详情页资源
      if (target.closest('.story-card-item')) {
        hoverTimer = window.setTimeout(() => {
          this.preloadStoryDetailResources()
        }, 500) // 500ms 后开始预加载
      }
    })

    document.addEventListener('mouseout', () => {
      if (hoverTimer) {
        clearTimeout(hoverTimer)
      }
    })

    // 监听滚动，预加载即将进入视口的内容
    let scrollTimer: number
    window.addEventListener(
      'scroll',
      () => {
        if (scrollTimer) {
          clearTimeout(scrollTimer)
        }

        scrollTimer = window.setTimeout(() => {
          this.preloadViewportResources()
        }, 100)
      },
      { passive: true }
    )
  }

  /**
   * 预加载故事详情页资源
   */
  private preloadStoryDetailResources(): void {
    const storyDetailResources: PreloadResource[] = [
      {
        url: '/src/mobile/views/story/index.vue',
        type: 'script',
        priority: 'medium',
        condition: () => window.innerWidth <= 768
      },
      {
        url: '/src/pc/views/story/index.vue',
        type: 'script',
        priority: 'medium',
        condition: () => window.innerWidth > 768
      }
    ]

    this.preloadResources(storyDetailResources)
  }

  /**
   * 预加载即将进入视口的资源
   */
  private preloadViewportResources(): void {
    // 这里可以根据滚动位置预加载即将显示的内容
    console.log('🔍 预加载视口资源')
  }

  /**
   * 获取预加载统计
   */
  getStats(): {
    preloadedCount: number
    networkType: string
    isSlowNetwork: boolean
  } {
    return {
      preloadedCount: this.preloadedResources.size,
      networkType: this.networkInfo?.effectiveType || 'unknown',
      isSlowNetwork: this.isSlowNetwork
    }
  }
}

// 创建全局实例
export const resourcePreloader = new ResourcePreloader()

// 自动启动预加载
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    resourcePreloader.preloadCriticalImages()
    resourcePreloader.smartPreload()

    // 延迟预加载路由组件
    setTimeout(() => {
      resourcePreloader.preloadCriticalRoutes()
    }, 1000)
  })
} else {
  resourcePreloader.preloadCriticalImages()
  resourcePreloader.smartPreload()

  setTimeout(() => {
    resourcePreloader.preloadCriticalRoutes()
  }, 1000)
}

export default resourcePreloader
