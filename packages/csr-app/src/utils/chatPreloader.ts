/**
 * 聊天页面资源预加载优化工具
 * 用于在进入聊天页面前预热关键资源，减少加载时间
 */

interface PreloadResource {
  url: string
  type: 'image' | 'video' | 'audio'
  priority: 'high' | 'medium' | 'low'
}

interface PreloadOptions {
  maxConcurrent?: number
  timeout?: number
  retryCount?: number
}

class ChatPreloader {
  private static instance: ChatPreloader
  private preloadCache = new Map<string, Promise<void>>()
  private loadingResources = new Set<string>()

  static getInstance(): ChatPreloader {
    if (!ChatPreloader.instance) {
      ChatPreloader.instance = new ChatPreloader()
    }
    return ChatPreloader.instance
  }

  /**
   * 预加载图片资源
   */
  private async preloadImage(url: string, timeout = 5000): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      const timer = setTimeout(() => {
        reject(new Error(`Image load timeout: ${url}`))
      }, timeout)

      img.onload = () => {
        clearTimeout(timer)
        resolve()
      }

      img.onerror = () => {
        clearTimeout(timer)
        reject(new Error(`Failed to load image: ${url}`))
      }

      img.src = url
    })
  }

  /**
   * 预加载视频资源（仅预加载元数据）
   */
  private async preloadVideo(url: string, timeout = 8000): Promise<void> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video')
      const timer = setTimeout(() => {
        reject(new Error(`Video load timeout: ${url}`))
      }, timeout)

      video.onloadedmetadata = () => {
        clearTimeout(timer)
        resolve()
      }

      video.onerror = () => {
        clearTimeout(timer)
        reject(new Error(`Failed to load video: ${url}`))
      }

      video.preload = 'metadata'
      video.src = url
    })
  }

  /**
   * 预加载音频资源
   */
  private async preloadAudio(url: string, timeout = 5000): Promise<void> {
    return new Promise((resolve, reject) => {
      const audio = new Audio()
      const timer = setTimeout(() => {
        reject(new Error(`Audio load timeout: ${url}`))
      }, timeout)

      audio.oncanplaythrough = () => {
        clearTimeout(timer)
        resolve()
      }

      audio.onerror = () => {
        clearTimeout(timer)
        reject(new Error(`Failed to load audio: ${url}`))
      }

      audio.preload = 'auto'
      audio.src = url
    })
  }

  /**
   * 预加载单个资源
   */
  private async preloadResource(resource: PreloadResource, options: PreloadOptions = {}): Promise<void> {
    const { timeout = 10000, retryCount = 2 } = options

    // 如果已经在加载或已加载，返回现有的Promise
    if (this.preloadCache.has(resource.url)) {
      return this.preloadCache.get(resource.url)!
    }

    // 如果正在加载中，等待完成
    if (this.loadingResources.has(resource.url)) {
      return new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (!this.loadingResources.has(resource.url)) {
            clearInterval(checkInterval)
            resolve()
          }
        }, 100)
      })
    }

    this.loadingResources.add(resource.url)

    const loadPromise = this.attemptLoad(resource, timeout, retryCount)
    this.preloadCache.set(resource.url, loadPromise)

    try {
      await loadPromise
    } finally {
      this.loadingResources.delete(resource.url)
    }
  }

  /**
   * 尝试加载资源（带重试机制）
   */
  private async attemptLoad(resource: PreloadResource, timeout: number, retryCount: number): Promise<void> {
    let lastError: Error | null = null

    for (let i = 0; i <= retryCount; i++) {
      try {
        switch (resource.type) {
          case 'image':
            await this.preloadImage(resource.url, timeout)
            break
          case 'video':
            await this.preloadVideo(resource.url, timeout)
            break
          case 'audio':
            await this.preloadAudio(resource.url, timeout)
            break
          default:
            throw new Error(`Unsupported resource type: ${resource.type}`)
        }
        return // 成功加载，退出重试循环
      } catch (error) {
        lastError = error as Error
        if (i < retryCount) {
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
        }
      }
    }

    // 所有重试都失败了
    console.warn(`Failed to preload resource after ${retryCount + 1} attempts:`, resource.url, lastError)
    throw lastError
  }

  /**
   * 批量预加载资源
   */
  async preloadResources(resources: PreloadResource[], options: PreloadOptions = {}): Promise<void> {
    const { maxConcurrent = 3 } = options

    // 按优先级排序
    const sortedResources = [...resources].sort((a, b) => {
      const priorityOrder = { high: 0, medium: 1, low: 2 }
      return priorityOrder[a.priority] - priorityOrder[b.priority]
    })

    // 分批并发加载
    for (let i = 0; i < sortedResources.length; i += maxConcurrent) {
      const batch = sortedResources.slice(i, i + maxConcurrent)
      const batchPromises = batch.map(resource => 
        this.preloadResource(resource, options).catch(error => {
          console.warn('Resource preload failed:', resource.url, error)
          // 不抛出错误，允许其他资源继续加载
        })
      )
      
      await Promise.allSettled(batchPromises)
    }
  }

  /**
   * 预热聊天页面关键资源
   */
  async preloadChatResources(actorId: string, storyId: string): Promise<void> {
    try {
      // 这里可以根据actorId和storyId获取需要预加载的资源列表
      // 暂时使用示例资源
      const resources: PreloadResource[] = [
        // 可以从store或API获取角色相关资源
      ]

      await this.preloadResources(resources, {
        maxConcurrent: 2,
        timeout: 5000,
        retryCount: 1
      })
    } catch (error) {
      console.warn('Chat resources preload failed:', error)
    }
  }

  /**
   * 清理预加载缓存
   */
  clearCache(): void {
    this.preloadCache.clear()
    this.loadingResources.clear()
  }

  /**
   * 检查资源是否已预加载
   */
  isPreloaded(url: string): boolean {
    return this.preloadCache.has(url) && !this.loadingResources.has(url)
  }
}

// 导出单例实例
export const chatPreloader = ChatPreloader.getInstance()

// 导出类型
export type { PreloadResource, PreloadOptions }
