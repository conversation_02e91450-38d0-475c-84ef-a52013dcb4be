// import { GenderType, ProjectStyle } from '@/api'
import { $t } from './i18n'
// import wx from 'weixin-js-sdk'
import isMobile from 'ismobilejs'
// import { AgeType } from '@/api'
import { compareVersions } from './tools'

// import { FastClick } from 'fastclick'

// export const genderMap = (value: number) => {
//   return {
//     [GenderType.Male]: $t('common.man'),
//     [GenderType.Female]: $t('common.woman'),
//     [GenderType.Unknown]: $t('common.unknown')
//   }[value]
// }

// export const ageMap = (value: number) => {
//   return {
//     [AgeType.Child]: $t('common.child'),
//     [AgeType.Teenager]: $t('common.teen'),
//     [AgeType.Youths]: $t('common.youngAdult'),
//     [AgeType.Modern]: $t('common.adult'),
//     [AgeType.Elderly]: $t('common.senior'),
//     [AgeType.Unknown]: $t('common.unknown')
//   }[value]
// }

// export const projectStyleMap = (id: string, projectStyles: ProjectStyle[]) => {
//   const projectStyleMap = projectStyles.reduce((prev, cur) => {
//     prev[cur.id] = cur.styleName
//     return prev
//   }, {})
//   return projectStyleMap[id] ?? ''
// }

export const stageMap = (value: number) => {
  return {
    0: $t('utils.util.inQueue'),
    1: $t('utils.util.imageGenerating'),
    2: $t('utils.util.waitPublish'),
    3: $t('utils.util.genFail')
  }[value]
}

// export function isMobile() {
//   return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
// }

// 是否在 app 环境下
export function isAppEnv() {
  return /com.aibrm.app/i.test(navigator.userAgent)
}

// 获取原生 app 版本
export function getNativeAppVersion() {
  const match = /com\.aibrm\.app\s?([\d.]+)?/i.exec(navigator.userAgent)
  const mobileInfo = isMobile()
  if (match) {
    return {
      isApp: true,
      isAndroid: mobileInfo.android.device,
      isIOS: mobileInfo.apple.device,
      version: match[1] || '1.0.0'
    }
  }
  return {
    isApp: false,
    version: '',
    isAndroid: mobileInfo.android.device,
    isIOS: mobileInfo.apple.device
  }
}

export function isMobileEnv() {
  return isMobile().any
}

export function getAppVersion() {
  return process.env.version || '1.0.0'
}

export function isSupportWebp() {
  return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp') === 0
}

// 是否支持支付
// export async function supportPayment() {
//   const env = await getBrowserEnv()
//   const appInfo = getNativeAppVersion()
//   // 只有安卓和 h5 才显示充值入口
//   if (
//     BrowserType.WxMiniProgram === env ||
//     (appInfo.isApp && (compareVersions(appInfo.version, '1.1.6') === -1 || appInfo.isIOS))
//   ) {
//     return false
//   }
//   return true
// }

// export enum BrowserType {
//   WxMiniProgram = 'wxMiniProgram', // 微信小程序
//   WxWeb = 'wxWeb', // 微信内 webview
//   App = 'app', // app
//   Alipay = 'alipay',
//   Others = 'others' // 其他
// }

// export function getBrowserEnv(): Promise<BrowserType> {
//   return new Promise((resolve) => {
//     if (isAppEnv()) {
//       resolve(BrowserType.App)
//       return
//     }
//     const ua = navigator.userAgent.toLowerCase()
//     console.log(ua, 'ua')
//     if (/alipayclient/gi.test(ua)) {
//       resolve(BrowserType.Alipay)
//       return
//     }
//     if (ua.match(/MicroMessenger/i)?.[0] === 'micromessenger') {
//       //微信环境下
//       wx.miniProgram.getEnv((res) => {
//         if (res.miniprogram) {
//           // 小程序环境下逻辑
//           resolve(BrowserType.WxMiniProgram)
//         } else {
//           //非小程序环境下逻辑
//           resolve(BrowserType.WxWeb)
//         }
//       })
//     } else {
//       resolve(BrowserType.Others)
//     }
//   })
// }
export function formatNumber(num: number) {
  if (!num) return 0
  if (num < 10000) return num
  return (num / 10000).toFixed(1) + '万'
}
// 兼容移动端ios复制
export function copyText(text: string | number): Promise<boolean> {
  return new Promise((resolve) => {
    const textString = text.toString()

    // 优先使用 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard.writeText(textString).then(
        () => resolve(true),
        (err) => {
          console.error('Clipboard API 复制失败:', err)
          fallbackCopyMethod(textString)
        }
      )
    } else {
      fallbackCopyMethod(textString)
    }
    function fallbackCopyMethod(textString: string) {
      const input = document.createElement('input')
      input.readOnly = true // 防止 iOS 聚焦触发键盘事件
      input.style.position = 'absolute'
      input.style.left = '-10000px' // 移动得更远，避免视觉上的闪烁
      input.style.opacity = '0' // 完全隐藏，避免白底
      document.body.appendChild(input)
      input.value = textString

      selectText(input, 0, textString.length)

      setTimeout(() => {
        try {
          const successful = document.execCommand('copy')
          if (!successful) {
            console.error('备用方法复制失败')
            resolve(false)
          } else {
            resolve(true)
          }
        } catch (err) {
          console.error('备用方法无法复制文本:', err)
          resolve(false)
        } finally {
          input.blur()
          document.body.removeChild(input) // 复制完成后立即移除
        }
      }, 0)
    }
    function selectText(textbox: HTMLInputElement, startIndex: number, stopIndex: number): void {
      textbox.setSelectionRange(startIndex, stopIndex)
      textbox.focus({ preventScroll: true }) // 阻止页面滚动
    }
  })
}
// H5键盘监控弹出（KeyboardUp）、收起（KeyboardDown）事件定义
export function monitorKeyboardEvents() {
  const isAndroid = isMobile().android.device
  const isiOS = isMobile().apple.device
  if (!isAndroid || !isiOS) {
    return
  }
  const KeyboardUpEvent = document.createEvent('KeyboardEvent')
  KeyboardUpEvent.initEvent('KeyboardUp', true, true)

  const KeyboardDownEvent = document.createEvent('KeyboardEvent')
  KeyboardDownEvent.initEvent('KeyboardDown', true, true)

  let resizeListener: (() => void) | null = null
  let focusinListener: (() => void) | null = null
  let focusoutListener: (() => void) | null = null

  if (isAndroid) {
    const originalHeight = document.documentElement.clientHeight || document.body.clientHeight
    resizeListener = function () {
      const resizeHeight = document.documentElement.clientHeight || document.body.clientHeight
      if (resizeHeight - 0 < originalHeight - 0) {
        document.dispatchEvent(KeyboardUpEvent)
      } else {
        document.dispatchEvent(KeyboardDownEvent)
      }
    }
    window.addEventListener('resize', resizeListener)
  } else if (isiOS) {
    focusinListener = function () {
      document.dispatchEvent(KeyboardUpEvent)
    }
    focusoutListener = function () {
      document.dispatchEvent(KeyboardDownEvent)
    }
    document.body.addEventListener('focusin', focusinListener)
    document.body.addEventListener('focusout', focusoutListener)
  } else {
    throw $t('utils.util.unrecognizedModel')
  }

  return function destroyListeners() {
    if (resizeListener) {
      window.removeEventListener('resize', resizeListener)
    }
    if (focusinListener) {
      document.body.removeEventListener('focusin', focusinListener)
    }
    if (focusoutListener) {
      document.body.removeEventListener('focusout', focusoutListener)
    }
  }
}

// 设置html标签的style属性，将所有的主题色变量都添加到行内样式
export const setStyle = (styles) => {
  if (styles) {
    for (const key in styles) {
      document.getElementsByTagName('html')[0].style.setProperty(`${key}`, styles[key])
    }
  }
}

export function isEmail(email: string): boolean {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  return emailRegex.test(email)
}

export function isValidPassword(password: string): boolean {
  const passwordReg =
    /^(?=.*[A-Za-z`~!@#$%^&*()\-_=+\[\]{};:'",.<>\/?\\|])(?=.*\d)[A-Za-z`~!@#$%^&*()\-_=+\[\]{};:'",.<>\/?\\|\d]{8,30}$/

  return passwordReg.test(password)
}

export const getCurrentUrl = () => {
  return window.location.href
}

export const isInAppBrowser = () => {
  const ua = window.navigator.userAgent.toLowerCase()
  return (
    ua.includes('micromessenger') || // WeChat
    ua.includes('weibo') || // Weibo
    ua.includes('qq') || // QQ Browser
    ua.includes('douban') || // Douban
    ua.includes('facebook') || // Facebook in-app
    ua.includes('instagram') || // Instagram in-app
    ua.includes('twitter') || // Twitter in-app
    ua.includes('line') || // Line
    ua.includes('linkedin') || // LinkedIn in-app
    /\bfb[\w-]+\//.test(ua) || // Facebook WebView
    ua.includes('instagram') || // Instagram WebView
    ua.includes('snapchat') // Snapchat WebView
  )
}

// 生成Canvas指纹
export function generateCanvasFingerprint(): string {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  if (!ctx) return ''

  // 设置canvas大小
  canvas.width = 220
  canvas.height = 30

  // 填充背景
  ctx.fillStyle = 'rgb(255,255,255)'
  ctx.fillRect(0, 0, 220, 30)

  // 渲染文本
  ctx.fillStyle = '#E13300'
  ctx.font = '14px Arial'
  ctx.fillText('Canvas Fingerprint 🎨', 2, 15)

  // 渲染图形
  ctx.strokeStyle = 'rgb(200,0,0)'
  ctx.beginPath()
  ctx.moveTo(10, 5)
  ctx.lineTo(15, 25)
  ctx.lineTo(20, 5)
  ctx.stroke()

  // 添加渐变
  const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0)
  gradient.addColorStop(0, 'blue')
  gradient.addColorStop(1, 'green')
  ctx.fillStyle = gradient
  ctx.fillRect(150, 5, 60, 20)

  return canvas.toDataURL()
}

// 生成或获取设备ID
export function getDeviceId(): string {
  const deviceId = localStorage.getItem('device_id')
  if (deviceId) {
    return deviceId
  }

  const fingerprint = generateCanvasFingerprint()
  const hashArray = new Uint8Array(fingerprint.length)
  for (let i = 0; i < fingerprint.length; i++) {
    hashArray[i] = fingerprint.charCodeAt(i)
  }
  const newDeviceId = Array.from(hashArray)
    .map((b) => b.toString(16).padStart(2, '0'))
    .join('')
    .slice(0, 16)
  return newDeviceId
}
