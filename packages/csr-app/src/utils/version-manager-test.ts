/**
 * 版本管理器测试工具
 * 仅在开发环境使用
 */

import { versionManager, initVersionManager } from './version-manager'

// 仅在开发环境下启用
if (import.meta.env.DEV) {
  console.log('🧪 版本管理器测试工具已加载')

  // 创建测试工具对象
  const versionTestTools = {
    /**
     * 手动触发版本检查
     */
    async checkForUpdates() {
      console.log('🔍 手动触发版本检查...')
      await versionManager.checkForUpdates()
    },

    /**
     * 获取当前版本信息
     */
    getCurrentVersion() {
      const version = versionManager.getCurrentVersionInfo()
      console.log('📦 当前版本信息:', version)
      return version
    },

    /**
     * 模拟新版本可用（静默更新）
     */
    simulateNewVersionSilent() {
      console.log('🎭 模拟新版本可用（静默更新）...')

      const currentVersion = versionManager.getCurrentVersionInfo()
      if (!currentVersion) {
        console.warn('无法获取当前版本信息')
        return
      }

      const mockNewVersion = {
        version: '1.4.0',
        buildTime: Date.now() + 1000,
        timestamp: Date.now() + 1000
      }

      // 直接调用版本管理器的更新处理逻辑，而不是触发事件
      const manager = versionManager.getInstance()
      console.log('🔄 触发静默更新逻辑')

      // 模拟静默更新（在测试环境下不实际刷新）
      setTimeout(() => {
        console.log('🔄 模拟静默更新执行')
        console.log('在生产环境中，这里会清除缓存并刷新页面')
        console.log('✅ 静默更新模拟完成')
      }, 1000)
    },

    /**
     * 模拟新版本可用（带通知）
     */
    simulateNewVersionWithNotification() {
      console.log('🎭 模拟新版本可用（带通知）...')

      const currentVersion = versionManager.getCurrentVersionInfo()
      if (!currentVersion) {
        console.warn('无法获取当前版本信息')
        return
      }

      const mockNewVersion = {
        version: '1.4.0',
        buildTime: Date.now() + 1000,
        timestamp: Date.now() + 1000
      }

      // 触发版本更新事件（会显示通知弹窗）
      const event = new CustomEvent('version-update-available', {
        detail: {
          newVersion: mockNewVersion,
          currentVersion,
          onUpdate: () => {
            console.log('🔄 用户选择立即更新')
            console.log('在生产环境中，这里会刷新页面')
          },
          onLater: () => {
            console.log('⏰ 用户选择推迟更新')
          }
        }
      })

      window.dispatchEvent(event)
    },

    /**
     * 创建带通知的版本管理器实例
     */
    createNotificationVersionManager() {
      console.log('🔔 创建带通知的版本管理器...')

      return initVersionManager({
        checkInterval: 10 * 1000, // 10秒检查一次（测试用）
        showUpdateNotification: true,
        autoUpdate: false,
        onUpdateAvailable: (newVersion, currentVersion) => {
          console.log('🆕 检测到新版本（带通知）:', {
            current: currentVersion.version,
            latest: newVersion.version
          })
        },
        onUpdateError: (error) => {
          console.error('❌ 版本检查错误（带通知）:', error)
        }
      })
    },

    /**
     * 创建静默更新的版本管理器实例
     */
    createSilentVersionManager() {
      console.log('🔇 创建静默更新的版本管理器...')

      return initVersionManager({
        checkInterval: 10 * 1000, // 10秒检查一次（测试用）
        showUpdateNotification: false,
        autoUpdate: true,
        onUpdateAvailable: (newVersion, currentVersion) => {
          console.log('🆕 检测到新版本（静默）:', {
            current: currentVersion.version,
            latest: newVersion.version
          })
        },
        onUpdateError: (error) => {
          console.error('❌ 版本检查错误（静默）:', error)
        }
      })
    },

    /**
     * 清除版本缓存
     */
    clearVersionCache() {
      console.log('🗑️ 清除版本缓存...')
      localStorage.removeItem('app_version_info')
      console.log('✅ 版本缓存已清除')
    },

    /**
     * 模拟版本文件
     */
    mockVersionFile(version = '1.4.0') {
      console.log('📝 模拟版本文件...')

      const mockVersion = {
        version,
        buildTime: Date.now(),
        timestamp: Date.now()
      }

      // 创建一个模拟的 fetch 响应
      const originalFetch = window.fetch
      window.fetch = async (url, options) => {
        if (typeof url === 'string' && url.includes('/version.json')) {
          console.log('🎭 拦截版本文件请求，返回模拟数据')
          return new Response(JSON.stringify(mockVersion), {
            status: 200,
            headers: {
              'Content-Type': 'application/json'
            }
          })
        }
        return originalFetch(url, options)
      }

      console.log('✅ 版本文件模拟已设置，模拟版本:', mockVersion)

      // 返回恢复函数
      return () => {
        window.fetch = originalFetch
        console.log('🔄 已恢复原始 fetch 函数')
      }
    },

    /**
     * 运行静默更新测试流程
     */
    async runSilentUpdateTest() {
      console.log('🧪 开始静默更新测试流程...')

      try {
        // 1. 获取当前版本
        console.log('1️⃣ 获取当前版本信息')
        this.getCurrentVersion()

        // 2. 模拟静默更新
        console.log('2️⃣ 模拟静默更新')
        this.simulateNewVersionSilent()

        console.log('✅ 静默更新测试流程完成')
      } catch (error) {
        console.error('❌ 静默更新测试流程出错:', error)
      }
    },

    /**
     * 运行通知更新测试流程
     */
    async runNotificationUpdateTest() {
      console.log('🧪 开始通知更新测试流程...')

      try {
        // 1. 获取当前版本
        console.log('1️⃣ 获取当前版本信息')
        this.getCurrentVersion()

        // 2. 模拟通知更新
        console.log('2️⃣ 模拟通知更新（会显示弹窗）')
        this.simulateNewVersionWithNotification()

        console.log('✅ 通知更新测试流程完成')
      } catch (error) {
        console.error('❌ 通知更新测试流程出错:', error)
      }
    },

    /**
     * 运行完整测试流程（静默更新）
     */
    async runFullTest() {
      console.log('🧪 开始完整测试流程...')

      try {
        // 1. 获取当前版本
        console.log('1️⃣ 获取当前版本信息')
        this.getCurrentVersion()

        // 2. 清除缓存
        console.log('2️⃣ 清除版本缓存')
        this.clearVersionCache()

        // 3. 模拟新版本文件
        console.log('3️⃣ 模拟新版本文件')
        const restoreFetch = this.mockVersionFile('1.5.0')

        // 4. 手动检查更新
        console.log('4️⃣ 手动检查更新')
        await this.checkForUpdates()

        // 5. 模拟新版本可用事件（静默更新）
        console.log('5️⃣ 模拟新版本可用事件（静默更新）')
        setTimeout(() => {
          this.simulateNewVersionSilent()
        }, 2000)

        // 6. 恢复原始状态
        setTimeout(() => {
          console.log('6️⃣ 恢复原始状态')
          restoreFetch()
        }, 5000)

        console.log('✅ 完整测试流程已启动')
      } catch (error) {
        console.error('❌ 测试流程出错:', error)
      }
    }
  }

  // 将测试工具挂载到全局对象
  const windowAny = window as any
  windowAny.__versionManagerTest = versionTestTools

  console.log('🔧 版本管理器测试工具已挂载到 window.__versionManagerTest')
  console.log('💡 可用的测试方法:')
  console.log('  🔇 静默更新测试:')
  console.log('    - runSilentUpdateTest(): 运行静默更新测试（无弹窗）')
  console.log('    - simulateNewVersionSilent(): 模拟新版本可用（静默更新）')
  console.log('  🔔 通知更新测试:')
  console.log('    - runNotificationUpdateTest(): 运行通知更新测试（有弹窗）')
  console.log('    - simulateNewVersionWithNotification(): 模拟新版本可用（带通知）')
  console.log('  🛠️ 基础工具:')
  console.log('    - checkForUpdates(): 手动检查更新')
  console.log('    - getCurrentVersion(): 获取当前版本')
  console.log('    - clearVersionCache(): 清除版本缓存')
  console.log('    - mockVersionFile(version): 模拟版本文件')
  console.log('    - runFullTest(): 运行完整测试流程（静默更新）')
  console.log('  ⚙️ 管理器创建:')
  console.log('    - createNotificationVersionManager(): 创建带通知的版本管理器')
  console.log('    - createSilentVersionManager(): 创建静默更新的版本管理器')
}
