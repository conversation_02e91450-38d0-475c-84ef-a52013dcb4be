type MessageHandler = (data: any) => void

export class EventSourceWrapper {
  private eventSource: EventSource | null = null
  private url: string
  private handlers: Map<string, MessageHandler[]> = new Map()
  private reconnectAttempts = 0
  private maxReconnectAttempts = 3
  private reconnectTimeout = 3000

  constructor(url: string) {
    this.url = url
  }

  connect() {
    if (this.eventSource) {
      return
    }

    this.eventSource = new EventSource(this.url)

    this.eventSource.onopen = () => {
      console.log('SSE connection established')
      this.reconnectAttempts = 0
    }

    this.eventSource.onerror = (error) => {
      console.error('SSE connection error:', error)
      this.handleError()
    }

    this.eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        this.handlers.get('message')?.forEach((handler) => handler(data))
      } catch (error) {
        console.error('Error parsing SSE message:', error)
      }
    }

    // 注册所有已添加的事件处理器
    this.handlers.forEach((handlers, event) => {
      if (event !== 'message') {
        this.eventSource?.addEventListener(event, (e: MessageEvent) => {
          try {
            console.log('SSE event:', event, e.data)
            const data = JSON.parse(e.data)
            handlers.forEach((handler) => handler(data))
          } catch (error) {
            console.error(`Error parsing SSE ${event} event:`, error)
          }
        })
      }
    })
  }

  on(event: string, handler: MessageHandler) {
    if (!this.handlers.has(event)) {
      this.handlers.set(event, [])
    }
    this.handlers.get(event)?.push(handler)

    // 如果连接已建立，立即注册事件监听器
    if (this.eventSource && event !== 'message') {
      this.eventSource.addEventListener(event, (e: MessageEvent) => {
        try {
          const data = JSON.parse(e.data)
          handler(data)
        } catch (error) {
          console.error(`Error parsing SSE ${event} event:`, error)
        }
      })
    }
  }

  off(event: string, handler: MessageHandler) {
    const handlers = this.handlers.get(event)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index !== -1) {
        handlers.splice(index, 1)
      }
    }
  }

  close() {
    this.eventSource?.close()
    this.eventSource = null
    this.handlers.clear()
  }

  private handleError() {
    this.eventSource?.close()
    this.eventSource = null

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(
        `Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`
      )
      setTimeout(() => this.connect(), this.reconnectTimeout)
    } else {
      console.error('Max reconnection attempts reached')
    }
  }
}
