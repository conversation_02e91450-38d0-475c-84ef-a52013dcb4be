import * as Sentry from '@sentry/vue'
import { App } from 'vue'
import { Router } from 'vue-router'
import { getDeviceId, getAppVersion, isMobileEnv } from './util'
import { performanceMonitor } from './performance'

/**
 * Sentry 配置选项
 */
interface SentryConfig {
  dsn: string
  environment: string
  release: string
  sampleRate?: number
  tracesSampleRate?: number
  replaysSessionSampleRate?: number
  replaysOnErrorSampleRate?: number
}

/**
 * 获取 Sentry 配置
 */
function getSentryConfig(): SentryConfig | null {
  const dsn = import.meta.env.VITE_SENTRY_DSN
  const environment = import.meta.env.VITE_SENTRY_ENVIRONMENT

  // 如果没有配置 DSN，则不启用 Sentry
  if (!dsn || dsn.includes('your-sentry-dsn')) {
    console.warn('Sentry DSN 未配置，跳过 Sentry 初始化')
    return null
  }

  return {
    dsn,
    environment: environment || 'development',
    release: '1.0.0',
    // 提高生产环境采样率以获得更准确的性能数据
    sampleRate: environment === 'production' ? 1.0 : 1.0,
    tracesSampleRate: environment === 'production' ? 1.0 : 1.0,
    // Session Replay 配置
    replaysSessionSampleRate: environment === 'production' ? 0.02 : 0.1,
    replaysOnErrorSampleRate: 1.0
  }
}

/**
 * 初始化 Sentry
 */
export function initSentry(app: App, router: Router): boolean {
  const config = getSentryConfig()

  if (!config) {
    return false
  }

  try {
    Sentry.init({
      app,
      dsn: config.dsn,
      environment: config.environment,
      release: config.release,

      // 性能监控
      tracesSampleRate: config.tracesSampleRate,

      // Session Replay
      replaysSessionSampleRate: config.replaysSessionSampleRate,
      replaysOnErrorSampleRate: config.replaysOnErrorSampleRate,

      // 错误过滤
      beforeSend(event, hint) {
        // 过滤掉一些不重要的错误
        const error = hint.originalException

        // 过滤网络错误
        if (error && typeof error === 'object' && 'message' in error) {
          const message = (error as Error).message.toLowerCase()
          if (
            message.includes('network error') ||
            message.includes('fetch') ||
            message.includes('load failed') ||
            message.includes('script error')
          ) {
            return null
          }
        }

        // 过滤掉取消的请求
        if (event.exception?.values?.[0]?.value?.includes('AbortError')) {
          return null
        }

        return event
      },

      // 集成配置
      integrations: [
        Sentry.browserTracingIntegration({
          router,
          // 启用更详细的性能追踪
          enableLongTask: true,
          enableInp: true
        }),
        Sentry.replayIntegration({
          // 优化录屏配置以减少性能影响
          maskAllText: false,
          blockAllMedia: false,
          maskAllInputs: true
        })
      ],

      // 更精确的性能测量配置
      tracePropagationTargets: [
        'localhost',
        /^https:\/\/api\.playshot\.ai/,
        /^https:\/\/.*\.playshot\.ai/
      ],

      // 用户上下文
      initialScope: {
        user: {
          id: getDeviceId()
        },
        tags: {
          device_type: isMobileEnv() ? 'mobile' : 'desktop',
          app_version: getAppVersion(),
          app_name: import.meta.env.VITE_APP_NAME || 'PlayShot'
        },
        contexts: {
          app: {
            name: import.meta.env.VITE_APP_NAME || 'PlayShot',
            version: getAppVersion()
          }
        }
      }
    })

    // 集成自定义性能监控数据
    setupPerformanceIntegration()

    console.log('Sentry 初始化成功', {
      environment: config.environment,
      release: config.release,
      tracesSampleRate: config.tracesSampleRate
    })

    return true
  } catch (error) {
    console.error('Sentry 初始化失败:', error)
    return false
  }
}

/**
 * 设置用户信息
 */
export function setSentryUser(userInfo: { id?: string; email?: string; username?: string }) {
  Sentry.setUser({
    id: userInfo.id || getDeviceId(),
    email: userInfo.email,
    username: userInfo.username
  })
}

/**
 * 设置用户上下文
 */
export function setSentryContext(key: string, context: Record<string, any>) {
  Sentry.setContext(key, context)
}

/**
 * 添加面包屑
 */
export function addSentryBreadcrumb(
  message: string,
  category?: string,
  level?: Sentry.SeverityLevel,
  data?: Record<string, any>
) {
  Sentry.addBreadcrumb({
    message,
    category: category || 'custom',
    level: level || 'info',
    data
  })
}

/**
 * 手动捕获异常
 */
export function captureSentryException(error: Error, context?: Record<string, any>) {
  if (context) {
    Sentry.withScope((scope) => {
      Object.entries(context).forEach(([key, value]) => {
        scope.setContext(key, value)
      })
      Sentry.captureException(error)
    })
  } else {
    Sentry.captureException(error)
  }
}

/**
 * 手动发送消息
 */
export function captureSentryMessage(
  message: string,
  level?: Sentry.SeverityLevel,
  context?: Record<string, any>
) {
  if (context) {
    Sentry.withScope((scope) => {
      Object.entries(context).forEach(([key, value]) => {
        scope.setContext(key, value)
      })
      Sentry.captureMessage(message, level || 'info')
    })
  } else {
    Sentry.captureMessage(message, level || 'info')
  }
}

/**
 * 开始性能事务
 */
export function startSentryTransaction(name: string, op?: string) {
  return Sentry.startSpan(
    {
      name,
      op: op || 'custom'
    },
    () => {
      // 返回一个空的 span 函数
    }
  )
}

/**
 * 检查 Sentry 是否已初始化
 */
export function isSentryEnabled(): boolean {
  return Sentry.getClient() !== undefined
}

/**
 * 设置精确的性能监控集成
 * 区分真实页面加载时间和延迟加载时间
 */
function setupPerformanceIntegration(): void {
  // 监控真实的首屏加载时间（不包含延迟加载）
  const markFirstScreenComplete = () => {
    // 使用FCP作为首屏完成的标准，而不是等待延迟加载
    const fcpEntries = performance.getEntriesByName('first-contentful-paint')
    if (fcpEntries.length > 0) {
      const fcpTime = fcpEntries[0].startTime

      // 向Sentry发送真实的首屏时间
      Sentry.addBreadcrumb({
        message: 'First Screen Complete (Real)',
        category: 'performance',
        level: 'info',
        data: {
          fcp_time: fcpTime,
          timestamp: Date.now()
        }
      })

      // 设置自定义性能指标
      Sentry.setMeasurement('first_screen_time', fcpTime, 'millisecond')
    }
  }

  // 监控延迟加载时间
  const markDelayedLoadingComplete = () => {
    const delayedLoadingTime = performance.now()

    Sentry.addBreadcrumb({
      message: 'Delayed Loading Complete',
      category: 'performance',
      level: 'info',
      data: {
        delayed_loading_time: delayedLoadingTime,
        timestamp: Date.now()
      }
    })

    Sentry.setMeasurement('delayed_loading_time', delayedLoadingTime, 'millisecond')
  }

  // 监听首屏完成事件
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', markFirstScreenComplete)
  } else {
    markFirstScreenComplete()
  }

  // 监听延迟加载完成（通过自定义事件）
  window.addEventListener('delayed-loading-complete', markDelayedLoadingComplete)

  // 集成自定义性能监控数据
  if (performanceMonitor) {
    // 定期同步自定义性能数据到Sentry
    setInterval(() => {
      const metrics = performanceMonitor.getAllMetrics()

      // 只发送关键指标，避免数据过多
      if (metrics.coreWebVitals.LCP) {
        Sentry.setMeasurement('lcp_custom', metrics.coreWebVitals.LCP, 'millisecond')
      }
      if (metrics.coreWebVitals.FID) {
        Sentry.setMeasurement('fid_custom', metrics.coreWebVitals.FID, 'millisecond')
      }
      if (metrics.coreWebVitals.CLS) {
        Sentry.setMeasurement('cls_custom', metrics.coreWebVitals.CLS, '')
      }
    }, 10000) // 每10秒同步一次
  }
}

/**
 * 手动发送精确的页面加载性能指标
 * 区分真实首屏时间和延迟加载时间
 */
export function reportAccuratePageLoadMetrics(): void {
  if (!isSentryEnabled()) return

  // 获取真实的首屏性能指标（基于Web Vitals）
  const fcpEntries = performance.getEntriesByName('first-contentful-paint')
  const lcpEntries = performance.getEntriesByType('largest-contentful-paint')

  if (fcpEntries.length > 0) {
    const fcpTime = fcpEntries[0].startTime

    // 发送真实的首屏时间（不包含延迟加载）
    Sentry.setMeasurement('real_first_screen_time', fcpTime, 'millisecond')

    // 添加标签区分指标类型
    Sentry.setTag('performance_type', 'real_first_screen')

    console.log(`📊 向Sentry发送真实首屏时间: ${fcpTime.toFixed(2)}ms`)
  }

  if (lcpEntries.length > 0) {
    const lcpTime = lcpEntries[lcpEntries.length - 1].startTime
    Sentry.setMeasurement('real_lcp_time', lcpTime, 'millisecond')
  }

  // 发送页面加载类型信息
  Sentry.setContext('page_load_strategy', {
    has_delayed_loading: true,
    delayed_loading_time: 200, // 默认延迟时间
    loading_strategy: 'progressive'
  })
}

/**
 * 发送延迟加载完成指标
 */
export function reportDelayedLoadingMetrics(completionTime: number): void {
  if (!isSentryEnabled()) return

  Sentry.setMeasurement('delayed_loading_completion_time', completionTime, 'millisecond')
  Sentry.setTag('performance_type', 'delayed_loading')

  console.log(`📊 向Sentry发送延迟加载完成时间: ${completionTime.toFixed(2)}ms`)
}
