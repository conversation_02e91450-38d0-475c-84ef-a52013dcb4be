import Cookies from 'js-cookie'
import { UserInfo } from '@/interface'
import { JSONParse } from './tools'
import router, { AppRouteName } from '@/router'
// import { useUserCenterStore, useUserInfoStore } from '@/store'
import { localStorageKeys } from '@/interface'

const TOKEN_KEY = 'token'
const REFRESH_KEY = 'refreshToken'
const USER_INFO_KEY = 'userInfo'
const ENVIRONMENT_KEY = 'environment'

const cookieParam: Cookies.CookieAttributes = {
  path: '/',
  domain: '.playshot.ai',
  secure: true,
  sameSite: 'strict'
}

export const isLogin = () => {
  return !!localStorage.getItem(TOKEN_KEY)
}

export const getAccessToken = () => {
  const accessToken = localStorage.getItem(TOKEN_KEY) || Cookies.get(TOKEN_KEY) // 共享子域名的登录态
  return accessToken
}
export const getRefreshToken = () => {
  return localStorage.getItem(REFRESH_KEY)
}
export const setAccessToken = (token: string) => {
  Cookies.set(TOKEN_KEY, token, cookieParam)
  localStorage.setItem(TOKEN_KEY, token)
}
export const setRefreshToken = (token: string) => {
  localStorage.setItem(REFRESH_KEY, token)
}
export const clearToken = () => {
  Cookies.remove(TOKEN_KEY, cookieParam)
  localStorage.removeItem(TOKEN_KEY)
  localStorage.removeItem(REFRESH_KEY)
}
export const clearRewardHistory = () => {
  localStorage.removeItem(localStorageKeys.lastCreditDate)
}
export const getUserInfo = (): UserInfo | null => {
  const userInfo = localStorage.getItem(USER_INFO_KEY)
  if (!userInfo) return null

  return JSONParse(userInfo)
}

export const setUserInfo = (userInfo: UserInfo | null) => {
  try {
    const data = userInfo ? JSON.stringify(userInfo) : ''
    localStorage.setItem(USER_INFO_KEY, data)
  } catch (e) {
    console.error(e)
  }
}
export const setEnvironment = (environment: string) => {
  localStorage.setItem(ENVIRONMENT_KEY, environment.toString())
}
export const getEnvironment = () => {
  return localStorage.getItem(ENVIRONMENT_KEY)
}

export function logout(autoJump = true) {
  // clearToken()
  // setUserInfo(null)
  // clearRewardHistory()
  // const userCenterStore = useUserCenterStore()
  // const userInfoStore = useUserInfoStore()
  // userCenterStore.$reset()
  // userCenterStore.clearLocalStorageInfo()
  // userInfoStore.$reset()
  // if (router.currentRoute.value.name === AppRouteName.Login || !autoJump) return

  const to = encodeURIComponent(router.currentRoute.value.fullPath)
  router.push({ name: AppRouteName.Login, query: { to } })
}
