import { AsyncComponentLoader } from 'vue'
import { RouteGroup, preloadRouteGroup, preloadComponent } from './advanced-route-loader'

/**
 * 路由预加载策略配置
 */
export interface PreloadStrategy {
  // 立即预加载的路由（高优先级）
  immediate: string[]
  // 用户交互后预加载的路由（中优先级）
  onInteraction: string[]
  // 空闲时预加载的路由（低优先级）
  onIdle: string[]
  // 路由组配置
  groups: RouteGroup[]
}

/**
 * 移动端路由加载器映射
 */
export const mobileRouteLoaders = new Map<string, AsyncComponentLoader>([
  // 核心页面 - 高优先级
  ['stories', () => import('@/mobile/views/stories/index.vue')],
  ['user-profile', () => import('@/mobile/views/user/profile.vue')],
  ['user-login', () => import('@/mobile/views/user/login.vue')],
  
  // 聊天相关 - 高优先级
  ['chat', () => import('@/mobile/views/chat/index.vue')],
  ['chat2', () => import('@/mobile/views/chat2/index.vue')],
  ['chat3', () => import('@/mobile/views/chat3/index.vue')],
  ['story-intro', () => import('@/mobile/views/chat/components/StoryIntro.vue')],
  
  // 用户功能 - 中优先级
  ['user-character', () => import('@/mobile/views/user-character/index.vue')],
  ['user-register', () => import('@/mobile/views/user/register.vue')],
  ['user-settings', () => import('@/mobile/views/user/settings.vue')],
  ['user-coins', () => import('@/mobile/views/user/coins.vue')],
  ['daily-tasks', () => import('@/mobile/views/tasks/index.vue')],
  
  // 支付相关 - 中优先级
  ['recharge-success', () => import('@/mobile/views/recharge-success/index.vue')],
  ['stripe-callback', () => import('@/mobile/views/payment/stripe-callback.vue')],
  
  // 编辑器相关 - 低优先级
  ['editor-list', () => import('@/mobile/views/editor/list.vue')],
  ['editor', () => import('@/mobile/views/editor/editor.vue')],
  ['flow-editor', () => import('@/mobile/views/flow-editor/index.vue')],
  
  // 静态页面 - 低优先级
  ['terms', () => import('@/mobile/views/terms/index.vue')],
  ['privacy', () => import('@/mobile/views/privacy/index.vue')],
  ['region-restricted', () => import('@/mobile/views/region-restricted/index.vue')],
  ['social-callback', () => import('@/mobile/views/user/SocialCallback.vue')],
  
  // 落地页 - 按需加载
  ['landing-direct', () => import('@/mobile/views/landingpage/direct-route.vue')],
  ['landing-index2', () => import('@/mobile/views/landingpage/index2.vue')],
  
  // 布局组件
  ['mobile-layout', () => import('@/mobile/components/MobileLayout.vue')]
])

/**
 * PC端路由加载器映射
 */
export const pcRouteLoaders = new Map<string, AsyncComponentLoader>([
  // PC布局
  ['pc-layout', () => import('@/pc/views/PCLayout.vue')],
  ['pc-home', () => import('@/pc/views/PCHome.vue')],
  
  // PC端适配页面
  ['pc-stories', () => import('@/pc/views/stories/index.vue')],
  ['pc-chat', () => import('@/pc/views/chat/index.vue')],
  ['pc-user-profile', () => import('@/pc/views/user/profile.vue')],
  ['pc-user-settings', () => import('@/pc/views/user/settings.vue')],
  
  // PC端编辑器
  ['pc-flow-editor', () => import('@/pc/views/flow-editor/index.vue')],
  ['pc-character-select', () => import('@/pc/views/flow-editor/CharacterSelectPage.vue')]
])

/**
 * 移动端预加载策略
 */
export const mobilePreloadStrategy: PreloadStrategy = {
  // 立即预加载 - 用户最可能访问的页面
  immediate: ['stories', 'user-login', 'mobile-layout'],
  
  // 用户交互后预加载 - 用户登录后可能访问的页面
  onInteraction: ['user-profile', 'chat', 'user-character', 'story-intro'],
  
  // 空闲时预加载 - 其他功能页面
  onIdle: [
    'chat2', 'chat3', 'user-register', 'user-settings', 'user-coins',
    'daily-tasks', 'recharge-success', 'stripe-callback', 'terms', 'privacy'
  ],
  
  // 路由组配置
  groups: [
    {
      name: 'core',
      routes: ['stories', 'user-login', 'mobile-layout'],
      priority: 'high',
      preload: true
    },
    {
      name: 'chat',
      routes: ['chat', 'chat2', 'chat3', 'story-intro'],
      priority: 'high',
      preload: false // 按需预加载
    },
    {
      name: 'user',
      routes: ['user-profile', 'user-character', 'user-settings', 'user-coins'],
      priority: 'normal',
      preload: false
    },
    {
      name: 'payment',
      routes: ['recharge-success', 'stripe-callback'],
      priority: 'normal',
      preload: false
    },
    {
      name: 'editor',
      routes: ['editor-list', 'editor', 'flow-editor'],
      priority: 'low',
      preload: false
    },
    {
      name: 'static',
      routes: ['terms', 'privacy', 'region-restricted'],
      priority: 'low',
      preload: false
    }
  ]
}

/**
 * PC端预加载策略
 */
export const pcPreloadStrategy: PreloadStrategy = {
  immediate: ['pc-layout', 'pc-stories'],
  onInteraction: ['pc-chat', 'pc-user-profile'],
  onIdle: ['pc-user-settings', 'pc-flow-editor', 'pc-character-select'],
  groups: [
    {
      name: 'pc-core',
      routes: ['pc-layout', 'pc-stories'],
      priority: 'high',
      preload: true
    },
    {
      name: 'pc-user',
      routes: ['pc-chat', 'pc-user-profile', 'pc-user-settings'],
      priority: 'normal',
      preload: false
    },
    {
      name: 'pc-editor',
      routes: ['pc-flow-editor', 'pc-character-select'],
      priority: 'low',
      preload: false
    }
  ]
}

/**
 * 预加载管理器
 */
export class PreloadManager {
  private strategy: PreloadStrategy
  private routeLoaders: Map<string, AsyncComponentLoader>
  private isPreloading = false
  private preloadedGroups = new Set<string>()

  constructor(strategy: PreloadStrategy, routeLoaders: Map<string, AsyncComponentLoader>) {
    this.strategy = strategy
    this.routeLoaders = routeLoaders
  }

  /**
   * 开始预加载
   */
  async startPreload(): Promise<void> {
    if (this.isPreloading) return
    this.isPreloading = true

    console.log('🚀 开始路由预加载策略')

    try {
      // 1. 立即预加载高优先级路由
      await this.preloadImmediate()

      // 2. 监听用户交互
      this.setupInteractionListeners()

      // 3. 空闲时预加载
      this.setupIdlePreload()

      // 4. 预加载指定的路由组
      await this.preloadGroups()

    } catch (error) {
      console.error('预加载策略执行失败:', error)
    } finally {
      this.isPreloading = false
    }
  }

  /**
   * 立即预加载
   */
  private async preloadImmediate(): Promise<void> {
    console.log('⚡ 开始立即预加载')
    const promises = this.strategy.immediate.map(route => {
      const loader = this.routeLoaders.get(route)
      return loader ? preloadComponent(loader, route) : null
    }).filter(Boolean)

    await Promise.allSettled(promises)
  }

  /**
   * 设置交互监听器
   */
  private setupInteractionListeners(): void {
    const preloadOnInteraction = () => {
      console.log('👆 用户交互触发预加载')
      this.preloadOnInteraction()
      // 移除监听器，只执行一次
      document.removeEventListener('click', preloadOnInteraction)
      document.removeEventListener('touchstart', preloadOnInteraction)
      document.removeEventListener('keydown', preloadOnInteraction)
    }

    document.addEventListener('click', preloadOnInteraction, { once: true })
    document.addEventListener('touchstart', preloadOnInteraction, { once: true })
    document.addEventListener('keydown', preloadOnInteraction, { once: true })
  }

  /**
   * 交互后预加载
   */
  private async preloadOnInteraction(): Promise<void> {
    const promises = this.strategy.onInteraction.map(route => {
      const loader = this.routeLoaders.get(route)
      return loader ? preloadComponent(loader, route) : null
    }).filter(Boolean)

    await Promise.allSettled(promises)
  }

  /**
   * 设置空闲预加载
   */
  private setupIdlePreload(): void {
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => this.preloadOnIdle(), { timeout: 5000 })
    } else {
      // 降级方案
      setTimeout(() => this.preloadOnIdle(), 2000)
    }
  }

  /**
   * 空闲时预加载
   */
  private async preloadOnIdle(): Promise<void> {
    console.log('😴 空闲时间预加载')
    const promises = this.strategy.onIdle.map(route => {
      const loader = this.routeLoaders.get(route)
      return loader ? preloadComponent(loader, route) : null
    }).filter(Boolean)

    await Promise.allSettled(promises)
  }

  /**
   * 预加载路由组
   */
  private async preloadGroups(): Promise<void> {
    const groupsToPreload = this.strategy.groups.filter(group => 
      group.preload && !this.preloadedGroups.has(group.name)
    )

    for (const group of groupsToPreload) {
      await preloadRouteGroup(group, this.routeLoaders)
      this.preloadedGroups.add(group.name)
    }
  }

  /**
   * 按需预加载路由组
   */
  async preloadGroup(groupName: string): Promise<void> {
    if (this.preloadedGroups.has(groupName)) return

    const group = this.strategy.groups.find(g => g.name === groupName)
    if (group) {
      await preloadRouteGroup(group, this.routeLoaders)
      this.preloadedGroups.add(groupName)
    }
  }
}

/**
 * 创建预加载管理器
 */
export function createPreloadManager(isMobile: boolean): PreloadManager {
  const strategy = isMobile ? mobilePreloadStrategy : pcPreloadStrategy
  const routeLoaders = isMobile ? mobileRouteLoaders : pcRouteLoaders
  return new PreloadManager(strategy, routeLoaders)
}
