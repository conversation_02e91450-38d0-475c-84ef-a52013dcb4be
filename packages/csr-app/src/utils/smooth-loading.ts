/**
 * 丝滑加载体验优化器
 * 专门解决加载指示器卡顿和性能问题
 */

class SmoothLoading {
  private progressValue = 0
  private targetProgress = 0
  private animationFrame: number | null = null
  private progressBar: HTMLElement | null = null
  private progressText: HTMLElement | null = null
  private loadingText: HTMLElement | null = null

  constructor() {
    this.initializeElements()
    this.startSmoothAnimation()
  }

  /**
   * 初始化 DOM 元素
   */
  private initializeElements(): void {
    this.progressBar = document.getElementById('loading-progress')
    this.progressText = document.getElementById('loading-percentage')
    this.loadingText = document.getElementById('loading-text')
  }

  /**
   * 启动丝滑动画
   */
  private startSmoothAnimation(): void {
    const animate = () => {
      // 使用缓动函数让进度条更丝滑
      const diff = this.targetProgress - this.progressValue
      if (Math.abs(diff) > 0.1) {
        this.progressValue += diff * 0.1 // 缓动系数
        this.updateVisualElements()
      } else if (diff !== 0) {
        this.progressValue = this.targetProgress
        this.updateVisualElements()
      }

      this.animationFrame = requestAnimationFrame(animate)
    }

    animate()
  }

  /**
   * 更新视觉元素
   */
  private updateVisualElements(): void {
    const progress = Math.min(100, Math.max(0, this.progressValue))

    // 更新进度条
    if (this.progressBar) {
      this.progressBar.style.width = `${progress}%`
    }

    // 更新百分比文本
    if (this.progressText) {
      this.progressText.textContent = `${Math.round(progress)}%`
    }

    // 保持简洁的加载文本
    if (this.loadingText && progress >= 100) {
      this.loadingText.textContent = 'Ready!'
    } else if (this.loadingText) {
      this.loadingText.textContent = 'Loading...'
    }
  }

  /**
   * 设置目标进度（只允许前进，不允许倒退）
   */
  setProgress(progress: number): void {
    const newProgress = Math.min(100, Math.max(0, progress))
    // 只有当新进度大于当前目标进度时才更新，防止倒退
    if (newProgress > this.targetProgress) {
      this.targetProgress = newProgress
      console.log(`📈 进度更新: ${newProgress}%`)
    } else if (newProgress < this.targetProgress) {
      console.warn(`⚠️ 尝试倒退进度被阻止: ${newProgress}% -> ${this.targetProgress}%`)
    }
  }

  /**
   * 立即设置进度（无动画，允许倒退，仅用于重置）
   */
  setProgressImmediate(progress: number): void {
    this.progressValue = Math.min(100, Math.max(0, progress))
    this.targetProgress = this.progressValue
    this.updateVisualElements()
    console.log(`🔄 进度立即设置: ${this.progressValue}%`)
  }

  /**
   * 强制设置进度（允许倒退，用于特殊情况）
   */
  forceSetProgress(progress: number): void {
    const newProgress = Math.min(100, Math.max(0, progress))
    this.targetProgress = newProgress
    console.log(`🔧 强制设置进度: ${newProgress}%`)
  }

  /**
   * 完成加载
   */
  complete(): void {
    this.setProgress(100)

    // 延迟隐藏加载指示器
    setTimeout(() => {
      this.hide()
    }, 800)
  }

  /**
   * 隐藏加载指示器
   */
  private hide(): void {
    const loadingIndicator = document.getElementById('loading-indicator')
    if (loadingIndicator) {
      loadingIndicator.style.opacity = '0'
      loadingIndicator.style.transform = 'scale(0.95)'

      setTimeout(() => {
        loadingIndicator.style.display = 'none'
      }, 500)
    }
  }

  /**
   * 销毁动画
   */
  destroy(): void {
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame)
      this.animationFrame = null
    }
  }
}

// 创建全局实例
export const smoothLoading = new SmoothLoading()

// 替换全局的进度更新函数
if (typeof window !== 'undefined') {
  // @ts-ignore
  window.updateLoadingProgress = (progress: number) => {
    smoothLoading.setProgress(progress)
  }

  // 提供强制更新方法（用于调试）
  // @ts-ignore
  window.forceUpdateLoadingProgress = (progress: number) => {
    smoothLoading.forceSetProgress(progress)
  }

  // @ts-ignore
  window.__smoothLoading = smoothLoading
}

// 模拟初始进度增长
let initialProgress = 0
const progressInterval = setInterval(() => {
  initialProgress += Math.random() * 8 + 2 // 2-10 的随机增长
  if (initialProgress >= 25) {
    clearInterval(progressInterval)
    initialProgress = 25
  }
  smoothLoading.setProgress(initialProgress)
}, 150)

// 在 DOM 加载完成时推进进度
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    smoothLoading.setProgress(40)
  })
} else {
  smoothLoading.setProgress(40)
}

// 在页面完全加载时推进进度
window.addEventListener('load', () => {
  smoothLoading.setProgress(70)
})

export default smoothLoading
