/**
 * 子应用微前端桥接器
 * 与主应用的桥接器保持同步
 */

export interface MicroFrontendEvent {
  type: string
  payload?: any
  source: 'shell' | 'chatApp'
  timestamp: number
}

export interface UserState {
  isAuthenticated: boolean
  userInfo?: {
    id: string
    name: string
    email?: string
    avatar?: string
    coins?: number
  }
  token?: string
}

export interface ChatState {
  currentChatType?: 'chat' | 'chat2' | 'chat3' | 'chat4'
  currentStoryId?: string
  currentCharacterId?: string
  isInChat: boolean
}

class ChatAppBridge {
  private bridge: any = null

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeBridge()
    }
  }

  /**
   * 初始化桥接器连接
   */
  private initializeBridge() {
    // 尝试获取主应用的桥接器
    this.bridge = (window as any).__MICRO_FRONTEND_BRIDGE__

    if (!this.bridge) {
      console.warn('Main app bridge not found, creating local bridge')
      this.createLocalBridge()
    } else {
      console.log('Connected to main app bridge')
    }
  }

  /**
   * 创建本地桥接器（独立运行时）
   */
  private createLocalBridge() {
    this.bridge = {
      emit: (type: string, payload?: any) => {
        console.log('Local bridge emit:', type, payload)
      },
      on: (type: string, listener: Function) => {
        console.log('Local bridge on:', type)
        return () => {} // 返回取消监听函数
      },
      updateState: (path: string, value: any) => {
        console.log('Local bridge updateState:', path, value)
      },
      getState: (path?: string) => {
        console.log('Local bridge getState:', path)
        return {}
      }
    }
  }

  /**
   * 发送事件到主应用
   */
  emit(type: string, payload?: any) {
    if (this.bridge) {
      this.bridge.emit(type, payload, 'chatApp')
    }
  }

  /**
   * 监听主应用事件
   */
  on(type: string, listener: (event: MicroFrontendEvent) => void) {
    if (this.bridge) {
      return this.bridge.on(type, listener)
    }
    return () => {}
  }

  /**
   * 更新共享状态
   */
  updateState(path: string, value: any) {
    if (this.bridge) {
      this.bridge.updateState(path, value)
    }
  }

  /**
   * 获取共享状态
   */
  getState(path?: string): any {
    if (this.bridge) {
      return this.bridge.getState(path)
    }
    return {}
  }

  /**
   * 通知主应用用户已认证
   */
  notifyUserAuth(userInfo: UserState['userInfo'], token?: string) {
    this.updateState('user.isAuthenticated', true)
    this.updateState('user.userInfo', userInfo)
    if (token) {
      this.updateState('user.token', token)
    }
    this.emit('user:authenticated', { userInfo, token })
  }

  /**
   * 通知主应用用户登出
   */
  notifyUserLogout() {
    this.updateState('user.isAuthenticated', false)
    this.updateState('user.userInfo', undefined)
    this.updateState('user.token', undefined)
    this.emit('user:logout')
  }

  /**
   * 通知主应用进入Chat
   */
  notifyEnterChat(chatType: ChatState['currentChatType'], storyId: string, characterId: string) {
    this.updateState('chat.isInChat', true)
    this.updateState('chat.currentChatType', chatType)
    this.updateState('chat.currentStoryId', storyId)
    this.updateState('chat.currentCharacterId', characterId)
    this.emit('chat:enter', { chatType, storyId, characterId })
  }

  /**
   * 通知主应用退出Chat
   */
  notifyExitChat() {
    this.updateState('chat.isInChat', false)
    this.updateState('chat.currentChatType', undefined)
    this.updateState('chat.currentStoryId', undefined)
    this.updateState('chat.currentCharacterId', undefined)
    this.emit('chat:exit')
  }

  /**
   * 请求导航到主应用页面
   */
  requestNavigation(path: string) {
    this.emit('navigation:request', { path })
  }

  /**
   * 检查是否在微前端环境中
   */
  isInMicroFrontend(): boolean {
    return !!(window as any).__MICRO_FRONTEND_BRIDGE__
  }
}

// 创建全局实例
export const chatAppBridge = new ChatAppBridge()

// 导出类型
export type { ChatAppBridge }
