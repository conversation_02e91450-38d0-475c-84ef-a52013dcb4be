import { ref, watch, type Ref } from 'vue'
import { useChatEventsStore } from '@/store/chat-events'

// 事件优先级枚举
export enum EventPriority {
  CRITICAL = 0,  // 服务端停止/场景切换
  HIGH = 1,      // 服务端新事件
  NORMAL = 2,    // 视频组下一个
  LOW = 3        // UI更新
}

// 视频组状态枚举
export enum VideoGroupState {
  IDLE = 'idle',
  LOADING = 'loading',
  PLAYING = 'playing',
  PAUSED = 'paused',
  WAITING_SERVER = 'waiting_server',
  STOPPED = 'stopped'
}

// 事件队列项接口
export interface EventQueueItem {
  id: string
  priority: EventPriority
  type: 'server_event' | 'video_next' | 'user_action'
  data: any
  timestamp: number
}

// 视频组控制器状态接口
export interface VideoGroupControllerState {
  currentVideo: any | null
  playStartTime: number
  expectedEndTime: number
  abortController: AbortController | null
}

/**
 * 视频组控制器服务类
 * 负责管理视频组的播放逻辑和状态转换
 */
export class VideoGroupController {
  private state: Ref<VideoGroupState>
  private eventQueue: Ref<EventQueueItem[]>
  private isProcessingEvents: Ref<boolean>
  private controllerState: Ref<VideoGroupControllerState>
  
  // 状态数据引用
  private videoGroupQueue: Ref<any[]>
  private currentVideoGroupIndex: Ref<number>
  private pendingUserMessage: Ref<string | null>
  private pausedState: Ref<any>
  
  constructor(
    state: Ref<VideoGroupState>,
    videoGroupQueue: Ref<any[]>,
    currentVideoGroupIndex: Ref<number>,
    pendingUserMessage: Ref<string | null>,
    pausedState: Ref<any>
  ) {
    this.state = state
    this.videoGroupQueue = videoGroupQueue
    this.currentVideoGroupIndex = currentVideoGroupIndex
    this.pendingUserMessage = pendingUserMessage
    this.pausedState = pausedState
    
    // 初始化内部状态
    this.eventQueue = ref([])
    this.isProcessingEvents = ref(false)
    this.controllerState = ref({
      currentVideo: null,
      playStartTime: 0,
      expectedEndTime: 0,
      abortController: null
    })
  }
  
  /**
   * 添加事件到优先级队列
   */
  addEvent(event: Omit<EventQueueItem, 'id' | 'timestamp'>) {
    const eventItem: EventQueueItem = {
      ...event,
      id: Date.now() + '-' + Math.random().toString(36).substr(2, 9),
      timestamp: Date.now()
    }
    
    this.eventQueue.value.push(eventItem)
    // 按优先级排序（优先级数字越小优先级越高）
    this.eventQueue.value.sort((a, b) => a.priority - b.priority)
    
    
    // 立即处理队列
    if (!this.isProcessingEvents.value) {
      this.processEventQueue()
    }
  }
  
  /**
   * 处理事件优先级队列
   */
  private async processEventQueue() {
    if (this.isProcessingEvents.value || this.eventQueue.value.length === 0) {
      return
    }
    
    this.isProcessingEvents.value = true
    
    try {
      while (this.eventQueue.value.length > 0) {
        const event = this.eventQueue.value.shift()!
        
        await this.handleEvent(event)
        
        // 检查是否需要立即停止处理（有更高优先级事件）
        if (this.eventQueue.value.length > 0 && this.eventQueue.value[0].priority < event.priority) {
          break
        }
      }
    } finally {
      this.isProcessingEvents.value = false
    }
  }
  
  /**
   * 处理单个事件
   */
  private async handleEvent(event: EventQueueItem) {
    switch (event.type) {
      case 'server_event':
        await this.handleServerEvent(event.data)
        break
      case 'video_next':
        await this.playNextVideo()
        break
      case 'user_action':
        await this.handleUserAction(event.data)
        break
      default:
        console.warn('未知事件类型:', event.type)
    }
  }
  
  /**
   * 处理服务端事件
   */
  private async handleServerEvent(data: any) {
    
    switch (data.type) {
      case 'stop':
      case 'scene_change':
        await this.stop()
        break
      case 'new_video_group':
        await this.startNewVideoGroup(data.videoGroup)
        break
      case 'pause_for_foreground_video':
      case 'pause_for_server_event':
        this.pause()
        if (data.type === 'pause_for_server_event') {
          // 让服务端事件先处理
          const chatEventsStore = useChatEventsStore()
          await chatEventsStore.processEventQueue()
          // 处理完成后尝试恢复
          if (this.state.value === VideoGroupState.PAUSED) {
            this.resume()
          }
        }
        break
      default:
        // 其他服务端事件处理
        if (this.state.value === VideoGroupState.PLAYING) {
          this.pause()
          const chatEventsStore = useChatEventsStore()
          await chatEventsStore.processEventQueue()
          if (this.state.value === VideoGroupState.PAUSED) {
            this.resume()
          }
        }
    }
  }
  
  /**
   * 播放下一个视频
   */
  private async playNextVideo() {
    if (this.state.value !== VideoGroupState.PLAYING) {
      return
    }
    
    // 检查是否有更高优先级事件
    const hasHigherPriorityEvents = this.eventQueue.value.some(
      event => event.priority < EventPriority.NORMAL
    )
    
    if (hasHigherPriorityEvents) {
      console.log('🔺 发现高优先级事件，跳过视频播放')
      return
    }
    
    if (this.currentVideoGroupIndex.value >= this.videoGroupQueue.value.length) {
      // 一轮播放完成，停止播放
      console.log('✅ 视频组播放完成，停止播放')
      await this.stop()
      return
    }
    
    const currentVideo = this.videoGroupQueue.value[this.currentVideoGroupIndex.value]
    if (!currentVideo) {
      console.error('🚨 未找到当前视频')
      return
    }
    
    console.log(
      `🎬 播放视频组第 ${this.currentVideoGroupIndex.value + 1}/${this.videoGroupQueue.value.length} 个视频:`,
      currentVideo.url?.substring(0, 60) + '...'
    )
    
    // 更新控制器状态
    this.controllerState.value.currentVideo = currentVideo
    this.controllerState.value.playStartTime = Date.now()
    
    // 如果有进行中的播放，取消它
    if (this.controllerState.value.abortController) {
      this.controllerState.value.abortController.abort()
    }
    this.controllerState.value.abortController = new AbortController()
    
    try {
      // 这里需要调用实际的视频播放逻辑
      await this.playVideo(currentVideo, this.controllerState.value.abortController.signal)
      
      // 视频播放完成，准备播放下一个
      if (this.state.value === VideoGroupState.PLAYING) {
        this.currentVideoGroupIndex.value++
        
        // 添加下一个视频播放任务
        this.addEvent({
          priority: EventPriority.NORMAL,
          type: 'video_next',
          data: null
        })
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('🛑 视频播放被取消')
      } else {
        console.error('🚨 视频播放失败:', error)
        // 播放失败时，尝试播放下一个
        if (this.state.value === VideoGroupState.PLAYING) {
          this.currentVideoGroupIndex.value++
          this.addEvent({
            priority: EventPriority.NORMAL,
            type: 'video_next',
            data: null
          })
        }
      }
    }
  }
  
  /**
   * 播放单个视频 - 这个方法需要外部注入具体实现
   */
  private async playVideo(videoData: any, signal: AbortSignal): Promise<void> {
    // 这里需要外部提供具体的视频播放实现
    throw new Error('playVideo method must be implemented by external service')
  }
  
  /**
   * 处理用户操作
   */
  private async handleUserAction(data: any) {
    console.log('👤 处理用户操作:', data)
    // 用户操作处理逻辑
  }
  
  /**
   * 开始播放视频组
   */
  start() {
    if (this.videoGroupQueue.value.length === 0) {
      console.log('🚫 视频组为空，无法开始播放')
      return
    }
    
    console.log(`🎥 开始视频组播放，共 ${this.videoGroupQueue.value.length} 个视频`)
    
    this.state.value = VideoGroupState.PLAYING
    this.currentVideoGroupIndex.value = 0
    
    // 添加第一个视频播放任务
    this.addEvent({
      priority: EventPriority.NORMAL,
      type: 'video_next',
      data: null
    })
  }
  
  /**
   * 暂停视频组
   */
  pause() {
    if (this.state.value !== VideoGroupState.PLAYING) {
      return
    }
    
    console.log('⏸️ 暂停视频组播放')
    
    // 保存当前状态
    this.pausedState.value = {
      videoGroupQueue: [...this.videoGroupQueue.value],
      currentVideoGroupIndex: this.currentVideoGroupIndex.value,
      pendingUserMessage: this.pendingUserMessage.value
    }
    
    // 取消当前播放
    if (this.controllerState.value.abortController) {
      this.controllerState.value.abortController.abort()
    }
    
    // 清空视频组事件
    this.eventQueue.value = this.eventQueue.value.filter(event => event.type !== 'video_next')
    
    this.state.value = VideoGroupState.PAUSED
  }
  
  /**
   * 恢复视频组
   */
  resume() {
    if (this.state.value !== VideoGroupState.PAUSED || !this.pausedState.value) {
      return
    }
    
    console.log('▶️ 恢复视频组播放')
    
    // 恢复状态
    this.videoGroupQueue.value = this.pausedState.value.videoGroupQueue
    this.currentVideoGroupIndex.value = this.pausedState.value.currentVideoGroupIndex
    this.pendingUserMessage.value = this.pausedState.value.pendingUserMessage
    
    this.pausedState.value = null
    this.state.value = VideoGroupState.PLAYING
    
    // 继续播放当前视频
    this.addEvent({
      priority: EventPriority.NORMAL,
      type: 'video_next',
      data: null
    })
  }
  
  /**
   * 停止视频组
   */
  async stop() {
    console.log('🛑 停止视频组播放')
    
    // 取消当前播放
    if (this.controllerState.value.abortController) {
      this.controllerState.value.abortController.abort()
    }
    
    // 清空所有视频组相关事件
    this.eventQueue.value = this.eventQueue.value.filter(event => event.type !== 'video_next')
    
    // 重置状态
    this.state.value = VideoGroupState.STOPPED
    this.videoGroupQueue.value = []
    this.currentVideoGroupIndex.value = 0
    this.pendingUserMessage.value = null
    this.pausedState.value = null
    
    // 清空控制器状态
    this.controllerState.value = {
      currentVideo: null,
      playStartTime: 0,
      expectedEndTime: 0,
      abortController: null
    }
    
    // 最后设置为空闲状态
    this.state.value = VideoGroupState.IDLE
  }
  
  /**
   * 开始新的视频组
   */
  async startNewVideoGroup(newVideoGroup: any[]) {
    console.log(`🆕 开始新的视频组，共 ${newVideoGroup.length} 个视频`)
    
    // 停止当前视频组
    await this.stop()
    
    // 设置新的视频组
    this.videoGroupQueue.value = newVideoGroup
    this.currentVideoGroupIndex.value = 0
    
    // 开始播放
    this.start()
  }
  
  /**
   * 设置视频播放实现
   */
  setVideoPlayer(playVideo: (videoData: any, signal: AbortSignal) => Promise<void>) {
    this.playVideo = playVideo
  }
  
  // Getters
  get currentState() {
    return this.state.value
  }
  
  get isPlaying() {
    return this.state.value === VideoGroupState.PLAYING
  }
  
  get isPaused() {
    return this.state.value === VideoGroupState.PAUSED
  }
  
  get isStopped() {
    return this.state.value === VideoGroupState.STOPPED || this.state.value === VideoGroupState.IDLE
  }
  
  get currentVideoIndex() {
    return this.currentVideoGroupIndex.value
  }
  
  get queueLength() {
    return this.videoGroupQueue.value.length
  }
}