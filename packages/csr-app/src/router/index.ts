import { createRouter, createWebHistory } from 'vue-router'
import mobileRoutes from '@/mobile/routes'
import pcRoutes from '@/pc/routes'
import createRouteGuard from './guard'
import { setupSEOGuard } from './seo-guard'
import { getDeploymentConfig } from '@/config/deployment'

// 合并移动端和PC端路由
const routes = [...mobileRoutes, ...pcRoutes]

const router = createRouter({
  history: createWebHistory(),
  // @ts-ignore
  routes,
})

createRouteGuard(router)

// 设置SEO守卫
setupSEOGuard(router)

// 微前端导航拦截
router.beforeEach((to, from, next) => {
  // 检测是否在iframe中运行
  const isInIframe = typeof window !== 'undefined' && window.self !== window.top

  if (!isInIframe) {
    // 不在iframe中，正常导航
    next()
    return
  }

  console.log('🔄 微前端路由拦截:', from.path, '->', to.path)

  // 向主应用发送导航消息的函数
  const sendNavigationMessage = (type: string, payload?: any) => {
    const message = {
      type,
      payload,
      timestamp: Date.now(),
      source: 'csr-app',
    }

    console.log('📤 CSR应用: 发送导航消息到主应用', message)

    try {
      // 使用配置中的主应用URL
      const config = getDeploymentConfig()
      window.parent.postMessage(message, config.mainAppUrl)
      return true
    } catch (error) {
      console.error('发送消息失败:', error)
      return false
    }
  }

  // 拦截主要页面的导航（但不拦截聊天页面）
  const targetPath = to.path

  // 检查是否是聊天页面，如果是则直接放行
  const isChatPage =
    targetPath.startsWith('/chat') ||
    targetPath.startsWith('/chat2') ||
    targetPath.startsWith('/chat3') ||
    targetPath.startsWith('/chat4')

  if (isChatPage) {
    console.log('聊天页面，直接放行:', targetPath)
    next()

    // 聊天页面路由完成后，报告进度并通知主应用路由已就绪
    setTimeout(async () => {
      try {
        const { notifyRouteReady, reportLoadingProgress } = await import(
          '@/utils/iframeNavigation'
        )

        // 报告路由就绪进度
        reportLoadingProgress('route-ready', 60)

        // 通知路由就绪
        notifyRouteReady()
      } catch (error) {
        console.warn('通知路由就绪失败:', error)
      }
    }, 100)

    return
  }

  if (targetPath === '/' || targetPath === '/home') {
    console.log('拦截首页导航，通知主应用')
    if (sendNavigationMessage('NAVIGATE_TO_HOME')) {
      // 阻止路由继续执行
      return
    }
  } else if (targetPath === '/stories') {
    console.log('拦截故事列表导航，通知主应用')
    if (sendNavigationMessage('NAVIGATE_TO_STORIES')) {
      // 阻止路由继续执行
      return
    }
  } else if (targetPath.startsWith('/story/')) {
    const storyId = targetPath.split('/')[2]
    if (storyId) {
      console.log('拦截故事详情导航，通知主应用')
      if (sendNavigationMessage('NAVIGATE_TO_STORY', { storyId })) {
        // 阻止路由继续执行
        return
      }
    }
  }

  // 其他路由正常执行
  next()
})

export * from './constants'
export default router
