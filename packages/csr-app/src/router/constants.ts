export const REDIRECT_ROUTE_NAME = 'Redirect'

export const DEFAULT_ROUTE_NAME = 'Workplace'

export const DEFAULT_ROUTE = {
  title: 'menu.dashboard.workplace',
  name: DEFAULT_ROUTE_NAME,
  fullPath: '/dashboard/workplace'
}

export enum AppRouteName {
  Index = 'index',
  Login = 'login',
  SSOLogin = 'sso-login', // Single-Sign-On
  Signup = 'signup',
  Notification = 'Notification',
  Feedback = 'feedback',
  Privacy = 'privacy',
  Terms = 'terms',
  Redirect = 'redirect',
  Help = 'help',
  UserCenter = 'userCenter',
  UserInfo = 'userInfo',
  Admin = 'admin',
  Activity = 'activity',
  Collection = 'collection',
  CollectionTemplate = 'collectionTemplate',
  CollectionDetail = 'collectionDetail',
  CollectionEditor = 'CollectionEditor',
  CollectionHistory = 'CollectionHistory',
  ActivityRule = 'activityRule',
  ProjectAdd = 'projectAdd',
  Editor = 'editor',
  ActivityRank = 'activityRank',
  ProjectDetail = 'projectDetail',
  RoleLibrary = 'roleLibrary',
  RoleCreate = 'roleCreate',
  RoleDetail = 'roleDetail',
  UserDetail = 'userDetail',
  Share = 'share',
  NotFound = 'notFound',
  Create = 'create',
  Template = 'template',
  LandingPage = 'landingPage',
  Invite = 'invite',
  PopularityRank = 'popularity',
  MyAccount = 'myAccount',
  UserSetting = 'userSetting',
  ContactSupport = 'contactSupport',
  CancelAccountStep1 = 'cancelAccountStep1', // 注销账号
  CancelAccountStep2 = 'cancelAccountStep2', // 注销账号步骤二
  CancelAccountStep3 = 'cancelAccountStep3', // 注销账号步骤三
  RoleProject = 'roleProject', // 角色出演的作品列表页
  AdminProjectList = 'adminProjectList',
  AdminUserList = 'adminUserList',
  AdminRoleList = 'adminRoleList',
  AdminConsole = 'AdminConsole', //管理员控制台
  Search = 'search',
  // 接龙活动
  CollectionContributeRule = 'collectionContributeRule', // 接龙投稿规则页
  CollectionRank = 'collectionRank', // 接龙排行榜页

  SearchResult = 'SearchResult', // 移动端搜索结果页
  About = 'about',
  Payment = 'Payment', // 支付页面
  PaymentOrder = 'PaymentOrder', // 充值订单页
  PaymentCreditRule = 'PaymentCreditRule', // 梦币规则
  PaymentRule = 'PaymentRule', // 付费规则
  DownloadAd = 'DownloadAd', // app下载广告页
  Reward = 'Reward', // 积分页
  RewardRule = 'RewardRule', // 积分规则页
  ActivityValentine = 'ActivityValentine', // 七夕活动
  ActivityValentineStartPage = 'ActivityValentineStartPage',
  CustomPersonalLora = 'CustomPersonalLora', // 定制专属形象
  GeneratePersonalLora = 'GeneratePersonalLora', // 生成专属形象中
  ModifyPersonalLora = 'ModifyPersonalLora', // 修改专属形象
  EditPersonalLora = 'EditPersonalLora', // 编辑专属形象
  ActivityValentineSharePage = 'ActivityValentineSharePage', // 七夕活动
  ActivityCharacter = 'ActivityCharacter', // 性格测试
  ActivityCharacterStartPage = 'ActivityCharacterStartPage',
  ActivityCharacterSharePage = 'ActivityCharacterSharePage', // 性格测试
  AboutUs = 'AboutUs', // 关于我们
  Welcome = 'Welcome',
  UpgradeNotice = 'UpgradeNotice', // pc升级公告

  // 移动端创建路由
  CustomLora = 'CustomLora', // 定制形象
  GenerateLora = 'GenerateLora', // 生成形象中
  ModifyLora = 'ModifyLora', // 修改形象
  EditLora = 'EditLora', // 编辑形象
  BannerStyle = 'BannerStyle', // banner风格介绍

  // 移动端精选tab
  ProjectList = 'ProjectList' // 作品列表
}
