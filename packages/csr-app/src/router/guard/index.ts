import type { Router } from 'vue-router'
import { setRouteEmitter, reportEvent } from '@/utils'
import { setupRouterGuards } from './permission'
import { setupRegionRestrictionGuard } from './regionRestriction'
import { ReportEvent } from '@/interface'
import { pick } from 'lodash-es'

function setupPageGuard(router: Router) {
  router.beforeEach(async (to) => {
    setRouteEmitter(to)
    // 上报页面数据
    reportEvent(ReportEvent.PageView, pick(to, ['name', 'path']))
  })
}

export default function createRouteGuard(router: Router) {
  // 地区限制守卫需要在其他守卫之前执行
  setupRegionRestrictionGuard(router)
  // setupPageGuard(router)
  setupRouterGuards(router)
}
