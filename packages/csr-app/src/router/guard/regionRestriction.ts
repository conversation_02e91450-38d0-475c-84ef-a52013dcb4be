import type { Router } from 'vue-router'
import { isCurrentUserFromChinaMainland } from '@/utils/geoLocation'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface'

/**
 * 地区限制检查状态
 */
interface RegionCheckState {
  isChecking: boolean
  isRestricted: boolean
  lastCheckTime: number
  checkPromise: Promise<boolean> | null
}

/**
 * 地区限制状态管理
 */
class RegionRestrictionManager {
  private static instance: RegionRestrictionManager
  private state: RegionCheckState = {
    isChecking: false,
    isRestricted: false,
    lastCheckTime: 0,
    checkPromise: null
  }

  // 缓存时间：30分钟
  private readonly CACHE_DURATION = 30 * 60 * 1000

  static getInstance(): RegionRestrictionManager {
    if (!RegionRestrictionManager.instance) {
      RegionRestrictionManager.instance = new RegionRestrictionManager()
    }
    return RegionRestrictionManager.instance
  }

  /**
   * 检查是否需要进行地区限制
   * @returns Promise<boolean> true表示需要限制访问
   */
  async checkRegionRestriction(): Promise<boolean> {
    // 只在 PlayShot 环境下进行检查
    const appName = import.meta.env.VITE_APP_NAME
    if (appName !== 'PlayShot') {
      return false
    }

    // 检查缓存
    const now = Date.now()
    if (this.state.lastCheckTime && now - this.state.lastCheckTime < this.CACHE_DURATION) {
      return this.state.isRestricted
    }

    // 如果正在检查中，返回现有的Promise
    if (this.state.isChecking && this.state.checkPromise) {
      return this.state.checkPromise
    }

    // 开始新的检查
    this.state.isChecking = true
    this.state.checkPromise = this.performRegionCheck()

    try {
      const result = await this.state.checkPromise
      this.state.isRestricted = result
      this.state.lastCheckTime = now
      return result
    } finally {
      this.state.isChecking = false
      this.state.checkPromise = null
    }
  }

  /**
   * 执行实际的地区检查
   */
  private async performRegionCheck(): Promise<boolean> {
    try {
      // 开发环境跳过检查
      if (import.meta.env.DEV) {
        console.log('[RegionRestriction] 开发环境，跳过地区检查')
        return false
      }

      console.log('[RegionRestriction] 开始检查用户地区...')
      const isChinaMainland = await isCurrentUserFromChinaMainland()

      if (isChinaMainland) {
        console.log('[RegionRestriction] 检测到中国大陆用户，启用地区限制')

        // 上报地区限制事件
        reportEvent(ReportEvent.RegionRestrictionShown, {
          userAgent: navigator.userAgent,
          timestamp: Date.now(),
          appName: import.meta.env.VITE_APP_NAME
        })
      } else {
        console.log('[RegionRestriction] 非中国大陆用户，允许访问')
      }

      return isChinaMainland
    } catch (error) {
      console.error('[RegionRestriction] 地区检查失败:', error)

      // 检查失败时的降级策略：允许访问，但记录错误
      reportEvent(ReportEvent.ApiError, {
        error: 'RegionCheckFailed',
        message: error.message,
        timestamp: Date.now()
      })

      return false
    }
  }

  /**
   * 清除缓存，强制重新检查
   */
  clearCache(): void {
    this.state.lastCheckTime = 0
    this.state.isRestricted = false
  }

  /**
   * 获取当前限制状态（不触发新检查）
   */
  getCurrentRestrictionStatus(): boolean {
    return this.state.isRestricted
  }
}

/**
 * 需要跳过地区检查的路由白名单
 */
const REGION_CHECK_WHITELIST = [
  '/region-restricted', // 地区限制页面本身
  '/api/', // API 路由
  '/static/' // 静态资源
]

/**
 * 检查路由是否在白名单中
 */
function isRouteInWhitelist(path: string): boolean {
  return REGION_CHECK_WHITELIST.some((whitelistPath) => path.startsWith(whitelistPath))
}

/**
 * 设置地区限制路由守卫
 * @param router Vue Router 实例
 */
export function setupRegionRestrictionGuard(router: Router): void {
  const manager = RegionRestrictionManager.getInstance()

  router.beforeEach(async (to, from, next) => {
    // 跳过白名单路由
    if (isRouteInWhitelist(to.path)) {
      next()
      return
    }

    try {
      // 检查地区限制
      const isRestricted = await manager.checkRegionRestriction()

      if (isRestricted) {
        // 如果当前不在限制页面，则重定向到限制页面
        if (to.path !== '/region-restricted') {
          console.log('[RegionRestriction] 重定向到地区限制页面')
          next('/region-restricted')
          return
        }
      }

      // 允许访问
      next()
    } catch (error) {
      console.error('[RegionRestriction] 路由守卫错误:', error)

      // 发生错误时允许访问，避免阻塞用户
      next()
    }
  })
}

/**
 * 获取地区限制管理器实例
 */
export function getRegionRestrictionManager(): RegionRestrictionManager {
  return RegionRestrictionManager.getInstance()
}
