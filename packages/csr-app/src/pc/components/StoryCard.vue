<template>
  <div
    class="story-card"
    :class="{
      'is-subscribed': story.is_subscribed,
      'is-loading': isLoading
    }"
    @click="handleClick"
  >
    <div class="card-image">
      <img
        :src="story.preview_url"
        :alt="story.title"
        loading="lazy"
        decoding="async"
        @load="handleImageLoaded"
      />
      <div class="card-overlay">
        <div class="card-tags">
          <!-- <span v-for="tag in visibleTags" :key="tag" class="tag">{{ tag }}</span> -->
        </div>
        <div class="card-status">
          <!-- <span v-if="story.is_free" class="free-badge">Free</span> -->
          <span v-if="story.is_subscribed" class="subscribed-badge">Subscribed</span>
        </div>
      </div>
    </div>
    <div class="card-content">
      <h3 class="card-title">{{ story.title }}</h3>
      <div class="card-meta">
        <div class="views">
          <!-- <EyeIcon /> -->
          <!-- <span>{{ formatNumber(story.view_count) }}</span> -->
        </div>
        <div class="likes">
          <!-- <HeartIcon /> -->
          <!-- <span>{{ formatNumber(story.like_count) }}</span> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { Story } from '@/api/stories'
import { formatNumber } from '@/utils/util'
// import EyeIcon from '@/assets/icon/eye.svg'
// import HeartIcon from '@/assets/icon/heart.svg'

const props = defineProps<{
  story: Story
}>()

const emit = defineEmits<{
  (e: 'click', story: Story): void
  (e: 'image-loaded', story: Story): void
  (e: 'subscription-change', story: Story): void
  (e: 'need-login'): void
}>()

const isLoading = ref(false)

// 处理点击事件
const handleClick = () => {
  emit('click', props.story)
}

// 处理图片加载完成事件
const handleImageLoaded = () => {
  emit('image-loaded', props.story)
}
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.story-card {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  background-color: var(--bg-card, rgba(255, 255, 255, 0.08));
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 8px var(--shadow-color, rgba(0, 0, 0, 0.2));

  /* 确保暗色模式下有足够的对比度 */
  body.dark-theme & {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px var(--shadow-color);

    .card-image img {
      transform: scale(1.05);
    }
  }

  &.is-free {
    .card-status .free-badge {
      display: flex;
    }
  }

  &.is-subscribed {
    .card-status .subscribed-badge {
      display: flex;
    }
  }

  &.is-loading {
    pointer-events: none;
    opacity: 0.7;
  }

  .card-image {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 133%; // 3:4 aspect ratio
    overflow: hidden;

    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.5s ease;
    }

    .card-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 12px;
      background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.3) 0%,
        rgba(0, 0, 0, 0) 30%,
        rgba(0, 0, 0, 0) 70%,
        rgba(0, 0, 0, 0.5) 100%
      );
    }

    .card-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      .tag {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
        font-size: 12px;
        font-weight: 500;
        backdrop-filter: blur(4px);
      }
    }

    .card-status {
      display: flex;
      justify-content: flex-end;

      .free-badge,
      .subscribed-badge {
        display: none;
        align-items: center;
        justify-content: center;
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        backdrop-filter: blur(4px);
      }

      .free-badge {
        background-color: var(--accent-color);
        color: #fff;
      }

      .subscribed-badge {
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
      }
    }
  }

  .card-content {
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;

    .card-title {
      font-size: 16px;
      font-weight: 500;
      color: var(--text-primary, #ffffff);
      margin: 0 0 12px 0;
      line-height: 1.3;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .card-meta {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-top: auto;

      .views,
      .likes {
        display: flex;
        align-items: center;
        gap: 4px;
        color: var(--text-secondary, rgba(255, 255, 255, 0.7));
        font-size: 14px;

        svg {
          width: 16px;
          height: 16px;
        }
      }
    }
  }
}

// 响应式调整
@media screen and (max-width: 1200px) {
  .story-card {
    .card-image {
      .card-overlay {
        padding: 10px;
      }

      .card-tags {
        .tag {
          padding: 3px 6px;
          font-size: 11px;
        }
      }
    }

    .card-content {
      padding: 12px;

      .card-title {
        font-size: 15px;
        margin-bottom: 10px;
      }

      .card-meta {
        gap: 12px;

        .views,
        .likes {
          font-size: 13px;

          svg {
            width: 14px;
            height: 14px;
          }
        }
      }
    }
  }
}
</style>
