<template>
  <PCModal v-model="modalVisible" title="Daily tasks" @close="handleClose">
    <!-- 签到部分 -->
    <div class="section checkin-section">
      <div class="section-title">Daily check-in</div>

      <div class="checkin-container">
        <div class="checkin-grid">
          <div
            v-for="[key, reward] in Object.entries(sysConfigStore.checkInCoinsPerDay || {})"
            :key="key"
            class="checkin-item"
            :class="{
              'is-today': checkinStore.getDayNumber(key) === checkinStore.currentDay,
              'is-claimed':
                checkinStore.getDayNumber(key) < checkinStore.currentDay ||
                (checkinStore.getDayNumber(key) === checkinStore.currentDay &&
                  checkinStore.todayClaimed)
            }"
          >
            <div class="diamond-container">
              <div
                class="diamond-icon"
                :class="{
                  'is-today': checkinStore.getDayNumber(key) === checkinStore.currentDay
                }"
              >
                <img
                  class="credit-icon"
                  src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
                />
                <div
                  v-if="
                    checkinStore.getDayNumber(key) < checkinStore.currentDay ||
                    (checkinStore.getDayNumber(key) === checkinStore.currentDay &&
                      checkinStore.todayClaimed)
                  "
                  class="check-mark"
                >
                  ✓
                </div>
                <div v-else class="reward-amount">
                  {{ reward }}
                </div>
              </div>
            </div>
            <div
              class="day-label"
              :class="{
                'is-today': checkinStore.getDayNumber(key) === checkinStore.currentDay
              }"
            >
              Day {{ checkinStore.getDayNumber(key) }}
            </div>
          </div>
        </div>

        <button
          class="claim-button"
          :class="{ 'is-claimed': checkinStore.todayClaimed }"
          :disabled="checkinStore.loading || checkinStore.todayClaimed"
          @click="handleClaim"
        >
          <template v-if="checkinStore.loading">
            <a-spin :size="18" />
          </template>
          <template v-else-if="checkinStore.todayClaimed"> Back tomorrow for more! </template>
          <template v-else> Get Today's Reward 🎁 </template>
        </button>
      </div>
    </div>

    <!-- 任务列表部分 -->
    <div class="section tasks-section">
      <div class="section-title">Task</div>

      <div class="task-list">
        <!-- 动态渲染任务列表 -->
        <template v-if="!tasksStore.loading">
          <div
            v-for="task in tasksStore.tasks"
            :key="task.id"
            class="task-item"
            :class="{ 'is-completed': task.is_done }"
          >
            <div class="task-icon">
              <img class="credit-icon" src="https://cdn.magiclight.ai/assets/mobile/diamond.png" />
              <div class="reward-amount">{{ task.reward }}</div>
            </div>
            <div class="task-info">
              <div class="task-title">{{ task.name }}</div>
              <div class="task-description">{{ task.description }}</div>
              <div v-if="task.count > 0 && !task.is_done" class="task-progress">
                {{ task.count }}/{{ task.limit }}
              </div>
            </div>
            <button
              class="task-button"
              :class="{
                'check-button': task.type === 'play_game',
                'ok-button': task.type !== 'play_game',
                'completed-button': task.is_done
              }"
              :disabled="task.is_done || tasksStore.loading"
              @click="handleTaskAction(task)"
            >
              <template v-if="tasksStore.loading && currentTaskId === task.id">
                <a-spin :size="18" />
              </template>
              <template v-else-if="task.is_done">Completed</template>
              <template v-else-if="task.type === 'play_game'">Check</template>
              <template v-else>Ok</template>
            </button>
          </div>
        </template>

        <!-- 加载中显示 - 骨架屏 -->
        <template v-else>
          <div v-for="i in 3" :key="`skeleton-${i}`" class="task-item skeleton-item">
            <div class="task-icon skeleton">
              <div class="skeleton-circle"></div>
            </div>
            <div class="task-info">
              <div class="task-title skeleton"></div>
              <div class="task-description skeleton"></div>
            </div>
            <div class="task-button skeleton"></div>
          </div>
        </template>

        <!-- 无任务时显示 -->
        <div v-if="!tasksStore.loading && tasksStore.tasks.length === 0" class="no-tasks">
          <p>No tasks available at the moment.</p>
        </div>

        <!-- 全部完成显示 -->
        <div
          v-if="!tasksStore.loading && tasksStore.allTasksCompleted && tasksStore.tasks.length > 0"
          class="all-completed"
        >
          <div class="completed-icon">✓</div>
          <p>All tasks completed for today!</p>
        </div>
      </div>
    </div>
  </PCModal>
</template>

<script setup lang="ts">
import { watch, ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useSysConfigStore } from '@/store/sysconfig'
import { useCheckinStore } from '@/store/checkin'
import { useTasksStore } from '@/store/tasks'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { Message } from '@/mobile/components/Message'
import type { TaskItem } from '@/interface/tasks'
import PCModal from '@/pc/components/PCModal.vue'

const router = useRouter()
const sysConfigStore = useSysConfigStore()
const checkinStore = useCheckinStore()
const tasksStore = useTasksStore()
const currentTaskId = ref<string | null>(null)

// 使用计算属性来连接 checkinStore.visible 和 PCModal 的 v-model
const modalVisible = computed({
  get: () => checkinStore.visible,
  set: (value: boolean) => {
    if (!value) {
      checkinStore.hideModal()
    }
  }
})

const handleClose = () => {
  reportEvent(ReportEvent.CheckinModalClose)
  checkinStore.hideModal()
}

const handleClaim = async () => {
  if (checkinStore.todayClaimed) {
    checkinStore.hideModal()
    return
  }

  const entry =
    Object.entries(sysConfigStore.checkInCoinsPerDay || {}).find(
      ([key]) => checkinStore.getDayNumber(key) === checkinStore.currentDay
    ) || []
  const key = entry[0]
  const reward = entry[1]

  if (!key) return

  reportEvent(ReportEvent.CheckinModalClaimClick, {
    day: checkinStore.currentDay,
    reward: reward
  })

  const success = await checkinStore.claimDailyReward()
  if (success) {
    reportEvent(ReportEvent.DailyTasksClaimSuccess, {
      day: checkinStore.currentDay,
      reward: reward
    })
    Message.success(`You've claimed ${reward} diamonds!`)
  }
}

const handleTaskAction = async (task: TaskItem) => {
  if (task.is_done || tasksStore.loading) {
    return
  }

  currentTaskId.value = task.id

  try {
    switch (task.type) {
      case 'play_game': {
        checkinStore.hideModal()
        router.push('/')
        break
      }
      case 'share': {
        checkinStore.hideModal()
        router.push('/')
        break
      }
      case 'invite': {
        const inviteCode = await tasksStore.fetchInviteCode()
        if (inviteCode) {
          try {
            const url = `${window.location.origin}?invite=${inviteCode}`
            await navigator.clipboard.writeText(url)
            Message.success('Invite code copied to clipboard!')
          } catch (err) {
            console.error('Failed to copy invite code:', err)
            Message.error('Failed to copy invite code')
          }
        }
        break
      }
    }
  } catch (error) {
    console.error('Failed to complete task:', error)
  } finally {
    currentTaskId.value = null
  }
}

watch(
  () => checkinStore.visible,
  async (newVal) => {
    if (newVal) {
      reportEvent(ReportEvent.CheckinModalOpen)

      // 当弹窗打开时，获取签到和任务数据
      if (!sysConfigStore.checkInCoinsPerDay) {
        await sysConfigStore.fetchConfigs()
      }

      // 获取任务列表
      await tasksStore.fetchTasks()
    }
  }
)
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.section {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
}

.checkin-container {
  background: rgba(224, 224, 224, 0.25);
  border-radius: 20px;
  padding: 24px;
  position: relative;
  overflow: hidden; // 确保内容不会溢出
}

.checkin-grid {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  overflow: visible;
  align-items: center;

  position: relative;

  // 添加一条贯穿所有签到项的连接线
  &::before {
    content: '';
    position: absolute;
    top: 35px; // 调整位置，使其与钻石图标中心对齐
    left: 37.5px; // 从第一个项的中心开始
    width: calc(100% - 75px); // 宽度为总宽度减去两端的项的一半宽度
    height: 2px;
    background: rgba(202, 147, 242, 0.35);
    z-index: 0; // 确保在最底层
  }

  .checkin-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 75px;
    text-align: center;
    position: relative;
  }

  .diamond-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 8px;
    z-index: 2; // 确保钻石容器在连接线上方
    background-color: var(--bg-secondary); // 添加背景色，确保不透明
    border-radius: 20px; // 圆形背景
  }

  .diamond-icon {
    position: relative;
    width: 60px;
    height: 60px;
    background: rgba(202, 147, 242, 0.15);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 4px;
    overflow: hidden;
    padding: 8px;
    z-index: 10;

    &.is-today {
      background: #ca93f2;
      border: 1px solid #1f0038;
      .reward-amount {
        color: #daff96;
      }
    }

    .credit-icon {
      width: 24px;
      height: 24px;
      z-index: 1;
    }

    .reward-amount {
      font-size: 16px;
      font-weight: 600;
      color: var(--reward-amount-color, #daff96);
      z-index: 2;
    }

    .check-mark {
      color: #fff;
      font-size: 16px;
      font-weight: bold;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 12px;
      z-index: 2;
    }
  }

  &.is-claimed .diamond-icon {
    opacity: 0.7;
  }

  .day-label {
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.45);
    margin-top: 8px;

    &.is-today {
      color: rgba(0, 0, 0, 0.85);
    }
  }
}

.claim-button {
  width: 100%;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  background: linear-gradient(180deg, #d6cafe 0%, #ca93f2 100%);
  color: #1f0038;
  box-shadow: 0 4px 8px rgba(202, 147, 242, 0.3);
  position: relative;
  overflow: hidden;
  border-radius: 40px;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: #d6cafe;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(202, 147, 242, 0.4);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(202, 147, 242, 0.2);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }

  &.is-claimed {
    background: rgba(202, 147, 242, 0.5);
    color: rgba(255, 255, 255, 0.8);
    box-shadow: none;
  }
}

// 任务列表样式
.tasks-section {
  .task-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .task-item {
    display: flex;
    align-items: center;
    background: rgba(202, 147, 242, 0.1);
    border-radius: 16px;
    padding: 16px;
    position: relative;

    &.is-completed {
      opacity: 0.7;
    }

    .task-icon {
      position: relative;
      width: 48px;
      height: 48px;
      background: var(--bg-tertiary);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin-right: 16px;
      flex-shrink: 0;

      .credit-icon {
        width: 20px;
        height: 20px;
      }

      .reward-amount {
        font-size: 12px;
        font-weight: 600;
        color: var(--reward-amount-color, #daff96);
      }
    }

    .task-info {
      flex: 1;
      min-width: 0;

      .task-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 4px;
      }

      .task-description {
        font-size: 14px;
        color: var(--text-secondary);
        margin-bottom: 4px;
      }

      .task-progress {
        font-size: 12px;
        color: var(--text-tertiary);
      }
    }

    .task-button {
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      border: none;
      margin-left: 16px;
      flex-shrink: 0;

      &.check-button {
        background: #ca93f2;
        color: #1f0038;
      }

      &.ok-button {
        background: #ca93f2;
        color: #1f0038;
      }

      &.completed-button {
        background: rgba(202, 147, 242, 0.3);
        color: var(--text-secondary);
        cursor: default;
      }

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }
    }
  }

  // 骨架屏样式
  .skeleton-item {
    .skeleton {
      background: linear-gradient(
        90deg,
        var(--bg-tertiary) 25%,
        var(--bg-hover) 50%,
        var(--bg-tertiary) 75%
      );
      background-size: 200% 100%;
      animation: skeleton-loading 1.5s infinite;
      border-radius: 4px;
    }

    .skeleton-circle {
      width: 100%;
      height: 100%;
      border-radius: 12px;
    }

    .task-info {
      .task-title.skeleton {
        height: 20px;
        width: 70%;
        margin-bottom: 8px;
      }

      .task-description.skeleton {
        height: 16px;
        width: 90%;
      }
    }

    .task-button.skeleton {
      width: 80px;
      height: 36px;
    }
  }

  @keyframes skeleton-loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }

  // 无任务和全部完成样式
  .no-tasks,
  .all-completed {
    text-align: center;
    padding: 24px;
    color: var(--text-secondary);
  }

  .all-completed {
    .completed-icon {
      width: 48px;
      height: 48px;
      background: rgba(202, 147, 242, 0.3);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      margin: 0 auto 16px;
      color: var(--text-primary);
    }
  }
}
</style>
