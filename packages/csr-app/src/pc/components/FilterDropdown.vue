<template>
  <div class="filter-dropdown" v-click-outside="closeDropdown">
    <button
      class="dropdown-trigger"
      :class="{ active: isOpen, 'has-selection': hasSelection }"
      @click="toggleDropdown"
    >
      <slot name="trigger"></slot>
    </button>
    <div class="dropdown-menu" v-show="isOpen">
      <slot name="default" :closeDropdown="closeDropdown"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineExpose } from 'vue'
import { useThemeStore } from '@/store/theme'

const props = defineProps<{
  hasSelection?: boolean
}>()

const themeStore = useThemeStore()
const isDarkTheme = computed(() => themeStore.isDarkTheme)

const isOpen = ref(false)

const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

const closeDropdown = () => {
  isOpen.value = false
}

// 暴露方法给父组件
defineExpose({
  closeDropdown
})

// 点击外部关闭下拉菜单的指令
const vClickOutside = {
  mounted(el: any, binding: any) {
    el._clickOutside = (event: MouseEvent) => {
      if (!(el === event.target || el.contains(event.target as Node))) {
        binding.value(event)
      }
    }
    document.addEventListener('click', el._clickOutside)
  },
  unmounted(el: any) {
    document.removeEventListener('click', el._clickOutside)
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.filter-dropdown {
  position: relative;
  display: inline-block;

  .dropdown-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 32px;
    padding: 10px 16px;
    border-radius: 36px;
    background-color: var(--filter-dropdown-bg);
    color: var(--filter-dropdown-text);
    border: 1px solid var(--filter-dropdown-border);
    font-family: 'Work Sans', sans-serif;
    font-size: 12px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 160px;
    height: 32px;

    &:hover {
      background-color: var(--filter-dropdown-hover-bg);
    }

    &.active {
      background-color: var(--filter-dropdown-hover-bg);
      border-color: var(--filter-dropdown-border);
    }

    &.has-selection {
      // background-color: var(--accent-bg);
      // color: var(--text-secondary);
      // border-color: var(--text-secondary);

      &:hover {
        background-color: var(--accent-bg);
        opacity: 0.9;
      }
    }
  }

  .dropdown-menu {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    min-width: 160px;
    background-color: var(--bg-secondary);
    border-radius: 16px;
    box-shadow: 0 4px 12px var(--shadow-color);
    z-index: 100;
    overflow: hidden;
    border: 1px solid var(--filter-dropdown-border);
  }
}
</style>
