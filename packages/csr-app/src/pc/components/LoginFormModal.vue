<template>
  <Teleport to="body">
    <Transition name="fade">
      <div v-if="visible" class="login-modal-overlay" @click.self="handleClose">
        <div class="login-modal">
          <div class="close-button" @click="handleClose">
            <icon-close />
          </div>

          <div class="login-content">
            <div class="logo-wrapper">
              <img :src="logoUrl" alt="ReelPlay" />
              <h2>{{ props.title || 'Continue with' }}</h2>
            </div>

            <div class="form-wrapper">
              <div class="input-group">
                <input
                  v-model="form.email"
                  type="email"
                  placeholder="Enter your email"
                  :class="{ error: errors.email }"
                  @blur="validateEmail"
                />
                <span v-if="errors.email" class="error-text">{{ errors.email }}</span>
              </div>

              <div class="input-group verification-group">
                <input
                  v-model="form.verificationCode"
                  type="text"
                  placeholder="Enter code"
                  :class="{ error: errors.verificationCode }"
                />
                <button
                  class="send-code-button"
                  :class="{ 'is-inactive': !isEmailValid }"
                  @click="handleSendCode"
                  :disabled="loading || countdown > 0 || !isEmailValid"
                >
                  {{ countdown > 0 ? `Resend (${countdown})` : 'Send' }}
                </button>
                <span v-if="errors.verificationCode" class="error-text">{{
                  errors.verificationCode
                }}</span>
              </div>

              <button
                class="login-button"
                :class="{ 'is-inactive': !isEmailValid || !form.verificationCode }"
                @click="handleLogin"
                :disabled="loading || !isEmailValid || !form.verificationCode"
              >
                <span v-if="!loading">Log in</span>
                <a-spin v-else />
              </button>

              <div class="divider">
                <span>Or continue with</span>
              </div>
              <div class="social-buttons">
                <div class="social-row">
                  <div class="social-item" v-if="!isAndroidWebView()">
                    <button
                      class="social-button google"
                      @click="handleSocialLogin('google')"
                      :disabled="loading"
                    >
                      <GoogleIcon />
                    </button>
                  </div>
                  <div class="social-item">
                    <button
                      class="social-button discord"
                      @click="handleSocialLogin('discord')"
                      :disabled="loading"
                    >
                      <DiscordIcon />
                    </button>
                  </div>
                  <div class="social-item">
                    <button
                      class="social-button facebook"
                      @click="handleSocialLogin('facebook')"
                      :disabled="loading"
                    >
                      <FacebookIcon />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="terms-text">
            By signing in you agree with our<br />
            <a
              href="/terms"
              @click.prevent="!loading && router.push('/terms')"
              :class="{ disabled: loading }"
              >Terms of Service</a
            >
            ,
            <a
              href="/privacy"
              @click.prevent="!loading && router.push('/privacy')"
              :class="{ disabled: loading }"
              >Privacy Policy</a
            >,
            <a
              href="/complaints"
              @click.prevent="!loading && router.push('/complaints')"
              :class="{ disabled: loading }"
              >Complaints Policy</a
            >,
            <a
              href="/content-removal"
              @click.prevent="!loading && router.push('/content-removal')"
              :class="{ disabled: loading }"
              >Content Removal Policy</a
            >,
            <a
              href="/record-keeping"
              @click.prevent="!loading && router.push('/record-keeping')"
              :class="{ disabled: loading }"
              >18 U.S.C. 2257 Compliance</a
            >,
            <a
              href="/about"
              @click.prevent="!loading && router.push('/about')"
              :class="{ disabled: loading }"
              >About Us</a
            >,
            <a
              href="/refund"
              @click.prevent="!loading && router.push('/refund')"
              :class="{ disabled: loading }"
              >Refund and Returns Policy</a
            >.
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@/mobile/components/Message'
import { useUserStore } from '@/store/user'
import GoogleIcon from '@/assets/icon/google.svg'
import DiscordIcon from '@/assets/icon/discord.svg'
import FacebookIcon from '@/assets/icon/facebook.svg'
import { getSocialLoginUrl } from '@/api/social-login'
import type { SocialLoginType } from '@/api/social-login'
import { reportEvent, isInAppBrowser } from '@/utils'
import { ReportEvent } from '@/interface'
import { useThrottleFn } from '@vueuse/core'
import { sendVerificationCode } from '@/api/user'
import { isAndroidWebView } from '@/utils/isAndroidWebView'

const props = defineProps<{
  visible: boolean
  title?: string
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'login'): void
}>()

const logoUrl = computed(
  () => import.meta.env.VITE_LOGO_URL || 'https://cdn.magiclight.ai/assets/playshot/logo-v2.png'
)

const isEmailValid = computed(() => {
  if (!form.email) return false
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(form.email)
})

interface FormState {
  email: string
  verificationCode: string
}

interface FormErrors {
  email?: string
  verificationCode?: string
}

const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)
const countdown = ref(0)

const form = reactive<FormState>({
  email: '',
  verificationCode: ''
})

const errors = reactive<FormErrors>({})

const validateEmail = () => {
  if (!form.email) {
    errors.email = 'Email is required'
    return false
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(form.email)) {
    errors.email = 'Invalid email format'
    return false
  }

  errors.email = ''
  return true
}

const validateForm = (): boolean => {
  let isValid = true
  errors.verificationCode = ''

  // 验证邮箱
  if (!validateEmail()) {
    isValid = false
  }

  if (!form.verificationCode) {
    errors.verificationCode = 'Verification code is required'
    isValid = false
  }

  return isValid
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleSocialLoginBase = async (type: SocialLoginType) => {
  if (loading.value) {
    return
  }

  if (type === 'google' && isInAppBrowser()) {
    Message.info('Please use a browser to login with Google')
    return
  }

  reportEvent(ReportEvent.ClickLoginPageSignInWithGoogleOrDiscord, {
    userId: userStore.userInfo?.uuid,
    loginType: type
  })

  loading.value = true
  try {
    const origin = window.location.origin
    const redirect_url = `${origin}/user/social-callback?login_type=${type}`

    const response = await getSocialLoginUrl(type, redirect_url)
    if (!response.data?.isOk || !response.data?.data?.url) {
      throw new Error('Invalid response from server')
    }

    const loginUrl = response.data.data.url
    if (!loginUrl.startsWith('http')) {
      throw new Error('Invalid login URL')
    }

    // 使用 setTimeout 确保 loading 动画持续到跳转发生
    setTimeout(() => {
      window.location.href = loginUrl
    }, 0)
    return // 防止执行到 finally 块
  } catch (error: any) {
    console.error('Social login error:', error)
    Message.error(error.message || 'Failed to initiate social login')
    loading.value = false // 只在错误时才结束 loading
  }
}

const handleSocialLogin = useThrottleFn(handleSocialLoginBase, 2000)

const handleLogin = async () => {
  if (!validateForm()) return

  try {
    loading.value = true
    const isLogin = await userStore.loginWithCode({
      email: form.email,
      code: form.verificationCode,
      code_type: 'login',
      gender: 'male',
      user_id: userStore.userInfo?.uuid
    })
    if (!isLogin) return
    reportEvent(ReportEvent.LoginSuccess, {
      userId: userStore.userInfo?.uuid,
      type: 'email'
    })
    Message.success('Login successful')
    emit('login')
    emit('update:visible', false)
  } catch (error: any) {
    console.error(error)
    Message.error(error.message || 'Login failed')
  } finally {
    loading.value = false
  }
}

const handleSendCode = async () => {
  if (!isEmailValid.value) {
    Message.error(errors.email || 'Invalid email')
    return
  }
  try {
    loading.value = true
    const response = await sendVerificationCode({
      email: form.email,
      code_type: 'login'
    })
    if (!response.data.isOk) {
      Message.error(response.data.message)
      return
    }

    // Start countdown
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)

    Message.success('Verification code sent')
  } catch (error: any) {
    console.error(error)
    Message.error(error.message || 'Failed to send verification code')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.login-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.login-modal {
  width: 460px;
  background: var(--bg-secondary);
  border-radius: 20px;
  position: relative;
  padding: 64px 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: zoomIn 0.3s ease;
  box-shadow: 0 10px 30px var(--shadow-color);
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-secondary);
  transition: color 0.2s ease;

  &:hover {
    color: var(--text-primary);
  }
}

.login-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.logo-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;

  img {
    height: 54px;
  }

  h2 {
    font-family: 'Work Sans', sans-serif;
    font-size: 16px;
    font-weight: 400;
    color: var(--text-secondary);
    margin: 0;
  }
}

.form-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.input-group {
  position: relative;

  input {
    width: 100%;
    height: 40px;
    border-radius: 40px;
    border: 0.5px solid var(--accent-color);
    background: var(--bg-tertiary);
    padding: 0 16px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);

    &::placeholder {
      color: var(--text-tertiary);
    }

    &:focus {
      outline: none;
      border-color: var(--accent-color);
    }

    &.error {
      border-color: #ec4551;
    }
  }

  .error-text {
    color: #ec4551;
    font-size: 12px;
    margin-top: 4px;
    display: block;
    margin-left: 16px;
  }
}

.verification-group {
  position: relative;

  .send-code-button {
    position: absolute;
    right: 5px;
    top: 20px;
    transform: translateY(-50%);
    background: #ca93f2;
    border: none;
    border-radius: 40px;
    padding: 8px 24px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    height: 30px;
    &.is-inactive {
      opacity: 0.5;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }

  .error-text {
    margin-top: 4px;
    display: block;
    margin-left: 16px;
    position: relative;
    z-index: 1;
  }
}

.login-button {
  width: 100%;
  height: 42px;
  border: none;
  border-radius: 54px;
  background: #ca93f2;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;

  &.is-inactive {
    opacity: 0.5;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  &:not(:disabled):hover {
    opacity: 0.9;
  }

  &:not(:disabled):active {
    transform: scale(0.98);
  }
}

.social-buttons {
  .social-row {
    display: flex;
    justify-content: space-between;
    svg {
      width: 24px;
      height: 24px;
    }
  }

  .social-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .social-name {
      font-size: 14px;
      color: var(--text-secondary);
    }

    .social-button {
      width: 100px;
      height: 42px;
      border-radius: 31px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 0.5px solid var(--border-color);
      background: transparent;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
      }

      &:active {
        transform: translateY(1px);
      }

      &.google {
        background: #fff;
      }

      &.discord {
        background: #6563ff;
      }

      &.facebook {
        background: #0866ff;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}

.divider {
  display: flex;
  align-items: center;
  margin: 0;

  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 0.5px;
    background: var(--divider-color);
  }

  span {
    padding: 0 12px;
    font-size: 14px;
    color: var(--text-secondary);
  }
}

.terms-text {
  margin-top: 24px;
  font-size: 12px;
  text-align: center;
  color: var(--text-tertiary);
  line-height: 1.17;
}
</style>
