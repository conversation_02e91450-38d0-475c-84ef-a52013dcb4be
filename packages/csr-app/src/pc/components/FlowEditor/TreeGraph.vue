<template>
  <div class="tree-graph-container">
    <!-- 顶部故事设置和创建新章节区域 - 只在有节点时显示 -->
    <div class="top-actions">
      <button
        class="story-settings-btn"
        :class="{ selected: isStorySettingsSelected }"
        @click.stop="openStorySettings"
      >
        Story setting
      </button>
      <button
        class="create-level-btn"
        :class="{ selected: isCreateLevelSelected }"
        @click.stop="addNewChapter"
      >
        {{ createButtonText }}
      </button>
    </div>

    <!-- 图形工具栏 - 只在有节点时显示 -->
    <div class="graph-toolbar" v-if="hasNodes">
      <button class="toolbar-button" @click.stop="zoomIn" title="放大">
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
        </svg>
        放大
      </button>
      <button class="toolbar-button" @click.stop="zoomOut" title="缩小">
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M19,13H5V11H19V13Z" />
        </svg>
        缩小
      </button>
      <button class="toolbar-button" @click.stop="resetView" title="重置视图">
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z" />
        </svg>
        重置视图
      </button>
    </div>
    <div class="graph-container" ref="container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue'
import { Graph, Shape, Node } from '@antv/x6'
import { register } from '@antv/x6-vue-shape'
import { VueShape } from '@antv/x6-vue-shape'
import CustomNodeComponent from './CustomNodeComponent.vue'
import JumpNodeComponent from './JumpNodeComponent.vue'
import { TreeNode } from '@/pc/components/FlowEditor/types'

// 添加选中状态变量 - 初始化时Story setting应该为选中状态
const isStorySettingsSelected = ref(true)
const isCreateLevelSelected = ref(false)

// 注册自定义节点组件
register({
  shape: 'custom-node',
  component: CustomNodeComponent,
  width: 180,
  height: 60
})

// 注册跳转节点组件
register({
  shape: 'jump-node',
  component: JumpNodeComponent,
  width: 150,
  height: 40
})

const props = defineProps<{
  nodes: TreeNode[]
  rootNodeId?: string
}>()

// 计算是否有节点，用于控制界面元素显示
const hasNodes = computed(() => {
  console.log('props.nodes', props.nodes)
  // 如果节点数组为空，则认为没有节点
  if (!props.nodes || props.nodes.length === 0) return false

  // 检查是否存在非 story_outline 和非 _BEGIN_ 的节点
  const hasOtherNodes = props.nodes.some(
    (node) => node.id !== 'story_outline' && node.id !== '_BEGIN_'
  )

  if (hasOtherNodes) {
    return true
  }

  // 如果只有 story_outline 节点，视为空图
  if (props.nodes.length === 1 && props.nodes[0].id === 'story_outline') {
    return false
  }

  // 检查 _BEGIN_ 节点
  const beginNode = props.nodes.find((node) => node.id === '_BEGIN_')
  if (beginNode) {
    // 检查节点自身属性或 properties 中的信息
    const hasNextScene =
      beginNode.next_scene_id || (beginNode.properties && beginNode.properties.next_scene_id)

    // 如果 _BEGIN_ 节点有 next_scene_id，则不为空
    if (hasNextScene) {
      return true
    }
  }

  // 其他情况（通常是只有空的 story_outline 和空的 _BEGIN_），视为空图
  return false
})

const emit = defineEmits<{
  'node-selected': [node: TreeNode | null]
  'node-added': [parentNode: TreeNode, position: { x: number; y: number }]
  'node-context-menu': [node: TreeNode, position: { x: number; y: number }]
  'open-story-settings': []
  'add-new-chapter': [sceneGroup?: string]
  'add-event': [parentId: string]
  'add-scene-group': []
  'example-data-added': [nodes: TreeNode[]]
  'jump-node-click': [parentNodeId: string]
}>()

const container = ref<HTMLElement | null>(null)
const graph = ref<Graph | null>(null)

// 选中的节点
const selectedNode = ref<TreeNode | null>(null)

// 添加 nodeUpdateTimer 变量
const nodeUpdateTimer = ref<number | null>(null)

// 在setup中添加isFirstRender变量
const isFirstRender = ref(true)

// 计算属性：根据选中节点的类型动态显示按钮文字
const createButtonText = computed(() => {
  if (!selectedNode.value) {
    return 'Create scene group'
  } else if (selectedNode.value.type === 'group') {
    return 'Add chapter'
  } else if (selectedNode.value.type === 'begin' || selectedNode.value.type === 'chapter') {
    return 'Add event'
  } else {
    return 'Create new level'
  }
})

// 打开故事设置面板
const openStorySettings = (event: MouseEvent) => {
  // 防止事件冒泡
  event.stopPropagation()
  // 设置选中状态
  isStorySettingsSelected.value = true
  isCreateLevelSelected.value = false
  // 发射打开故事设置事件
  emit('open-story-settings')
}

// 根据选中节点类型动态执行不同操作
const addNewChapter = (event: MouseEvent) => {
  // 防止事件冒泡
  event.stopPropagation()
  // 设置选中状态
  isStorySettingsSelected.value = false
  isCreateLevelSelected.value = true

  // 根据选中节点类型执行不同操作
  if (!selectedNode.value) {
    // 未选中任何节点，创建场景组
    console.log('创建场景组')
    emit('add-scene-group')
  } else if (selectedNode.value.type === 'group') {
    // 选中场景组，添加章节
    console.log('为场景组添加章节:', selectedNode.value.id)
    emit('add-new-chapter', selectedNode.value.scene_group)
  } else if (selectedNode.value.type === 'begin' || selectedNode.value.type === 'chapter') {
    // 选中章节，添加事件
    console.log('为章节添加事件:', selectedNode.value.id)
    emit('add-event', selectedNode.value.id)
  } else {
    // 其他情况，默认创建章节
    console.log('默认创建章节')
    emit('add-new-chapter')
  }
}

// 组件加载时触发打开故事设置
onMounted(() => {
  if (container.value) {
    initGraph()
    renderTree()

    // 触发故事设置事件
    emit('open-story-settings')

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)

    // 设置节点更新检查定时器
    setupNodeUpdateTimer()
  }
})

// 该方法可以被父组件直接调用，强制更新节点视图而不重新渲染整棵树
const forceNodesUpdate = (nodeIds?: string[]) => {
  console.log('强制更新节点视图', nodeIds)
  if (!graph.value) return false

  // 检查是否需要重新渲染跳转节点
  let needRenderJumpNodes = false

  if (nodeIds && nodeIds.length) {
    // 更新指定的节点
    nodeIds.forEach((id) => {
      const nodeData = props.nodes.find((node) => node.id === id)
      if (nodeData) {
        updateNodeView(id, nodeData)

        // 如果节点的next_scene_id发生变化，需要重新渲染跳转节点
        if (nodeData.next_scene_id) {
          console.log('检测到节点有 next_scene_id:', nodeData.next_scene_id)
          needRenderJumpNodes = true

          // 删除现有的跳转节点
          const jumpNodeId = `jump_${id}`
          const jumpNode = graph.value.getCellById(jumpNodeId)
          if (jumpNode) {
            console.log('删除现有跳转节点:', jumpNodeId)
            graph.value.removeCell(jumpNode)
          }

          // 获取父节点位置
          const parentNode = graph.value.getCellById(id) as Node
          if (parentNode) {
            try {
              const parentPos = parentNode.getPosition()
              const parentSize = parentNode.getSize()

              // 创建新的跳转节点
              console.log('准备创建新的跳转节点:', nodeData.id, nodeData.next_scene_id)
              const jumpNode = createJumpNode(
                nodeData,
                parentPos.x + parentSize.width / 2 - 100, // 将跳转节点放在父节点的正上方，考虑跳转节点的宽度为200，所以偏移100
                parentPos.y - 60 // 将跳转节点放在父节点的上方，保持一定距离
              )
              // 将跳转节点添加到图形中
              graph.value.addNode(jumpNode)
              console.log('新的跳转节点已添加')
            } catch (error) {
              console.error('获取节点位置时出错:', error)
              // 如果出错，则重新渲染整个图
              setTimeout(() => {
                renderTree()
              }, 100)
            }
          }
        } else {
          // 如果节点没有next_scene_id，删除对应的跳转节点
          const jumpNodeId = `jump_${id}`
          const jumpNode = graph.value.getCellById(jumpNodeId)
          if (jumpNode) {
            graph.value.removeCell(jumpNode)
          }

          // 删除可能存在的连接线
          graph.value.getEdges().forEach((edge) => {
            // 获取连接线的源节点和目标节点
            const sourceId = edge.getSourceCellId()
            const targetId = edge.getTargetCellId()

            // 如果连接线的源节点是当前节点，则删除该连接线
            if (sourceId === id && targetId !== id) {
              graph.value.removeCell(edge)
            }
          })
        }
      }
    })
  } else {
    // 更新所有节点
    refreshAllNodes()
    needRenderJumpNodes = true
  }

  // 如果需要重新渲染跳转节点，重新渲染整个图
  if (needRenderJumpNodes) {
    // 使用延迟确保节点更新完成
    setTimeout(() => {
      renderTree()
    }, 100)
  }

  return true
}

// 替换原有的深度监听实现，采用更可靠的方法检测变化
watch(
  () => props.nodes,
  (newNodes, oldNodes) => {
    console.log('节点数组变化检测')

    // 如果图表尚未初始化，不处理
    if (!graph.value) return

    // 获取图表中当前的所有节点
    const graphNodes = graph.value.getNodes()
    console.log('当前图表节点:', graphNodes)

    // 如果节点数量变化，完全重绘图表
    if (!oldNodes || newNodes.length !== oldNodes?.length) {
      console.log('节点数量变化，重新渲染整棵树')
      clearTimeout(renderTimeoutId)
      renderTimeoutId = setTimeout(() => {
        renderTree()
      }, 50)
      return
    }

    // 检查是否有 ID 变化
    const oldNodeIds = oldNodes.map((node) => node.id)
    const newNodeIds = newNodes.map((node) => node.id)
    console.log(newNodeIds, 'newNodeIds')
    const idChanged = JSON.stringify(oldNodeIds) !== JSON.stringify(newNodeIds)

    if (idChanged) {
      console.log('ID 变化检测: 节点 ID 变化，重新渲染整棒树')
      clearTimeout(renderTimeoutId)
      renderTimeoutId = setTimeout(() => {
        renderTree()
      }, 50)
      return
    }

    // 获取当前图表中的节点 ID
    const graphNodeIds = graphNodes.map((node) => node.id)

    // 查找有变化的节点
    const changedNodeIds: string[] = []
    newNodes.forEach((newNode, index) => {
      // 检查节点是否在画布上
      if (!graphNodeIds.includes(newNode.id)) {
        console.log('跳过不在画布上的节点:', newNode.id)
        return // 跳过不在画布上的节点
      }

      const oldNode = oldNodes[index]

      // 检查关键属性是否变化
      if (
        newNode.name !== oldNode.name ||
        newNode.type !== oldNode.type ||
        JSON.stringify(newNode.properties) !== JSON.stringify(oldNode.properties)
      ) {
        console.log('节点属性变化:', newNode.id, newNode.name)
        changedNodeIds.push(newNode.id)
      }
    })

    // 更新变化的节点
    if (changedNodeIds.length > 0) {
      console.log('检测到节点变化，更新节点:', changedNodeIds)
      changedNodeIds.forEach((id) => {
        const nodeData = newNodes.find((node) => node.id === id)
        if (nodeData) {
          updateNodeView(id, nodeData)
        }
      })
    }
  },
  { deep: true }
)

// 优化后的节点更新检查定时器
const setupNodeUpdateTimer = () => {
  // 清除现有定时器
  if (nodeUpdateTimer.value) {
    clearInterval(nodeUpdateTimer.value)
  }

  // 使用 Set 来跟踪待更新的节点
  const pendingUpdates = new Set<string>()
  let lastUpdateTime = 0
  const UPDATE_INTERVAL = 5000 // 5秒更新一次
  const MIN_UPDATE_INTERVAL = 1000 // 最小更新间隔1秒

  nodeUpdateTimer.value = window.setInterval(() => {
    const now = Date.now()
    if (now - lastUpdateTime < MIN_UPDATE_INTERVAL) {
      return // 如果距离上次更新时间太短，跳过本次更新
    }

    if (pendingUpdates.size === 0) {
      return // 没有待更新的节点，跳过本次更新
    }

    // 创建节点数据映射以减少查找时间
    const nodeDataMap = new Map(props.nodes.map((node) => [node.id, node]))

    // 使用 requestAnimationFrame 进行批量处理
    requestAnimationFrame(() => {
      const updates = Array.from(pendingUpdates)
      pendingUpdates.clear()

      // 批量处理节点更新
      updates.forEach((nodeId) => {
        const nodeData = nodeDataMap.get(nodeId)
        if (nodeData) {
          updateNodeView(nodeId, nodeData)
        }
      })

      lastUpdateTime = now
    })
  }, UPDATE_INTERVAL)

  // 返回清理函数
  return () => {
    if (nodeUpdateTimer.value) {
      clearInterval(nodeUpdateTimer.value)
      nodeUpdateTimer.value = null
    }
  }
}

// 避免深度观察导致的循环更新问题
// 使用一个局部变量保存最后一次渲染的节点数
let lastNodesLength = 0
let renderTimeoutId: ReturnType<typeof setTimeout> | null = null

// 初始化图形
const initGraph = () => {
  if (!container.value) {
    console.warn('容器元素未准备好')
    return
  }

  try {
    graph.value = new Graph({
      container: container.value,
      width: container.value.clientWidth,
      height: container.value.clientHeight,
      background: {
        color: '#1a0030'
      },
      grid: {
        visible: false
      },
      interacting: {
        nodeMovable: false,
        edgeMovable: false,
        edgeLabelMovable: false,
        magnetConnectable: false
      },
      panning: {
        enabled: true, // 启用画布拖动
        modifiers: [] // 不需要修饰键，直接左键拖动
      },
      mousewheel: {
        enabled: true, // 启用鼠标滚轮缩放
        modifiers: ['ctrl', 'meta'] // 按住这些键可以缩放
      },
      autoResize: true,
      virtual: true,
      async: true,
      connecting: {
        snap: true,
        allowBlank: false,
        allowLoop: false,
        highlight: true,
        connector: 'smooth',
        connectionPoint: 'boundary',
        anchor: 'center',
        validateConnection({ sourceCell, targetCell }) {
          if (!sourceCell || !targetCell) return false
          const sourceData = sourceCell.getData()
          const targetData = targetCell.getData()
          return sourceData && targetData
        }
      }
    })

    // 设置事件监听
    setupGraphEventListeners()

    // 初始化完成后设置节点更新定时器
    setupNodeUpdateTimer()

    console.log('图表初始化成功')
  } catch (error) {
    console.error('初始化图形时出错:', error)
  }
}

// 设置图表事件监听
const setupGraphEventListeners = () => {
  if (!graph.value) return

  // 节点点击事件
  graph.value.on('node:click', ({ node }) => {
    if (!graph.value) return

    try {
      console.log('节点被点击:', node.id)

      // 获取节点数据
      const nodeData = node.getData() as any
      if (!nodeData?.id) {
        console.warn('节点数据缺少ID，无法处理点击事件')
        return
      }

      const clickedNodeId = nodeData.id

      // 如果点击已选中的节点，则取消选中
      if (selectedNode.value?.id === clickedNodeId) {
        selectedNode.value = null
        emit('node-selected', null)

        // 记录取消选中事件
        console.log('取消选中节点:', clickedNodeId)
      } else {
        // 否则选中点击的节点
        const completeNode = props.nodes.find((n) => n.id === clickedNodeId)

        if (completeNode) {
          selectedNode.value = completeNode
          emit('node-selected', selectedNode.value)

          // 记录选中事件
          console.log('选中节点:', clickedNodeId, '节点类型:', completeNode.type)
        } else {
          console.warn(`找不到ID为 ${clickedNodeId} 的完整节点数据`)
        }
      }

      const allNodes = graph.value.getNodes()
      allNodes.forEach((n) => {
        const data = n.getData() as any
        if (data) {
          const isNodeSelected = data.id === selectedNode.value?.id

          // 更新数据
          n.setData({
            ...data,
            isSelected: isNodeSelected
          })

          // 直接设置节点样式 - 通过添加/移除CSS类
          if (isNodeSelected) {
            // 添加选中样式类
            n.addTools([
              {
                name: 'boundary',
                args: {
                  padding: 0,
                  attrs: {
                    fill: 'none',
                    stroke: '#ca93f2',
                    'stroke-width': 1,
                    'stroke-dasharray': 0
                  }
                }
              }
            ])
          } else {
            // 移除选中样式类
            n.removeTools()
          }
        }
      })
    } catch (error) {
      console.error('处理节点点击事件失败:', error)
    }
  })

  // 空白处点击事件
  graph.value.on('blank:click', () => {
    if (!graph.value) return

    try {
      // 只有在当前有选中节点时才进行处理
      if (selectedNode.value) {
        const previousSelectedId = selectedNode.value.id
        selectedNode.value = null
        emit('node-selected', null)

        console.log('点击空白区域，取消选中节点:', previousSelectedId)

        // 清除所有节点的选中状态
        const allNodes = graph.value.getNodes()
        allNodes.forEach((n) => {
          const data = n.getData() as any
          if (data) {
            // 更新数据
            n.setData({
              ...data,
              isSelected: false
            })

            // 移除选中样式
            n.removeTools()
          }
        })
      }
    } catch (error) {
      console.error('处理空白处点击事件失败:', error)
    }
  })

  // 右键菜单事件
  graph.value.on('node:contextmenu', ({ node, e }) => {
    e.preventDefault()

    try {
      const nodeData = node.getData<TreeNode>()
      if (nodeData) {
        // 使用深拷贝避免对原始数据的修改
        const nodeDataCopy = JSON.parse(JSON.stringify(nodeData))

        // 记录右键菜单事件
        console.log('节点右键菜单:', nodeData.id)

        // 获取节点的位置和大小信息
        const nodeBBox = node.getBBox()

        // 计算菜单应该出现的位置 - 节点右侧中间
        const menuX = nodeBBox.x + nodeBBox.width
        const menuY = nodeBBox.y + nodeBBox.height / 2

        // 将节点坐标转换为屏幕坐标
        const screenPoint = graph.value.localToGraph(menuX, menuY)

        nextTick(() => {
          // 传递节点数据和计算出的菜单位置
          emit('node-context-menu', nodeDataCopy, {
            x: screenPoint.x,
            y: screenPoint.y
          })
        })
      } else {
        console.warn('节点没有有效数据，无法触发右键菜单')
      }
    } catch (error) {
      console.error('处理右键菜单事件失败:', error)
    }
  })
}

// 渲染树形图
const renderTree = () => {
  // 节点和边的初始化
  if (!graph.value) return

  // // 检查是否应该添加示例数据
  // if (!props.nodes || props.nodes.length === 0) {
  //   // addExampleData()
  //   return
  // }

  // 在清除图之前，保存当前的缩放和平移状态
  const currentScale = graph.value.zoom()
  const currentTranslate = graph.value.translate()

  // 保存已存在的节点信息，以便重用
  const existingNodes = new Map()
  graph.value.getNodes().forEach((node) => {
    const data = node.getData()
    if (data && data.id) {
      existingNodes.set(data.id, node)
    }
  })

  // 清除图
  graph.value.clearCells()

  // 创建一个Map来存储所有创建的节点，方便后续连接
  const nodeMap = new Map()

  // 布局参数
  const rootX = 100
  const rootY = 150
  const horizontalGap = 280 // 增加层级之间的水平间距
  const verticalGap = 120 // 增加节点之间的垂直间距
  const groupVerticalGap = 200 // 增加组之间的垂直间距

  // 节点分类
  const rootNodes = []
  const groupNodes = []
  const sceneNodes = {}
  const otherNodes = []

  // 对节点进行分类
  props.nodes.forEach((node) => {
    // 跳过事件节点
    if (node.type === 'event') {
      return
    }

    if (node.type === 'root' || node.id === 'root') {
      rootNodes.push(node)
    } else if (node.type === 'group') {
      groupNodes.push(node)
    } else if (node.parentId) {
      if (!sceneNodes[node.parentId]) {
        sceneNodes[node.parentId] = []
      }
      sceneNodes[node.parentId].push(node)
    } else {
      otherNodes.push(node)
    }
  })

  // 递归创建子节点的函数 - 在内部定义以访问作用域内的变量
  const createChildNodes = (parentId, parentX, parentY, nodeMap) => {
    const children = sceneNodes[parentId] || []
    if (children.length === 0) return

    const childX = parentX + horizontalGap
    const childrenHeight = (children.length - 1) * verticalGap
    let startY = parentY - childrenHeight / 2

    children.forEach((child, idx) => {
      // 跳过事件节点
      if (child.type === 'event') return

      const childY = startY + idx * verticalGap
      const childNode = createNode(child, childX, childY)
      graph.value.addNode(childNode)
      nodeMap.set(child.id, childNode)

      // 连接父子节点
      createEdge(parentId, child.id)

      // 递归处理
      createChildNodes(child.id, childX, childY, nodeMap)
    })
  }

  // 创建根节点
  let rootNode
  if (rootNodes.length > 0) {
    rootNode = createNode(rootNodes[0], rootX, rootY)
  } else {
    rootNode = createNode(
      {
        id: 'root',
        name: '故事',
        type: 'root',
        properties: {
          isRoot: true,
          color: '#333'
        }
      },
      rootX,
      rootY
    )
  }
  graph.value.addNode(rootNode)
  nodeMap.set('root', rootNode)

  // 创建分组节点
  const groupCount = groupNodes.length
  groupNodes.forEach((group, index) => {
    const groupX = rootX + horizontalGap
    const groupY = rootY - ((groupCount - 1) * groupVerticalGap) / 2 + index * groupVerticalGap

    const groupNode = createNode(group, groupX, groupY)
    graph.value.addNode(groupNode)
    nodeMap.set(group.id, groupNode)

    // 连接根节点和分组节点
    createEdge('root', group.id)
  })

  // 创建分组下的节点
  groupNodes.forEach((group) => {
    const children = sceneNodes[group.id] || []
    if (children.length === 0) return

    const groupNode = nodeMap.get(group.id)
    if (!groupNode) return

    const groupPos = groupNode.getPosition()
    const sceneX = groupPos.x + horizontalGap

    // 计算这个组下所有子节点的垂直排布
    const childrenHeight = (children.length - 1) * verticalGap
    let startY = groupPos.y - childrenHeight / 2

    children.forEach((child, idx) => {
      // 跳过事件节点
      if (child.type === 'event') return

      const childY = startY + idx * verticalGap
      const sceneNode = createNode(child, sceneX, childY)
      graph.value.addNode(sceneNode)
      nodeMap.set(child.id, sceneNode)

      // 连接分组和场景节点
      createEdge(group.id, child.id)

      // 如果该场景有子节点，递归创建
      createChildNodes(child.id, sceneX, childY, nodeMap)
    })
  })

  // 处理其他未分类节点
  otherNodes.forEach((node) => {
    // 跳过事件节点
    if (node.type === 'event') return

    if (!nodeMap.has(node.id)) {
      const otherNode = createNode(node, rootX + horizontalGap, rootY + 300)
      graph.value.addNode(otherNode)
      nodeMap.set(node.id, otherNode)
    }
  })

  // 创建next_scene_id连接和跳转节点
  props.nodes.forEach((node) => {
    // 跳过事件节点
    if (node.type === 'event') return

    if (node.next_scene_id && nodeMap.has(node.id)) {
      // 不创建连接线，只创建跳转节点
      console.log('渲染树时发现节点有 next_scene_id:', node.id, node.next_scene_id)

      // 创建跳转节点
      const parentNode = nodeMap.get(node.id)
      if (parentNode) {
        const parentPos = parentNode.getPosition()
        const parentSize = parentNode.getSize()
        // 将跳转节点放在父节点的上方，而不是右侧，避免与子节点重叠
        console.log('准备创建跳转节点:', node.id, node.next_scene_id)
        const jumpNode = createJumpNode(
          node,
          parentPos.x + parentSize.width / 2 - 100, // 将跳转节点放在父节点的正上方，考虑跳转节点的宽度为200，所以偏移100
          parentPos.y - 60 // 将跳转节点放在父节点的上方，保持一定距离
        )
        // 将跳转节点添加到图形中
        graph.value.addNode(jumpNode)
        console.log('跳转节点已添加')
      }
    }
  })

  // 仅在首次渲染时自动适应视图
  if (isFirstRender.value) {
    setTimeout(() => {
      if (graph.value) {
        graph.value.zoom(0.8, { absolute: true })
        graph.value.centerContent()
      }
    }, 200)
  } else {
    // 在后续渲染中恢复之前的视图状态
    setTimeout(() => {
      if (graph.value) {
        graph.value.zoom(currentScale, { absolute: true })
        graph.value.translate(currentTranslate.tx, currentTranslate.ty)
      }
    }, 10)
  }

  isFirstRender.value = false

  // 打印节点统计信息
  if (graph.value) {
    const currentNodes = graph.value.getNodes()
    console.log(`渲染完成: 共创建 ${currentNodes.length} 个节点`)
    console.log(`节点映射表大小: ${nodeMap.size}`)

    // 检查是否有重复ID的节点
    const idCounts = new Map()
    currentNodes.forEach((node) => {
      const nodeData = node.getData()
      if (nodeData && nodeData.id) {
        idCounts.set(nodeData.id, (idCounts.get(nodeData.id) || 0) + 1)
      }
    })

    // 查找并输出重复ID
    const duplicates = Array.from(idCounts.entries())
      .filter(([id, count]) => count > 1)
      .map(([id, count]) => `${id} (${count}次)`)

    if (duplicates.length > 0) {
      console.warn(`检测到重复ID的节点: ${duplicates.join(', ')}`)
    } else {
      console.log('节点ID检查通过: 无重复ID')
    }
  }
}

// 创建跳转节点
const createJumpNode = (parentNode: TreeNode, x: number, y: number) => {
  console.log('父节点完整数据:', JSON.stringify(parentNode))
  console.log('父节点 next_scene_id:', parentNode.next_scene_id)

  // 创建节点数据对象
  const nodeData = {
    targetSceneId: parentNode.next_scene_id || '',
    parentNodeId: parentNode.id
  }

  console.log('创建跳转节点数据:', nodeData)

  // 创建节点实例
  const jumpNode = graph.value.createNode({
    id: `jump_${parentNode.id}`,
    x,
    y,
    width: 200, // 增加宽度以容纳更多文本
    height: 40,
    shape: 'jump-node',
    draggable: false,
    selectable: false,
    data: nodeData
  })

  // 添加点击事件
  jumpNode.on('click', () => {
    console.log('跳转节点被点击:', parentNode.id)
    emit('jump-node-click', parentNode.id)
  })

  // 添加额外的点击事件处理
  jumpNode.on('cell:click', () => {
    console.log('跳转节点 cell:click 事件:', parentNode.id)
    emit('jump-node-click', parentNode.id)
  })

  return jumpNode
}

// 创建节点
const createNode = (node: TreeNode, x: number, y: number) => {
  // 确保每个节点有唯一的ID
  if (!node.id) {
    console.warn('节点缺少ID，自动生成唯一ID')
    node.id = `node_${Date.now()}_${Math.floor(Math.random() * 1000)}`
  }

  const isSelected = selectedNode.value?.id === node.id

  // 根据节点类型选择不同大小和形状
  let width = 180
  let height = 60
  let shape = 'custom-node'

  // 根据节点类型设置不同的尺寸
  if (node.type === 'outline') {
    width = 220
    height = 70
  } else if (node.type === 'root') {
    width = 120
    height = 50
  } else if (node.type === 'group') {
    width = 180
    height = 50
  }

  // 创建节点数据对象，确保包含完整属性
  const nodeData = {
    id: node.id,
    name: node.name || '',
    type: node.type || 'node',
    parentId: node.parentId || null,
    properties: node.properties || {},
    isSelected: isSelected
  }

  // 检查该节点是否已存在于图表中
  if (graph.value) {
    const existingNode = graph.value.getCellById(node.id)
    if (existingNode) {
      console.warn(`节点ID '${node.id}' 已存在，请确保ID唯一性`)
    }
  }

  // 创建节点实例
  const nodeInstance = new VueShape({
    id: node.id, // 添加ID属性以确保唯一性
    x,
    y,
    width,
    height,
    shape: shape,
    draggable: false,
    selectable: true,
    data: nodeData
  })

  // 如果节点处于选中状态，添加高亮工具
  if (isSelected) {
    nodeInstance.addTools([
      {
        name: 'boundary',
        args: {
          padding: 3,
          attrs: {
            fill: 'none',
            stroke: '#ca93f2',
            'stroke-width': 2,
            'stroke-dasharray': 0
          }
        }
      }
    ])
  }

  return nodeInstance
}

// 创建连线
const createEdge = (sourceId: string, targetId: string, isAcrossGroup: boolean = false) => {
  if (!graph.value) return null

  // 检查ID是否有效
  if (!sourceId || !targetId) {
    console.warn(`无效的边连接: 源节点ID或目标节点ID为空`)
    return null
  }

  // 如果源节点和目标节点相同，不创建自环
  if (sourceId === targetId) {
    console.warn(`不创建自环连线: ${sourceId} -> ${targetId}`)
    return null
  }

  // 获取节点
  const sourceNode = graph.value.getCellById(sourceId)
  const targetNode = graph.value.getCellById(targetId)

  if (!sourceNode || !targetNode) {
    console.warn(`无法创建连线: ${sourceId} -> ${targetId}, 节点不存在`)
    return null
  }

  // 检查是否已存在相同的边
  const existingEdges = graph.value.getEdges()
  const edgeExists = existingEdges.some(
    (edge) => edge.getSourceCellId() === sourceId && edge.getTargetCellId() === targetId
  )

  if (edgeExists) {
    console.log(`已存在连线: ${sourceId} -> ${targetId}, 跳过创建`)
    return null
  }

  try {
    const edgeStyle = isAcrossGroup
      ? {
          stroke: 'rgba(255, 128, 0, 0.7)',
          strokeWidth: 2,
          strokeDasharray: '5 5',
          targetMarker: {
            name: 'classic',
            width: 12,
            height: 8
          }
        }
      : {
          stroke: 'rgba(255, 255, 255, 0.5)',
          strokeWidth: 2,
          targetMarker: {
            name: 'classic',
            width: 12,
            height: 8
          }
        }

    return graph.value.addEdge(
      new Shape.Edge({
        source: { cell: sourceId },
        target: { cell: targetId },
        attrs: {
          line: edgeStyle
        },
        router: {
          name: 'normal'
        },
        connector: {
          name: 'rounded',
          args: {
            radius: 8
          }
        },
        zIndex: 0,
        interactive: false
      })
    )
  } catch (error) {
    console.error(`创建连线时发生错误: ${sourceId} -> ${targetId}`, error)
    return null
  }
}

// 处理窗口大小调整
const handleResize = () => {
  if (graph.value && container.value) {
    const { clientWidth, clientHeight } = container.value
    graph.value.resize(clientWidth, clientHeight)

    // 不重新渲染整个树图，只调整大小
    // 如果需要重新居中内容，可以使用以下代码
    // graph.value.centerContent()
  }
}

// 更新节点视图
const updateNodeView = (nodeId: string, nodeData: TreeNode) => {
  if (!graph.value) return false

  const node = graph.value.getCellById(nodeId)
  if (!node) {
    console.warn('找不到节点:', nodeId)
    return false
  }

  // 获取当前节点是否被选中
  const isSelected = selectedNode.value?.id === nodeId

  // 使用 setData 方法更新节点数据，这会触发 'change:data' 事件
  try {
    // 先获取当前数据
    const currentData = node.getData() || {}

    // 合并新数据，保留原有数据的其他属性
    const updatedData = {
      ...currentData,
      ...nodeData,
      isSelected: isSelected
    }

    // 设置新数据
    node.setData(updatedData)

    console.log('节点数据已更新:', nodeId, updatedData)
  } catch (error) {
    console.error('更新节点数据时出错:', error)
    return false
  }

  return true
}

// 放大
const zoomIn = (event: MouseEvent) => {
  // 防止事件冒泡
  event.stopPropagation()
  console.log('执行放大操作')

  if (graph.value) {
    // 获取当前缩放比例
    const currentZoom = graph.value.zoom()
    console.log('当前缩放比例:', currentZoom)

    if (currentZoom < 3) {
      const newZoom = Math.min(3, currentZoom + 0.2)
      // 直接设置新的缩放比例，而不是作为乘数参数
      graph.value.zoom(newZoom, { absolute: true })
      console.log('放大到:', newZoom)
    } else {
      console.log('已达到最大缩放比例')
    }
  }
}

// 缩小
const zoomOut = (event: MouseEvent) => {
  // 防止事件冒泡
  event.stopPropagation()
  console.log('执行缩小操作')

  if (graph.value) {
    // 获取当前缩放比例
    const currentZoom = graph.value.zoom()
    console.log('当前缩放比例:', currentZoom)

    if (currentZoom > 0.5) {
      const newZoom = Math.max(0.5, currentZoom - 0.2)
      // 直接设置新的缩放比例，而不是作为乘数参数
      graph.value.zoom(newZoom, { absolute: true })
      console.log('缩小到:', newZoom)
    } else {
      console.log('已达到最小缩放比例')
    }
  }
}

// 重置视图
const resetView = (event?: MouseEvent) => {
  // 防止事件冒泡
  if (event) event.stopPropagation()
  console.log('执行重置视图操作')

  if (graph.value) {
    // 明确设置缩放比例为1.0，而不是使用zoomToFit自动计算
    graph.value.zoom(1.0, { absolute: true })

    // 将画布居中显示所有内容
    graph.value.centerContent()

    console.log('视图已重置，当前缩放比例:', graph.value.zoom())
  }
}

// 组件卸载时移除监听器
const beforeUnmount = () => {
  window.removeEventListener('resize', handleResize)
  graph.value?.dispose()
}

// 定位到特定节点
const focusNode = (nodeId: string) => {
  console.log('尝试定位到节点:', nodeId)

  if (!graph.value) {
    console.warn('图表未初始化，无法定位节点')
    return false
  }

  // 查找节点 - 先尝试直接查找
  let cell = graph.value.getCellById(nodeId)

  // 如果直接查找失败，尝试遍历所有节点查找
  if (!cell || !cell.isNode()) {
    console.log('直接查找节点失败，尝试遍历所有节点查找:', nodeId)
    const allNodes = graph.value.getNodes()
    for (const node of allNodes) {
      const nodeData = node.getData()
      if (nodeData && nodeData.id === nodeId) {
        console.log('通过遍历找到节点:', nodeId)
        cell = node
        break
      }
    }
  }

  if (cell && cell.isNode()) {
    console.log('找到节点，准备定位:', nodeId)
    const node = cell as Node

    // 清除所有节点的高亮状态
    const cells = graph.value.getCells()
    cells.forEach((c) => {
      if (c.isNode()) {
        c.setAttrByPath('body/stroke', 'transparent')
        c.setAttrByPath('body/strokeWidth', 0)
        c.setAttrByPath('body/shadowBlur', 0)
        c.setAttrByPath('body/shadowColor', 'transparent')
      }
    })

    // 高亮当前节点
    node.setAttrByPath('body/stroke', '#FFD700') // 金色边框
    node.setAttrByPath('body/strokeWidth', 2.5)
    node.setAttrByPath('body/shadowBlur', 10)
    node.setAttrByPath('body/shadowColor', 'rgba(255, 215, 0, 0.5)')

    // 将视图中心对准节点
    try {
      // 获取节点的位置和大小
      const nodeBBox = node.getBBox()
      const nodeCenter = nodeBBox.getCenter()

      // 获取容器元素的尺寸
      const containerEl = container.value
      if (containerEl) {
        const containerWidth = containerEl.clientWidth
        const containerHeight = containerEl.clientHeight

        // 计算当前节点位置与视图中心的偏移量
        const currentScale = graph.value.zoom()
        const targetX = containerWidth / 2 - nodeCenter.x * currentScale
        const targetY = containerHeight / 2 - nodeCenter.y * currentScale

        // 使用动画平移图形
        graph.value.centerPoint(nodeCenter.x, nodeCenter.y)
        console.log('节点已居中:', nodeId)
      }
    } catch (error) {
      console.error('定位节点时出错:', error)
    }

    // 获取节点数据
    const nodeData = node.getData<TreeNode>()
    if (nodeData) {
      // 触发节点选中事件
      emit('node-selected', nodeData)
    }

    return true
  }

  // 如果节点未找到，可能是节点尚未渲染完成，尝试延迟重试
  console.log('节点未找到，尝试延迟重试:', nodeId)

  // 使用递归函数进行多次重试
  const retryFocus = (retryCount = 0, maxRetries = 5) => {
    if (retryCount >= maxRetries) {
      console.warn('达到最大重试次数，无法找到节点:', nodeId)
      return
    }

    setTimeout(
      () => {
        console.log(`重试 ${retryCount + 1}/${maxRetries} 查找节点:`, nodeId)

        // 重新查找节点 - 先尝试直接查找
        let retryCell = graph.value?.getCellById(nodeId)

        // 如果直接查找失败，尝试遍历所有节点查找
        if (!retryCell || !retryCell.isNode()) {
          console.log(
            `重试 ${retryCount + 1}/${maxRetries} 直接查找失败，尝试遍历所有节点查找:`,
            nodeId
          )
          const allNodes = graph.value?.getNodes() || []
          for (const node of allNodes) {
            const nodeData = node.getData()
            if (nodeData && nodeData.id === nodeId) {
              console.log(`重试 ${retryCount + 1}/${maxRetries} 通过遍历找到节点:`, nodeId)
              retryCell = node
              break
            }
          }
        }

        if (retryCell && retryCell.isNode()) {
          console.log('重试成功，找到节点:', nodeId)
          // 递归调用自身，但不再重试
          const node = retryCell as Node

          // 清除所有节点的高亮状态
          const cells = graph.value?.getCells() || []
          cells.forEach((c) => {
            if (c.isNode()) {
              c.setAttrByPath('body/stroke', 'transparent')
              c.setAttrByPath('body/strokeWidth', 0)
              c.setAttrByPath('body/shadowBlur', 0)
              c.setAttrByPath('body/shadowColor', 'transparent')
            }
          })

          // 高亮当前节点
          node.setAttrByPath('body/stroke', '#FFD700')
          node.setAttrByPath('body/strokeWidth', 2.5)
          node.setAttrByPath('body/shadowBlur', 10)
          node.setAttrByPath('body/shadowColor', 'rgba(255, 215, 0, 0.5)')

          // 将视图中心对准节点
          try {
            const nodeBBox = node.getBBox()
            const nodeCenter = nodeBBox.getCenter()

            // 使用更直接的方式居中节点
            graph.value?.centerPoint(nodeCenter.x, nodeCenter.y)
            console.log('重试后节点已居中:', nodeId)

            // 获取节点数据并触发选中事件
            const nodeData = node.getData<TreeNode>()
            if (nodeData) {
              emit('node-selected', nodeData)
            }
          } catch (error) {
            console.error('重试定位节点时出错:', error)
          }
        } else {
          console.log(`重试 ${retryCount + 1}/${maxRetries} 未找到节点，继续重试`)
          // 继续重试
          retryFocus(retryCount + 1, maxRetries)
        }
      },
      200 * (retryCount + 1)
    ) // 递增延迟时间
  }

  // 开始重试
  retryFocus()

  return false
}

// 添加一个公共方法，强制刷新所有节点视图
const refreshAllNodes = () => {
  if (!graph.value) return

  const nodes = graph.value.getNodes()
  if (nodes.length === 0) return

  // 创建节点数据映射
  const nodeDataMap = new Map(props.nodes.map((node) => [node.id, node]))

  // 批量处理节点更新
  nodes.forEach((node) => {
    const nodeId = node.id
    const nodeData = nodeDataMap.get(nodeId)
    if (nodeData) {
      updateNodeView(nodeId, nodeData)
    }
  })
}

// 对外暴露方法
defineExpose({
  resetView,
  zoomOut,
  focusNode,
  updateNodeView,

  // 批量更新多个节点
  updateNodes: (nodeIds: string[]) => {
    nodeIds.forEach((id) => {
      const nodeData = props.nodes.find((node) => node.id === id)
      if (nodeData) {
        updateNodeView(id, nodeData)
      }
    })
  },

  // 刷新所有节点视图
  refreshAllNodes,

  // 强制更新节点
  forceNodesUpdate,

  // 强制重新渲染树图并定位到指定节点
  forceRenderAndFocus: (nodeId: string) => {
    console.log('强制重新渲染树图并定位到节点:', nodeId)

    // 先重新渲染树图
    renderTree()

    // 等待渲染完成后定位到节点 - 减少延迟时间
    setTimeout(() => {
      console.log('树图重新渲染完成，尝试定位到节点:', nodeId)

      // 尝试定位到节点
      if (typeof focusNode === 'function') {
        focusNode(nodeId)
      } else {
        console.warn('focusNode方法不可用')
      }
    }, 100) // 减少延迟时间从500ms到100ms
  },

  // 快速定位到节点 - 不重新渲染整个树图
  quickFocusNode: (nodeId: string) => {
    console.log('快速定位到节点:', nodeId)

    if (!graph.value) {
      console.warn('图表未初始化，无法定位节点')
      return false
    }

    // 查找节点 - 先尝试直接查找
    let cell = graph.value.getCellById(nodeId)

    // 如果直接查找失败，尝试遍历所有节点查找
    if (!cell || !cell.isNode()) {
      console.log('直接查找节点失败，尝试遍历所有节点查找:', nodeId)
      const allNodes = graph.value.getNodes()
      for (const node of allNodes) {
        const nodeData = node.getData()
        if (nodeData && nodeData.id === nodeId) {
          console.log('通过遍历找到节点:', nodeId)
          cell = node
          break
        }
      }
    }

    if (cell && cell.isNode()) {
      console.log('找到节点，准备定位:', nodeId)
      const node = cell as Node

      // 清除所有节点的高亮状态
      const cells = graph.value.getCells()
      cells.forEach((c) => {
        if (c.isNode()) {
          c.setAttrByPath('body/stroke', 'transparent')
          c.setAttrByPath('body/strokeWidth', 0)
          c.setAttrByPath('body/shadowBlur', 0)
          c.setAttrByPath('body/shadowColor', 'transparent')
        }
      })

      // 高亮当前节点
      node.setAttrByPath('body/stroke', '#FFD700')
      node.setAttrByPath('body/strokeWidth', 2.5)
      node.setAttrByPath('body/shadowBlur', 10)
      node.setAttrByPath('body/shadowColor', 'rgba(255, 215, 0, 0.5)')

      // 将视图中心对准节点
      try {
        const nodeBBox = node.getBBox()
        const nodeCenter = nodeBBox.getCenter()

        // 使用更直接的方式居中节点
        graph.value.centerPoint(nodeCenter.x, nodeCenter.y)
        console.log('节点已居中:', nodeId)

        // 获取节点数据并触发选中事件
        const nodeData = node.getData<TreeNode>()
        if (nodeData) {
          emit('node-selected', nodeData)
        }

        return true
      } catch (error) {
        console.error('定位节点时出错:', error)
      }
    }

    // 如果节点未找到，尝试重新渲染树图并再次定位
    console.log('节点未找到，尝试重新渲染树图并再次定位:', nodeId)
    renderTree()

    // 等待渲染完成后再次尝试定位
    setTimeout(() => {
      console.log('树图重新渲染完成，再次尝试定位到节点:', nodeId)

      // 再次尝试定位到节点
      if (typeof focusNode === 'function') {
        focusNode(nodeId)
      }
    }, 100)

    return false
  }
})
</script>

<style lang="less" scoped>
.tree-graph-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;

  .top-actions {
    position: absolute;
    top: 46px;
    left: 16px;
    z-index: 10;
    display: flex;
    // gap: 12px;

    .story-settings-btn,
    .create-level-btn {
      padding: 8px 16px;
      height: 40px;
      border-radius: 4px;
      border: none;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s;
    }

    .story-settings-btn {
      background-color: rgba(58, 38, 84, 0.8);
      color: rgba(255, 255, 255, 0.8);

      &:hover {
        background-color: rgba(75, 50, 107, 0.9);
        color: rgba(255, 255, 255, 1);
      }

      &.selected {
        background-color: rgba(202, 147, 242, 0.8);
        color: #1a0030;
        font-weight: 500;
      }
    }

    .create-level-btn {
      background-color: rgba(58, 38, 84, 0.8);
      color: rgba(255, 255, 255, 0.8);

      &:hover {
        background-color: rgba(75, 50, 107, 0.9);
        color: rgba(255, 255, 255, 1);
      }

      &.selected {
        background-color: rgba(202, 147, 242, 0.8);
        color: #1a0030;
        font-weight: 500;
      }
    }
  }

  .graph-toolbar {
    display: flex;
    gap: 8px;
    padding: 8px;
    // background-color: rgba(0, 0, 0, 0.2);
    // border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .toolbar-button {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 6px 10px;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 4px;
      color: #fff;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;
      min-width: 70px;
      justify-content: center;

      &:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      &:active {
        transform: translateY(0px);
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }

  .graph-container {
    flex: 1;
    overflow: hidden;
    position: relative;
    background-color: #1a0030;
    background-image: radial-gradient(circle at 50% 50%, #2a0049 0%, #1a0030 100%);

    /* 添加以下样式确保缩放正常 */
    & :deep(.x6-graph) {
      overflow: visible !important;
    }

    & :deep(.x6-graph-scroller) {
      overflow: visible !important;
    }

    & :deep(.x6-graph-svg) {
      overflow: visible;
    }
  }
}
</style>
