<template>
  <div class="character-select-modal" v-if="visible">
    <div class="modal-overlay" @click="$emit('close')"></div>
    <div class="modal-content">
      <div class="modal-header">
        <h2>角色设置</h2>
        <button class="close-button" @click="$emit('close')">
          <svg viewBox="0 0 24 24" class="icon-close">
            <path
              fill="currentColor"
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            />
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <div class="character-settings">
          <!-- 角色图片选择 -->
          <div class="section">
            <h3 class="section-title">角色图片</h3>
            <div class="character-image-grid">
              <div
                v-for="actor in actors"
                :key="actor.id"
                class="character-card"
                :class="{ selected: selectedActorId === actor.id }"
                @click="selectActor(actor)"
              >
                <img :src="actor.avatar_url" :alt="actor.name" />
                <div class="character-free-badge" v-if="actor.is_free">Free</div>
                <div class="character-info" v-if="actor.id === selectedActorId">
                  <div v-if="actor.name">{{ actor.name }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 角色详细信息 -->
          <div v-if="selectedActorId" class="section selected-actor-details">
            <div class="details-header">
              <h3 class="section-title">角色详情</h3>
              <div class="header-actions">
                <button v-if="isEditing" class="action-button cancel-button" @click="cancelEdit">
                  取消
                </button>
                <button
                  class="action-button edit-button"
                  @click="toggleEdit"
                  :class="{ active: isEditing }"
                >
                  {{ isEditing ? '保存' : '编辑' }}
                </button>
              </div>
            </div>

            <!-- 基本信息 -->
            <div class="section-content">
              <div class="detail-section">
                <h4>基本信息</h4>

                <!-- 名称和小标题在同一行 -->
                <div class="detail-row">
                  <!-- 角色名称 -->
                  <div class="detail-item">
                    <div class="label">名称:</div>
                    <div class="value" v-if="!isEditing">{{ editedActor.name }}</div>
                    <input
                      v-else
                      type="text"
                      v-model="editedActor.name"
                      placeholder="输入角色名称"
                      class="edit-input"
                    />
                  </div>

                  <!-- 角色小标题 -->
                  <div class="detail-item">
                    <div class="label">小标题:</div>
                    <div class="value" v-if="!isEditing">{{ editedActor.subtitle || '无' }}</div>
                    <input
                      v-else
                      type="text"
                      v-model="editedActor.subtitle"
                      placeholder="输入角色小标题"
                      class="edit-input"
                    />
                  </div>
                </div>

                <!-- URL信息 -->
                <div class="detail-row">
                  <!-- 头像URL -->
                  <div class="detail-item">
                    <div class="label">头像URL:</div>
                    <div class="value image-preview" v-if="!isEditing">
                      <img :src="editedActor.avatar_url" :alt="editedActor.name + ' avatar'" />
                    </div>
                    <input
                      v-else
                      type="text"
                      v-model="editedActor.avatar_url"
                      placeholder="头像URL"
                      class="edit-input"
                    />
                  </div>

                  <!-- 预览URL -->
                  <div class="detail-item">
                    <div class="label">预览URL:</div>
                    <div class="value image-preview" v-if="!isEditing">
                      <img :src="editedActor.preview_url" :alt="editedActor.name + ' preview'" />
                    </div>
                    <input
                      v-else
                      type="text"
                      v-model="editedActor.preview_url"
                      placeholder="预览URL"
                      class="edit-input"
                    />
                  </div>
                </div>

                <!-- 配置信息 -->
                <div class="detail-row">
                  <!-- 声音ID -->
                  <div class="detail-item">
                    <div class="label">声音ID:</div>
                    <div class="value" v-if="!isEditing">
                      {{ editedActor.chat_config?.voice_id || '无' }}
                    </div>
                    <input
                      v-else
                      v-model="editedActor.chat_config.voice_id"
                      placeholder="声音ID"
                      class="edit-input"
                    />
                  </div>

                  <!-- 金币 -->
                  <div class="detail-item">
                    <div class="label">金币:</div>
                    <div class="value" v-if="!isEditing">{{ editedActor.coins }}</div>
                    <input
                      v-else
                      v-model.number="editedActor.coins"
                      type="number"
                      placeholder="金币"
                      class="edit-input"
                    />
                  </div>
                </div>

                <!-- 其他信息 -->
                <div class="detail-row">
                  <!-- 版本 -->
                  <div class="detail-item">
                    <div class="label">版本:</div>
                    <div class="value" v-if="!isEditing">{{ editedActor.version }}</div>
                    <input
                      v-else
                      v-model="editedActor.version"
                      placeholder="版本"
                      class="edit-input"
                    />
                  </div>

                  <!-- 是否购买 -->
                  <div class="detail-item">
                    <div class="label">已购买:</div>
                    <div class="value" v-if="!isEditing">
                      {{ editedActor.is_purchased ? '是' : '否' }}
                    </div>
                    <select v-else v-model="editedActor.is_purchased" class="edit-input">
                      <option :value="true">是</option>
                      <option :value="false">否</option>
                    </select>
                  </div>
                </div>
              </div>

              <!-- 额外信息 -->
              <div
                class="detail-section"
                v-if="editedActor.extra && Object.keys(editedActor.extra).length > 0"
              >
                <h4>额外信息</h4>
                <!-- 将额外属性分组显示，每行最多显示2个属性 -->
                <div
                  class="detail-row"
                  v-for="(chunk, index) in extraPropertiesChunks"
                  :key="index"
                >
                  <div v-for="item in chunk" :key="item.key" class="detail-item">
                    <div class="label">{{ item.key }}:</div>
                    <div class="value" v-if="!isEditing">{{ item.value || '未知' }}</div>
                    <input
                      v-else
                      v-model="editedActor.extra[item.key]"
                      :placeholder="item.key"
                      class="edit-input"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="confirm-button" @click="confirmSelection" :disabled="!canConfirm">
          确认选择
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive } from 'vue'
import type { Actor } from '@/api/stories'
import { Message } from '@/mobile/components/Message'

// 扩展Actor类型以包含更多属性
interface ExtendedActor extends Actor {
  gender?: string
  age?: number | string
  bwh?: string
  relationship?: string
  profession?: string
  is_free?: boolean
}

const props = defineProps<{
  visible: boolean
  actors: ExtendedActor[]
  currentActor?: ExtendedActor | null
}>()

const emit = defineEmits(['close', 'confirm', 'update-actor'])

// 基本状态
const selectedActorId = ref<string | null>(props.currentActor?.id || null)
const characterName = ref(props.currentActor?.name || '')
const characterGender = ref('')
const characterAge = ref('')
const characterRelationship = ref('')
const characterVoice = ref('female')
const characterTags = ref<string[]>([])
const tagInput = ref('')
const worldBackground = ref('')
const characterIntro = ref('')

// 是否处于编辑模式
const isEditing = ref(false)
const isLoading = ref(false)

// 创建一个reactive对象用于保存编辑中的actor数据
const editedActor = reactive<{
  name: string
  subtitle: string
  avatar_url: string
  preview_url: string
  coins: number
  is_purchased: boolean
  version: string
  chat_config?: Record<string, string>
  extra: Record<string, string>
}>({
  name: '',
  subtitle: '',
  avatar_url: '',
  preview_url: '',
  coins: 0,
  is_purchased: false,
  version: '',
  chat_config: {},
  extra: {}
})

// 语音预览相关
const voiceTime = ref('00:30')
const voiceDuration = ref('05:00')
const voiceProgress = ref(10)

// 计算属性
const canConfirm = computed(() => {
  return (
    selectedActorId.value &&
    (isEditing.value ? editedActor.name.trim() !== '' : characterName.value.trim() !== '')
  )
})

// 将额外属性按每行最多2个分组
const extraPropertiesChunks = computed(() => {
  if (!editedActor.extra) return []

  const items = Object.entries(editedActor.extra).map(([key, value]) => ({ key, value }))
  const chunks = []

  for (let i = 0; i < items.length; i += 2) {
    chunks.push(items.slice(i, i + 2))
  }

  return chunks
})

// 自动选中第一个角色
watch(
  () => props.visible,
  (isVisible) => {
    if (isVisible && props.actors.length > 0 && !selectedActorId.value) {
      // 如果弹窗显示，有角色，且没有选中任何角色，则自动选中第一个
      const firstActor = props.actors[0]
      selectedActorId.value = firstActor.id
      selectActor(firstActor)
      console.log('自动选中第一个角色:', firstActor.name)
    }
  },
  { immediate: true }
)

// 监听当前角色变化，初始化编辑数据
watch(
  () => selectedActorId.value,
  (newActorId) => {
    if (!newActorId) return

    const actor = props.actors.find((a) => a.id === newActorId)
    if (!actor) return

    // 初始化表单数据
    characterName.value = actor.name || ''
    characterGender.value = actor.gender || ''
    characterAge.value = actor.age ? String(actor.age) : ''
    characterRelationship.value = actor.relationship || ''

    // 初始化编辑数据
    editedActor.name = actor.name || ''
    editedActor.subtitle = actor.subtitle || ''
    editedActor.avatar_url = actor.avatar_url || ''
    editedActor.preview_url = actor.preview_url || ''
    editedActor.coins = actor.coins || 0
    editedActor.is_purchased = actor.is_purchased || false
    editedActor.version = actor.version || ''

    // 初始化chat_config数据
    if (actor.chat_config) {
      editedActor.chat_config = { ...actor.chat_config }
    } else {
      editedActor.chat_config = { voice_id: '' }
    }

    // 初始化extra数据
    editedActor.extra = {}
    if (actor.extra) {
      Object.entries(actor.extra).forEach(([key, value]) => {
        editedActor.extra[key] = String(value || '')
      })
    }
  },
  { immediate: true }
)

// 选择角色
function selectActor(actor: ExtendedActor) {
  selectedActorId.value = actor.id

  // 退出编辑模式
  isEditing.value = false
}

// 创建新角色（可能需要跳转到角色创建页面或调用API）
function createNewCharacter() {
  console.log('创建新角色功能待实现')
  // emit('create-character')
}

// 添加标签
function addTag() {
  if (tagInput.value.trim() && !characterTags.value.includes(tagInput.value.trim())) {
    characterTags.value.push(tagInput.value.trim())
    tagInput.value = ''
  }
}

// 移除标签
function removeTag(index: number) {
  characterTags.value.splice(index, 1)
}

// 确认选择
async function confirmSelection() {
  if (!selectedActorId.value || isLoading.value) return

  try {
    isLoading.value = true
    const selectedActor = props.actors.find((actor) => actor.id === selectedActorId.value)

    if (!selectedActor) {
      throw new Error('未找到选择的角色')
    }

    // 如果处于编辑模式，先保存修改
    if (isEditing.value && hasChanges()) {
      await saveChanges()
    }

    // 发送选择结果
    emit('confirm', selectedActor)
  } catch (error) {
    console.error('确认选择出错:', error)
    Message.error(error instanceof Error ? error.message : '选择角色失败')
  } finally {
    isLoading.value = false
  }
}

// 切换编辑模式
function toggleEdit() {
  if (isEditing.value) {
    if (hasChanges()) {
      saveChanges()
    } else {
      isEditing.value = false
    }
  } else {
    // 开始编辑状态 - 用当前值初始化编辑数据
    if (selectedActorId.value) {
      const actor = props.actors.find((a) => a.id === selectedActorId.value)
      if (actor) {
        // 设置基本数据
        editedActor.name = actor.name || ''
        editedActor.subtitle = actor.subtitle || ''
        editedActor.avatar_url = actor.avatar_url || ''
        editedActor.preview_url = actor.preview_url || ''
        editedActor.coins = actor.coins || 0
        editedActor.is_purchased = actor.is_purchased || false
        editedActor.version = actor.version || ''

        // 设置chat_config (如果存在)
        if (actor.chat_config) {
          editedActor.chat_config = { ...actor.chat_config }
        } else {
          editedActor.chat_config = { voice_id: '' }
        }

        // 清除以前的extra值
        Object.keys(editedActor.extra).forEach((key) => delete editedActor.extra[key])

        // 设置新的extra值
        if (actor.extra) {
          Object.entries(actor.extra).forEach(([key, value]) => {
            editedActor.extra[key] = String(value || '')
          })
        }
      }
    }
    isEditing.value = true
  }
}

// 检查是否有变更
function hasChanges() {
  if (!selectedActorId.value) return false

  const actor = props.actors.find((a) => a.id === selectedActorId.value)
  if (!actor) return false

  const originalActor = {
    name: actor.name,
    subtitle: actor.subtitle,
    avatar_url: actor.avatar_url,
    preview_url: actor.preview_url,
    coins: actor.coins,
    is_purchased: actor.is_purchased,
    version: actor.version,
    chat_config: actor.chat_config || { voice_id: '' },
    extra: actor.extra
  }

  const currentActor = {
    name: editedActor.name,
    subtitle: editedActor.subtitle,
    avatar_url: editedActor.avatar_url,
    preview_url: editedActor.preview_url,
    coins: editedActor.coins,
    is_purchased: editedActor.is_purchased,
    version: editedActor.version,
    chat_config: editedActor.chat_config || { voice_id: '' },
    extra: editedActor.extra
  }

  // 检查各字段是否有变化
  if (
    originalActor.name !== currentActor.name ||
    originalActor.subtitle !== currentActor.subtitle ||
    originalActor.avatar_url !== currentActor.avatar_url ||
    originalActor.preview_url !== currentActor.preview_url ||
    originalActor.coins !== currentActor.coins ||
    originalActor.is_purchased !== currentActor.is_purchased ||
    originalActor.version !== currentActor.version ||
    originalActor.chat_config?.voice_id !== currentActor.chat_config?.voice_id
  ) {
    return true
  }

  // 检查extra字段变化
  if (actor.extra && editedActor.extra) {
    const originalKeys = Object.keys(actor.extra || {})
    const currentKeys = Object.keys(editedActor.extra)

    if (originalKeys.length !== currentKeys.length) {
      return true
    }

    for (const key of originalKeys) {
      if (String(actor.extra[key] || '') !== String(editedActor.extra[key] || '')) {
        return true
      }
    }
  }

  return false
}

// 保存编辑的更改
async function saveChanges() {
  if (!selectedActorId.value) return

  const actor = props.actors.find((a) => a.id === selectedActorId.value)
  if (!actor) return

  try {
    isLoading.value = true
    const updatedActor = {
      ...actor,
      ...editedActor,
      extra: { ...editedActor.extra }
    }

    // 这里应该调用API保存角色修改
    console.log('保存角色修改:', updatedActor)

    // 模拟API调用成功
    // 在实际实现中，替换为真实的API调用
    // const { data } = await updateActor(updatedActor)

    // 通过emit事件通知父组件更新actors数据
    emit('update-actor', updatedActor)

    // 更新本地状态
    characterName.value = editedActor.name
    Message.success('角色更新成功')
    isEditing.value = false
  } catch (error) {
    console.error('保存角色失败:', error)
    Message.error(error instanceof Error ? error.message : '更新角色失败')
  } finally {
    isLoading.value = false
  }
}

// 取消编辑
function cancelEdit() {
  resetEditData()
  isEditing.value = false
}

// 重置编辑数据
function resetEditData() {
  if (!selectedActorId.value) return

  const actor = props.actors.find((a) => a.id === selectedActorId.value)
  if (!actor) return

  editedActor.name = actor.name || ''
  editedActor.subtitle = actor.subtitle || ''
  editedActor.avatar_url = actor.avatar_url || ''
  editedActor.preview_url = actor.preview_url || ''
  editedActor.coins = actor.coins || 0
  editedActor.is_purchased = actor.is_purchased || false
  editedActor.version = actor.version || ''

  // 重置chat_config数据
  if (actor.chat_config) {
    editedActor.chat_config = { ...actor.chat_config }
  } else {
    editedActor.chat_config = { voice_id: '' }
  }

  // 重置extra数据
  editedActor.extra = {}
  if (actor.extra) {
    Object.entries(actor.extra).forEach(([key, value]) => {
      editedActor.extra[key] = String(value || '')
    })
  }
}

// 监听visible属性变化
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      console.log('角色选择模态框显示：', {
        actors: props.actors.length,
        actorsList: props.actors,
        currentActor: props.currentActor
      })
    }
  },
  { immediate: true }
)

// 监听actors属性变化
watch(
  () => props.actors,
  (newActors) => {
    console.log('角色列表更新，当前角色数量：', newActors.length)
  },
  { deep: true, immediate: true }
)
</script>

<style lang="less" scoped>
.character-select-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
  }

  .modal-content {
    position: relative;
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    background-color: #1a0030;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    color: white;

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      h2 {
        margin: 0;
        font-size: 22px;
        font-weight: 500;
        color: #ca93f2;
      }

      .close-button {
        width: 32px;
        height: 32px;
        background: transparent;
        border: none;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: rgba(255, 255, 255, 0.6);

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          color: white;
        }

        .icon-close {
          width: 24px;
          height: 24px;
        }
      }
    }

    .modal-body {
      flex: 1;
      overflow-y: auto;
      padding: 20px;
      display: flex;
      flex-direction: column;

      .character-settings {
        display: flex;
        flex-direction: column;
        flex: 1;

        .section {
          margin-bottom: 24px;

          .section-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 12px;
            color: rgba(255, 255, 255, 0.9);
          }

          .character-image-grid {
            display: flex;
            flex-wrap: nowrap;
            overflow-x: auto;
            gap: 16px;
            margin-bottom: 20px;
            padding-bottom: 8px; /* 为滚动条预留空间 */

            /* 自定义滚动条样式 */
            &::-webkit-scrollbar {
              height: 6px;
            }

            &::-webkit-scrollbar-track {
              background: rgba(255, 255, 255, 0.05);
              border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
              background: rgba(202, 147, 242, 0.3);
              border-radius: 3px;

              &:hover {
                background: rgba(202, 147, 242, 0.5);
              }
            }

            .character-card {
              flex: 0 0 140px; /* 固定宽度，不缩放 */
              height: 186px; /* 保持3:4的宽高比例 */
              border-radius: 8px;
              overflow: hidden;
              position: relative;
              cursor: pointer;
              transition: all 0.2s ease;

              &:hover {
                transform: translateY(-4px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
              }

              border: 2px solid transparent;

              &.selected {
                border-color: #ca93f2;
                box-shadow: 0 0 12px rgba(202, 147, 242, 0.4);
              }

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }

              .character-free-badge {
                position: absolute;
                top: 8px;
                left: 8px;
                background: rgba(0, 0, 0, 0.6);
                color: gold;
                font-size: 12px;
                padding: 2px 6px;
                border-radius: 4px;
              }

              .character-info {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: rgba(0, 0, 0, 0.7);
                padding: 8px;
                font-size: 12px;
                line-height: 1.4;
              }
            }
          }

          &.selected-actor-details {
            flex: 1;
            display: flex;
            flex-direction: column;
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.05);
            padding: 0;
            max-height: none; /* 移除最大高度限制 */
            min-height: 500px; /* 添加最小高度，确保有足够空间 */
            overflow: hidden; /* 改为hidden，让内部控制滚动 */
            margin-bottom: 8px; /* 减少底部间距 */

            .details-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 20px;
              flex-shrink: 0; /* 防止头部被压缩 */
              position: sticky;
              top: 0;
              z-index: 1;
              background: rgba(26, 0, 48, 0.9); /* 使用与modal背景匹配的颜色 */
              backdrop-filter: blur(4px);
              border-bottom: 1px solid rgba(255, 255, 255, 0.05);

              .header-actions {
                display: flex;
                gap: 8px;
              }

              .cancel-button {
                height: 32px;
                padding: 0 12px;
                border-radius: 16px;
                background: rgba(255, 77, 79, 0.1);
                border: none;
                color: #ff4d4f;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                  background: rgba(255, 77, 79, 0.2);
                }
              }

              .edit-button {
                height: 32px;
                padding: 0 12px;
                border-radius: 16px;
                background: rgba(255, 255, 255, 0.1);
                border: none;
                color: white;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                  background: rgba(202, 147, 242, 0.2);
                }

                &.active {
                  background: rgba(202, 147, 242, 0.3);
                  color: #ca93f2;
                }
              }
            }

            .section-content {
              flex: 1;
              overflow-y: auto;
              padding: 20px;
              padding-bottom: 60px; /* 增加底部间距，确保内容完全可见 */

              &::-webkit-scrollbar {
                width: 6px;
              }

              &::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 4px;
              }

              &::-webkit-scrollbar-thumb {
                background: rgba(255, 255, 255, 0.2);
                border-radius: 4px;

                &:hover {
                  background: rgba(255, 255, 255, 0.3);
                }
              }

              .detail-section {
                background: rgba(255, 255, 255, 0.03);
                border-radius: 12px;
                padding: 16px;
                margin-bottom: 16px;

                &:last-child {
                  margin-bottom: 16px; /* 调整为适当的底部间距，不用太大 */
                }

                h4 {
                  color: rgba(255, 255, 255, 0.9);
                  font-size: 16px;
                  font-weight: 500;
                  margin: 0 0 12px;
                }
              }

              .detail-row {
                display: flex;
                flex-wrap: wrap;
                gap: 16px;
                margin-bottom: 16px;

                &:last-child {
                  margin-bottom: 0;
                }

                .detail-item {
                  flex: 1;
                  min-width: calc(50% - 8px);
                  margin-bottom: 0;

                  @media (max-width: 768px) {
                    min-width: 100%;
                    margin-bottom: 12px;

                    &:last-child {
                      margin-bottom: 0;
                    }
                  }
                }
              }

              .detail-item {
                margin-bottom: 12px;

                &:last-child {
                  margin-bottom: 0;
                }

                .label {
                  font-size: 14px;
                  color: rgba(255, 255, 255, 0.6);
                  margin-bottom: 4px;
                }

                .value {
                  font-size: 16px;
                  color: white;
                  word-break: break-all;

                  &.image-preview {
                    img {
                      max-width: 100%;
                      max-height: 200px;
                      border-radius: 8px;
                      background: rgba(255, 255, 255, 0.05);

                      &[alt$='avatar'] {
                        width: 64px;
                        height: 64px;
                        border-radius: 50%;
                        object-fit: cover;
                      }
                    }
                  }
                }

                .edit-input {
                  width: 100%;
                  background: rgba(255, 255, 255, 0.05);
                  border: 1px solid rgba(255, 255, 255, 0.1);
                  border-radius: 8px;
                  color: white;
                  font-size: 14px;
                  padding: 8px 12px;
                  transition: all 0.2s ease;

                  &:hover {
                    border-color: rgba(202, 147, 242, 0.5);
                    background: rgba(255, 255, 255, 0.08);
                  }

                  &:focus {
                    border-color: #ca93f2;
                    background: rgba(255, 255, 255, 0.08);
                    box-shadow: 0 0 0 2px rgba(202, 147, 242, 0.2);
                    outline: none;
                  }

                  &[type='number'] {
                    appearance: none;
                    -moz-appearance: textfield;
                    &::-webkit-outer-spin-button,
                    &::-webkit-inner-spin-button {
                      -webkit-appearance: none;
                      margin: 0;
                    }
                  }
                }

                select.edit-input {
                  appearance: none;
                  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3e%3cpath d='M7 10l5 5 5-5z'/%3e%3c/svg%3e");
                  background-repeat: no-repeat;
                  background-position: right 8px center;
                  background-size: 16px;
                  padding-right: 32px;
                  cursor: pointer;
                }
              }
            }
          }
        }
      }
    }

    .modal-footer {
      padding: 20px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      justify-content: center;

      .confirm-button {
        padding: 12px 40px;
        background: rgba(202, 147, 242, 0.2);
        border: 1px solid rgba(202, 147, 242, 0.4);
        border-radius: 6px;
        color: #ca93f2;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          background: rgba(202, 147, 242, 0.3);
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        &:active {
          transform: translateY(0);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }
      }
    }
  }
}
</style>
