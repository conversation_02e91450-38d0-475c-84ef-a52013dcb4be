<template>
  <div class="story-config-panel" v-if="isShow" :class="{ 'panel-visible': isShow }">
    <div class="panel-header">
      <h3>故事设置</h3>
      <button class="close-button" @click="$emit('close')">
        <svg viewBox="0 0 24 24" class="icon-close">
          <path
            fill="currentColor"
            d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
          />
        </svg>
      </button>
    </div>

    <div class="panel-content">
      <a-form :model="storyConfig" layout="vertical">
        <!-- 当前选择的角色信息 -->
        <div class="config-section selected-character-section">
          <h3 class="section-title">当前主角</h3>
          <div class="selected-character-card" @click="selectCharacter">
            <template v-if="selectedActor">
              <img
                :src="selectedActor.avatar_url"
                :alt="selectedActor.name"
                class="character-avatar"
              />
              <div class="character-details">
                <div class="character-name">{{ selectedActor.name }}</div>
                <div
                  class="character-description"
                  v-if="selectedActor.extra?.gender || selectedActor.extra?.age"
                >
                  {{ selectedActor.extra?.gender }}
                  {{ selectedActor.extra?.age ? '· ' + selectedActor.extra?.age : '' }}
                </div>
              </div>
            </template>
            <div v-else class="select-character">
              <i class="ri-user-add-line"></i>
              <span>选择角色</span>
            </div>
            <div class="edit-icon">
              <svg viewBox="0 0 24 24" width="16" height="16">
                <path
                  fill="currentColor"
                  d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"
                />
              </svg>
            </div>
          </div>
        </div>

        <!-- 基础信息配置 -->
        <div class="config-section">
          <h3 class="section-title">基础信息</h3>
          <div class="form-field">
            <label for="story-name">故事名称</label>
            <input
              id="story-name"
              type="text"
              v-model="storyConfig.project_name"
              placeholder="请输入故事名称"
              class="form-input"
            />
          </div>
          <div class="form-field">
            <label for="story-description">故事描述</label>
            <textarea
              id="story-description"
              v-model="storyConfig.description"
              placeholder="请输入故事描述"
              class="form-textarea"
              rows="4"
            ></textarea>
          </div>
        </div>

        <!-- 媒体资源配置 -->
        <div class="config-section">
          <h3 class="section-title">媒体资源</h3>
          <a-form-item label="预览图片">
            <MediaSelector
              v-model="storyConfig.preview_url"
              v-model:preview="storyConfig.preview_url"
              type="image"
            />
          </a-form-item>
          <a-form-item label="预览视频">
            <MediaSelector
              v-model="storyConfig.preview_video"
              v-model:preview="storyConfig.preview_video"
              type="video"
            />
          </a-form-item>
          <a-form-item label="背景音乐">
            <MediaSelector
              v-model="storyConfig.bgm_url"
              v-model:preview="storyConfig.bgm_url"
              type="audio"
            />
          </a-form-item>
        </div>

        <!-- 技能配置 -->
        <div class="config-section">
          <h3 class="section-title">技能配置</h3>
          <a-form-item label="启用技能">
            <a-switch v-model="isActiveSkill" />
          </a-form-item>
          <a-form-item v-if="isActiveSkill" label="技能选择">
            <div class="skill-selector" @click="openSkillSelector">
              <div v-if="selectedSkills.length > 0" class="selected-skills">
                <div v-for="skill in selectedSkills" :key="skill.id" class="skill-tag">
                  <img :src="skill.image_url" :alt="skill.name" class="skill-avatar" />
                  <span class="skill-name">{{ skill.name }}</span>
                </div>
              </div>
              <div v-else class="select-button">
                <i class="ri-add-line"></i>
                <span>选择技能</span>
              </div>
            </div>
          </a-form-item>
        </div>

        <!-- 换脸配置 -->
        <div class="config-section">
          <h3 class="section-title">换脸配置</h3>
          <a-form-item label="启用换脸">
            <a-switch v-model="isSupportFaceSwap" />
          </a-form-item>
        </div>

        <!-- 保存按钮 -->
        <div class="panel-actions">
          <button class="save-button" @click="saveConfig">保存设置</button>
        </div>
      </a-form>
    </div>

    <!-- 技能选择弹窗 -->
    <!-- DesktopSkillSelect组件移至父组件StoryFlowEditor -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useEditorStore } from '@/store/editor'
import { storeToRefs } from 'pinia'
import { Message } from '@/mobile/components/Message'
import MediaSelector from '@/mobile/components/Common/MediaSelector.vue'
import type { Actor } from '@/api/stories'
import type { SkillInfo } from '@/interface/skill'
import { useWindowSize } from '@vueuse/core'
import { useSkillStore } from '@/store/skill'
import { PropType } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const emit = defineEmits(['close', 'saved', 'open-skill-selector', 'open-character-selector'])

const editorStore = useEditorStore()
const skillStore = useSkillStore()
// 直接从store获取数据引用，实现双向绑定
const { storyConfig, selectedActor } = storeToRefs(editorStore)
const { width } = useWindowSize()

const props = defineProps({
  isShow: {
    type: Boolean,
    default: false
  },
  initialSkills: {
    type: Array as PropType<SkillInfo[]>,
    default: () => []
  }
})

// 计算属性，通过storyConfig直接双向绑定
const isActiveSkill = computed({
  get: () => !!storyConfig.value.is_active_skill,
  set: (val) => {
    storyConfig.value.is_active_skill = val
    if (!val) {
      storyConfig.value.skill_ids = []
      selectedSkills.value = []
    }
  }
})

const isSupportFaceSwap = computed({
  get: () => !!storyConfig.value.is_support_face_swap,
  set: (val) => {
    storyConfig.value.is_support_face_swap = val
  }
})

const showSkillSelect = ref(false)
const selectedSkills = ref<SkillInfo[]>([])

// 监听技能选择状态变化
watch(
  () => props.initialSkills,
  (newSkills) => {
    if (newSkills && newSkills.length > 0) {
      // 更新本地技能列表
      selectedSkills.value = newSkills
      // 确保技能配置已经激活
      isActiveSkill.value = true
      // 更新store中的skill_ids
      storyConfig.value.skill_ids = newSkills.map((skill) => skill.id)
    }
  },
  { immediate: true, deep: true }
)

// 保存配置
const saveConfig = async () => {
  try {
    // 表单验证
    if (!storyConfig.value.project_name.trim()) {
      Message.error('请输入故事名称')
      return
    }

    // 验证是否选择了角色
    if (!selectedActor.value) {
      Message.error('请先选择一个角色作为故事主角')
      return
    }

    const projectId = router.currentRoute.value.params.projectId
    if (!projectId) {
      Message.error('故事ID不存在')
      return
    }

    // 直接保存到服务器，不需要额外更新storyConfig
    await editorStore.saveStory()

    Message.success('故事设置已保存')

    emit('saved')
  } catch (error) {
    if (error instanceof Error) {
      Message.error(error.message)
    } else {
      Message.error('保存失败')
    }
  }
}

// 打开技能选择器
const openSkillSelector = () => {
  emit('open-skill-selector', selectedSkills.value)
}

// 打开角色选择模态框
const selectCharacter = () => {
  emit('open-character-selector')
}

// 组件挂载时初始化
onMounted(async () => {
  console.log('StoryConfigPanel mounted, initializing...')

  // 确保角色列表已加载
  if (editorStore.actors.length === 0) {
    console.log('Fetching actor list...')
    try {
      await editorStore.fetchActorList()
      console.log('Actor list fetched, length:', editorStore.actors.length)
    } catch (error) {
      console.error('Failed to fetch actor list:', error)
    }
  }

  // 确保技能列表已加载
  if (skillStore.skills.length === 0) {
    console.log('Fetching skill info...')
    try {
      await skillStore.fetchAllSkillInfo()
      console.log('Skill info fetched, length:', skillStore.skills.length)
    } catch (error) {
      console.error('Failed to fetch skill info:', error)
    }
  }

  // 初始化技能列表
  if (storyConfig.value.is_active_skill && storyConfig.value.skill_ids?.length) {
    selectedSkills.value = skillStore.skills.filter(
      (skill) => storyConfig.value.skill_ids?.includes(skill.id)
    )
  }
})
</script>

<style lang="less" scoped>
.story-config-panel {
  width: 400px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(32, 0, 56, 0.95);
  color: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  position: absolute;
  top: 0;
  right: 0;
  z-index: 11;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.3);
  transform: translateX(100%);
  transition: transform 0.3s ease;

  &.panel-visible {
    transform: translateX(0);
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
      color: #ca93f2;
    }

    .close-button {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: transparent;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      padding: 0;
      color: rgba(255, 255, 255, 0.6);

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.9);
      }

      .icon-close {
        width: 20px;
        height: 20px;
      }
    }
  }

  .panel-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;

    // 配置界面
    .config-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 16px;
      }

      .section-title {
        font-size: 14px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.85);
        margin: 0 0 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      &.selected-character-section {
        .selected-character-card {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 12px;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          transition: all 0.2s;
          cursor: pointer;
          position: relative;

          &:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: #ca93f2;

            .edit-icon {
              opacity: 1;
            }
          }

          .character-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
          }

          .character-details {
            flex: 1;

            .character-name {
              font-size: 16px;
              font-weight: 500;
              color: white;
              margin-bottom: 4px;
            }

            .character-description {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.6);
            }
          }

          .select-character {
            display: flex;
            align-items: center;
            gap: 8px;
            color: rgba(255, 255, 255, 0.7);
            width: 100%;
            justify-content: center;

            i {
              font-size: 20px;
            }
          }

          .edit-icon {
            position: absolute;
            right: 12px;
            top: 12px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(202, 147, 242, 0.2);
            border-radius: 50%;
            color: #ca93f2;
            opacity: 0;
            transition: opacity 0.2s;
          }
        }
      }
    }

    .form-field {
      margin-bottom: 20px;

      label {
        display: block;
        margin-bottom: 8px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.75);
      }

      .form-input,
      .form-textarea {
        width: 100%;
        padding: 12px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 6px;
        color: white;
        font-size: 14px;
        transition: all 0.2s ease;

        &:focus {
          outline: none;
          border-color: #ca93f2;
          background: rgba(255, 255, 255, 0.08);
          box-shadow: 0 0 0 2px rgba(202, 147, 242, 0.2);
        }

        &::placeholder {
          color: rgba(255, 255, 255, 0.3);
        }
      }

      .form-textarea {
        resize: vertical;
        min-height: 80px;
      }
    }

    .skill-selector {
      cursor: pointer;
      transition: all 0.3s ease;

      .select-button,
      .selected-skills {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: #ca93f2;
        }

        i {
          font-size: 20px;
        }
      }

      .selected-skills {
        flex-wrap: wrap;

        .skill-tag {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 4px 12px;
          background: rgba(202, 147, 242, 0.1);
          border-radius: 20px;

          .skill-avatar {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            object-fit: cover;
          }

          .skill-name {
            font-size: 14px;
            color: #ca93f2;
          }
        }
      }
    }

    .panel-actions {
      margin-top: 24px;
      display: flex;
      justify-content: flex-end;

      .save-button {
        padding: 8px 20px;
        background: rgba(46, 204, 113, 0.15);
        border: 1px solid rgba(46, 204, 113, 0.3);
        border-radius: 4px;
        color: #2ecc71;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          background: rgba(46, 204, 113, 0.25);
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        &:active {
          transform: translateY(0);
          box-shadow: none;
        }
      }
    }
  }
}
</style>
