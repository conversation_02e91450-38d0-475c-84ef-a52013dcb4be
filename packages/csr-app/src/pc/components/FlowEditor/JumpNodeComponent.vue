<template>
  <div class="jump-node" @click="handleClick" :title="'跳转到: ' + targetSceneName">
    <div class="jump-content">跳转到: {{ targetSceneName }}</div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue'
import { useEditorStore } from '@/store/editor'

const props = defineProps<{
  node: any // 使用 any 类型以允许访问 X6 节点的方法
}>()

const emit = defineEmits<{
  (e: 'click', parentNodeId: string): void
}>()

const editorStore = useEditorStore()

// 节点数据状态
const nodeState = ref<any>({})

// 在组件挂载后设置监听器
onMounted(() => {
  try {
    // 获取初始数据
    const initialData = props.node.getData() || {}
    nodeState.value = { ...initialData }
    console.log('跳转节点初始数据:', initialData)

    // 监听数据变化事件
    props.node.on('change:data', ({ current }: { current: any }) => {
      console.log('跳转节点数据变化:', current)
      nodeState.value = { ...current }
    })
  } catch (error) {
    console.error('设置跳转节点数据监听器时出错:', error)
  }
})

// 作为后备的监听方案，如果直接监听不生效
watch(
  () => props.node,
  (newNode, oldNode) => {
    // 只在关键属性变化时更新
    if (
      !oldNode ||
      newNode.id !== oldNode.id ||
      JSON.stringify(newNode.getData()) !== JSON.stringify(oldNode.getData())
    ) {
      // 更新节点状态
      try {
        nodeState.value = { ...newNode.getData() }
        console.log('通过watch更新跳转节点数据:', nodeState.value)
      } catch (error) {
        console.error('通过watch更新跳转节点数据时出错:', error)
      }
    }
  },
  { deep: false }
)

// 节点数据
const nodeData = computed(() => {
  // 优先使用 nodeState 中的数据
  const data = nodeState.value || {}

  // 如果 nodeState 中没有数据，则尝试从 props.node 中获取
  if (Object.keys(data).length === 0) {
    try {
      const nodeData = props.node.getData() || {}
      return {
        targetSceneId: nodeData.targetSceneId || '',
        parentNodeId: nodeData.parentNodeId || '',
        ...nodeData
      }
    } catch (error) {
      console.error('获取跳转节点数据时出错:', error)
      return {
        targetSceneId: '',
        parentNodeId: ''
      }
    }
  }

  return {
    targetSceneId: data.targetSceneId || '',
    parentNodeId: data.parentNodeId || '',
    ...data
  }
})

// 获取目标场景名称
const targetSceneName = computed(() => {
  const targetSceneId = nodeData.value.targetSceneId
  console.log('目标场景 ID:', targetSceneId)

  if (!targetSceneId) return '未设置'

  const scene = editorStore.gameConfig.scenes.find((s) => s.id === targetSceneId)
  console.log('找到的场景:', scene)

  if (!scene) return targetSceneId

  // 获取场景组名称
  const sceneGroupName = scene.scene_group || '默认组'

  // 返回格式：场景组-场景名称
  return `${sceneGroupName}（${scene.name})`
})

// 处理点击事件
const handleClick = () => {
  const parentNodeId = nodeData.value.parentNodeId
  console.log('跳转节点组件被点击:', parentNodeId)

  if (!parentNodeId) {
    console.error('父节点ID不存在')
    return
  }

  // 触发点击事件
  emit('click', parentNodeId)
}
</script>

<style lang="less" scoped>
.jump-node {
  border-radius: 8px;
  background: #ca93f2;
  color: #1f0038;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px; // 缩小字体
  cursor: pointer;
  transition: all 0.2s ease-in-out; // 添加过渡效果
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-style: normal;
  font-weight: 500; // 增加字重
  line-height: normal;
  position: relative; // 添加相对定位
  border: 1px solid rgba(255, 255, 255, 0.4); // 添加边框，增强立体感
  border-bottom: 1px solid rgba(0, 0, 0, 0.05); // 底部边框稍暗，增强立体感

  // 添加一个向下的箭头，指向父节点
  &:after {
    content: '';
    position: absolute;
    bottom: -7px; // 调整位置，使其更靠近节点
    left: 50%;
    margin-left: -8px; // 与箭头宽度保持一致
    width: 0; // 确保没有宽度
    height: 0; // 确保没有高度
    border-left: 8px solid transparent; // 缩小箭头
    border-right: 8px solid transparent; // 缩小箭头
    border-top: 8px solid #ca93f2; // 缩小箭头
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1)); // 添加阴影，增强立体感
  }

  &:hover {
    background: #b66fe0;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
    transform: translateY(-2px); // 微小的上浮效果
    border-color: rgba(255, 255, 255, 0.5); // 增强边框

    &:after {
      border-top-color: #b66fe0;
      filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.15)); // hover 状态下增强阴影
      width: 0; // 确保没有宽度
      height: 0; // 确保没有高度
    }
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    background: #a55bd0; // 点击时的颜色
  }
}

.jump-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px; // 增加宽度以容纳更多文本
  font-size: 13px; // 缩小字体以显示更多文本
}
</style>
