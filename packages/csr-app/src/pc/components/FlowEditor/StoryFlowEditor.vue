<template>
  <div class="story-flow-editor">
    <div class="graph-container">
      <TreeGraph
        ref="treeGraph"
        :nodes="treeNodes"
        @node-selected="handleNodeSelected"
        @node-added="handleNodeAdded"
        @node-context-menu="handleNodeContextMenu"
        @open-story-settings="handleOpenStorySettings"
        @add-new-chapter="handleAddChapter"
        @add-event="handleAddEvent"
        @add-scene-group="handleContextAddSceneGroup"
        @jump-node-click="handleJumpNodeClick"
      />
    </div>
    <div
      class="property-panel"
      :class="{ 'panel-visible': isPropertyPanelVisible }"
      :style="{ width: panelWidth + 'px' }"
    >
      <div class="panel-resizer" @mousedown="startResize"></div>
      <div class="panel-header">
        <h3>{{ selectedNode?.name || '节点配置' }}</h3>
        <button class="close-button" @click="selectedNode = null">
          <svg viewBox="0 0 24 24" class="icon-close">
            <path
              fill="currentColor"
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            />
          </svg>
        </button>
      </div>

      <div class="panel-content">
        <PropertyPanel
          v-if="isPropertyPanelVisible"
          :scene="selectedScene"
          :event="getCurrentEvent"
          :availableNodes="panelAvailableNodes"
          :selected-event-id="selectedEventId"
          @update="handlePropertyUpdate"
          @update-scene="updateScene"
          @save-event="handleSaveEvent"
          @add-event="handleAddEvent"
          @select-event="handleEventSelect"
        />
      </div>
    </div>

    <!-- 故事配置面板 -->
    <StoryConfigPanel
      :is-show="isStoryConfigPanelVisible"
      :current-actor="currentActor"
      :story-name="storyName"
      :story-desc="storyDesc"
      :current-story-id="currentStoryId"
      :portrait-url="portraitUrl"
      :initial-skills="tempSelectedSkills"
      @close="handleStoryConfigClose"
      @saved="handleStorySaved"
      @open-skill-selector="handleOpenSkillSelector"
      @open-character-selector="handleOpenCharacterSelector"
    />

    <CharacterSelectModal
      v-if="showCharacterSelectModal"
      :visible="showCharacterSelectModal"
      :actors="editorStore.actors"
      :current-actor="editorStore.selectedActor"
      @close="showCharacterSelectModal = false"
      @confirm="handleCharacterConfirm"
    />

    <!-- 技能选择模态框 -->
    <DesktopSkillSelect
      v-model:visible="showSkillSelectModal"
      v-model="tempSelectedSkills"
      @confirm="handleSkillConfirm"
    />

    <!-- 右键菜单 -->
    <div
      class="context-menu"
      v-show="
        contextMenuVisible &&
        ['root', 'group', 'chapter', 'event', 'begin'].includes(contextMenuNodeType)
      "
      :style="contextMenuStyle as any"
    >
      <ul class="menu-list">
        <!-- Root节点菜单选项 -->
        <li @click="handleContextAddSceneGroup" v-if="contextMenuNodeType === 'root'">
          创建新的场景组
        </li>

        <!-- 场景组节点菜单选项 -->
        <li @click="handleContextAddChapterToGroup" v-if="contextMenuNodeType === 'group'">
          在此组添加章节
        </li>

        <!-- 开始章节和普通章节菜单选项 - 合并处理以保持相同功能 -->
        <li
          @click="handleAddEvent"
          v-if="contextMenuNodeType === 'begin' || contextMenuNodeType === 'chapter'"
        >
          添加事件
        </li>
        <li
          @click="handleDeleteNode"
          v-if="contextMenuNodeType === 'begin' || contextMenuNodeType === 'chapter'"
        >
          删除章节
        </li>
        <li
          @click="handleCopyNode"
          v-if="contextMenuNodeType === 'begin' || contextMenuNodeType === 'chapter'"
        >
          复制章节
        </li>
        <li @click="handleSetJumpScene" v-if="contextMenuNodeType === 'chapter'">设置跳转场景</li>

        <!-- 事件节点菜单选项 -->
        <li @click="handleDeleteEvent" v-if="contextMenuNodeType === 'event'">删除事件</li>
      </ul>
    </div>

    <!-- 事件类型对话框 -->
    <EventTypeDialog
      :visible="showEventTypeDialog"
      @select="handleEventTypeSelect"
      @close="showEventTypeDialog = false"
    />

    <!-- 保存成功提示 -->
    <div class="save-notification" v-show="saveNotification.visible">
      {{ saveNotification.message }}
    </div>

    <!-- 场景选择对话框 -->
    <SceneSelectDialog
      :visible="showSceneSelectDialog"
      :title="sceneSelectDialogTitle"
      :current-scene-id="currentEditingNodeId"
      :initial-selected-scene-id="initialSelectedSceneId"
      @cancel="showSceneSelectDialog = false"
      @confirm="handleSceneSelectConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  computed,
  watch,
  defineExpose,
  nextTick,
  markRaw,
  defineEmits,
  onUnmounted
} from 'vue'
import { useEditorStore } from '@/store/editor'
import PropertyPanel from '@/pc/components/FlowEditor/PropertyPanel.vue'
import type {
  Scene,
  GameEvent,
  GameEventType,
  ActionHandler,
  ActionHandlerParams
} from '@/types/editor'
import { nanoid } from 'nanoid'
import TreeGraph from './TreeGraph.vue'
import { TreeNode, FlowEventType } from '@/pc/components/FlowEditor/types'
import { getEventTypeName } from '@/pc/components/FlowEditor/utils/eventUtils'
import EventTypeDialog from '@/pc/components/FlowEditor/EventTypeDialog.vue'
import StoryConfigPanel from '@/pc/components/FlowEditor/StoryConfigPanel.vue'
import CharacterSelectModal from '@/pc/components/FlowEditor/CharacterSelectModal.vue'
import type { Actor } from '@/api/stories'
import DesktopSkillSelect from '@/mobile/components/Editor/DesktopSkillSelect.vue'
import SceneSelectDialog from '@/pc/components/FlowEditor/SceneSelectDialog.vue'
import { debounce } from 'lodash-es'

// 定义组件发出的事件
const emit = defineEmits<{
  (e: 'node-selected', nodeId: string): void
  (e: 'nodes-changed'): void
}>()

// 事件相关类型定义

// 扩展现有的Scene接口，添加scene_group字段
interface ExtendedScene extends Scene {
  scene_group?: string
}

const editorStore = useEditorStore()
const selectedNode = ref<TreeNode | null>(null)
const contextMenuVisible = ref(false)
const contextMenuPosition = ref({ x: 0, y: 0 })
const contextMenuTargetId = ref('')
const treeGraph = ref<InstanceType<typeof TreeGraph> | null>(null)
const contextMenuNodeType = ref('') // 'begin', 'chapter', 'event'
const showEventTypeDialog = ref(false)
const showCharacterSelectModal = ref(false)
const showSkillSelectModal = ref(false)
const tempSelectedSkills = ref<any[]>([])
const showStoryConfig = ref(false)
const isActiveStoryConfig = ref(false)
const contextMenuNode = ref<TreeNode | null>(null)
// 添加selectedEventId变量，用于新创建的事件自动展开
const selectedEventId = ref('')

// 场景跳转相关变量
const showSceneSelectDialog = ref(false)
const sceneSelectDialogTitle = ref('选择目标场景')
const currentEditingNodeId = ref('')
const initialSelectedSceneId = ref<string | null>(null)

// 属性面板宽度调整相关变量
const panelWidth = ref(320) // 默认宽度
const minPanelWidth = 250 // 最小宽度
const maxPanelWidth = 1000 // 最大宽度
const isResizing = ref(false)

// 开始调整面板宽度
const startResize = (e: MouseEvent) => {
  isResizing.value = true
  const startX = e.clientX
  const startWidth = panelWidth.value

  const handleMouseMove = (moveEvent: MouseEvent) => {
    if (!isResizing.value) return
    const deltaX = startX - moveEvent.clientX
    const newWidth = Math.min(Math.max(startWidth + deltaX, minPanelWidth), maxPanelWidth)
    panelWidth.value = newWidth
  }

  const handleMouseUp = () => {
    isResizing.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 添加关闭上下文菜单的函数
const closeContextMenu = () => {
  contextMenuVisible.value = false
  contextMenuNode.value = null
}

// 添加计算属性控制面板显示 - 确保放在被使用之前定义
const isPropertyPanelVisible = computed(() => {
  // 只有当选中了节点且故事配置面板未显示时，才显示属性面板
  return selectedNode.value !== null && !showStoryConfig.value
})

// 修改故事配置面板的显示状态
const isStoryConfigPanelVisible = computed(() => {
  // 只有当故事配置面板设置为显示时才显示
  return showStoryConfig.value
})

// 处理故事配置面板关闭的方法
const handleStoryConfigClose = () => {
  showStoryConfig.value = false
  isActiveStoryConfig.value = false
}

// 右键菜单样式 - 使菜单始终出现在节点右侧
const contextMenuStyle = computed(() => {
  // 使用小的偏移量，确保菜单与节点有一定间距
  const offsetX = 5
  const offsetY = 0

  return {
    position: 'absolute' as const,
    left: `${contextMenuPosition.value.x + offsetX}px`,
    top: `${contextMenuPosition.value.y + offsetY}px`,
    transformOrigin: 'left center'
  }
})

// 将treeNodes实现修改为可缓存的计算属性，避免不必要的重新计算
const treeNodes = computed<TreeNode[]>(() => {
  // 将编辑器的场景数据转为树节点数据
  const nodes: TreeNode[] = []

  try {
    // 获取所有场景并按scene_group分组
    const allScenes = editorStore.gameConfig.scenes.filter(
      (scene) => scene.id !== '~'
    ) as ExtendedScene[]

    // 收集所有的场景组
    const sceneGroups = new Set<string>()
    allScenes.forEach((scene) => {
      const groupName = scene.scene_group || 'default'
      sceneGroups.add(groupName)
    })

    // 添加所有场景组节点（第二层），它们的parentId会在TreeGraph.vue中设置为虚拟根节点
    Array.from(sceneGroups).forEach((groupName) => {
      const groupNode: TreeNode = {
        id: `group_${groupName}`,
        name: groupName.charAt(0).toUpperCase() + groupName.slice(1), // 首字母大写
        type: 'group',
        // 注意：不设置parentId，由TreeGraph.vue处理
        scene_group: groupName,
        properties: {
          scene_group: groupName
        }
      }
      nodes.push(markRaw(groupNode))
    })

    // 为每个场景组构建父子关系树
    Array.from(sceneGroups).forEach((groupName) => {
      // 过滤出属于当前分组的场景
      const groupScenes = allScenes.filter(
        (scene) => (scene.scene_group || 'default') === groupName
      )

      // 如果分组为空，跳过
      if (groupScenes.length === 0) return

      // 创建父子关系映射
      const parentChildMap = new Map<string, string[]>()

      // 初始化映射
      groupScenes.forEach((scene) => {
        if (!parentChildMap.has(scene.id)) {
          parentChildMap.set(scene.id, [])
        }
      })

      // 填充父子关系映射
      groupScenes.forEach((scene) => {
        const parentId = scene.parent_id || '~'
        if (parentId !== '~' && groupScenes.some((s) => s.id === parentId)) {
          if (!parentChildMap.has(parentId)) {
            parentChildMap.set(parentId, [])
          }
          parentChildMap.get(parentId)?.push(scene.id)
        }
      })

      // 找出该分组的根节点（parent_id 是 ~ 或不在当前分组中的节点）
      const rootScenes = groupScenes.filter((scene) => {
        const parentId = scene.parent_id || '~'
        return parentId === '~' || !groupScenes.some((s) => s.id === parentId)
      })

      // 递归函数，用于构建树结构
      const buildSceneTree = (scene: ExtendedScene, parentId: string = `group_${groupName}`) => {
        // 创建当前场景节点
        const sceneNode: TreeNode = {
          id: scene.id,
          name: scene.name || '未命名章节',
          type: scene.id === '_BEGIN_' ? 'begin' : scene.id === '_END_' ? 'end' : 'chapter',
          parentId: parentId,
          scene_group: groupName,
          next_scene_id: scene.next_scene_id,
          properties: scene,
          conditions: scene.conditions,
          action_handlers: scene.action_handlers
        }
        nodes.push(markRaw(sceneNode))

        // 添加事件节点
        if (scene.events && scene.events.length > 0) {
          scene.events.forEach((event, index) => {
            const eventNode: TreeNode = {
              id: event.id || `${scene.id}_event_${index}`,
              name: getEventTypeName(event.type as FlowEventType) || `事件${index + 1}`,
              type: 'event',
              eventType: event.type,
              parentId: scene.id,
              properties: event
            }
            nodes.push(markRaw(eventNode))
          })
        }

        // 添加互动事件
        if (scene.action_handlers && scene.action_handlers.length > 0) {
          const interactiveEventNode: TreeNode = {
            id: `${scene.id}_interactive`,
            name: '互动事件',
            type: 'event',
            eventType: 'interactive' as GameEventType,
            parentId: scene.id,
            properties: {
              id: `${scene.id}_interactive`,
              type: 'interactive',
              params: scene.action_handlers[0]?.params || {}
            }
          }
          nodes.push(markRaw(interactiveEventNode))
        }

        // 递归处理子节点
        const children = parentChildMap.get(scene.id) || []
        children.forEach((childId) => {
          const childScene = groupScenes.find((s) => s.id === childId)
          if (childScene) {
            buildSceneTree(childScene, scene.id)
          }
        })
      }

      // 处理每个根场景及其子树
      rootScenes.forEach((rootScene) => {
        buildSceneTree(rootScene)
      })
    })
  } catch (error) {
    console.error('构建树节点时出错:', error)
  }

  // 当节点变化时通知父组件
  nextTick(() => {
    emit('nodes-changed')
  })

  return nodes
})

// 获取选中节点对应的场景
const selectedScene = computed(() => {
  if (!selectedNode.value) return null

  // 如果是事件节点，返回其父节点的场景
  if (selectedNode.value.type === 'event') {
    const parentId = selectedNode.value.parentId
    if (parentId) {
      return editorStore.gameConfig.scenes.find((s) => s.id === parentId) || null
    }
    return null
  }

  // 否则返回节点对应的场景
  return editorStore.gameConfig.scenes.find((s) => s.id === selectedNode.value?.id) || null
})

// 是否可以删除节点
const canDeleteNode = computed(() => {
  return (
    selectedNode.value && !['_END_', '~'].includes(selectedNode.value.id) // 移除 '_BEGIN_' 限制
  )
})

// 节点选中事件处理
const handleNodeSelected = (node: TreeNode | null) => {
  if (node === null) {
    selectedNode.value = null
    emit('node-selected', '') // 发射空节点ID事件
    return
  }

  try {
    console.log('节点选中:', node.id, node.name)

    // 如果故事配置面板正在显示，先关闭它
    if (showStoryConfig.value) {
      showStoryConfig.value = false
      isActiveStoryConfig.value = false
    }

    // 使用一个简单的非响应式对象避免Vue进行深度响应式代理
    const simpleNode = {
      id: node.id,
      name: node.name || '',
      type: node.type,
      parentId: node.parentId,
      eventType: node.eventType,
      properties: node.properties
    }

    // 直接设置，不经过Vue的响应式系统
    selectedNode.value = Object.freeze(simpleNode)

    // 发射节点选中事件，传递节点ID
    emit('node-selected', node.id)
  } catch (error) {
    console.error('处理节点选择时出错:', error)
    // 失败时也发送空节点ID
    emit('node-selected', '')
  }
}

// 添加节点事件处理
const handleNodeAdded = (parentNode: TreeNode, _position: { x: number; y: number }) => {
  if (parentNode.type === 'event') return

  if (parentNode.type === 'root' || parentNode.type === 'group') {
    // 添加新章节到分组节点下
    handleAddChapter(parentNode.scene_group)
  } else if (parentNode.type === 'begin' || parentNode.type === 'chapter') {
    // 添加事件
    addEventNode(parentNode.id)
  }
}

// 计算当前选中的事件
const getCurrentEvent = computed(() => {
  if (!selectedNode.value || selectedNode.value.type !== 'event') return null

  const eventNode = selectedNode.value

  // 如果是互动事件
  if (eventNode.eventType === 'interactive' && eventNode.id.endsWith('_interactive')) {
    // 获取父场景
    const parentId = eventNode.parentId
    const parentScene = editorStore.gameConfig.scenes.find((s) => s.id === parentId)

    if (parentScene && parentScene.action_handlers && parentScene.action_handlers.length > 0) {
      return {
        id: eventNode.id,
        type: 'interactive' as GameEventType,
        plot: parentScene.action_handlers[0].params || {}
      }
    }

    // 如果父场景没有action_handlers，返回空的互动事件
    return {
      id: eventNode.id,
      type: 'interactive' as GameEventType,
      plot: {}
    }
  }

  // 常规事件
  return eventNode.properties as GameEvent
})

// 改进PropertyPanel的绑定方式，避免使用计算属性的结果
const panelAvailableNodes = computed(() => {
  // 返回一个新数组，避免直接使用响应式对象
  return treeNodes.value.map((node) => ({
    id: node.id,
    name: node.name,
    type: node.type
  }))
})

// 处理属性面板的更新事件
const handlePropertyUpdate = (updatedEvent: Partial<GameEvent>) => {
  console.log('处理属性面板更新事件:', JSON.stringify(updatedEvent))
  if (!selectedNode.value || selectedNode.value.type !== 'event') return

  // 找到当前选中的事件节点在树结构中的位置
  const index = treeNodes.value.findIndex((node) => node.id === selectedNode.value?.id)
  if (index > -1) {
    // 创建新的节点数组，避免直接修改原数组
    const updatedNodes = [...treeNodes.value]

    // 保存原始的节点类型
    const originalType = updatedNodes[index].type
    const originalEventType = updatedNodes[index].eventType || updatedEvent.type

    // 更新节点，使用深拷贝避免共享引用，但避免覆盖type
    const updatedNode = {
      ...updatedNodes[index],
      ...JSON.parse(JSON.stringify(updatedEvent)),
      // 保留原始节点类型
      type: originalType,
      // 更新eventType
      eventType: updatedEvent.type || originalEventType
    }

    // 将plot数据复制到单独的属性，方便updateEventNode处理
    if (updatedEvent.plot) {
      updatedNode.plot = JSON.parse(JSON.stringify(updatedEvent.plot))
    }

    console.log('更新后的节点:', JSON.stringify(updatedNode))

    // 使用新节点替换原节点
    updatedNodes[index] = updatedNode

    // 使用防抖更新树节点
    debouncedUpdateTreeNodes(updatedNodes)
  }
}

// 使用防抖处理更新
const debouncedUpdateTreeNodes = (() => {
  let timer: ReturnType<typeof setTimeout> | null = null
  let isUpdating = false
  let lastValue: string | null = null

  return (updatedNodes: TreeNode[]) => {
    if (timer) clearTimeout(timer)
    if (isUpdating) return

    // 找到要更新的节点
    const updatedNode = updatedNodes.find((n) => n.id === selectedNode.value?.id)
    if (!updatedNode) return

    // 比较新旧值，包含所有需要比较的字段
    const currentValue = JSON.stringify({
      id: updatedNode.id,
      name: updatedNode.name,
      type: updatedNode.type,
      next_scene_id: updatedNode.next_scene_id,
      conditions: updatedNode.conditions,
      parentId: updatedNode.parentId,
      events: updatedNode.events,
      properties: updatedNode.properties
    })

    if (currentValue === lastValue) {
      return
    }

    timer = setTimeout(() => {
      isUpdating = true
      lastValue = currentValue
      // 更新节点
      updateNodesInEditor(updatedNodes)
      setTimeout(() => {
        isUpdating = false
      }, 100)
    }, 50)
  }
})()

// 更新树节点到编辑器存储的辅助函数
const updateNodesInEditor = (updatedNodes: TreeNode[]) => {
  try {
    // 找到更新的节点
    if (selectedNode.value) {
      const updatedNode = updatedNodes.find((n) => n.id === selectedNode.value?.id)
      if (updatedNode) {
        console.log('更新节点222:', JSON.stringify(updatedNode))

        // 判断节点类型
        const isEventNode =
          updatedNode.type === 'event' ||
          // 检查事件类型是否被错误地赋值给了type属性
          [
            'message',
            'wait',
            'show_tips',
            'play_audio',
            'play_video',
            'show_image',
            'show_chat_options',
            'scene_transition',
            'heart_value',
            'show_ending',
            'interactive'
          ].includes(updatedNode.type)

        if (isEventNode) {
          // 修正节点类型
          if (updatedNode.type !== 'event') {
            console.log('节点类型异常：', updatedNode.type, '，已自动修正为event类型')
            // 记录原始类型到eventType
            updatedNode.eventType = updatedNode.type
            // 修正type为正确的节点类型
            updatedNode.type = 'event'
          }
          // 更新事件节点
          updateEventNode(updatedNode)
        } else {
          // 更新场景节点
          updateSceneNode(updatedNode)
        }
      }
    }
  } catch (error) {
    console.error('更新节点数据时出错:', error)
  }
}

// 更新事件节点
const updateEventNode = (node: TreeNode & { plot?: Record<string, any> }) => {
  console.log('开始更新事件节点:', node.id, JSON.stringify(node.properties))

  if (!node.parentId) {
    console.error('无法更新事件节点: 缺少父节点ID')
    return
  }

  // 查找父场景 (这里不要使用引用，而是使用深拷贝)
  const parentSceneIndex = editorStore.gameConfig.scenes.findIndex((s) => s.id === node.parentId)
  if (parentSceneIndex === -1) {
    console.error('无法更新事件节点: 未找到对应的父场景', node.parentId)
    return
  }

  // 使用深拷贝获取父场景，避免直接修改原始数据
  const parentScene = JSON.parse(JSON.stringify(editorStore.gameConfig.scenes[parentSceneIndex]))
  console.log('找到父场景:', parentScene.id, parentScene.name)

  try {
    // 检查是否是互动事件类型
    if (node.eventType === 'interactive' && node.id.endsWith('_interactive')) {
      console.log('更新互动事件:', node.id)

      // 从节点提取互动事件参数
      let params: ActionHandlerParams | null = null

      // 尝试从各种可能的位置获取参数
      if (node.properties && typeof node.properties === 'object') {
        if ('plot' in node.properties) {
          // 如果是GameEvent格式
          params = node.properties.plot as any
        } else if ('params' in node.properties) {
          // 如果是直接包含params字段
          params = node.properties.params as any
        }
      } else if (node.plot) {
        // 如果plot直接在节点上
        params = node.plot as any
      }

      if (!params) {
        console.warn('互动事件缺少参数，使用默认值')
        params = {
          level: '',
          background: '',
          heart_key: '',
          heart_value: 0,
          clean_history: false,
          limit_chat_count: 3,
          agree_sentences: [],
          streamer_tpl: ''
        }
      }

      // 确保父场景有action_handlers数组
      if (!parentScene.action_handlers) {
        parentScene.action_handlers = []
      }

      // 创建或更新action_handler
      const actionHandler: ActionHandler = {
        type: 'ScoreLimitWithLLMChatV2',
        params: {
          level: params.level || '',
          background: params.background || '',
          heart_key: params.heart_key || '',
          heart_value: params.heart_value || 0,
          clean_history: params.clean_history || false,
          limit_chat_count: params.limit_chat_count || 3,
          agree_sentences: params.agree_sentences || [],
          streamer_tpl: params.streamer_tpl || ''
        }
      }

      // 更新父场景的action_handlers
      parentScene.action_handlers = [actionHandler]

      // 把更新后的场景保存回store
      editorStore.updateScene(parentScene)
      console.log('互动事件已更新')
      return
    }

    // 常规事件处理逻辑
    // 找到事件在父场景中的索引
    const eventIndex = parentScene.events.findIndex((event: GameEvent) => event.id === node.id)

    console.log('当前事件索引:', eventIndex)
    if (eventIndex >= 0) {
      console.log('更新前的事件:', JSON.stringify(parentScene.events[eventIndex]))

      // 提取当前事件，以便保留未被修改的字段
      const currentEvent = parentScene.events[eventIndex]
      console.log('当前事件:', JSON.stringify(currentEvent))

      // 确保事件类型是有效的GameEventType
      const nodeEventType = node.eventType
        ? (node.eventType as GameEventType)
        : (currentEvent.type as GameEventType)

      // 初始化更新后的事件对象
      let updatedEvent: GameEvent = {
        id: node.id,
        type: nodeEventType, // 使用转换后的eventType
        plot: JSON.parse(JSON.stringify(currentEvent.plot || {}))
      }

      // 首先检查是否有直接的plot属性
      if (node.plot && typeof node.plot === 'object') {
        console.log('使用直接的plot属性更新:', JSON.stringify(node.plot))
        updatedEvent.plot = {
          ...updatedEvent.plot,
          ...JSON.parse(JSON.stringify(node.plot))
        }
      }
      // 然后检查node.properties的格式并进行适当处理
      else if (node.properties && typeof node.properties === 'object') {
        // 情况1: 完整的GameEvent对象
        if ('id' in node.properties && 'type' in node.properties && 'plot' in node.properties) {
          // 从node.properties获取完整的GameEvent对象
          const gameEvent = node.properties as GameEvent
          updatedEvent = {
            id: node.id, // 确保ID保持不变
            type: nodeEventType || gameEvent.type, // 使用转换后的eventType优先
            plot: JSON.parse(JSON.stringify(gameEvent.plot || {}))
          }
          console.log('使用完整GameEvent更新:', JSON.stringify(updatedEvent))
        }
        // 情况2: FlowEvent格式转换
        else if (
          'id' in node.properties &&
          'type' in node.properties &&
          'params' in node.properties
        ) {
          // 从FlowEvent转换为GameEvent
          const flowEvent = node.properties as any
          updatedEvent = {
            id: node.id,
            type: nodeEventType || (flowEvent.type as GameEventType), // 使用转换后的eventType优先
            plot: JSON.parse(JSON.stringify(flowEvent.params || {}))
          }
          console.log('从FlowEvent转换为GameEvent:', JSON.stringify(updatedEvent))
        }
        // 情况3: 直接的plot对象
        else {
          // 保留原始类型和ID，只更新plot
          updatedEvent = {
            id: node.id,
            type: nodeEventType || currentEvent.type, // 使用转换后的eventType优先
            plot: {
              ...JSON.parse(JSON.stringify(currentEvent.plot || {})),
              ...JSON.parse(JSON.stringify(node.properties))
            }
          }
          console.log('更新plot属性:', JSON.stringify(updatedEvent))
        }
      }

      // 根据事件类型检查必要属性
      switch (updatedEvent.type) {
        case 'wait':
          // 确保wait事件有必要的属性
          if (!updatedEvent.plot || Object.keys(updatedEvent.plot).length === 0) {
            updatedEvent.plot = { seconds: 1 }
          } else if (updatedEvent.plot.seconds === undefined) {
            updatedEvent.plot.seconds = 1
          }
          // 移除不支持的属性
          if ('showIndicator' in updatedEvent.plot) {
            delete (updatedEvent.plot as any).showIndicator
          }
          console.log('处理wait事件，最终plot:', JSON.stringify(updatedEvent.plot))
          break

        case 'play_video':
          // 视频事件必要属性处理
          if (updatedEvent.plot.is_background === undefined) {
            updatedEvent.plot.is_background = false
          }
          if (
            !updatedEvent.plot.is_background &&
            updatedEvent.plot.min_watch_duration === undefined
          ) {
            updatedEvent.plot.min_watch_duration = 2
          }
          if (updatedEvent.plot.is_background && 'min_watch_duration' in updatedEvent.plot) {
            delete updatedEvent.plot.min_watch_duration
          }
          console.log('处理video事件，最终plot:', JSON.stringify(updatedEvent.plot))
          break // 可以添加其他事件类型的特殊处理
      }

      // 日志输出最终要更新的事件数据
      console.log('最终要更新的事件数据:', JSON.stringify(updatedEvent))

      // 更新父场景中的事件
      parentScene.events[eventIndex] = updatedEvent

      console.log('更新后的场景:', parentScene.events[eventIndex])
      // 把更新后的场景保存回store (使用updateScene方法)
      editorStore.updateScene(parentScene)

      console.log('事件节点已更新:', updatedEvent.id)
    } else {
      console.error('无法更新事件节点: 在父场景中未找到对应事件', node.id)
    }
  } catch (error) {
    console.error('更新事件节点时出错:', error)
  }
}

// 更新场景节点
const updateSceneNode = (node: TreeNode) => {
  const existingScene = editorStore.gameConfig.scenes.find((s) => s.id === node.id)
  if (existingScene) {
    // 创建更新后的场景对象
    const updatedScene = {
      ...existingScene,
      name: node.name || existingScene.name,
      next_scene_id: node.next_scene_id || existingScene.next_scene_id,
      conditions: node.conditions || existingScene.conditions,
      parent_id: node.properties?.parent_id || existingScene.parent_id,
      events: node.properties?.events || existingScene.events || []
    }

    // 直接更新store
    const index = editorStore.gameConfig.scenes.findIndex((s) => s.id === node.id)
    if (index !== -1) {
      // 使用Vue的响应式更新方法
      editorStore.gameConfig.scenes[index] = updatedScene

      // 强制更新 treeNodes
      nextTick(() => {
        // 触发节点变化事件
        emit('nodes-changed')

        // 如果是当前选中的节点，更新选中状态
        if (selectedNode.value && selectedNode.value.id === node.id) {
          const updatedTreeNode = treeNodes.value.find((n) => n.id === node.id)
          if (updatedTreeNode) {
            selectedNode.value = markRaw(updatedTreeNode)
          }
        }
      })
    }
  }
}

// 更新场景属性
const updateScene = (updatedScene: Partial<Scene> & { _oldId?: string }) => {
  if (!selectedNode.value || selectedNode.value.type === 'event') return

  console.log('更新场景属性:', JSON.stringify(updatedScene))

  // 检查是否有 ID 变化
  const hasIdChange =
    updatedScene._oldId && updatedScene.id && updatedScene._oldId !== updatedScene.id

  // 如果有 ID 变化，需要特殊处理
  if (hasIdChange) {
    console.log('检测到 ID 变化，从', updatedScene._oldId, '到', updatedScene.id)

    // 在 editorStore 中找到对应的场景
    const sceneIndex = editorStore.gameConfig.scenes.findIndex((s) => s.id === updatedScene._oldId)
    if (sceneIndex !== -1) {
      // 更新场景的 ID
      const oldId = updatedScene._oldId
      const newId = updatedScene.id as string

      // 在更新前保存当前选中节点的引用
      const currentSelectedNode = selectedNode.value

      // 创建更新后的场景对象
      const updatedSceneObj = {
        ...editorStore.gameConfig.scenes[sceneIndex],
        ...updatedScene,
        id: newId
      }

      // 删除临时属性
      delete (updatedSceneObj as any)._oldId

      // 更新场景 - 使用数组替换方式触发响应式更新
      const newScenes = [...editorStore.gameConfig.scenes]
      newScenes[sceneIndex] = updatedSceneObj

      // 更新其他场景的引用
      newScenes.forEach((scene, idx) => {
        if (scene.next_scene_id === oldId) {
          newScenes[idx] = { ...scene, next_scene_id: newId }
        }
        if (scene.parent_id === oldId) {
          newScenes[idx] = { ...scene, parent_id: newId }
        }
      })

      // 替换整个场景数组，触发响应式更新
      editorStore.gameConfig.scenes = newScenes

      // 为了避免 PropertyPanel 闪现，先更新 selectedNode
      // 创建一个新的节点对象，保留原来的属性，但更新 id
      selectedNode.value = markRaw({
        ...currentSelectedNode,
        id: newId,
        properties: {
          ...currentSelectedNode.properties,
          id: newId
        }
      })

      // 使用 X6 的数据更新机制更新节点
      nextTick(() => {
        console.log('使用 X6 数据更新机制更新节点:', newId)

        // 如果有 treeGraph 实例，使用 forceNodesUpdate 方法更新特定节点
        if (treeGraph.value) {
          // 先触发节点变化事件，确保 treeNodes 计算属性更新
          emit('nodes-changed')

          // 等待 treeNodes 更新
          setTimeout(() => {
            // 强制更新节点
            if (treeGraph.value && typeof treeGraph.value.forceNodesUpdate === 'function') {
              console.log('调用 forceNodesUpdate 方法')
              treeGraph.value.forceNodesUpdate()
            } else {
              console.warn('treeGraph.value 或 forceNodesUpdate 方法不可用')
              // 使用备用方案
              emit('nodes-changed')
            }

            // 聚焦到新节点
            setTimeout(() => {
              if (treeGraph.value && typeof treeGraph.value.focusNode === 'function') {
                console.log('调用 focusNode 方法，聚焦到节点:', newId)
                treeGraph.value.focusNode(newId)
              } else {
                console.warn('treeGraph.value 或 focusNode 方法不可用')
              }
            }, 100)
          }, 200)
        } else {
          // 如果没有 treeGraph 实例，使用原来的方式
          emit('nodes-changed')
        }
      })

      return
    }
  }

  // 正常的场景更新处理
  // 找到当前选中的场景节点
  const index = treeNodes.value.findIndex((node) => node.id === selectedNode.value?.id)
  if (index > -1) {
    // 创建新的节点数组
    const updatedNodes = [...treeNodes.value]

    // 合并更新并使用深拷贝断开引用
    const updatedNode = {
      ...updatedNodes[index],
      name: updatedScene.name || updatedNodes[index].name,
      next_scene_id: updatedScene.next_scene_id,
      conditions: updatedScene.conditions,
      properties: {
        ...updatedNodes[index].properties,
        ...updatedScene,
        name: updatedScene.name || updatedNodes[index].name
      }
    }

    // 确保保留原始的type和其他重要属性
    updatedNode.type = updatedNodes[index].type
    updatedNode.id = updatedNodes[index].id
    updatedNode.parentId = updatedNodes[index].parentId

    // 使用新节点替换原节点
    updatedNodes[index] = updatedNode

    // 直接更新节点
    updateSceneNode(updatedNode)
  }
}

// 创建新章节的通用函数
const createNewChapter = (sceneGroup: string, afterCreation?: (chapterId: string) => void) => {
  console.log('在场景组中创建新章节:', sceneGroup)

  // 默认场景组为default
  const targetSceneGroup = sceneGroup || 'default'

  // 找到当前场景组的所有章节
  const allScenes = editorStore.gameConfig.scenes as ExtendedScene[]
  const groupScenes = allScenes.filter(
    (scene) => (scene.scene_group || 'default') === targetSceneGroup
  )

  console.log(`场景组 "${targetSceneGroup}" 中找到 ${groupScenes.length} 个章节`)

  // 计算下一个章节编号
  const levelPattern = /^level-(\d+)$/
  const levelNumbers = groupScenes
    .map((scene) => {
      const match = scene.name?.match(levelPattern)
      return match ? parseInt(match[1], 10) : 0
    })
    .filter((num) => num > 0)
    .sort((a, b) => a - b)

  let nextLevelNumber = 1
  if (levelNumbers.length > 0) {
    nextLevelNumber = levelNumbers[levelNumbers.length - 1] + 1
  }

  const newChapterName = `level-${nextLevelNumber}`
  console.log(`为场景组 "${targetSceneGroup}" 创建新章节: ${newChapterName}`)

  // 为新章节生成唯一ID
  const newChapterId = nanoid()

  // 如果场景组为空，创建第一个章节
  if (groupScenes.length === 0) {
    console.log(`场景组 "${targetSceneGroup}" 为空，创建第一个章节，parent_id 为根节点`)
    // 创建新章节作为该组的第一个章节
    const newChapter: ExtendedScene = {
      id: newChapterId,
      name: newChapterName,
      parent_id: '~', // 没有前置章节时，设置为根节点的子节点
      scene_group: targetSceneGroup,
      events: []
    }

    // 寻找合适的插入位置 - 按场景组名称字母顺序排序
    // 获取所有场景组
    const allGroups = Array.from(
      new Set(allScenes.map((scene) => scene.scene_group || 'default'))
    ).sort()

    // 找出当前场景组在所有场景组中的索引位置
    const currentGroupIndex = allGroups.indexOf(targetSceneGroup)

    if (currentGroupIndex !== -1 && currentGroupIndex < allGroups.length - 1) {
      // 如果不是最后一个场景组，查找下一个场景组的第一个场景
      const nextGroup = allGroups[currentGroupIndex + 1]
      const nextGroupFirstSceneIndex = allScenes.findIndex(
        (scene) => (scene.scene_group || 'default') === nextGroup
      )

      if (nextGroupFirstSceneIndex !== -1) {
        // 在下一个场景组的第一个场景之前插入
        editorStore.gameConfig.scenes.splice(nextGroupFirstSceneIndex, 0, newChapter as any)
        console.log(
          `新章节插入到场景组 "${nextGroup}" 的第一个场景之前，索引: ${nextGroupFirstSceneIndex}`
        )

        // 更新场景引用关系
        updateSceneReferences('insert', newChapter, nextGroupFirstSceneIndex)
      } else {
        // 追加到数组末尾
        editorStore.gameConfig.scenes.push(newChapter as any)
        console.log('未找到下一个场景组的场景，将新章节追加到末尾')
      }
    } else {
      // 如果是最后一个场景组或未找到场景组索引，追加到数组末尾
      editorStore.gameConfig.scenes.push(newChapter as any)
      console.log('当前场景组是最后一个或未找到场景组索引，将新章节追加到末尾')
    }
  } else {
    // 找到当前场景组中的最后一个章节
    const lastChapter = groupScenes[groupScenes.length - 1]
    console.log(
      `找到场景组 "${targetSceneGroup}" 的最后一个章节: ID=${lastChapter.id}, 名称=${lastChapter.name}`
    )

    // 创建新章节，将parent_id设置为最后一个章节的ID
    const newChapter: ExtendedScene = {
      id: newChapterId,
      name: newChapterName,
      parent_id: lastChapter.id, // 设置为场景组最后一个章节的ID
      scene_group: targetSceneGroup, // 确保场景组标识一致
      events: []
    }

    console.log(
      `创建新章节: ID=${newChapterId}, 名称=${newChapterName}, parent_id=${lastChapter.id}, scene_group=${targetSceneGroup}`
    )

    // 查找父章节在数组中的索引位置
    const parentIndex = editorStore.gameConfig.scenes.findIndex((s) => s.id === lastChapter.id)

    if (parentIndex !== -1) {
      // 在父章节后面插入新章节，而不是简单地追加到数组末尾
      editorStore.gameConfig.scenes.splice(parentIndex + 1, 0, newChapter as any)
      console.log(`新章节已插入到父章节(索引: ${parentIndex})之后`)

      // 更新场景引用关系
      updateSceneReferences('insert', newChapter, parentIndex + 1)
    } else {
      // 如果找不到父章节，则追加到数组末尾（这种情况不应该发生）
      console.warn('未找到父章节，将新章节追加到末尾')
      editorStore.gameConfig.scenes.push(newChapter as any)
    }
  }

  // 刷新视图并聚焦到新节点
  refreshAndFocusNode(newChapterId)

  // 执行创建后的回调函数（如果有的话）
  if (afterCreation) {
    afterCreation(newChapterId)
  }

  return newChapterId
}

// 添加子章节
const handleAddChapter = (sceneGroup?: string) => {
  console.log('添加新章节, 场景组:', sceneGroup)
  createNewChapter(sceneGroup || 'default')
}

// 添加事件节点
const addEventNode = (parentId?: string) => {
  if (!parentId && selectedNode.value) {
    parentId = selectedNode.value.id
  }

  if (!parentId) {
    console.warn('无法添加事件：未选中节点且未提供父节点ID')
    return null
  }

  // 显示事件类型选择对话框
  showEventTypeDialog.value = true
}

// 添加 handleNodeContextMenu 函数
const handleNodeContextMenu = (node: TreeNode, position: { x: number; y: number }) => {
  // 设置右键菜单位置 - 使用传入的位置
  // 这个位置已经在 TreeGraph 组件中计算为节点的右侧中间位置
  contextMenuPosition.value = position

  // 记录当前右键菜单的目标节点
  contextMenuNode.value = node
  contextMenuTargetId.value = node.id

  // 设置右键菜单类型
  if (node.type === 'root') {
    contextMenuNodeType.value = 'root'
  } else if (node.type === 'group') {
    contextMenuNodeType.value = 'group'
  } else if (node.type === 'begin' || node.type === 'chapter') {
    contextMenuNodeType.value = 'chapter'
  } else if (node.type === 'event') {
    contextMenuNodeType.value = 'event'
  } else {
    console.log(node.type)
    contextMenuNodeType.value = ''
  }

  // 显示右键菜单
  contextMenuVisible.value = true
}

// 删除节点
const deleteNode = () => {
  if (!canDeleteNode.value || !selectedNode.value) return

  const nodeToDelete = { ...selectedNode.value }
  const isBeginNode = nodeToDelete.id === '_BEGIN_'

  // 先清空选中状态，避免在删除过程中触发不必要的更新
  selectedNode.value = null

  if (nodeToDelete.type === 'event') {
    // 删除事件
    const parentId = nodeToDelete.parentId
    const eventId = nodeToDelete.id

    if (parentId && eventId) {
      const parentScene = editorStore.gameConfig.scenes.find((s) => s.id === parentId)
      if (parentScene) {
        // 检查是否是互动事件
        if (eventId.endsWith('_interactive')) {
          // 如果是互动事件，直接清空action_handlers
          if (parentScene.action_handlers) {
            parentScene.action_handlers = []
          }
        }
        // 普通事件
        else if (parentScene.events) {
          const eventIndex = parentScene.events.findIndex((e) => e.id === eventId)
          if (eventIndex >= 0) {
            parentScene.events.splice(eventIndex, 1)
          }
        }

        // 更新场景
        editorStore.updateScene(parentScene)

        // 触发节点变化事件
        setTimeout(() => emit('nodes-changed'), 100)
      }
    }
  } else {
    // 删除场景节点前，先找到它在场景列表中的位置
    const scenes = editorStore.gameConfig.scenes
    const deleteIndex = scenes.findIndex((s) => s.id === nodeToDelete.id)

    if (deleteIndex !== -1) {
      // 找到要删除节点的场景对象
      const sceneToDelete = scenes[deleteIndex] as ExtendedScene

      // 先更新引用关系
      updateSceneReferences('delete', sceneToDelete)

      // 删除节点
      scenes.splice(deleteIndex, 1)
    }

    // 如果删除的是开始章节（_BEGIN_），则创建一个新的开始章节
    if (isBeginNode) {
      // 创建新的开始章节
      const newBeginScene: Scene = {
        id: '_BEGIN_',
        name: '开始章节',
        events: []
      }

      // 添加到 store
      editorStore.gameConfig.scenes.push(newBeginScene)
    }

    // 触发节点变化事件
    setTimeout(() => emit('nodes-changed'), 100)
  }
}

// 右键菜单 - 删除事件处理
const handleDeleteEvent = () => {
  const targetId = contextMenuTargetId.value

  // 先关闭菜单
  contextMenuVisible.value = false

  if (!targetId) return

  // 找到对应的节点
  const node = treeNodes.value.find((n) => n.id === targetId)
  if (node && node.type === 'event' && node.parentId) {
    // 设置选中节点
    selectedNode.value = markRaw(node)

    // 延迟删除操作
    setTimeout(() => {
      deleteNode()
    }, 100)
  }
}

// 复制节点
const handleCopyNode = () => {
  const targetId = contextMenuTargetId.value

  // 先关闭菜单
  contextMenuVisible.value = false

  if (!targetId) return

  const targetScene = editorStore.gameConfig.scenes.find((s) => s.id === targetId)
  if (targetScene) {
    // 创建新场景，如果是开始章节（_BEGIN_），则特殊处理
    const isBeginNode = targetId === '_BEGIN_'
    const newScene: Scene = {
      ...JSON.parse(JSON.stringify(targetScene)),
      // 如果是复制开始章节，使用新生成的ID，否则会冲突
      id: isBeginNode ? nanoid() : nanoid(),
      name: `${targetScene.name}${isBeginNode ? ' (副本)' : ' (复制)'}`,
      parent_id: targetScene.id
    }

    // 例子： [{id:1,parent_id:2},{id:2,parent_id:1, {id:3,parent_id:2}}]在id:1后面插入{id:3,parent_id:1}，则{id:2,parent_id:1}的parent_id要变成3, {id:3,parent_id:2}的parent_id要变成2
    const scenes = editorStore.gameConfig.scenes
    const insertIndex = scenes.findIndex((s) => s.id === targetId)

    if (insertIndex !== -1) {
      // 将新章节插入到原章节的后面
      scenes.splice(insertIndex + 1, 0, newScene)

      // 更新后续章节的 parent_id 链
      for (let i = insertIndex + 2; i < scenes.length; i++) {
        const currentScene = scenes[i]
        // 如果当前场景的 parent_id 指向目标节点，更新为新节点的 id
        if (currentScene.parent_id === targetId) {
          currentScene.parent_id = newScene.id
        }
        // 如果当前场景的 parent_id 指向下一个节点，更新为当前节点的 id
        else if (i < scenes.length - 1 && currentScene.parent_id === scenes[i + 1].id) {
          currentScene.parent_id = scenes[i].id
        }
      }

      // 等待 treeNodes 更新后再选中节点
      setTimeout(() => {
        const newNode = treeNodes.value.find((node) => node.id === newScene.id)
        if (newNode) {
          // 使用一个简单的非响应式对象，避免触发不必要的更新
          const simpleNode = {
            id: newNode.id,
            name: newNode.name,
            type: newNode.type,
            parentId: newNode.parentId,
            properties: { ...newNode.properties }
          }
          // 使用Object.freeze确保对象不可变
          selectedNode.value = Object.freeze(simpleNode)

          // 触发一次节点变化事件
          emit('nodes-changed')
        }
      }, 200)
    }
  }

  contextMenuVisible.value = false
}

// 右键菜单 - 删除节点处理
const handleDeleteNode = () => {
  const targetId = contextMenuTargetId.value

  // 先关闭菜单
  contextMenuVisible.value = false

  if (!targetId) return

  // 找到对应的节点
  const node = treeNodes.value.find((n) => n.id === targetId)
  if (node && node.type !== 'event') {
    // 移除 && node.id !== '_BEGIN_' 限制
    // 设置选中节点
    selectedNode.value = markRaw(node)

    // 延迟删除操作
    setTimeout(() => {
      deleteNode()
    }, 100)
  }
}

// 处理右键菜单的添加事件选项
const handleAddEvent = () => {
  if (contextMenuNodeType.value !== 'chapter' && contextMenuNodeType.value !== 'begin') {
    if (
      !selectedNode.value ||
      (selectedNode.value.type !== 'chapter' && selectedNode.value.type !== 'begin')
    ) {
      console.warn('无法添加事件：未选中有效的章节节点')
      return
    }
  }

  // 确定事件的父节点ID
  let parentId: string | undefined

  // 如果是从右键菜单触发的
  if (contextMenuNodeType.value === 'chapter' || contextMenuNodeType.value === 'begin') {
    parentId = contextMenuTargetId.value

    // 设置选中节点为右键菜单目标节点
    if (parentId) {
      const targetNode = treeNodes.value.find((node) => node.id === parentId)
      if (targetNode) {
        selectedNode.value = markRaw(targetNode)
      }
    }
  } else {
    // 如果是从属性面板触发的
    parentId = selectedNode.value?.id
  }

  // 验证parentId是否有效
  if (!parentId) {
    console.error('无法添加事件：父节点ID无效')
    return
  }

  // 显示事件类型选择对话框
  showEventTypeDialog.value = true

  // 关闭上下文菜单
  contextMenuVisible.value = false

  console.log(`准备为节点 ${parentId} 添加新事件`)
}

// 监听场景数据变化
watch(
  () => editorStore.gameConfig.scenes,
  () => {
    // 延迟处理场景变化，避免与其他更新循环冲突
    setTimeout(() => {
      // 在这里不需要做任何特殊处理，只需要让组件自然更新
      // 让TreeGraph组件自己处理视图状态
    }, 0)
  },
  { deep: true }
)

// 初始化和渲染
onMounted(() => {
  console.log('StoryFlowEditor 组件已挂载')

  // 使用 nextTick 确保 DOM 完全渲染
  nextTick(() => {
    console.log('DOM 已更新')

    // 从 store 中加载场景数据并渲染为图表
    // 仅在初始加载时重置视图是合理的
    setTimeout(() => {
      treeGraph.value?.resetView()
    }, 300)
  })

  // 响应窗口大小变化
  const handleWindowResize = debounce(() => {
    console.log('窗口大小变化')
    // 仅调整图表大小，不重新渲染整个树图
    if (treeGraph.value) {
      // 使用正确的方法调整大小
      // 直接调用 TreeGraph 组件的 resetView 方法，但不重新渲染树图
      // 这个方法已经在 TreeGraph.vue 中修改为只调整大小而不重新渲染
      treeGraph.value.resetView()
    }
  }, 200) // 添加防抖动，避免频繁触发

  window.addEventListener('resize', handleWindowResize)

  // 组件卸载时移除事件监听器
  onUnmounted(() => {
    window.removeEventListener('resize', handleWindowResize)
  })

  // 点击其他地方关闭右键菜单
  document.addEventListener('click', () => {
    contextMenuVisible.value = false
  })
})

// 重置视图并刷新
const resetView = () => {
  // 重置图形显示
  if (treeGraph.value) {
    treeGraph.value.resetView()
  }

  // 通知节点变化
  emit('nodes-changed')
}

// 打开故事设置
const handleOpenStorySettings = () => {
  // 设置模态框显示状态
  showStoryConfig.value = true
  isActiveStoryConfig.value = true

  // 获取当前角色信息
  currentActor.value = editorStore.selectedActor

  // 获取技能列表
  if (editorStore.storyConfig.is_active_skill && editorStore.storyConfig.skill_ids) {
    tempSelectedSkills.value = [...editorStore.storyConfig.skill_ids]
  } else {
    tempSelectedSkills.value = []
  }

  // 重置其他显示状态
  selectedNode.value = null
}

// 处理事件类型选择
const handleEventTypeSelect = (eventType: FlowEventType) => {
  if (
    !selectedNode.value ||
    (selectedNode.value.type !== 'chapter' && selectedNode.value.type !== 'begin')
  )
    return

  const parentId = selectedNode.value.id
  const parentScene = editorStore.gameConfig.scenes.find((s) => s.id === parentId)
  if (!parentScene) return

  // 处理互动事件
  if (eventType === 'interactive') {
    // 创建互动事件处理器
    const interactiveHandler: ActionHandler = {
      type: 'ScoreLimitWithLLMChatV2',
      params: {
        level: '',
        background: '',
        heart_key: '',
        heart_value: 0,
        clean_history: false,
        limit_chat_count: 3,
        agree_sentences: [],
        streamer_tpl: ''
      }
    }

    // 确保action_handlers数组存在
    if (!parentScene.action_handlers) {
      parentScene.action_handlers = []
    }

    // 添加到场景的action_handlers中
    parentScene.action_handlers = [interactiveHandler]
  } else {
    // 其他类型的事件，维持原有逻辑
    // 创建新事件
    const newEvent: GameEvent = {
      id: nanoid(),
      type: eventType,
      plot: {}
    }

    // 添加到父场景的事件数组中
    if (!parentScene.events) {
      parentScene.events = []
    }
    parentScene.events.push(newEvent)

    // 设置selectedEventId以便让ChapterConfig组件自动展开新事件
    selectedEventId.value = newEvent.id
  }

  // 更新场景
  editorStore.updateScene(parentScene)

  // 关闭事件类型选择对话框
  showEventTypeDialog.value = false

  // 强制更新selectedNode，触发PropertyPanel和ChapterConfig的重新渲染
  // 创建一个新的对象引用，确保触发响应式更新
  const updatedNode = { ...selectedNode.value }
  selectedNode.value = null // 先设置为null

  // 使用setTimeout确保在下一个事件循环中重新设置节点
  setTimeout(() => {
    selectedNode.value = updatedNode // 然后重新设置为更新后的节点

    // 触发节点变化事件，通知树图重新渲染
    emit('nodes-changed')
  }, 50)
}

// 保存事件提示状态
const saveNotification = ref({
  visible: false,
  message: ''
})

// 处理显式保存事件
const handleSaveEvent = (eventData: Partial<GameEvent>) => {
  if (!selectedNode.value || selectedNode.value.type !== 'event') {
    console.log('无法保存事件：未选中事件节点或节点类型不是事件')
    return
  }

  try {
    // 保存原始的节点类型和事件类型
    const originalType = selectedNode.value.type
    const eventType = selectedNode.value.eventType || eventData.type

    // 如果是互动事件，需要特殊处理
    if (eventType === 'interactive' && selectedNode.value.id.endsWith('_interactive')) {
      const parentId = selectedNode.value.parentId
      if (!parentId) {
        console.error('无法保存互动事件：未找到父场景ID')
        return
      }

      // 查找父场景
      const parentScene = editorStore.gameConfig.scenes.find((s) => s.id === parentId)
      if (!parentScene) {
        console.error('无法保存互动事件：未找到父场景')
        return
      }

      // 确保父场景有action_handlers数组
      if (!parentScene.action_handlers) {
        parentScene.action_handlers = []
      }

      // 创建或更新action_handler
      const actionHandler: ActionHandler = {
        type: 'ScoreLimitWithLLMChatV2',
        params: {
          level: (eventData.plot as any)?.level || '',
          background: (eventData.plot as any)?.background || '',
          heart_key: (eventData.plot as any)?.heart_key || '',
          heart_value: (eventData.plot as any)?.heart_value || 0,
          clean_history: (eventData.plot as any)?.clean_history || false,
          limit_chat_count: (eventData.plot as any)?.limit_chat_count || 3,
          agree_sentences: (eventData.plot as any)?.agree_sentences || [],
          streamer_tpl: (eventData.plot as any)?.streamer_tpl || ''
        }
      }

      // 更新父场景的action_handlers
      parentScene.action_handlers = [actionHandler]

      // 保存父场景
      editorStore.updateScene(parentScene)

      // 显示保存成功的提示
      saveNotification.value = {
        visible: true,
        message: '互动事件已保存'
      }

      // 3秒后自动隐藏提示
      setTimeout(() => {
        saveNotification.value.visible = false
      }, 3000)

      // 触发节点变化事件
      emit('nodes-changed')

      return
    }

    // 确保事件有正确的ID
    const eventId = eventData.id || selectedNode.value.id

    // 创建一个非响应式的节点用于更新
    const updatedNode = {
      ...selectedNode.value,
      // 确保type不被覆盖，eventType正确设置
      type: originalType,
      eventType: eventType,
      // 将事件数据保存到properties
      properties: {
        ...eventData,
        id: eventId // 确保ID一致
      },
      // 同时保存plot属性，便于updateEventNode处理
      plot: eventData.plot || {}
    }
    console.log('更新的节点数据:', JSON.stringify(updatedNode))

    // 调用更新节点方法
    updateEventNode(updatedNode)

    // 显示保存成功的提示
    saveNotification.value = {
      visible: true,
      message: '事件已保存'
    }

    // 3秒后自动隐藏提示
    setTimeout(() => {
      saveNotification.value.visible = false
    }, 3000)

    // 触发节点变化事件，通知树图重新渲染
    emit('nodes-changed')

    console.log('事件保存成功')
  } catch (error) {
    console.error('保存事件时发生错误:', error)

    // 显示保存失败的提示
    saveNotification.value = {
      visible: true,
      message: '保存失败，请查看控制台'
    }

    // 3秒后自动隐藏提示
    setTimeout(() => {
      saveNotification.value.visible = false
    }, 3000)
  }
}

// 处理故事配置保存
const handleStorySaved = () => {
  // 在这里处理故事配置保存后的逻辑
  console.log('Story configuration saved')
  handleStoryConfigClose()
}

// 处理角色选择确认
const handleCharacterConfirm = (actor: Actor & any) => {
  editorStore.setSelectedActor(actor)
  showCharacterSelectModal.value = false

  // 更新本地currentActor
  currentActor.value = actor

  // 选择角色后自动打开故事配置面板
  showStoryConfig.value = true
  isActiveStoryConfig.value = true

  // 保存角色的自定义属性
  if (actor.custom_name || actor.custom_gender || actor.tags) {
    // 在这里处理自定义属性的保存
    console.log('保存角色自定义属性:', {
      name: actor.custom_name,
      gender: actor.custom_gender,
      tags: actor.tags
    })
  }
}

// 打开技能选择模态框
function handleOpenSkillSelector(currentSkills: any[]) {
  console.log('打开技能选择模态框', currentSkills)
  tempSelectedSkills.value = currentSkills || []
  showSkillSelectModal.value = true
}

// 处理技能选择确认
function handleSkillConfirm(skills: any[]) {
  console.log('选中的技能:', skills)
  // 关闭模态框
  showSkillSelectModal.value = false

  // 通知StoryConfigPanel更新技能
  if (isActiveStoryConfig.value) {
    // 使用技能数据做后续处理
    console.log('更新技能数据:', skills)
    // 如果editorStore没有setSelectedSkills方法，则可以使用其他方式
    // 例如直接传递给StoryConfigPanel
    tempSelectedSkills.value = skills
  }
}

// 故事配置相关状态
const storyName = ref('')
const storyDesc = ref('')
const currentStoryId = ref('')
const portraitUrl = ref('')
const currentActor = ref<Actor | null>(null)

// 在适当的时机更新currentActor
watch(
  () => editorStore.selectedActor,
  (newActor) => {
    if (newActor) {
      currentActor.value = newActor
    }
  },
  { immediate: true }
)

// 处理打开角色选择器
const handleOpenCharacterSelector = () => {
  console.log('打开角色选择器')
  // 检查角色列表是否已加载
  if (editorStore.actors.length === 0) {
    editorStore.fetchActorList().then(() => {
      showCharacterSelectModal.value = true
    })
  } else {
    showCharacterSelectModal.value = true
  }
}

// 处理大纲节点右键菜单 - 添加新场景组
const handleContextAddSceneGroup = () => {
  // 关闭上下文菜单
  contextMenuVisible.value = false

  // 获取所有场景组
  const allScenes = editorStore.gameConfig.scenes as ExtendedScene[]

  // 获取已有的场景组名称
  const existingGroups = Array.from(
    new Set(allScenes.map((scene) => scene.scene_group || 'default'))
  )

  // 获取当前已有的Begin-X格式的场景组，并计算下一个序号
  const beginGroups = existingGroups
    .filter((name) => /^Begin-\d+$/.test(name))
    .map((name) => parseInt(name.replace('Begin-', ''), 10))
    .sort((a, b) => a - b)

  let nextBeginNumber = 1
  if (beginGroups.length > 0) {
    nextBeginNumber = beginGroups[beginGroups.length - 1] + 1
  }

  // 创建新的场景组名称 (Begin-X)
  const newGroupName = `Begin-${nextBeginNumber}`
  console.log(`创建新场景组: ${newGroupName}`)

  // 寻找上一个场景组的最后一个章节
  let parentId = '~' // 默认为根节点

  if (existingGroups.length > 0) {
    // 找到按字母顺序排序的所有场景组名称
    const sortedGroups = [...existingGroups].sort()
    // 确定新场景组在排序后的位置
    let insertIndex = sortedGroups.findIndex((name) => newGroupName.localeCompare(name) < 0)
    if (insertIndex === -1) insertIndex = sortedGroups.length

    // 确定前一个场景组名称
    const prevGroupIndex = insertIndex - 1
    if (prevGroupIndex >= 0) {
      const prevGroupName = sortedGroups[prevGroupIndex]

      // 找到前一个场景组的所有章节
      const prevGroupScenes = allScenes.filter(
        (scene) => (scene.scene_group || 'default') === prevGroupName
      )

      // 如果前一个场景组有章节，取最后一个作为父节点
      if (prevGroupScenes.length > 0) {
        parentId = prevGroupScenes[prevGroupScenes.length - 1].id
      }
    }
  }

  // 直接创建一个章节节点，并设置scene_group属性和正确的parent_id
  const chapterId = nanoid()
  const chapterNode: ExtendedScene = {
    id: chapterId,
    name: `level-1`, // 第一个章节总是level-1
    scene_group: newGroupName, // 设置场景组属性
    events: [],
    parent_id: parentId // 设置为上一个场景组最后一个章节的ID，或默认为根节点
  }

  console.log(`创建新场景组 ${newGroupName}，第一个章节的名称: level-1, parent_id: ${parentId}`)

  // 将章节添加到编辑器存储中
  editorStore.gameConfig.scenes.push(chapterNode as any)

  // 刷新视图并聚焦到新节点
  refreshAndFocusNode(chapterId)
}

// 处理场景组节点右键菜单 - 添加新章节
const handleContextAddChapterToGroup = () => {
  if (!contextMenuNode.value) {
    console.error('无法添加章节：上下文节点为空')
    return
  }

  // 调试：输出完整的contextMenuNode信息
  console.log('上下文节点信息:', JSON.stringify(contextMenuNode.value))

  // 获取场景组名称 - 修复方式
  let sceneGroup = 'default'

  // 方法1: 从节点ID中提取
  if (contextMenuNode.value.id && contextMenuNode.value.id.startsWith('group_')) {
    sceneGroup = contextMenuNode.value.id.replace('group_', '')
    console.log(`从节点ID中提取场景组名: ${sceneGroup}`)
  }
  // 方法2: 从属性中获取
  else if (contextMenuNode.value.scene_group) {
    sceneGroup = contextMenuNode.value.scene_group
    console.log(`从节点属性中获取场景组名: ${sceneGroup}`)
  }
  // 方法3: 从properties中获取
  else if (contextMenuNode.value.properties && contextMenuNode.value.properties.scene_group) {
    sceneGroup = contextMenuNode.value.properties.scene_group
    console.log(`从节点properties属性中获取场景组名: ${sceneGroup}`)
  }

  console.log(`最终确定在场景组 "${sceneGroup}" 中添加新章节`)

  // 使用通用函数创建新章节，并在创建后关闭上下文菜单
  createNewChapter(sceneGroup, () => {
    closeContextMenu()
  })
}

// 添加一个辅助函数用于刷新视图并聚焦到节点
const refreshAndFocusNode = (nodeId: string) => {
  nextTick(() => {
    if (treeGraph.value) {
      // 重绘树图
      treeGraph.value.resetView()

      // 选中新创建的节点
      setTimeout(() => {
        if (treeGraph.value) {
          treeGraph.value.focusNode(nodeId)
        }
      }, 300)
    }
  })
}

// 更新场景引用关系的辅助函数
const updateSceneReferences = (
  action: 'insert' | 'delete',
  affectedNode: ExtendedScene,
  insertPosition?: number
) => {
  const scenes = editorStore.gameConfig.scenes as ExtendedScene[]

  if (action === 'insert') {
    // 插入操作 - 更新后续章节的parent_id
    if (insertPosition !== undefined) {
      // 获取插入位置之前的节点（如果存在）
      const previousNode = insertPosition > 0 ? scenes[insertPosition - 1] : null
      // 获取插入位置之后的节点（如果存在）
      const nextNode = insertPosition < scenes.length - 1 ? scenes[insertPosition + 1] : null

      // 如果有后续节点且其父节点是插入位置之前的节点
      if (nextNode && previousNode && nextNode.parent_id === previousNode.id) {
        // 更新后续节点的父节点为新插入的节点
        console.log(
          `更新节点 ${nextNode.id} 的parent_id，从 ${nextNode.parent_id} 改为 ${affectedNode.id}`
        )
        nextNode.parent_id = affectedNode.id
      }
    }
  } else if (action === 'delete') {
    // 删除操作 - 更新受影响节点的子节点的parent_id
    const childNodes = scenes.filter((scene) => scene.parent_id === affectedNode.id)
    const parentId = affectedNode.parent_id || '~'

    // 将所有子节点的parent_id更新为被删除节点的parent_id
    childNodes.forEach((child) => {
      console.log(`更新节点 ${child.id} 的parent_id，从 ${child.parent_id} 改为 ${parentId}`)
      child.parent_id = parentId
    })
  }
}

// 处理事件选择
const handleEventSelect = (event: GameEvent) => {
  console.log('选择事件进行编辑:', event.id, event.type)

  // 找到该事件对应的节点
  const eventNode = treeNodes.value.find((node) => node.type === 'event' && node.id === event.id)

  if (eventNode) {
    // 使用markRaw防止Vue对新节点进行深度响应式代理
    selectedNode.value = markRaw(eventNode)

    // 发射节点选中事件
    emit('node-selected', eventNode.id)
  } else {
    console.warn('未找到对应的事件节点:', event.id)
  }
}

// 处理设置跳转场景
const handleSetJumpScene = () => {
  if (!contextMenuNode.value) return

  // 设置当前编辑的节点ID
  currentEditingNodeId.value = contextMenuNode.value.id

  // 获取当前节点的场景对象
  const scene = editorStore.gameConfig.scenes.find((s) => s.id === contextMenuNode.value?.id)

  // 设置初始选中的场景（如果已有跳转场景）
  initialSelectedSceneId.value = scene?.next_scene_id || null

  // 显示场景选择对话框
  sceneSelectDialogTitle.value = `为「${scene?.name || '章节'}」设置跳转场景`
  showSceneSelectDialog.value = true

  // 关闭右键菜单
  contextMenuVisible.value = false
}

// 处理场景选择确认
const handleSceneSelectConfirm = (sceneId: string | null) => {
  console.log('处理场景选择确认:', sceneId)

  // 关闭对话框
  showSceneSelectDialog.value = false

  // 获取当前编辑的节点对应的场景
  const scene = editorStore.gameConfig.scenes.find((s) => s.id === currentEditingNodeId.value)
  console.log('当前编辑的节点ID:', currentEditingNodeId.value)
  console.log('找到的场景:', scene)
  if (!scene) return

  // 创建场景的副本
  const updatedScene = { ...scene }

  // 设置或清除next_scene_id
  if (sceneId) {
    updatedScene.next_scene_id = sceneId
    console.log('设置 next_scene_id:', sceneId)
  } else {
    delete updatedScene.next_scene_id
    console.log('清除 next_scene_id')
  }

  // 更新场景
  editorStore.updateScene(updatedScene)
  console.log('更新后的场景:', updatedScene)

  // 刷新视图
  emit('nodes-changed')

  // 如果有treeGraph实例，强制更新节点
  if (treeGraph.value) {
    setTimeout(() => {
      if (treeGraph.value && typeof treeGraph.value.forceNodesUpdate === 'function') {
        console.log('强制更新节点')
        treeGraph.value.forceNodesUpdate([currentEditingNodeId.value])
      }
    }, 100)
  }
}

// 处理跳转节点点击
const handleJumpNodeClick = (parentNodeId: string) => {
  console.log('处理跳转节点点击:', parentNodeId)

  // 设置当前编辑的节点ID
  currentEditingNodeId.value = parentNodeId

  // 获取当前节点的场景对象
  const scene = editorStore.gameConfig.scenes.find((s) => s.id === parentNodeId)
  console.log('找到的场景:', scene)

  // 设置初始选中的场景
  initialSelectedSceneId.value = scene?.next_scene_id || null
  console.log('初始选中的场景:', initialSelectedSceneId.value)

  // 显示场景选择对话框
  sceneSelectDialogTitle.value = `为「${scene?.name || '章节'}」设置跳转场景`
  showSceneSelectDialog.value = true
  console.log('场景选择对话框显示状态:', showSceneSelectDialog.value)

  // 强制更新视图
  setTimeout(() => {
    console.log('强制更新对话框状态')
    showSceneSelectDialog.value = true
  }, 100)
}

// 最后在文件末尾添加defineExpose
// 暴露必要的方法供外部调用
defineExpose({
  resetView,
  handleOpenStorySettings,
  handleCharacterConfirm,
  handleSkillConfirm,
  handleEventTypeSelect,
  handleAddEvent,
  handleAddChapter,
  handleSetJumpScene,
  handleSceneSelectConfirm,
  handleJumpNodeClick
})
</script>

<style lang="less" scoped>
.story-flow-editor {
  display: flex;
  height: 100%;
  overflow: hidden;
  position: relative;

  .graph-container {
    flex: 1;
    overflow: hidden;
    position: relative;
  }

  .property-panel {
    height: 100%;
    background: rgba(32, 0, 56, 0.9);
    backdrop-filter: blur(10px);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;
    overflow: hidden;

    &.panel-visible {
      transform: translateX(0);
      box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
    }

    .panel-resizer {
      position: absolute;
      left: 0;
      top: 0;
      width: 5px;
      height: 100%;
      cursor: col-resize;
      background-color: transparent;
      z-index: 11;

      &:hover,
      &:active {
        background-color: rgba(202, 147, 242, 0.3);
      }
    }

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: #ca93f2;
      }

      .close-button {
        width: 24px;
        height: 24px;
        background: transparent;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        padding: 0;
        color: rgba(255, 255, 255, 0.6);
        border-radius: 4px;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.9);
        }

        .icon-close {
          width: 18px;
          height: 18px;
        }
      }
    }

    .panel-content {
      flex: 1;
      overflow: hidden;
      padding: 16px;
      display: flex;
      flex-direction: column;
    }

    .panel-actions {
      padding: 16px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);

      .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        button {
          padding: 8px 12px;
          background: rgba(255, 255, 255, 0.08);
          border: 1px solid rgba(255, 255, 255, 0.15);
          border-radius: 4px;
          color: rgba(255, 255, 255, 0.9);
          font-size: 14px;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: rgba(255, 255, 255, 0.12);
            transform: translateY(-1px);
          }

          &.add-node {
            background: rgba(52, 152, 219, 0.15);
            border-color: rgba(52, 152, 219, 0.3);
            color: #3498db;

            &:hover {
              background: rgba(52, 152, 219, 0.25);
            }
          }

          &.add-event {
            background: rgba(46, 204, 113, 0.15);
            border-color: rgba(46, 204, 113, 0.3);
            color: #2ecc71;

            &:hover {
              background: rgba(46, 204, 113, 0.25);
            }
          }

          &.delete-node {
            background: rgba(231, 76, 60, 0.15);
            border-color: rgba(231, 76, 60, 0.3);
            color: #e74c3c;

            &:hover {
              background: rgba(231, 76, 60, 0.25);
            }
          }

          &.save-event {
            background: rgba(46, 204, 113, 0.15);
            border-color: rgba(46, 204, 113, 0.3);
            color: #2ecc71;

            &:hover {
              background: rgba(46, 204, 113, 0.25);
            }
          }
        }
      }
    }
  }

  // 保存成功提示
  .save-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(46, 204, 113, 0.9);
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 2000;
    transition: all 0.3s ease;
    transform: translateY(0);
    opacity: 1;

    &.hide {
      transform: translateY(-20px);
      opacity: 0;
    }
  }

  .context-menu {
    position: fixed;
    background: rgba(26, 0, 48, 0.95);
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(202, 147, 242, 0.2);
    backdrop-filter: blur(10px);
    z-index: 1000;
    min-width: 160px;
    overflow: hidden;
    animation: contextMenuFadeIn 0.15s ease-out;

    .menu-list {
      margin: 0;
      padding: 4px 0;
      list-style: none;

      li {
        padding: 10px 16px;
        color: rgba(255, 255, 255, 0.9);
        cursor: pointer;
        font-size: 14px;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        position: relative;

        &:hover {
          background-color: rgba(202, 147, 242, 0.15);
          color: #ca93f2;
          padding-left: 20px;
        }

        &:active {
          background-color: rgba(202, 147, 242, 0.25);
        }

        &:not(:last-child) {
          border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }
      }
    }
  }

  @keyframes contextMenuFadeIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .event-type-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(3px);

    .event-type-dialog {
      background: rgba(32, 0, 56, 0.95);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 10px;
      width: 80%;
      max-width: 800px;
      max-height: 85vh;
      overflow-y: auto;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

      .dialog-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 500;
          color: #ca93f2;
        }

        .close-button {
          background: none;
          border: none;
          color: rgba(255, 255, 255, 0.6);
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
          }
        }
      }

      .dialog-content {
        padding: 20px;

        .event-type-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
          gap: 16px;

          .event-type-card {
            display: flex;
            align-items: center;
            padding: 14px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
              background: rgba(255, 255, 255, 0.1);
              transform: translateY(-2px);
              box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            }

            .type-icon {
              width: 40px;
              height: 40px;
              border-radius: 8px;
              margin-right: 14px;
              display: flex;
              justify-content: center;
              align-items: center;
              flex-shrink: 0;

              svg {
                width: 24px;
                height: 24px;
                color: white;
              }
            }

            .type-details {
              flex: 1;
              text-align: left;

              .type-name {
                font-size: 15px;
                font-weight: 500;
                color: white;
                margin-bottom: 4px;
              }

              .type-desc {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.6);
              }
            }
          }
        }
      }
    }
  }

  // 故事配置面板
  .story-config-container {
    position: fixed;
    top: 0;
    right: 0;
    width: 400px;
    height: 100%;
    background: rgba(32, 0, 56, 0.9);
    backdrop-filter: blur(10px);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 10;
    overflow: hidden;

    &.panel-visible {
      transform: translateX(0);
      box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
    }

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: #ca93f2;
      }

      .close-button {
        width: 24px;
        height: 24px;
        background: transparent;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        padding: 0;
        color: rgba(255, 255, 255, 0.6);
        border-radius: 4px;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.9);
        }

        .icon-close {
          width: 18px;
          height: 18px;
        }
      }
    }

    .panel-content {
      flex: 1;
      overflow: hidden;
      padding: 16px;
      display: flex;
      flex-direction: column;
    }

    .panel-actions {
      padding: 16px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);

      .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        button {
          padding: 8px 12px;
          background: rgba(255, 255, 255, 0.08);
          border: 1px solid rgba(255, 255, 255, 0.15);
          border-radius: 4px;
          color: rgba(255, 255, 255, 0.9);
          font-size: 14px;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: rgba(255, 255, 255, 0.12);
            transform: translateY(-1px);
          }

          &.add-node {
            background: rgba(52, 152, 219, 0.15);
            border-color: rgba(52, 152, 219, 0.3);
            color: #3498db;

            &:hover {
              background: rgba(52, 152, 219, 0.25);
            }
          }

          &.add-event {
            background: rgba(46, 204, 113, 0.15);
            border-color: rgba(46, 204, 113, 0.3);
            color: #2ecc71;

            &:hover {
              background: rgba(46, 204, 113, 0.25);
            }
          }

          &.delete-node {
            background: rgba(231, 76, 60, 0.15);
            border-color: rgba(231, 76, 60, 0.3);
            color: #e74c3c;

            &:hover {
              background: rgba(231, 76, 60, 0.25);
            }
          }

          &.save-event {
            background: rgba(46, 204, 113, 0.15);
            border-color: rgba(46, 204, 113, 0.3);
            color: #2ecc71;

            &:hover {
              background: rgba(46, 204, 113, 0.25);
            }
          }
        }
      }
    }
  }
}
</style>
