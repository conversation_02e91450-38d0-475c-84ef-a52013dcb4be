<template>
  <div class="operation-guide" v-if="modelValue">
    <div class="guide-header">
      <h3>快速操作指南</h3>
      <button class="close-guide" @click="$emit('update:modelValue', false)">
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path
            fill="currentColor"
            d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
          />
        </svg>
      </button>
    </div>
    <div class="guide-content">
      <ul>
        <li>
          <span class="key-action">导入/导出</span>: 使用顶部工具栏的导入/导出按钮管理YAML配置
        </li>
        <li><span class="key-action">右键点击节点</span>: 根据节点级别显示对应的菜单选项</li>
        <li><span class="key-action">选中节点</span>: 点击节点显示右侧编辑面板</li>
        <li><span class="key-action">添加子章节</span>: 右键点击开始节点选择"添加子章节"</li>
        <li><span class="key-action">添加事件</span>: 右键点击章节节点选择"添加事件"</li>
        <li><span class="key-action">YAML预览</span>: 点击顶部的"YAML预览"按钮查看当前配置</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  modelValue: boolean
}>()

defineEmits<{
  'update:modelValue': [value: boolean]
}>()
</script>

<style lang="less" scoped>
.operation-guide {
  position: absolute;
  bottom: 20px;
  left: 20px;
  width: 350px;
  background: rgba(42, 0, 73, 0.8);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(202, 147, 242, 0.2);
  z-index: 50;

  .guide-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    h3 {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: #ca93f2;
    }

    .close-guide {
      width: 24px;
      height: 24px;
      background: transparent;
      border: none;
      color: rgba(255, 255, 255, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 4px;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }

  .guide-content {
    padding: 12px 16px;

    ul {
      margin: 0;
      padding: 0 0 0 18px;

      li {
        margin-bottom: 8px;
        font-size: 13px;
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }

        .key-action {
          color: #ca93f2;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
