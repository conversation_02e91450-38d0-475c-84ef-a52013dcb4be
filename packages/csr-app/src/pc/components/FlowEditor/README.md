# 流程编辑器组件库

## 概述

这个组件库提供了一套完整的流程编辑器组件，用于创建和编辑交互式故事流程。组件库采用了模块化设计，使各个组件可以独立使用，也可以组合使用。

## 目录结构

```
FlowEditor/
├── EventConfigs/           # 事件配置组件
│   ├── index.ts            # 导出所有事件配置组件
│   ├── MessageEventConfig.vue
│   ├── WaitEventConfig.vue
│   └── ...                 # 其他事件配置组件
├── utils/                  # 工具函数
│   └── eventUtils.ts       # 事件处理工具
├── CustomNodeComponent.vue # 自定义节点组件
├── EventNodeComponent.vue  # 事件节点组件
├── EventConfigPanel.vue    # 事件配置面板
├── EventManager.vue        # 事件管理器
├── NodeConfigPanel.vue     # 节点配置面板
├── OperationGuide.vue      # 操作指南组件
├── PropertyPanel.vue       # 属性面板
├── StoryFlowEditor.vue     # 主编辑器组件
├── TreeGraph.vue           # 树形图组件
└── index.ts                # 导出所有组件
```

## 使用方法

### 导入主编辑器组件

```javascript
import { StoryFlowEditor } from '@/pc/components/FlowEditor'

// 在组件中使用
<StoryFlowEditor
  :initial-data="flowData"
  @node-selected="handleNodeSelected"
  @node-added="handleNodeAdded"
/>
```

### 导入单个组件

```javascript
import { TreeGraph, PropertyPanel } from '@/pc/components/FlowEditor'

// 在组件中使用
<TreeGraph
  :nodes="treeNodes"
  @node-selected="handleNodeSelected"
/>

<PropertyPanel
  v-if="selectedNode"
  :node="selectedNode"
  @update="handleUpdate"
/>
```

### 使用事件配置组件

```javascript
import { getEventConfigComponent } from '@/pc/components/FlowEditor'

// 动态加载对应类型的事件配置组件
const EventConfigComponent = getEventConfigComponent(eventType)
```

### 使用工具函数

```javascript
import { getEventTypeName, getEventTypeColor, createNewEvent } from '@/pc/components/FlowEditor'

// 获取事件类型名称
const typeName = getEventTypeName('message') // 返回 "对话事件"

// 创建新事件
const newEvent = createNewEvent('message', 'scene_001')
```

## 主要组件说明

### StoryFlowEditor

主要的流程编辑器组件，整合了树形图和属性面板。

### TreeGraph

树形图组件，用于可视化显示流程节点之间的关系。

### PropertyPanel

属性面板组件，用于编辑选中节点的属性。

### EventManager

事件管理组件，用于管理节点中的事件列表。

### EventConfigPanel

事件配置面板，用于配置特定类型事件的参数。

## 类型依赖

组件库中的类型定义依赖于 `@/pc/views/flow-editor/types` 目录下的类型文件。
