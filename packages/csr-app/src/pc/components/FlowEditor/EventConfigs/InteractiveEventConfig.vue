<template>
  <div class="interactive-event-config">
    <div class="config-section">
      <div class="section-title">互动事件配置</div>

      <div class="form-group">
        <label>心值关键词 (heart_key)</label>
        <input
          type="text"
          v-model="params.heart_key"
          class="custom-input"
          placeholder="请输入心值关键词"
        />
      </div>

      <div class="form-group">
        <label>心值奖励 (heart_value)</label>
        <input
          type="number"
          v-model.number="params.heart_value"
          class="custom-input"
          placeholder="请输入心值奖励"
          min="0"
        />
      </div>

      <div class="form-group">
        <label>对话次数限制 (limit_chat_count)</label>
        <input
          type="number"
          v-model.number="params.limit_chat_count"
          class="custom-input"
          placeholder="请输入对话次数限制"
          min="1"
        />
      </div>

      <div class="form-group">
        <label>Actor同意词 (agree_sentences)</label>
        <textarea
          v-model="agreeSentences"
          class="custom-textarea"
          rows="3"
          placeholder="请输入Actor同意词，每行一个"
          @input="updateAgreeSentences"
        ></textarea>
      </div>

      <div class="form-group">
        <label>提示词模板 (streamer_tpl)</label>
        <PromptTemplateEditor
          v-model="params.streamer_tpl"
          @template-selected="handleTemplateSelected"
        />
      </div>

      <div class="form-group">
        <div class="checkbox-control">
          <input type="checkbox" id="cleanHistory" v-model="params.clean_history" />
          <label for="cleanHistory">清除历史记录 (clean_history)</label>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue'
import PromptTemplateEditor from '@/components/PromptTemplateEditor.vue'
import type { PromptTemplate } from '@/api/prompt'

interface Props {
  params: {
    heart_key?: string
    heart_value?: number
    limit_chat_count?: number
    agree_sentences?: string[]
    streamer_tpl?: string
    clean_history?: boolean
    type?: string
  }
}

// 属性定义
const props = withDefaults(defineProps<Props>(), {
  params: () => ({
    heart_key: '',
    heart_value: 0,
    limit_chat_count: 3,
    agree_sentences: [],
    streamer_tpl: '',
    clean_history: false,
    type: 'chat'
  })
})

// 事件定义
const emit = defineEmits<{
  update: [updatedParams: any]
}>()

// 本地状态
const params = ref({ ...props.params })

// 将数组转换为多行文本
const agreeSentences = computed({
  get: () => (params.value.agree_sentences || []).join('\n'),
  set: (value: string) => {
    params.value.agree_sentences = value.split('\n').filter((line) => line.trim() !== '')
    emit('update', params.value)
  }
})

// 更新同意词句
const updateAgreeSentences = (event: Event) => {
  const value = (event.target as HTMLTextAreaElement).value
  params.value.agree_sentences = value.split('\n').filter((line) => line.trim() !== '')
  emit('update', params.value)
}

// 监听本地状态变化并向父组件发送更新
watch(
  params,
  (newParams) => {
    emit('update', newParams)
  },
  { deep: true }
)

// 监听props变化来更新本地状态
watch(
  () => props.params,
  (newParams) => {
    // 避免循环更新
    if (JSON.stringify(newParams) !== JSON.stringify(params.value)) {
      params.value = { ...newParams }
    }
  },
  { deep: true }
)

// 处理模板选择
const handleTemplateSelected = (template: PromptTemplate | null) => {
  if (template) {
    console.log('选择了模板:', template.name)
  }
}

// 在组件挂载时获取模板
onMounted(() => {
  // 确保所有需要的字段都存在
  if (params.value.heart_key === undefined) {
    params.value.heart_key = ''
  }
  if (params.value.heart_value === undefined) {
    params.value.heart_value = 0
  }
  if (params.value.limit_chat_count === undefined) {
    params.value.limit_chat_count = 3
  }
  if (!params.value.agree_sentences) {
    params.value.agree_sentences = []
  }
  if (params.value.streamer_tpl === undefined) {
    params.value.streamer_tpl = ''
  }
  if (params.value.clean_history === undefined) {
    params.value.clean_history = false
  }
  if (params.value.type === undefined) {
    params.value.type = 'chat'
  }
})
</script>

<style lang="less" scoped>
.interactive-event-config {
  .config-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .form-group {
      margin-bottom: 16px;

      label {
        display: block;
        font-size: 13px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 6px;

        &.required::after {
          content: '*';
          color: #e74c3c;
          margin-left: 4px;
        }
      }

      .template-selector {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .select-wrapper {
          position: relative;

          &::after {
            content: '';
            position: absolute;
            top: 50%;
            right: 12px;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid rgba(255, 255, 255, 0.7);
            pointer-events: none;
          }
        }

        .template-select {
          width: 100%;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          color: #fff;
          padding: 8px 10px;
          transition: border-color 0.2s;
          font-size: 13px;
          appearance: none;
          cursor: pointer;

          &:focus {
            border-color: #ca93f2;
            outline: none;
          }

          option {
            background-color: #2c2c2c;
          }
        }
      }

      .custom-input {
        width: 100%;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: #fff;
        padding: 8px 10px;
        transition: border-color 0.2s;
        font-size: 13px;

        &:focus {
          border-color: #ca93f2;
          outline: none;
        }
      }

      .custom-textarea {
        width: 100%;
        min-height: 80px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: #fff;
        padding: 8px 10px;
        transition: border-color 0.2s;
        font-size: 13px;
        resize: vertical;

        &:focus {
          border-color: #ca93f2;
          outline: none;
        }
      }

      .checkbox-control {
        display: flex;
        align-items: center;
        gap: 8px;

        input[type='checkbox'] {
          -webkit-appearance: none;
          appearance: none;
          width: 18px;
          height: 18px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 4px;
          background: rgba(255, 255, 255, 0.05);
          display: inline-block;
          position: relative;
          margin: 0;

          &:checked {
            background: #ca93f2;
            border-color: #ca93f2;

            &::after {
              content: '';
              position: absolute;
              width: 4px;
              height: 8px;
              border: solid white;
              border-width: 0 2px 2px 0;
              top: 3px;
              left: 6px;
              transform: rotate(45deg);
            }
          }
        }

        label {
          display: inline;
          margin: 0;
        }
      }

      .field-hint {
        margin-top: 4px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.4);
      }
    }
  }
}
</style>
