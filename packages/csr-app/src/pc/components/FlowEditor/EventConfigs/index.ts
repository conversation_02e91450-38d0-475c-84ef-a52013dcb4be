// 导出所有事件配置组件
import MessageEventConfig from './MessageEventConfig.vue'
import WaitEventConfig from './WaitEventConfig.vue'
import ShowTipsEventConfig from './ShowTipsEventConfig.vue'
import AudioEventConfig from './AudioEventConfig.vue'
import ShowImageEventConfig from './ShowImageEventConfig.vue'
import PlayVideoEventConfig from './PlayVideoEventConfig.vue'
import ChatOptionsEventConfig from './ChatOptionsEventConfig.vue'
import SceneTransitionEventConfig from './SceneTransitionEventConfig.vue'
import HeartValueEventConfig from './HeartValueEventConfig.vue'
import ShowEndingEventConfig from './ShowEndingEventConfig.vue'
import AnimatedImagesEventConfig from './AnimatedImagesEventConfig.vue'
import InteractiveEventConfig from './InteractiveEventConfig.vue'
import VoiceConfigEventConfig from './VoiceConfigEventConfig.vue'
import ShowOverlayEventConfig from './ShowOverlayEventConfig.vue'

// 事件类型到组件的映射
export const eventConfigMap = {
  message: MessageEventConfig,
  wait: WaitEventConfig,
  show_tips: ShowTipsEventConfig,
  play_audio: AudioEventConfig,
  show_image: ShowImageEventConfig,
  play_video: PlayVideoEventConfig,
  show_chat_options: ChatOptionsEventConfig,
  scene_transition: SceneTransitionEventConfig,
  heart_value: HeartValueEventConfig,
  show_ending: ShowEndingEventConfig,
  animated_images: AnimatedImagesEventConfig,
  interactive: InteractiveEventConfig,
  voice_config: VoiceConfigEventConfig,
  show_overlay: ShowOverlayEventConfig
}

// 为特定事件类型获取对应的配置组件
export const getEventConfigComponent = (type: string) => {
  return eventConfigMap[type] || null
}

export {
  MessageEventConfig,
  WaitEventConfig,
  ShowTipsEventConfig,
  AudioEventConfig,
  ShowImageEventConfig,
  PlayVideoEventConfig,
  ChatOptionsEventConfig,
  SceneTransitionEventConfig,
  HeartValueEventConfig,
  ShowEndingEventConfig,
  AnimatedImagesEventConfig,
  InteractiveEventConfig,
  VoiceConfigEventConfig,
  ShowOverlayEventConfig
}
