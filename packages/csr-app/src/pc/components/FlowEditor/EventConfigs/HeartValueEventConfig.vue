<template>
  <div class="heart-value-config">
    <div class="config-section">
      <div class="section-title">好感度配置</div>

      <div class="form-group">
        <label class="required">好感度值 (heart_options.heart_value)</label>
        <input
          type="number"
          v-model.number="heartValue"
          class="custom-input"
          placeholder="请输入好感度值"
          @input="updateHeartOptions('heart_value')"
        />
      </div>

      <div class="form-group">
        <div class="checkbox-control">
          <input
            type="checkbox"
            id="isAllowMessage"
            :checked="isAllowMessage"
            @change="updateHeartOptions('is_allow_message', $event)"
          />
          <label for="isAllowMessage">允许发送消息 (heart_options.is_allow_message)</label>
        </div>
        <div class="field-hint">设置是否允许用户发送消息</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'

interface HeartOptions {
  heart_value: number
  is_allow_message: boolean
}

interface Props {
  params: {
    heart_options?: HeartOptions
  }
}

// 属性定义
const props = withDefaults(defineProps<Props>(), {
  params: () => ({
    heart_options: {
      heart_value: 0,
      is_allow_message: false
    }
  })
})

// 事件定义
const emit = defineEmits<{
  update: [updatedParams: any]
}>()

// 本地状态
const params = ref({ ...props.params })
const heartValue = ref(props.params.heart_options?.heart_value || 0)
const isAllowMessage = ref(props.params.heart_options?.is_allow_message || false)

// 更新心值选项
const updateHeartOptions = (key: string, event?: Event) => {
  if (!params.value.heart_options) {
    params.value.heart_options = {
      is_allow_message: false,
      heart_value: 0
    }
  }

  if (key === 'heart_value') {
    params.value.heart_options.heart_value = heartValue.value
  } else if (key === 'is_allow_message' && event) {
    const value = (event.target as HTMLInputElement).checked
    params.value.heart_options.is_allow_message = value
    isAllowMessage.value = value
  }

  emit('update', params.value)
}

// 监听props变化来更新本地状态
watch(
  () => props.params,
  (newParams) => {
    // 避免循环更新
    if (JSON.stringify(newParams) !== JSON.stringify(params.value)) {
      params.value = { ...newParams }
      heartValue.value = newParams.heart_options?.heart_value || 0
      isAllowMessage.value = newParams.heart_options?.is_allow_message || false
    }
  },
  { deep: true }
)

// 初始化默认值
onMounted(() => {
  // 确保heart_options存在
  if (!params.value.heart_options) {
    params.value.heart_options = {
      heart_value: 0,
      is_allow_message: false
    }
    heartValue.value = 0
    isAllowMessage.value = false
  }
})
</script>

<style lang="less" scoped>
.heart-value-config {
  .config-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .form-group {
      margin-bottom: 16px;

      label {
        display: block;
        font-size: 13px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 6px;

        &.required::after {
          content: '*';
          color: #e74c3c;
          margin-left: 4px;
        }
      }

      .custom-input {
        width: 100%;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: #fff;
        padding: 8px 10px;
        transition: border-color 0.2s;
        font-size: 13px;

        &:focus {
          border-color: #ca93f2;
          outline: none;
        }
      }

      .checkbox-control {
        display: flex;
        align-items: center;
        gap: 8px;

        input[type='checkbox'] {
          -webkit-appearance: none;
          appearance: none;
          width: 18px;
          height: 18px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 4px;
          background: rgba(255, 255, 255, 0.05);
          display: inline-block;
          position: relative;
          margin: 0;

          &:checked {
            background: #ca93f2;
            border-color: #ca93f2;

            &::after {
              content: '';
              position: absolute;
              width: 4px;
              height: 8px;
              border: solid white;
              border-width: 0 2px 2px 0;
              top: 3px;
              left: 6px;
              transform: rotate(45deg);
            }
          }
        }

        label {
          display: inline;
          margin: 0;
        }
      }

      .field-hint {
        margin-top: 4px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.4);
      }
    }
  }
}
</style>
