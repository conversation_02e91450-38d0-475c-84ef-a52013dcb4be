<template>
  <div class="animated-images-config">
    <div class="config-section">
      <div class="section-title">动画图片配置</div>

      <div v-for="(_, index) in params.urls" :key="index" class="form-group image-item">
        <div class="image-header">
          <label>图片 {{ index + 1 }} (urls[{{ index }}])</label>
          <button class="delete-button" @click="removeImage(index)" title="删除图片">
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path
                fill="currentColor"
                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
              />
            </svg>
          </button>
        </div>
        <div class="media-selector-container">
          <input
            type="text"
            v-model="params.urls[index]"
            class="custom-input"
            placeholder="输入图片URL..."
            @input="updateUrl(index, $event)"
          />
          <MediaSelector
            type="image"
            v-model="params.urls[index]"
            v-model:preview="imagePreviewUrls[index]"
            class="media-selector"
          />
        </div>
      </div>

      <div class="form-group">
        <button class="add-image-button" @click="addImage">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
          </svg>
          添加图片
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import MediaSelector from '@/pc/components/Common/MediaSelector.vue'

interface Props {
  params: {
    urls?: string[]
  }
}

// 属性定义
const props = withDefaults(defineProps<Props>(), {
  params: () => ({
    urls: []
  })
})

// 事件定义
const emit = defineEmits<{
  update: [updatedParams: any]
}>()

// 本地状态
const params = ref({ ...props.params })

// 图片预览URL数组
const imagePreviewUrls = ref<string[]>([])

// 添加图片
const addImage = () => {
  if (!params.value.urls) {
    params.value.urls = []
  }
  params.value.urls.push('')
  imagePreviewUrls.value.push('')
  emit('update', params.value)
}

// 更新图片URL
const updateUrl = (index: number, event: Event) => {
  const url = (event.target as HTMLInputElement).value
  params.value.urls[index] = url
  emit('update', params.value)
}

// 删除图片
const removeImage = (index: number) => {
  params.value.urls.splice(index, 1)
  imagePreviewUrls.value.splice(index, 1)
  emit('update', params.value)
}

// 监听本地状态变化并向父组件发送更新
watch(
  params,
  (newParams) => {
    emit('update', newParams)
  },
  { deep: true }
)

// 监听props变化来更新本地状态
watch(
  () => props.params,
  (newParams) => {
    // 避免循环更新
    if (JSON.stringify(newParams) !== JSON.stringify(params.value)) {
      params.value = { ...newParams }
    }
  },
  { deep: true }
)

// 初始化默认值
onMounted(() => {
  // 确保urls数组存在
  if (!params.value.urls) {
    params.value.urls = []
  }

  // 初始化预览URL数组
  imagePreviewUrls.value = [...(params.value.urls || [])]
})

// 监听图片URL数组变化，更新预览
watch(
  () => params.value.urls,
  (newUrls) => {
    // 确保预览数组长度与实际URL数组一致
    if (newUrls && newUrls.length !== imagePreviewUrls.value.length) {
      imagePreviewUrls.value = [...newUrls]
    }

    // 更新每个预览URL
    if (newUrls) {
      newUrls.forEach((url, index) => {
        if (url && url !== imagePreviewUrls.value[index]) {
          imagePreviewUrls.value[index] = url
        }
      })
    }
  },
  { deep: true }
)
</script>

<style lang="less" scoped>
.animated-images-config {
  .config-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .form-group {
      margin-bottom: 16px;

      &.image-item {
        padding: 12px;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid rgba(255, 255, 255, 0.1);
        position: relative;
      }

      .image-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        label {
          display: block;
          font-size: 13px;
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 6px;

          &.required::after {
            content: '*';
            color: #e74c3c;
            margin-left: 4px;
          }
        }

        .delete-button {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 77, 79, 0.1);
          border: none;
          border-radius: 4px;
          color: #ff4d4f;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: rgba(255, 77, 79, 0.2);
          }
        }
      }

      .custom-input {
        width: 100%;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: #fff;
        padding: 8px 10px;
        transition: border-color 0.2s;
        font-size: 13px;

        &:focus {
          border-color: #ca93f2;
          outline: none;
        }
      }

      .media-selector-container {
        margin-bottom: 8px;
        display: flex;
        flex-direction: column;
        gap: 8px;

        .media-selector {
          margin-top: 8px;
        }
      }
    }

    .add-image-button {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      width: 100%;
      height: 40px;
      border: 1px dashed rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      background: none;
      color: rgba(255, 255, 255, 0.6);
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        border-color: #ca93f2;
        color: #ca93f2;
      }

      svg {
        width: 18px;
        height: 18px;
      }
    }
  }
}
</style>
