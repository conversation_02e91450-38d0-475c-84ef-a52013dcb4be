<template>
  <div class="scene-transition-config">
    <div class="config-section">
      <div class="section-title">场景转换配置</div>

      <div class="form-group">
        <label>转换类型 (scene_transition.type)</label>
        <!-- <select v-model="transitionType" class="custom-select" @change="updateTransition">
          <option value="fade">淡入淡出</option>
          <option value="slide">滑动</option>
        </select> -->
        <Select
          v-model="transitionType"
          class="custom-select"
          @update:model-value="updateTransition"
          :options="[
            { label: '淡入淡出', value: 'fade' },
            { label: '滑动', value: 'slide' }
          ]"
        ></Select>
        <div class="field-hint">选择场景间的转换效果</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import Select from '@/mobile/components/Select.vue'

interface SceneTransition {
  type: string
}

interface Props {
  params: {
    scene_transition?: SceneTransition
  }
}

// 属性定义
const props = withDefaults(defineProps<Props>(), {
  params: () => ({
    scene_transition: { type: 'fade' }
  })
})

// 事件定义
const emit = defineEmits<{
  update: [updatedParams: any]
}>()

// 本地状态
const params = ref({ ...props.params })
const transitionType = ref(props.params.scene_transition?.type || 'fade')

// 更新转场效果
const updateTransition = () => {
  if (!params.value.scene_transition) {
    params.value.scene_transition = { type: 'fade' }
  }
  params.value.scene_transition.type = transitionType.value
  emit('update', params.value)
}

// 监听props变化来更新本地状态
watch(
  () => props.params,
  (newParams) => {
    // 避免循环更新
    if (JSON.stringify(newParams) !== JSON.stringify(params.value)) {
      params.value = { ...newParams }
      transitionType.value = newParams.scene_transition?.type || 'fade'
    }
  },
  { deep: true }
)

// 初始化默认值
onMounted(() => {
  // 确保scene_transition存在
  if (!params.value.scene_transition) {
    params.value.scene_transition = { type: 'fade' }
    transitionType.value = 'fade'
  }
})
</script>

<style lang="less" scoped>
.scene-transition-config {
  .config-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .form-group {
      margin-bottom: 16px;

      label {
        display: block;
        font-size: 13px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 6px;

        &.required::after {
          content: '*';
          color: #e74c3c;
          margin-left: 4px;
        }
      }

      .custom-select {
        width: 100%;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: #fff;
        padding: 8px 10px;
        transition: border-color 0.2s;
        font-size: 13px;

        &:focus {
          border-color: #ca93f2;
          outline: none;
        }
      }

      .field-hint {
        margin-top: 4px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.4);
      }
    }
  }
}
</style>
