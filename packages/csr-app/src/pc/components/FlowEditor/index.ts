// 导出所有流程编辑器组件
import StoryFlowEditor from './StoryFlowEditor.vue'
import TreeGraph from './TreeGraph.vue'
import PropertyPanel from './PropertyPanel.vue'
import NodeConfigPanel from './NodeConfigPanel.vue'
import EventConfigPanel from './EventConfigPanel.vue'
import EventManager from './EventManager.vue'
import CustomNodeComponent from './CustomNodeComponent.vue'
import EventNodeComponent from './EventNodeComponent.vue'
import OperationGuide from './OperationGuide.vue'

// 导出事件配置组件和工具函数
export * from './EventConfigs'
export * from './utils/eventUtils'

// 导出所有主要组件
export {
  StoryFlowEditor,
  TreeGraph,
  PropertyPanel,
  NodeConfigPanel,
  EventConfigPanel,
  EventManager,
  CustomNodeComponent,
  EventNodeComponent,
  OperationGuide
}

// 默认导出主编辑器组件
export default StoryFlowEditor
