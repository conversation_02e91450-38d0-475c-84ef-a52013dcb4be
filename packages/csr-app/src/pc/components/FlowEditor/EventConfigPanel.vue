<template>
  <div class="event-config-panel">
    <div class="panel-header">
      <h3>{{ isNewEvent ? '添加事件' : '编辑事件' }}</h3>
      <button class="close-button" @click="closePanel">
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path
            fill="currentColor"
            d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"
          />
        </svg>
      </button>
    </div>

    <div class="panel-content">
      <!-- 事件类型选择 -->
      <div class="config-section" v-if="isNewEvent">
        <div class="section-title">选择事件类型</div>
        <div class="event-type-grid">
          <div
            v-for="type in eventTypes"
            :key="type.value"
            class="event-type-card"
            :class="{ selected: editedEvent.type === type.value }"
            @click="selectEventType(type.value)"
          >
            <div class="type-icon" :style="{ backgroundColor: type.color }">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path fill="currentColor" :d="type.icon" />
              </svg>
            </div>
            <div class="type-details">
              <div class="type-name">{{ type.label }}</div>
              <div class="type-desc">{{ type.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 基本信息配置 -->
      <div class="config-section">
        <div class="section-title">事件基本信息</div>
        <div class="form-group">
          <label>事件ID</label>
          <input
            type="text"
            v-model="editedEvent.id"
            :disabled="!isNewEvent"
            placeholder="系统自动生成，可自定义"
          />
        </div>
      </div>

      <!-- 事件特定配置 -->
      <template v-if="editedEvent.type">
        <component
          :is="getConfigComponent(editedEvent.type)"
          v-bind="getEventSpecificProps()"
          @update="updateEventParams"
        />
      </template>
    </div>

    <div class="panel-footer">
      <button class="cancel-btn" @click="closePanel">取消</button>
      <button class="save-btn" @click="saveEvent" :disabled="!canSave">保存</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { nanoid } from 'nanoid'
import { FlowEventType, FlowEvent, FlowNode } from '@/pc/components/FlowEditor/types'
import { getEventConfigComponent } from './EventConfigs'
import { getEventTypeName, getEventTypeIcon, getEventTypeColor } from './utils/eventUtils'

// 事件类型定义
interface EventTypeDefinition {
  value: FlowEventType
  label: string
  icon: string
  color: string
  description: string
  componentName?: string
}

// 定义可用的事件类型
const eventTypes: EventTypeDefinition[] = [
  {
    value: 'message',
    label: '对话事件',
    icon: 'M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M6,9H18V11H6M14,14H6V12H14M18,8H6V6H18',
    color: '#3498db',
    description: '添加一条角色对话',
    componentName: 'MessageEventConfig'
  },
  {
    value: 'wait',
    label: '等待',
    icon: 'M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z',
    color: '#9b59b6',
    description: '等待指定时间后继续',
    componentName: 'WaitEventConfig'
  },
  {
    value: 'show_tips',
    label: '显示提示',
    icon: 'M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,13H13V17H11V13Z',
    color: '#f39c12',
    description: '在界面上显示提示信息',
    componentName: 'ShowTipsEventConfig'
  },
  {
    value: 'play_audio',
    label: '播放音频',
    icon: 'M14,3.23V5.29C16.89,6.15 19,8.83 19,12C19,15.17 16.89,17.84 14,18.7V20.77C18,19.86 21,16.28 21,12C21,7.72 18,4.14 14,3.23M16.5,12C16.5,10.23 15.5,8.71 14,7.97V16C15.5,15.29 16.5,13.76 16.5,12M3,9V15H7L12,20V4L7,9H3Z',
    color: '#27ae60',
    description: '播放背景音乐或音效',
    componentName: 'AudioEventConfig'
  },
  {
    value: 'show_image',
    label: '显示图片',
    icon: 'M20,5A2,2 0 0,1 22,7V17A2,2 0 0,1 20,19H4C2.89,19 2,18.1 2,17V7C2,5.89 2.89,5 4,5H20M5,16H19L14.5,10L11,14.5L8.5,11.5L5,16Z',
    color: '#e74c3c',
    description: '在界面上显示一张图片',
    componentName: 'ShowImageEventConfig'
  },
  {
    value: 'play_video',
    label: '播放视频',
    icon: 'M4,4H7L9,2H15L17,4H20A2,2 0 0,1 22,6V18A2,2 0 0,1 20,20H4A2,2 0 0,1 2,18V6A2,2 0 0,1 4,4M12,7A5,5 0 0,0 7,12A5,5 0 0,0 12,17A5,5 0 0,0 17,12A5,5 0 0,0 12,7M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9Z',
    color: '#e74c3c',
    description: '播放视频',
    componentName: 'PlayVideoEventConfig'
  },
  {
    value: 'show_chat_options',
    label: '对话选项',
    icon: 'M3,9H17V7H3V9M3,13H17V11H3V13M3,17H17V15H3V17M19,17H21V15H19V17M19,7V9H21V7H19M19,13H21V11H19V13Z',
    color: '#2980b9',
    description: '显示可选择的对话选项',
    componentName: 'ChatOptionsEventConfig'
  },
  {
    value: 'scene_transition',
    label: '场景转换',
    icon: 'M8,5.14V19.14L19,12.14L8,5.14Z',
    color: '#16a085',
    description: '切换到其他场景',
    componentName: 'SceneTransitionEventConfig'
  },
  {
    value: 'heart_value',
    label: '好感度',
    icon: 'M12,21.35L10.55,20.03C5.4,15.36 2,12.28 2,8.5C2,5.42 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.09C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.42 22,8.5C22,12.28 18.6,15.36 13.45,20.03L12,21.35Z',
    color: '#e74c3c',
    description: '增加或减少好感度',
    componentName: 'HeartValueEventConfig'
  },
  {
    value: 'show_ending',
    label: '显示结局',
    icon: 'M5,3H19A2,2 0 0,1 21,5V19A2,2 0 0,1 19,21H5A2,2 0 0,1 3,19V5A2,2 0 0,1 5,3M12,10A2,2 0 0,0 10,12A2,2 0 0,0 12,14A2,2 0 0,0 14,12A2,2 0 0,0 12,10Z',
    color: '#d35400',
    description: '显示游戏结局',
    componentName: 'ShowEndingEventConfig'
  },
  {
    value: 'animated_images',
    label: '动画图片',
    icon: 'M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z',
    color: '#9b59b6',
    description: '显示一组连续的图片动画',
    componentName: 'AnimatedImagesEventConfig'
  },
  {
    value: 'interactive',
    label: '互动事件',
    icon: 'M7.5,5.6L5,7L6.4,4.5L5,2L7.5,3.4L10,2L8.6,4.5L10,7L7.5,5.6M19.5,15.4L22,14L20.6,16.5L22,19L19.5,17.6L17,19L18.4,16.5L17,14L19.5,15.4M22,2L20.6,4.5L22,7L19.5,5.6L17,7L18.4,4.5L17,2L19.5,3.4L22,2M13.34,12.78L15.78,10.34L13.66,8.22L11.22,10.66L13.34,12.78M14.37,7.29L16.71,9.63C17.1,10 17.1,10.65 16.71,11.04L5.04,22.71C4.65,23.1 4,23.1 3.63,22.71L1.29,20.37C0.9,20 0.9,19.35 1.29,18.96L12.96,7.29C13.35,6.9 14,6.9 14.37,7.29Z',
    color: '#3498db',
    description: '创建游戏中的互动环节',
    componentName: 'InteractiveEventConfig'
  },
  {
    value: 'show_overlay',
    label: '显示覆盖层',
    icon: 'M3,3H21V5H3V3M3,7H21V9H3V7M3,11H21V13H3V11M3,15H21V17H3V15M3,19H21V21H3V19Z',
    color: '#f1c40f',
    description: '显示浮层提示信息',
    componentName: 'ShowOverlayEventConfig'
  }
]

// 属性定义
const props = defineProps<{
  event?: FlowEvent
  node?: FlowNode
  isNew?: boolean
  availableNodes?: FlowNode[]
}>()

// 事件定义
const emit = defineEmits<{
  save: [event: FlowEvent]
  close: []
  delete: [eventId: string]
}>()

// 编辑中的事件数据（深度克隆，避免直接修改原始数据）
const editedEvent = ref<FlowEvent>({
  id: '',
  type: '' as FlowEventType,
  params: {}
})

// 是否是新事件
const isNewEvent = computed(() => props.isNew || !props.event)

// 使用从EventConfigs导入的getEventConfigComponent，但用一个不同的名称避免命名冲突
const getConfigComponent = (type: FlowEventType) => {
  return getEventConfigComponent(type)
}

// 获取事件特定属性
const getEventSpecificProps = () => {
  return {
    params: editedEvent.value.params,
    availableNodes: props.availableNodes,
    node: props.node
  }
}

// 选择事件类型
const selectEventType = (type: FlowEventType) => {
  editedEvent.value.type = type
  // 初始化该类型事件的默认参数
  initEventDefaultParams(type)
}

// 初始化事件默认参数
const initEventDefaultParams = (type: FlowEventType) => {
  switch (type) {
    case 'message':
      editedEvent.value.params = {
        content: '',
        character: '',
        mood: 'normal',
        speed: 1,
        volume: 1,
        autoNext: true
      }
      break
    case 'wait':
      editedEvent.value.params = {
        seconds: 1,
        showIndicator: false
      }
      break
    case 'show_tips':
      editedEvent.value.params = {
        content: '',
        position: 'center',
        duration: 3,
        style: 'info',
        showIcon: true
      }
      break
    case 'play_audio':
      editedEvent.value.params = {
        url: '',
        type: 'bgm',
        volume: 1,
        loop: false,
        fade: true,
        fadeDuration: 2,
        waitForComplete: false
      }
      break
    case 'show_image':
      editedEvent.value.params = {
        url: '',
        position: 'center',
        sizeType: 'percentage',
        width: 80,
        height: 80,
        animation: 'fade',
        animationDuration: 0.5,
        duration: 0,
        waitForDisplay: true
      }
      break
    case 'play_video':
      editedEvent.value.params = {
        url: '',
        autoplay: true,
        controls: true,
        fullscreen: false,
        loop: false,
        volume: 1,
        allowSkip: true
      }
      break
    case 'show_chat_options':
      editedEvent.value.params = {
        options: [{ id: nanoid(6), text: '选项1', targetEventId: '' }],
        timeout: 0,
        defaultOption: '',
        allowMultipleChoices: false
      }
      break
    case 'scene_transition':
      editedEvent.value.params = {
        targetNodeId: '',
        seconds: 0,
        transitionEffect: 'fade'
      }
      break
    case 'heart_value':
      editedEvent.value.params = {
        value: 5,
        character: '',
        showAnimation: true,
        playSound: true,
        milestone: false,
        milestoneValue: 50
      }
      break
    case 'show_ending':
      editedEvent.value.params = {
        title: '',
        description: '',
        image: '',
        showRestart: true,
        showShare: true,
        animation: 'fade'
      }
      break
    default:
      editedEvent.value.params = {}
  }
}

// 更新事件参数
const updateEventParams = (params: any) => {
  editedEvent.value.params = { ...params }
}

// 是否可以保存
const canSave = computed(() => {
  // 必须有有效的事件类型
  if (!editedEvent.value.type) return false

  // 根据事件类型验证必填字段
  const params = editedEvent.value.params

  switch (editedEvent.value.type) {
    case 'message':
      return !!params.content
    case 'scene_transition':
      return !!params.targetNodeId
    // 其他类型可以添加特定验证逻辑
    default:
      return true
  }
})

// 保存事件
const saveEvent = () => {
  // 确保有ID，若没有则生成新ID
  if (!editedEvent.value.id) {
    editedEvent.value.id = nanoid()
  }

  emit('save', { ...editedEvent.value })
}

// 关闭面板
const closePanel = () => {
  emit('close')
}

// 初始化事件数据
const initEventData = () => {
  if (props.event) {
    // 编辑现有事件
    editedEvent.value = JSON.parse(JSON.stringify(props.event))
  } else {
    // 新建事件
    editedEvent.value = {
      id: nanoid(),
      type: '' as FlowEventType,
      params: {}
    }
  }
}

// 监听事件变化，重新初始化
watch(
  () => props.event,
  () => {
    initEventData()
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.event-config-panel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1a1a1c;
  border-left: 1px solid rgba(255, 255, 255, 0.1);

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    h3 {
      margin: 0;
      color: #fff;
      font-size: 16px;
      font-weight: 600;
    }

    .close-button {
      background: transparent;
      border: none;
      color: rgba(255, 255, 255, 0.6);
      cursor: pointer;
      transition: color 0.2s;

      &:hover {
        color: #fff;
      }
    }
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;

    .config-section {
      margin-bottom: 24px;

      .section-title {
        font-size: 14px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .form-group {
        margin-bottom: 16px;

        label {
          display: block;
          font-size: 13px;
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 6px;
        }

        input[type='text'],
        input[type='number'],
        textarea,
        select {
          width: 100%;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          color: #fff;
          padding: 8px 10px;
          transition: border-color 0.2s;
          font-size: 13px;

          &:focus {
            border-color: #ca93f2;
            outline: none;
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }

        textarea {
          resize: vertical;
          min-height: 80px;
        }
      }
    }

    .event-type-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
      margin-top: 12px;

      .event-type-card {
        display: flex;
        align-items: center;
        padding: 12px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          background: rgba(255, 255, 255, 0.08);
        }

        &.selected {
          border-color: #ca93f2;
          background: rgba(202, 147, 242, 0.1);
        }

        .type-icon {
          width: 40px;
          height: 40px;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          color: #fff;
        }

        .type-details {
          flex: 1;

          .type-name {
            font-weight: 500;
            color: #fff;
            margin-bottom: 4px;
          }

          .type-desc {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.5);
          }
        }
      }
    }
  }

  .panel-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 12px 16px;
    background-color: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.1);

    button {
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 13px;
      transition: all 0.2s;

      &.cancel-btn {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.7);

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          color: #fff;
        }
      }

      &.save-btn {
        background: #ca93f2;
        border: 1px solid #ca93f2;
        color: #000;

        &:hover {
          background: #d5a4fc;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }
}
</style>
