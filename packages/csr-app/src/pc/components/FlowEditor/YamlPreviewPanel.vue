<template>
  <div
    class="yaml-preview-panel"
    :class="{ 'panel-visible': visible }"
    :style="{ width: panelWidth + 'px' }"
  >
    <div class="panel-resizer" @mousedown="startResize"></div>
    <div class="panel-header">
      <h3>YAML 预览</h3>
      <div class="panel-actions">
        <button class="action-button" @click="copyYaml" title="复制 YAML">
          <svg viewBox="0 0 24 24" class="icon">
            <path
              fill="currentColor"
              d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"
            />
          </svg>
        </button>
        <button class="action-button" @click="downloadYaml" title="下载 YAML">
          <svg viewBox="0 0 24 24" class="icon">
            <path fill="currentColor" d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" />
          </svg>
        </button>
        <button class="close-button" @click="$emit('close')" title="关闭">
          <svg viewBox="0 0 24 24" class="icon-close">
            <path
              fill="currentColor"
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            />
          </svg>
        </button>
      </div>
    </div>
    <div class="panel-content">
      <MonacoYamlViewer
        ref="yamlViewer"
        :content="yamlContent"
        :readOnly="true"
        :highlightLines="highlightLines"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import MonacoYamlViewer from './MonacoYamlViewer.vue'
import { dump } from 'js-yaml'
import { saveAs } from 'file-saver'

// 定义组件接收的属性
const props = defineProps<{
  visible: boolean
  content: any
  highlightLines?: number[]
}>()

// 定义组件发出的事件
const emit = defineEmits<{
  (e: 'close'): void
  (e: 'copy-success'): void
  (e: 'copy-error'): void
  (e: 'download-success'): void
  (e: 'download-error'): void
}>()

// 组件引用
const yamlViewer = ref<InstanceType<typeof MonacoYamlViewer> | null>(null)

// YAML 内容
const yamlContent = ref('')

// 面板宽度调整相关变量
const panelWidth = ref(400) // 默认宽度
const minPanelWidth = 300 // 最小宽度
const maxPanelWidth = 1000 // 最大宽度
const isResizing = ref(false)

// 开始调整面板宽度
const startResize = (e: MouseEvent) => {
  isResizing.value = true
  const startX = e.clientX
  const startWidth = panelWidth.value

  const handleMouseMove = (moveEvent: MouseEvent) => {
    if (!isResizing.value) return
    // 反转计算逻辑，使其符合直觉：向左拖动时面板变宽，向右拖动时面板变窄
    const deltaX = startX - moveEvent.clientX // 注意这里的顺序反过来了
    const newWidth = Math.min(Math.max(startWidth + deltaX, minPanelWidth), maxPanelWidth)
    panelWidth.value = newWidth
  }

  const handleMouseUp = () => {
    isResizing.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 将对象转换为 YAML 字符串
const convertToYaml = (obj: any): string => {
  try {
    // 使用 js-yaml 的 dump 方法，设置选项以避免常见错误
    return dump(obj, {
      indent: 2,
      lineWidth: -1, // 禁用行宽限制
      noRefs: true, // 避免循环引用问题
      skipInvalid: true, // 跳过无效值
      sortKeys: false // 保持键的顺序
    })
  } catch (error) {
    console.error('转换 YAML 时出错:', error)
    return `# 转换 YAML 时出错: ${error}\n# 原始数据:\n${JSON.stringify(obj, null, 2)}`
  }
}

// 复制 YAML 到剪贴板
const copyYaml = async () => {
  try {
    await navigator.clipboard.writeText(yamlContent.value)
    emit('copy-success')
  } catch (error) {
    console.error('复制 YAML 时出错:', error)
    emit('copy-error')
  }
}

// 下载 YAML 文件
const downloadYaml = () => {
  try {
    const blob = new Blob([yamlContent.value], { type: 'text/yaml;charset=utf-8' })
    saveAs(blob, 'game-config.yaml')
    emit('download-success')
  } catch (error) {
    console.error('下载 YAML 时出错:', error)
    emit('download-error')
  }
}

// 监听内容变化，更新 YAML
watch(
  () => props.content,
  (newContent) => {
    if (newContent) {
      yamlContent.value = convertToYaml(newContent)
    }
  },
  { immediate: true, deep: true }
)

// 监听高亮行变化
watch(
  () => props.highlightLines,
  (newHighlightLines) => {
    if (yamlViewer.value && newHighlightLines) {
      nextTick(() => {
        yamlViewer.value?.highlightLinesFun(newHighlightLines)
      })
    }
  },
  { immediate: true }
)

// 组件挂载后初始化
onMounted(() => {
  // 初始化 YAML 内容
  if (props.content) {
    yamlContent.value = convertToYaml(props.content)
  }
})
</script>

<style lang="less" scoped>
.yaml-preview-panel {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  background-color: #1e1e1e;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  z-index: 13;

  &.panel-visible {
    transform: translateX(0);
  }

  .panel-resizer {
    position: absolute;
    left: 0;
    top: 0;
    width: 5px;
    height: 100%;
    cursor: ew-resize;
    background-color: transparent;
    z-index: 11;

    &:hover {
      background-color: rgba(202, 147, 242, 0.3);
    }
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
    }

    .panel-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .action-button,
    .close-button {
      background: none;
      border: none;
      cursor: pointer;
      padding: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: rgba(255, 255, 255, 0.7);
      border-radius: 4px;
      transition: all 0.2s;

      &:hover {
        color: rgba(255, 255, 255, 0.9);
        background-color: rgba(255, 255, 255, 0.1);
      }

      .icon,
      .icon-close {
        width: 18px;
        height: 18px;
      }
    }
  }

  .panel-content {
    flex: 1;
    overflow: hidden;
    position: relative;
  }
}
</style>
