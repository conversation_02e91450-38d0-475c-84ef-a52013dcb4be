// TreeNode 节点数据类型
export interface TreeNode {
  id: string
  name: string
  type: string // 'outline' | 'begin' | 'chapter' | 'event' | 'group'
  parentId?: string
  level?: number
  properties?: Record<string, any>
  children?: TreeNode[]
  events?: FlowEvent[]
  // 新增事件类型信息
  eventType?: string // 使用字符串类型，兼容任何事件类型
  // 新增章节信息
  next_scene_id?: string
  conditions?: any[]
  action_handlers?: any[]
  // 新增场景组属性
  scene_group?: string
  // 新增 data 属性，用于与组件通信
  data?: {
    id: string
    name: string
    type: string
    isSelected?: boolean
    [key: string]: any
  }
  // 新增选中状态
  isSelected?: boolean
}

// 流程节点
export interface FlowNode {
  id: string
  name: string
  type: string
  parentId?: string
  level?: number
  properties?: Record<string, any>
  events?: FlowEvent[]
}

// 事件类型
export type FlowEventType =
  | 'message'
  | 'wait'
  | 'show_tips'
  | 'play_audio'
  | 'show_image'
  | 'play_video'
  | 'show_chat_options'
  | 'scene_transition'
  | 'heart_value'
  | 'show_ending'
  | 'show_overlay'
  | 'animated_images'
  | 'update_task_progress'
  | 'interactive'
  | 'voice_config'

// 流程事件
export interface FlowEvent {
  id: string
  type: FlowEventType
  params: any // 不同类型的事件有不同的参数结构
}

// 消息事件参数
export interface MessageEventParams {
  content: string
  character: string
  mood: string
  speed?: number
  volume?: number
  autoNext?: boolean
}

// 等待事件参数
export interface WaitEventParams {
  duration: number // 毫秒
}

// 提示事件参数
export interface TipsEventParams {
  content: string
  duration: number // 毫秒
  position?: 'top' | 'center' | 'bottom'
}

// 音频事件参数
export interface AudioEventParams {
  url: string
  volume: number
  loop: boolean
}

// 图片事件参数
export interface ImageEventParams {
  url: string
  position: string
  duration: number // 0表示不自动关闭
}

// 视频事件参数
export interface VideoEventParams {
  url: string
  autoplay: boolean
  controls: boolean
}

// 对话选项事件参数
export interface ChatOptionsEventParams {
  options: ChatOption[]
}

export interface ChatOption {
  id: string
  text: string
  targetEventId: string
}

// 场景转换事件参数
export interface SceneTransitionEventParams {
  targetNodeId: string
  seconds: number
  transitionEffect?: string
}

// 好感度事件参数
export interface HeartValueEventParams {
  value: number
  character: string
}

// 结局事件参数
export interface EndingEventParams {
  title: string
  description: string
  image: string
}

// 节点配置面板类型
export interface NodeConfig {
  id: string
  name: string
  type: string
  fields: ConfigField[]
}

// 配置字段类型
export interface ConfigField {
  id: string
  label: string
  type: 'text' | 'number' | 'select' | 'textarea' | 'color' | 'radio' | 'checkbox'
  value: any
  options?: Array<{ label: string; value: any }>
  required?: boolean
  placeholder?: string
}

// 树形图配置选项
export interface TreeGraphOptions {
  container: HTMLElement
  width: number
  height: number
}

// 节点类型定义
export enum NodeType {
  OUTLINE = 'outline',
  BEGIN = 'begin',
  OPENING = 'opening',
  DIALOGUE = 'dialogue',
  VIDEO = 'video',
  JUMP = 'jump'
}

// 级别类型
export interface LevelTypeOption {
  id: string
  label: string
  icon: string
  color: string
}

// 节点数据
export interface NodeData {
  id: string
  name: string
  type: string
  properties: Record<string, any>
}
