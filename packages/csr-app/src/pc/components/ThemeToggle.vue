<template>
  <button class="theme-toggle" @click="toggleTheme" :title="themeTitle">
    <SunIcon v-if="isDarkTheme" />
    <MoonIcon v-else />
  </button>
</template>

<script setup lang="ts">
import { computed, defineComponent, h } from 'vue'
import { useThemeStore } from '@/store/theme'

const themeStore = useThemeStore()
const isDarkTheme = computed(() => themeStore.isDarkTheme)
const themeTitle = computed(() =>
  isDarkTheme.value ? 'Switch to Light Mode' : 'Switch to Dark Mode'
)

const toggleTheme = () => {
  themeStore.toggleTheme()
}

// 太阳图标组件
const SunIcon = defineComponent({
  name: 'SunIcon',
  render() {
    return h(
      'svg',
      {
        viewBox: '0 0 24 24',
        fill: 'none',
        stroke: 'currentColor',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        class: 'theme-icon'
      },
      [
        h('circle', { cx: '12', cy: '12', r: '5' }),
        h('line', { x1: '12', y1: '1', x2: '12', y2: '3' }),
        h('line', { x1: '12', y1: '21', x2: '12', y2: '23' }),
        h('line', { x1: '4.22', y1: '4.22', x2: '5.64', y2: '5.64' }),
        h('line', { x1: '18.36', y1: '18.36', x2: '19.78', y2: '19.78' }),
        h('line', { x1: '1', y1: '12', x2: '3', y2: '12' }),
        h('line', { x1: '21', y1: '12', x2: '23', y2: '12' }),
        h('line', { x1: '4.22', y1: '19.78', x2: '5.64', y2: '18.36' }),
        h('line', { x1: '18.36', y1: '5.64', x2: '19.78', y2: '4.22' })
      ]
    )
  }
})

// 月亮图标组件
const MoonIcon = defineComponent({
  name: 'MoonIcon',
  render() {
    return h(
      'svg',
      {
        viewBox: '0 0 24 24',
        fill: 'none',
        stroke: 'currentColor',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        class: 'theme-icon'
      },
      [h('path', { d: 'M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z' })]
    )
  }
})
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--bg-tertiary, rgba(255, 255, 255, 0.05));
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-primary, #ffffff);

  &:hover {
    background-color: var(--bg-hover, rgba(255, 255, 255, 0.1));
    transform: rotate(15deg);
  }

  .theme-icon {
    width: 20px;
    height: 20px;
  }

  // 确保在亮色主题下有良好的显示效果
  body.light-theme & {
    background-color: rgba(0, 0, 0, 0.05);
    color: #333333;

    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }
  }

  // 确保在暗色主题下有良好的显示效果
  body.dark-theme & {
    background-color: rgba(255, 255, 255, 0.05);
    color: #ffffff;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}
</style>
