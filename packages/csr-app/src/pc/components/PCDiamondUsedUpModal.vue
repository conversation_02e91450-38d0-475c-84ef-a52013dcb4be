<template>
  <Teleport to="body">
    <Transition name="fade">
      <div v-if="visible" class="pc-diamond-modal-overlay" @click.stop="handleOverlayClick">
        <div class="pc-diamond-modal" @click.stop>
          <!-- <button class="close-button" @click="() => emit('update:visible', false)">
            <IconClose />
          </button> -->

          <div class="modal-icon">
            <img
              src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
              alt="Diamond"
              class="diamond-icon"
            />
          </div>

          <div class="modal-title">Diamonds Used Up</div>

          <div class="modal-content">
            Your diamonds have been used up. Click Recharge to continue playing, or click Exit the
            game
          </div>

          <div class="modal-actions">
            <button class="leave-button" @click="handleLeave">Leave</button>
            <button class="topup-button" @click="handleTopUp">Top Up</button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { useRechargeStore } from '@/store/recharge'
import { useChatUIStore } from '@/store/chat-ui'
import { useChatEventsStore } from '@/store/chat-events'

defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'leave'): void
}>()

const rechargeStore = useRechargeStore()
const chatUIStore = useChatUIStore()
const chatEventsStore = useChatEventsStore()

// 处理离开按钮点击
const handleLeave = () => {
  // 设置已确认离开标记，避免二次确认
  chatEventsStore.isConfirmedLeave = true
  emit('update:visible', false)
  emit('leave')
}

// 处理充值按钮点击
const handleTopUp = () => {
  // 先隐藏钻石用完弹窗，避免层级冲突
  // emit('update:visible', false)
  // 显示PC版充值弹窗
  rechargeStore.showRechargeModal()
  // 同时获取价格列表
  rechargeStore.fetchPriceList()
  // 锁定聊天输入框
  chatUIStore.setDisableInput(true)
}

// 处理点击遮罩层
const handleOverlayClick = () => {
  // PC版默认不允许点击遮罩层关闭，保持与移动端一致的行为
}
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.pc-diamond-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  backdrop-filter: blur(8px);
}

.pc-diamond-modal {
  width: calc(100% - 32px);
  max-width: 420px;
  background: linear-gradient(180deg, #2b1b3b 0%, #1a0f24 100%);
  border-radius: 16px;
  padding: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: zoomIn 0.3s ease;
  position: relative;
}

.close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: rgba(255, 255, 255, 0.7);

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

.modal-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  margin-bottom: 8px;

  .diamond-icon {
    width: 48px;
    height: 48px;
    object-fit: contain;
  }
}

.modal-title {
  font-size: 24px;
  font-weight: 700;
  color: white;
  text-align: center;
  margin-bottom: 8px;
}

.modal-content {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  line-height: 1.5;
  margin-bottom: 8px;
  max-width: 320px;
}

.modal-actions {
  display: flex;
  gap: 16px;
  width: 100%;
  max-width: 320px;

  button {
    flex: 1;
    height: 48px;
    border-radius: 24px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .leave-button {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);

    &:hover {
      background: rgba(255, 255, 255, 0.15);
    }
  }

  .topup-button {
    background: var(--accent-color);
    color: var(--bg-primary);

    &:hover {
      background: var(--accent-hover);
    }
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 响应式调整
@media screen and (max-width: 480px) {
  .pc-diamond-modal {
    width: calc(100% - 24px);
    padding: 24px;
    gap: 20px;

    .modal-title {
      font-size: 20px;
    }

    .modal-content {
      font-size: 14px;
    }

    .modal-actions {
      button {
        height: 44px;
        font-size: 14px;
      }
    }
  }
}
</style>
