<template>
  <div class="story-detail-modal" v-if="visible" @click.self="handleClose">
    <div class="modal-content">
      <button
        class="close-button"
        @click="handleClose"
        :title="isLoading ? 'Cancel operation (ESC)' : 'Close (ESC)'"
      >
        <icon-close />
      </button>

      <div class="modal-header">
        <div class="header-content">
          <!-- 标题骨架屏 -->
          <div v-if="isLoading" class="skeleton skeleton-title"></div>
          <h2 v-else>{{ storyDetail.story?.title }}</h2>

          <div class="story-cost">
            <!-- 成本标签骨架屏 -->
            <div v-if="isLoading" class="skeleton skeleton-badge"></div>
            <template v-else>
              <div class="cost-badge" v-if="!isStorePurchased && storyCost > 0">
                <img
                  src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
                  class="diamond-icon"
                />
                <span>{{ storyCost }}</span>
              </div>
              <div class="unlocked-badge" v-else-if="isStorePurchased">Unlocked</div>
              <div class="free-badge" v-else>Free</div>
            </template>
          </div>
        </div>

        <div class="favorite-button" :class="{ active: isFavorited }" @click.stop="toggleFavorite">
          <div class="heart-icon" ref="favoriteIconRef">
            <icon-favorited v-if="isFavorited" />
            <icon-favorite v-else />
          </div>
          <span class="favorite-text">{{ isFavorited ? 'Favorited' : 'Add favorite' }}</span>
        </div>
      </div>

      <div class="modal-body">
        <div class="story-info">
          <div class="description">
            <h3>Description</h3>
            <!-- 描述骨架屏 -->
            <div v-if="isLoading" class="skeleton skeleton-description"></div>
            <p v-else>{{ storyDetail.story?.description }}</p>
          </div>
        </div>

        <!-- 只有当故事版本不是3时才显示角色选择 -->
        <div v-if="storyDetail.story?.version !== '3'" class="character-section">
          <h3>Select Character</h3>
          <div class="character-carousel">
            <button
              class="carousel-arrow prev"
              @click="scrollCharacters('prev')"
              :disabled="isScrollStart"
              v-show="hasMultipleActors && !isLoading"
            >
              <icon-chevron-left />
            </button>

            <div class="character-list" ref="characterListRef">
              <!-- 角色列表骨架屏 -->
              <template v-if="isLoading">
                <div v-for="i in 3" :key="`skeleton-${i}`" class="character-card skeleton-card">
                  <div class="card-content">
                    <div class="skeleton skeleton-image"></div>
                    <div class="character-info">
                      <div class="skeleton skeleton-name"></div>
                      <div class="skeleton skeleton-actor-badge"></div>
                    </div>
                  </div>
                </div>
              </template>
              <!-- 实际角色列表 -->
              <template v-else>
                <div
                  v-for="actor in storyDetail.story?.actors"
                  :key="actor.id"
                  class="character-card"
                  :class="{ 'is-selected': selectedActor?.id === actor.id }"
                  @click="selectActor(actor)"
                >
                  <div class="card-content">
                    <img :src="actor.preview_url" :alt="actor.name" class="preview-url" />
                    <div class="character-info">
                      <div class="character-name">{{ actor.name }}</div>
                      <div v-if="!actor.is_purchased" class="actor-cost-badge">
                        <img
                          src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
                          class="diamond-icon"
                        />
                        {{ getActorCost(actor) }}
                      </div>
                      <div
                        v-else-if="actor.is_purchased && actor.coins > 0"
                        class="actor-unlocked-badge"
                      >
                        Unlocked
                      </div>
                      <div v-else class="actor-free-badge">Free</div>
                    </div>
                  </div>
                </div>
              </template>
            </div>

            <button
              class="carousel-arrow next"
              @click="scrollCharacters('next')"
              :disabled="isScrollEnd"
              v-show="hasMultipleActors && !isLoading"
            >
              <icon-chevron-right />
            </button>
          </div>
        </div>

        <!-- 只有当故事版本不是3时才显示角色属性 -->
        <template v-if="storyDetail.story?.version !== '3'">
          <!-- 角色属性骨架屏 -->
          <div v-if="isLoading" class="character-attributes skeleton-attributes">
            <div v-for="i in 4" :key="`attr-skeleton-${i}`" class="attribute-item">
              <div class="skeleton skeleton-attr-label"></div>
              <div class="skeleton skeleton-attr-value"></div>
            </div>
          </div>
          <!-- 实际角色属性 -->
          <div v-else-if="selectedActor" class="character-attributes">
            <div class="attribute-item">
              <div class="attribute-label">Age</div>
              <div class="attribute-value">{{ selectedActor.extra?.age || '18' }}</div>
            </div>
            <div class="attribute-item">
              <div class="attribute-label">Relationship</div>
              <div class="attribute-value">
                {{ selectedActor.extra?.relationship || 'Unknown' }}
              </div>
            </div>
            <div class="attribute-item">
              <div class="attribute-label">Measurements</div>
              <div class="attribute-value">
                {{ selectedActor.extra?.measurements || 'Unknown' }}
              </div>
            </div>
            <div class="attribute-item">
              <div class="attribute-label">Occupation</div>
              <div class="attribute-value">{{ selectedActor.extra?.occupation || 'Unknown' }}</div>
            </div>
          </div>
        </template>
      </div>

      <div class="modal-footer">
        <button class="cancel-button" @click="handleClose">Cancel</button>
        <button
          class="play-button"
          :disabled="(storyDetail.story?.version !== '3' && !selectedActor) || isLoading"
          @click="handlePlay"
        >
          <span v-if="isLoading" class="play-button-spinner"></span>
          <span v-if="isLoading" class="play-button-loading-text">Loading...</span>
          <template v-else>
            Play
            <span v-if="selectedActor && !selectedActor.is_purchased" class="cost">
              <img src="https://cdn.magiclight.ai/assets/mobile/diamond.png" class="diamond-icon" />
              {{ getActorCost(selectedActor) }}
            </span>
          </template>
        </button>
      </div>

      <PCConfirmDialog
        v-model:visible="showDialog"
        content="Your previous progress has been safely saved, you can either continue or start over"
        confirm-text="Continue"
        cancel-text="Restart"
        :show-icon="false"
        :show-cancel="true"
        :close-on-click-overlay="false"
        @confirm="handleContinue"
        @cancel="handleRestart"
        @update:visible="handleDialogClose"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import IconClose from '@/assets/icon/close.svg'
import IconFavorited from '@/assets/icon/favorited.svg'
import IconFavorite from '@/assets/icon/favorite.svg'
import IconChevronLeft from '@/assets/icon/chevron-left.svg'
import IconChevronRight from '@/assets/icon/chevron-right.svg'
import { useStoryStore } from '@/store'
import { setDynamicSEO } from '@/router/seo-guard'
import { Actor } from '@/api/stories'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { animate } from 'motion'
import PCConfirmDialog from '@/pc/components/PCConfirmDialog.vue'
import { getUserChatHistory } from '@/api/chat'
import { useChatEventsStore } from '@/store/chat-events'
import { useRoute } from 'vue-router'

const props = defineProps<{
  visible: boolean
  storyId: string
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'play', actor: Actor): void
}>()

const router = useRouter()
const route = useRoute()
const storyStore = useStoryStore()
const chatEventsStore = useChatEventsStore()
// 动态SEO将通过路由守卫自动处理
const selectedActor = ref<Actor | null>(null)
const currentActor = ref<Actor | null>(null) // 存储当前使用的actor（可能是选中的或伪造的）
const isLoading = ref(false)
const characterListRef = ref<HTMLElement | null>(null)
const favoriteIconRef = ref<HTMLElement | null>(null)
const isScrollStart = ref(true)
const isScrollEnd = ref(false)
const showDialog = ref(false)
const storyDetail = ref({ story: null })

// 用于取消正在进行的请求
const abortController = ref<AbortController | null>(null)

// 记住每个故事的角色选择状态
const lastSelectedActorMap = ref<Map<string, string>>(new Map())

// 判断是否有多个角色
const hasMultipleActors = computed(() => {
  return (storyDetail.value.story?.actors?.length || 0) > 1
})

// 计算故事成本
const storyCost = computed(() => {
  if (!storyDetail.value.story) return 0
  return storyDetail.value.story.coins || 0
})

// 判断故事是否已购买
const isStorePurchased = computed(() => {
  return storyDetail.value.story?.is_purchased || false
})

// 故事是否已收藏
const isFavorited = ref(false)

// 获取角色成本
const getActorCost = (actor: Actor) => {
  if (!actor) return 0
  const totalCost = actor.coins + (isStorePurchased.value ? 0 : storyCost.value)
  return totalCost === 0 ? 'Free' : totalCost
}

// 选择角色
const selectActor = (actor: Actor) => {
  selectedActor.value = actor
}

// 播放收藏动画
const playFavoriteAnimation = () => {
  if (!favoriteIconRef.value) return

  animate(
    favoriteIconRef.value,
    {
      transform: [
        'scale(1) rotate(0deg)',
        'scale(1.4) rotate(-15deg)',
        'scale(0.9) rotate(15deg)',
        'scale(1.1) rotate(0deg)',
        'scale(1) rotate(0deg)'
      ],
      opacity: [0.6, 1]
    },
    {
      duration: 0.8,
      easing: [0.22, 0.68, 0.22, 0.95]
    }
  )
}

// 切换收藏状态
const toggleFavorite = async () => {
  if (!storyDetail.value.story) return

  // 乐观更新
  const previousState = isFavorited.value
  isFavorited.value = !previousState

  // 只在添加收藏时播放动画
  if (isFavorited.value) {
    playFavoriteAnimation()
  }

  try {
    const success = await storyStore.toggleFavorite()
    if (success) {
      reportEvent(ReportEvent.ClickStoryIntroFavorite, {
        storyId: storyDetail.value.story.id,
        isFavorited: isFavorited.value
      })
    } else {
      // 如果API调用失败，恢复之前的状态
      isFavorited.value = previousState
    }
  } catch (error) {
    console.error('Failed to toggle favorite:', error)
    // 如果发生错误，恢复之前的状态
    isFavorited.value = previousState
  }
}

// 滚动角色列表
const scrollCharacters = (direction: 'prev' | 'next') => {
  if (!characterListRef.value) return

  const container = characterListRef.value
  const scrollAmount = 220 // 每次滚动的距离，大约是一个卡片的宽度加上间距

  if (direction === 'prev') {
    container.scrollBy({ left: -scrollAmount, behavior: 'smooth' })
  } else {
    container.scrollBy({ left: scrollAmount, behavior: 'smooth' })
  }

  // 延迟检查滚动位置，等待滚动动画完成
  setTimeout(checkScrollPosition, 300)
}

// 检查滚动位置
const checkScrollPosition = () => {
  if (!characterListRef.value) return

  const container = characterListRef.value
  // 检查是否到达开始位置（左侧）
  isScrollStart.value = container.scrollLeft <= 10

  // 检查是否到达结束位置（右侧）
  // 添加一个小的容差值，确保检测更准确
  const scrollableWidth = container.scrollWidth - container.clientWidth
  isScrollEnd.value = container.scrollLeft >= scrollableWidth - 5
}

// 关闭弹窗
const handleClose = () => {
  // 取消正在进行的请求
  if (abortController.value) {
    abortController.value.abort()
    abortController.value = null
  }

  // 重置状态
  isLoading.value = false
  // 注意：不重置 selectedActor，保持用户的选择状态
  showDialog.value = false

  // 关闭弹窗
  emit('update:visible', false)
}

// 开始游戏
const handlePlay = async () => {
  // 版本3不需要选择角色，其他版本需要
  if (storyDetail.value.story?.version !== '3' && !selectedActor.value) return
  if (isLoading.value) return

  isLoading.value = true

  // 创建新的 AbortController 用于取消请求
  abortController.value = new AbortController()

  try {
    let actorToUse = selectedActor.value

    // 如果是版本3，伪造一个actor对象
    if (storyDetail.value.story?.version === '3') {
      actorToUse = {
        id: 'reasoning',
        name: 'Reasoning'
      } as Actor
    }

    // 存储当前使用的actor
    currentActor.value = actorToUse

    // 设置当前角色
    if (actorToUse) {
      storyStore.setCurrentActor(actorToUse)
    }

    // 上报事件
    reportEvent(ReportEvent.ClickToPlayInStoryIntro, {
      storyId: storyDetail.value.story?.id,
      actorId: actorToUse?.id
    })

    // 根据版本决定路由
    let ChatType = 'Chat'
    if (storyDetail.value.story?.version === '3') {
      ChatType = 'Chat3'
    } else if (actorToUse?.version === '4' || actorToUse?.version === '5') {
      ChatType = 'Chat4'
    } else if (actorToUse?.version === '2') {
      ChatType = 'Chat2'
    }

    // 只有当角色版本为2、3或4，或故事版本为3时才检查聊天历史记录
    if (
      actorToUse?.version === '2' ||
      actorToUse?.version === '3' ||
      actorToUse?.version === '4' ||
      actorToUse?.version === '5' ||
      storyDetail.value.story?.version === '3'
    ) {
      // 检查是否有聊天历史记录
      await getChatHistory(actorToUse)

      // 如果有历史记录，显示弹窗让用户选择，直接返回等待用户操作
      if (showDialog.value) {
        return
      }
    } else {
      // 如果角色版本不是2、3或4，直接设置为重新开始
      chatEventsStore.setShouldRestart(true)
    }

    // 触发播放事件
    if (actorToUse) {
      emit('play', actorToUse)
    }

    // 优化：在路由跳转前预热聊天资源
    try {
      // 预热聊天store，提前初始化状态
      if (storyDetail.value.story?.version === '2' || storyDetail.value.story?.version === '3') {
        const chatEventsStore = useChatEventsStore()
        // 预设基本信息，减少初始化时间
        chatEventsStore.currentActorId = actorToUse.id
      }

      // 预加载关键资源（角色预览图等）
      if (actorToUse.preview_url) {
        const img = new Image()
        img.src = actorToUse.preview_url
      }
    } catch (error) {
      console.warn('预热聊天资源失败:', error)
    }

    // 导航到聊天页面
    router.push({
      name: ChatType,
      params: {
        storyId: storyDetail.value.story?.id,
        actorId: actorToUse.id
      }
    })
  } catch (error) {
    // 如果是用户主动取消的请求，不显示错误
    if (error instanceof Error && error.name === 'AbortError') {
      console.log('Request was cancelled by user')
      return
    }
    console.error('Failed to start game:', error)
  } finally {
    isLoading.value = false
    abortController.value = null
    if (!showDialog.value) {
      handleClose()
    }
  }
}

// 获取聊天历史记录
const getChatHistory = async (actor?: Actor) => {
  try {
    const actorId = (route.params?.actorId as string) || actor?.id || selectedActor.value?.id
    const storyId = (route.params?.storyId as string) || storyDetail.value.story?.id

    const versionToUse = storyDetail.value.story?.version

    const { data } = await getUserChatHistory(storyId, actorId, versionToUse)

    console.log('Chat history check result:', {
      storyId,
      actorId,
      storyVersion: storyDetail.value.story?.version,
      actorVersion: actor?.version,
      versionUsed: versionToUse,
      hasHistory: data.isOk && data.data.history?.length > 0,
      historyLength: data.data.history?.length
    })

    if (data.isOk && data.data.history?.length > 0) {
      chatEventsStore.setShouldRestart(false)
      showDialog.value = true
    } else {
      chatEventsStore.setShouldRestart(true)
    }
  } catch (error) {
    console.error('Failed to check chat history:', error)
    throw error
  }
}

// 初始化角色选择
const initializeActorSelection = () => {
  // 如果没有选中的角色，默认选择第一个角色
  if (!selectedActor.value && storyDetail.value?.story?.actors?.length > 0) {
    selectedActor.value = storyDetail.value.story.actors[0]
  }
}

// 监听故事ID变化，加载故事详情
watch(
  () => props.storyId,
  async (newStoryId) => {
    if (newStoryId && props.visible) {
      reportEvent(ReportEvent.StoryDetailPageView, {
        storyId: newStoryId
      })
      isLoading.value = true

      // 创建新的 AbortController 用于取消故事详情加载
      abortController.value = new AbortController()

      try {
        storyDetail.value = await storyStore.getStoreDetail(newStoryId, false)

        // 初始化角色选择
        initializeActorSelection()

        isFavorited.value = storyDetail.value?.story?.is_fav || false

        // 更新SEO信息
        if (storyDetail.value?.story) {
          setDynamicSEO('StoryIntro', { story: storyDetail.value.story })
        }

        // 故事详情加载完成后，延迟检查滚动位置
        // 确保角色列表已完全渲染
        setTimeout(() => {
          checkScrollPosition()
        }, 300)
      } catch (error) {
        // 如果是用户主动取消的请求，不显示错误
        if (error instanceof Error && error.name === 'AbortError') {
          console.log('Story detail loading was cancelled by user')
          return
        }
        console.error('Failed to load story details:', error)
      } finally {
        isLoading.value = false
        abortController.value = null
      }
    }
  },
  { immediate: true }
)

// 监听弹窗可见性变化
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible && storyDetail.value?.story) {
      // 弹窗打开时，确保有角色被选中
      initializeActorSelection()
    }
  }
)

// ESC 键关闭弹窗
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    handleClose()
  }
}

// 初始化滚动检测和键盘事件
onMounted(() => {
  if (characterListRef.value) {
    characterListRef.value.addEventListener('scroll', checkScrollPosition)

    // 初始检查 - 使用多个延迟时间检查，确保DOM完全渲染后能正确检测
    setTimeout(checkScrollPosition, 100)
    setTimeout(checkScrollPosition, 300)
    setTimeout(checkScrollPosition, 500)
  }

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

// 监听角色列表变化
watch(
  () => storyDetail.value.story?.actors,
  () => {
    // 角色列表变化后，延迟检查滚动位置
    setTimeout(() => {
      checkScrollPosition()
    }, 300)
  }
)

// 清理事件监听
onUnmounted(() => {
  if (characterListRef.value) {
    characterListRef.value.removeEventListener('scroll', checkScrollPosition)
  }

  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeydown)

  // 清理 AbortController
  if (abortController.value) {
    abortController.value.abort()
    abortController.value = null
  }
})

// 处理继续游戏
const handleContinue = () => {
  showDialog.value = false

  if (!currentActor.value) return

  chatEventsStore.setShouldRestart(false)

  // 根据版本决定路由
  let ChatType = 'Chat'
  if (storyDetail.value.story?.version === '3') {
    ChatType = 'Chat3'
  } else if (currentActor.value?.version === '4' || currentActor.value?.version === '5') {
    ChatType = 'Chat4'
  } else if (currentActor.value?.version === '2') {
    ChatType = 'Chat2'
  }

  // 导航到聊天页面
  router.push({
    name: ChatType,
    params: {
      storyId: storyDetail.value.story?.id,
      actorId: currentActor.value.id
    }
  })
}

// 处理重新开始游戏
const handleRestart = () => {
  if (!currentActor.value) {
    showDialog.value = false
    return
  }

  chatEventsStore.setShouldRestart(true)
  showDialog.value = false

  // 触发播放事件
  emit('play', currentActor.value)

  // 根据版本决定路由
  let ChatType = 'Chat'
  if (storyDetail.value.story?.version === '3') {
    ChatType = 'Chat3'
  } else if (currentActor.value?.version === '4' || currentActor.value?.version === '5') {
    ChatType = 'Chat4'
  } else if (currentActor.value?.version === '2') {
    ChatType = 'Chat2'
  }

  // 导航到聊天页面
  router.push({
    name: ChatType,
    params: {
      storyId: storyDetail.value.story?.id,
      actorId: currentActor.value.id
    }
  })
}

// 处理对话框关闭
const handleDialogClose = (visible: boolean) => {
  if (visible) return

  showDialog.value = false
  isLoading.value = false // 重置加载状态
}
</script>

<style lang="less" scoped>
@import '@/assets/style/mixin.less';
@import '@/assets/style/theme.less';

.story-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;

  .close-button {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.15);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    transition: all 0.2s ease;
    z-index: 1010;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

    &:hover {
      background: rgba(255, 255, 255, 0.25);
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }

    // 亮色主题适配
    body.light-theme & {
      background: rgba(0, 0, 0, 0.1);
      color: var(--text-primary);
      border: 1px solid var(--border-color);
      box-shadow: 0 2px 8px var(--shadow-color);

      &:hover {
        background: rgba(0, 0, 0, 0.15);
      }
    }
  }
}

.modal-content {
  width: 800px;
  max-width: 90%;
  max-height: 90vh;
  background: linear-gradient(180deg, #2b1b3b 0%, #1a0f24 100%);
  border-radius: 24px;
  padding: 46px;
  animation: zoomIn 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 24px;
  position: relative;
  overflow: hidden;
  color: white;

  // 亮色主题适配
  body.light-theme & {
    background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    box-shadow: 0 10px 30px var(--shadow-color);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  // 亮色主题适配
  body.light-theme & {
    border-bottom: 1px solid var(--border-color);
  }
  padding-bottom: 16px;
  min-height: 80px;

  .header-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: 100%;
    flex: 1;
    justify-content: center;
  }

  h2 {
    font-size: 24px;
    font-weight: 700;
    margin: 0;

    // 亮色主题适配
    body.light-theme & {
      color: var(--text-primary);
    }
  }

  .story-cost {
    display: flex;
    align-items: center;
  }

  .cost-badge,
  .unlocked-badge,
  .free-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 600;
  }

  .cost-badge {
    background: rgba(0, 0, 0, 0.2);
    color: #daff96;

    .diamond-icon {
      width: 16px;
      height: 16px;
    }

    // 亮色主题适配
    body.light-theme & {
      background: rgba(0, 0, 0, 0.05);
      color: var(--accent-color);
    }
  }

  .unlocked-badge {
    background: rgba(218, 255, 150, 0.2);
    color: #daff96;

    // 亮色主题适配
    body.light-theme & {
      background: rgba(0, 0, 0, 0.05);
      color: var(--accent-color);
    }
  }

  .free-badge {
    background: rgba(218, 255, 150, 0.2);
    color: #daff96;

    // 亮色主题适配
    body.light-theme & {
      background: rgba(0, 0, 0, 0.05);
      color: var(--accent-color);
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .favorite-button {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    padding: 8px 12px;
    border-radius: 20px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    .heart-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      transform-origin: center;
      will-change: transform;
      color: rgba(255, 255, 255, 0.6);
      transition: color 0.2s ease;
    }

    .favorite-text {
      font-size: 14px;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.8);
      transition: color 0.2s ease;
    }

    &:hover .heart-icon {
      color: white;
    }

    &:hover .favorite-text {
      color: white;
    }

    &:active {
      transform: scale(0.98);
    }

    // 亮色主题适配
    body.light-theme & {
      &:hover {
        background-color: var(--bg-tertiary);
      }

      .heart-icon {
        color: var(--text-tertiary);
      }

      .favorite-text {
        color: var(--text-secondary);
      }

      &:hover .heart-icon {
        color: var(--text-primary);
      }

      &:hover .favorite-text {
        color: var(--text-primary);
      }
    }
  }
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.story-info {
  position: relative;

  .description {
    h3 {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 8px;

      // 亮色主题适配
      body.light-theme & {
        color: var(--text-primary);
      }
    }

    p {
      font-size: 16px;
      line-height: 1.6;
      color: rgba(255, 255, 255, 0.8);

      // 亮色主题适配
      body.light-theme & {
        color: var(--text-secondary);
      }
    }
  }
}

.cost-badge,
.unlocked-badge,
.free-badge {
  position: relative;
  right: 0;
  top: 0;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 16px;
  font-weight: 600;
  backdrop-filter: blur(4px);
}

.cost-badge {
  background: rgba(0, 0, 0, 0.6);
  color: #daff96;

  .diamond-icon {
    width: 20px;
    height: 20px;
  }
}

.unlocked-badge {
  background: rgba(218, 255, 150, 0.2);
  color: #daff96;
}

.free-badge {
  background: rgba(218, 255, 150, 0.2);
  color: #daff96;
}

.character-section {
  h3 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 16px;
  }
}

.character-carousel {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;

  .carousel-arrow {
    position: absolute;
    z-index: 10;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.1);
    padding: 0;
    color: white;
    transition: all 0.2s ease;

    &:disabled {
      opacity: 0.3;
      cursor: not-allowed;
    }

    &:not(:disabled):hover {
      background: rgba(255, 255, 255, 0.2);
    }

    &.prev {
      left: 0px;
    }

    &.next {
      right: 0px;
    }

    // 亮色主题适配
    body.light-theme & {
      background: rgba(0, 0, 0, 0.1);
      color: var(--text-primary);

      &:not(:disabled):hover {
        background: rgba(0, 0, 0, 0.2);
      }
    }
  }
}

.character-list {
  display: flex;
  overflow-x: auto;
  scroll-behavior: smooth;
  padding: 10px 0;
  gap: 20px;
  width: 100%;

  // 隐藏滚动条
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.character-card {
  cursor: pointer;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 280px;
  flex: 0 0 180px; // 固定宽度，不允许缩放

  &:hover {
    transform: translateY(-8px);
  }

  &.is-selected {
    z-index: 1;

    .card-content {
      border-color: #ca93f2;
    }

    .character-name {
      color: #ca93f2;
    }

    // 亮色模式下的选中样式
    body.light-theme & {
      .card-content {
        border-color: var(--accent-color);
      }

      .character-name {
        color: var(--accent-color);
      }
    }
  }

  .card-content {
    width: 100%;
    height: 100%;
    border-radius: 20px;
    overflow: hidden;
    position: relative;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.05);

    // 亮色主题适配
    body.light-theme & {
      background: var(--bg-card);
      border: 1px solid var(--border-color);
      box-shadow: 0 4px 8px var(--shadow-color);
    }
  }

  .preview-url {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }

  .character-info {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 16px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0));
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .character-name {
    font-size: 20px;
    font-weight: 600;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: color 0.3s ease;
    margin-bottom: 2px;

    // 亮色主题适配 - 保持白色，因为角色卡片上的文字需要在图片上清晰可见
    body.light-theme & {
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }
  }

  .actor-cost-badge,
  .actor-unlocked-badge,
  .actor-free-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    backdrop-filter: blur(4px);
  }

  .actor-cost-badge {
    background: rgba(0, 0, 0, 0.6);
    color: #daff96;

    .diamond-icon {
      width: 12px;
      height: 12px;
    }
  }

  .actor-unlocked-badge {
    background: rgba(218, 255, 150, 0.2);
    color: #daff96;
  }

  .actor-free-badge {
    background: rgba(218, 255, 150, 0.2);
    color: #daff96;
  }
}

.character-attributes {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
  margin-bottom: 16px;

  .attribute-item {
    flex: 1;
    text-align: center;
    padding: 16px 8px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    margin: 0 6px;

    // 亮色主题适配
    body.light-theme & {
      background: rgba(0, 0, 0, 0.03);
    }

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }

    .attribute-label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.6);
      margin-bottom: 8px;

      // 亮色主题适配
      body.light-theme & {
        color: #ca93f2;
      }
    }

    .attribute-value {
      font-size: 16px;
      color: white;
      font-weight: 500;

      // 亮色主题适配
      body.light-theme & {
        color: var(--text-primary);
      }
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 16px;
}

.cancel-button {
  padding: 12px 24px;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
  min-width: 180px;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }

  // 亮色主题适配
  body.light-theme & {
    background: rgba(0, 0, 0, 0.05);
    color: var(--text-primary);

    &:hover {
      background: rgba(0, 0, 0, 0.1);
    }
  }
}

.play-button {
  padding: 12px 32px;
  border-radius: 24px;
  background: #ca93f2;
  border: none;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 180px;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(202, 147, 242, 0.3);
  }

  &:not(:disabled):active {
    transform: translateY(0);
  }

  .cost {
    display: flex;
    align-items: center;
    gap: 4px;
    // background: rgba(36, 29, 73, 0.2);
    padding: 4px 10px;
    border-radius: 16px;
    margin-left: 4px;

    .diamond-icon {
      width: 16px;
      height: 16px;
    }
  }

  .play-button-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 0.8s linear infinite;
    flex-shrink: 0; // 防止spinner被压缩
  }

  .play-button-loading-text {
    font-size: 14px;
    opacity: 0.8;
    line-height: 20px; // 与spinner高度保持一致
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 骨架屏样式
.skeleton {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 25%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 8px;

  // 亮色主题适配
  body.light-theme & {
    background: linear-gradient(
      90deg,
      rgba(0, 0, 0, 0.05) 25%,
      rgba(0, 0, 0, 0.1) 50%,
      rgba(0, 0, 0, 0.05) 75%
    );
    background-size: 200% 100%;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// 具体骨架屏元素样式
.skeleton-title {
  height: 32px;
  width: 60%;
  margin-bottom: 12px;
}

.skeleton-badge {
  height: 24px;
  width: 80px;
  border-radius: 16px;
}

.skeleton-description {
  height: 60px;
  width: 100%;
  border-radius: 8px;
}

.skeleton-card {
  pointer-events: none;

  .skeleton-image {
    width: 100%;
    height: 100%;
    border-radius: 20px;
  }

  .skeleton-name {
    height: 20px;
    width: 80%;
    margin-bottom: 4px;
  }

  .skeleton-actor-badge {
    height: 16px;
    width: 60px;
    border-radius: 12px;
  }
}

.skeleton-attributes {
  .skeleton-attr-label {
    height: 14px;
    width: 70%;
    margin-bottom: 8px;
  }

  .skeleton-attr-value {
    height: 16px;
    width: 50%;
  }
}
</style>
