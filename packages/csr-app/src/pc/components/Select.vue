<template>
  <div class="custom-select" :class="{ active: isOpen }">
    <div class="select-trigger" ref="triggerRef" @click="toggleDropdown">
      <span class="selected-text">{{ selectedLabel }}</span>
      <div class="select-arrow" :class="{ open: isOpen }">
        <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
          <path
            d="M1 1.5L6 6.5L11 1.5"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
          />
        </svg>
      </div>
    </div>

    <Teleport to="body">
      <Transition name="slide-fade">
        <div v-if="isOpen" class="select-dropdown" :style="dropdownStyle">
          <div
            v-for="option in options"
            :key="option.value"
            class="select-option"
            :class="{ active: modelValue === option.value }"
            @click="selectOption(option.value)"
          >
            {{ option.label }}
          </div>
        </div>
      </Transition>
    </Teleport>

    <!-- Backdrop for closing dropdown -->
    <div v-if="isOpen" class="select-backdrop" @click="closeDropdown" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

interface Option {
  label: string
  value: string
}

interface DropdownStyle {
  top: string
  left: string
  width: string
  transform?: string
}

const props = defineProps<{
  modelValue: string
  options: Option[]
  placeholder?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

const isOpen = ref(false)
const triggerRef = ref<HTMLElement | null>(null)
const dropdownStyle = ref<DropdownStyle>({ top: '0px', left: '0px', width: '0px' })

const selectedLabel = computed(() => {
  const selected = props.options.find((opt) => opt.value === props.modelValue)
  return selected?.label || props.placeholder || 'Select option'
})

const updateDropdownPosition = () => {
  if (triggerRef.value) {
    const rect = triggerRef.value.getBoundingClientRect()
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft

    dropdownStyle.value = {
      top: `${rect.bottom + scrollTop + 4}px`,
      left: `${rect.left + scrollLeft}px`,
      width: `${rect.width}px`,
      transform: 'translateY(0)'
    }
  }
}

const toggleDropdown = () => {
  if (!isOpen.value) {
    // 先设置位置，再打开
    updateDropdownPosition()
  }
  isOpen.value = !isOpen.value
}

const closeDropdown = () => {
  isOpen.value = false
}

const selectOption = (value: string) => {
  emit('update:modelValue', value)
  closeDropdown()
}

onMounted(() => {
  window.addEventListener('resize', updateDropdownPosition)
  window.addEventListener('scroll', updateDropdownPosition)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateDropdownPosition)
  window.removeEventListener('scroll', updateDropdownPosition)
})
</script>

<style lang="less" scoped>
.custom-select {
  position: relative;
  width: 100%;
  color: rgba(255, 255, 255, 0.95);
  user-select: none;
  z-index: 1;

  &.active {
    z-index: 100;
  }
}

.select-trigger {
  width: 100%;
  height: 42px;
  padding: 0 16px;
  border: 1px solid rgba(184, 196, 255, 0.1);
  background: rgba(204, 213, 255, 0.05);
  color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;

  &:hover {
    border-color: #ca93f2;
    background: rgba(204, 213, 255, 0.08);
  }

  .selected-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .select-arrow {
    margin-left: 8px;
    color: rgba(255, 255, 255, 0.5);
    transition: transform 0.2s ease;

    &.open {
      transform: rotate(180deg);
      color: #ca93f2;
    }

    svg {
      display: block;
    }
  }
}

.select-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 98;
}

.select-dropdown {
  position: fixed;
  background: #1f0038;
  border: 1px solid rgba(184, 196, 255, 0.1);
  border-radius: 8px;
  padding: 4px 0;
  z-index: 9999;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  max-height: 240px;
  overflow-y: auto;
  transform-origin: top;
  will-change: transform;
}

.select-option {
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: rgba(255, 255, 255, 0.95);

  &:hover {
    background: rgba(204, 213, 255, 0.05);
  }

  &.active {
    background: rgba(202, 147, 242, 0.15);
    color: #ca93f2;
  }
}

// Transition animations
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  transform: translateY(-8px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateY(-8px);
  opacity: 0;
}
</style>
