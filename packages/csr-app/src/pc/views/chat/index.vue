<template>
  <div class="pc-chat-container">
    <!-- 全屏聊天区域 -->
    <div class="chat-area">
      <!-- 聊天区域背景图 -->
      <div
        class="chat-area-background"
        :style="{ backgroundImage: `url(${fullBackgroundImage})` }"
      ></div>

      <!-- 顶部Logo -->
      <div v-if="!showCollapsedMenu" class="chat-logo" @click="handleLogoClick">
        <img
          :src="logoUrl"
          :alt="`${websiteTitle} logo`"
          loading="eager"
          width="142"
          height="31"
          role="img"
        />
      </div>

      <!-- 菜单切换按钮 -->
      <div
        class="menu-toggle-btn"
        :class="{ 'menu-expanded': showCollapsedMenu }"
        @click="toggleCollapsedMenu"
      >
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
          <path
            opacity="0.7"
            d="M1.08 2.16C0.483538 2.16 -1.95159e-06 1.67646 -2.00373e-06 1.08C-2.05587e-06 0.483542 0.483537 2.05587e-06 1.08 2.00373e-06L22.92 9.44166e-08C23.5165 4.22724e-08 24 0.48354 24 1.08C24 1.67646 23.5165 2.16 22.92 2.16L1.08 2.16ZM1.08 9.45C0.483538 9.45 -1.31427e-06 8.96646 -1.36642e-06 8.37C-1.41856e-06 7.77354 0.483538 7.29 1.08 7.29L14.16 7.29C14.7565 7.29 15.24 7.77354 15.24 8.37C15.24 8.96646 14.7565 9.45 14.16 9.45L1.08 9.45ZM17.7374 16.4577C17.6738 16.4577 17.6127 16.4324 17.5677 16.3874C17.5227 16.3424 17.4974 16.2814 17.4974 16.2177L17.4974 7.7823C17.4974 7.73835 17.5095 7.69524 17.5323 7.65767C17.5551 7.62011 17.5878 7.58953 17.6268 7.56928C17.6658 7.54902 17.7097 7.53987 17.7535 7.54281C17.7974 7.54576 17.8396 7.56069 17.8755 7.58598L23.871 11.8037C23.9025 11.8258 23.9282 11.8552 23.9459 11.8894C23.9636 11.9236 23.9729 11.9615 23.9729 12C23.9729 12.0385 23.9636 12.0764 23.9459 12.1106C23.9282 12.1447 23.9025 12.1741 23.871 12.1963L17.8755 16.414C17.8351 16.4424 17.7869 16.4577 17.7374 16.4577L17.7374 16.4577ZM1.08 16.71C0.483539 16.71 -6.79584e-07 16.2265 -7.31729e-07 15.63C-7.83873e-07 15.0335 0.483539 14.55 1.08 14.55L14.16 14.55C14.7565 14.55 15.24 15.0335 15.24 15.63C15.24 16.2265 14.7565 16.71 14.16 16.71L1.08 16.71ZM1.08 24C0.48354 24 -4.22724e-08 23.5165 -9.44166e-08 22.92C-1.46561e-07 22.3235 0.483539 21.84 1.08 21.84L22.92 21.84C23.5165 21.84 24 22.3235 24 22.92C24 23.5165 23.5165 24 22.92 24L1.08 24Z"
            fill="white"
          />
        </svg>
      </div>

      <!-- 收起的菜单 -->
      <div v-if="showCollapsedMenu" class="collapsed-menu">
        <!-- 顶部Logo区域 -->
        <div class="menu-logo" @click="handleLogoClick">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="38"
            height="34"
            viewBox="0 0 38 34"
            fill="none"
          >
            <path
              d="M8.7832 1.88281L8.92188 1.8916C9.0602 1.90917 9.19464 1.95259 9.31738 2.02051L24.8232 10.6006L24.9404 10.6758C25.0047 10.7228 25.0622 10.7771 25.1143 10.8359V9.10645C25.1145 8.49735 25.6087 8.00409 26.2178 8.00391H35C35.6093 8.00391 36.1033 8.49724 36.1035 9.10645V31.0146C36.1034 31.624 35.6094 32.1182 35 32.1182H29.2168C29.0766 32.1181 28.938 32.0916 28.8086 32.04L28.6826 31.9805L13.1768 23.3994C13.0651 23.3376 12.9679 23.257 12.8857 23.1641V24.8936C12.8857 25.5029 12.3915 25.9969 11.7822 25.9971H3C2.39058 25.9971 1.89648 25.503 1.89648 24.8936V2.98633C1.89652 2.37694 2.39061 1.88282 3 1.88281H8.7832ZM23.6357 21.957L25.1689 22.6738V20.9648L23.6357 21.957ZM25.0596 12.3564L24.9541 12.4473L12.8857 21.5557V21.7041C12.9339 21.6496 12.9867 21.5984 13.0459 21.5537L25.1143 12.4443V12.2959C25.0963 12.3162 25.0791 12.3374 25.0596 12.3564ZM12.8311 13.0352L14.3633 12.043L12.8311 11.3262V13.0352Z"
              fill="#CA93F2"
              stroke="#1F0038"
              stroke-width="2.2069"
              stroke-linejoin="round"
            />
            <rect
              x="5.20703"
              y="6.29688"
              width="2.2069"
              height="9.93103"
              rx="1.10345"
              fill="white"
            />
          </svg>
        </div>

        <!-- 菜单项区域 -->
        <div class="menu-items">
          <div
            class="menu-item"
            :class="{ active: route.path === '/' || route.path === '/stories' }"
            @click="handleMenuItemClick('/')"
          >
            <svg viewBox="0 0 22 20" fill="none">
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M21.7341 9.32921L11.9676 0.376785C11.7032 0.134439 11.3576 0 10.9989 0C10.6403 0 10.2947 0.134439 10.0303 0.376785L0.264829 9.32921C0.143469 9.44076 0.0585832 9.58632 0.0212672 9.74688C-0.0160489 9.90743 -0.00405933 10.0755 0.0556686 10.2291C0.115396 10.3828 0.220084 10.5148 0.356049 10.608C0.492013 10.7012 0.652929 10.7512 0.817764 10.7515C1.35104 10.7515 1.78335 11.1838 1.78335 11.7171V17.9192C1.78335 18.1881 1.83632 18.4544 1.93924 18.7029C2.04216 18.9513 2.193 19.1771 2.38317 19.3672C2.57334 19.5574 2.7991 19.7083 3.04756 19.8112C3.29602 19.9141 3.56232 19.9671 3.83126 19.9671H18.1666C18.4355 19.9671 18.7018 19.9141 18.9503 19.8112C19.1988 19.7083 19.4245 19.5574 19.6147 19.3672C19.8049 19.1771 19.9557 18.9513 20.0586 18.7029C20.1615 18.4544 20.2145 18.1881 20.2145 17.9192V11.7181C20.2145 11.1844 20.6474 10.7519 21.1811 10.7525C21.3462 10.7524 21.5074 10.7025 21.6436 10.6093C21.7798 10.516 21.8846 10.3838 21.9444 10.23C22.0042 10.0761 22.0161 9.9078 21.9785 9.74706C21.941 9.58633 21.8558 9.44068 21.7341 9.32921ZM6.95868 11.8423C6.92923 11.8398 6.89964 11.8385 6.86998 11.8385C6.73542 11.8385 6.60217 11.865 6.47787 11.9165C6.35356 11.968 6.24063 12.0436 6.14553 12.1388C5.95357 12.3308 5.84573 12.5912 5.84573 12.8627C5.84573 13.1342 5.95357 13.3946 6.14553 13.5866C7.26926 14.7104 8.75002 15.3949 10.3212 15.5299C10.5125 15.5464 10.7051 15.5548 10.8986 15.5548C12.6806 15.5548 14.3896 14.8469 15.6497 13.5868C15.8363 13.3937 15.9395 13.135 15.9371 12.8666C15.9348 12.5981 15.8271 12.3413 15.6373 12.1514C15.4657 11.9799 15.2395 11.8754 14.9994 11.8551C14.9732 11.8529 14.9467 11.8516 14.9202 11.8514C14.6517 11.849 14.3931 11.9523 14.2 12.1388C13.3239 13.0146 12.1359 13.5066 10.8972 13.5066C10.7644 13.5066 10.6323 13.501 10.501 13.4898C9.40812 13.3964 8.37801 12.9205 7.59633 12.1389C7.50124 12.0437 7.38831 11.9682 7.264 11.9167C7.1665 11.8763 7.0635 11.8512 6.95868 11.8423Z"
                fill="currentColor"
              />
            </svg>
          </div>
          <div
            class="menu-item"
            :class="{ active: route.path === '/user/profile' }"
            @click="handleMenuItemClick('/user/profile')"
          >
            <svg viewBox="0 0 22 22" fill="none">
              <path
                d="M11 0C4.92422 0 0 4.92422 0 11C0 17.0758 4.92422 22 11 22C17.0758 22 22 17.0758 22 11C22 4.92422 17.0758 0 11 0ZM11 5.5C12.7089 5.5 14.0937 6.88531 14.0937 8.59375C14.0937 10.3022 12.7102 11.6875 11 11.6875C9.29156 11.6875 7.90625 10.3022 7.90625 8.59375C7.90625 6.88531 9.28984 5.5 11 5.5ZM11 19.25C8.72566 19.25 6.66445 18.3249 5.16914 16.8313C5.86523 15.0348 7.58398 13.75 9.625 13.75H12.375C14.4177 13.75 16.1365 15.0339 16.8309 16.8313C15.3355 18.3262 13.273 19.25 11 19.25Z"
                fill="currentColor"
              />
            </svg>
          </div>
          <!-- <div class="menu-item" @click="handleMenuItemClick('/chat')">
            <svg viewBox="0 0 24 24" fill="none">
              <path
                d="M12 2C6.48 2 2 6.48 2 12C2 13.54 2.36 14.99 3 16.28V22L8.72 19.14C9.78 19.68 10.87 20 12 20C17.52 20 22 15.52 22 10C22 4.48 17.52 0 12 0V2ZM12 18C11.11 18 10.26 17.86 9.47 17.61L6 19V16.28C5.36 14.99 5 13.54 5 12C5 8.13 8.13 5 12 5S19 8.13 19 12S15.87 18 12 18Z"
                fill="currentColor"
              />
            </svg>
          </div> -->
        </div>

        <!-- 底部区域 -->
        <div class="menu-bottom">
          <!-- 签到按钮 -->
          <button
            class="daily-reward-btn"
            @click="handleDailyReward"
            type="button"
            :aria-label="
              checkinStore.canShowCheckin ? 'Claim daily reward' : 'Sign up to claim daily rewards'
            "
          >
            🎁
          </button>

          <!-- Discord按钮 -->
          <button class="discord-btn" @click="handleDiscord">
            <svg viewBox="0 0 20 16" fill="none">
              <path
                d="M16.942 1.556C15.6 0.956 14.178 0.537 12.694 0.333C12.678 0.33 12.661 0.338 12.653 0.353C12.5 0.653 12.33 1.053 12.212 1.369C10.636 1.181 9.069 1.181 7.525 1.369C7.407 1.046 7.231 0.653 7.077 0.353C7.069 0.339 7.052 0.331 7.036 0.333C5.553 0.536 4.131 0.955 2.788 1.556C2.781 1.559 2.775 1.564 2.771 1.571C0.407 5.156 -0.244 8.656 0.076 12.111C0.078 12.128 0.087 12.144 0.1 12.153C1.908 13.444 3.661 14.244 5.383 14.778C5.399 14.783 5.416 14.777 5.426 14.764C5.818 14.244 6.166 13.694 6.466 13.111C6.477 13.089 6.466 13.064 6.443 13.056C5.901 12.844 5.386 12.589 4.893 12.3C4.867 12.286 4.865 12.25 4.889 12.233C4.999 12.15 5.109 12.064 5.214 11.978C5.225 11.969 5.241 11.967 5.254 11.972C8.757 13.578 12.576 13.578 16.035 11.972C16.048 11.966 16.064 11.968 16.076 11.977C16.181 12.063 16.291 12.15 16.402 12.233C16.426 12.25 16.425 12.286 16.399 12.3C15.906 12.594 15.391 12.844 14.848 13.055C14.825 13.063 14.815 13.089 14.826 13.111C15.132 13.694 15.48 14.244 15.866 14.764C15.876 14.777 15.893 14.783 15.909 14.778C17.64 14.244 19.393 13.444 21.201 12.153C21.215 12.144 21.223 12.129 21.225 12.112C21.612 8.156 20.644 4.689 16.967 1.572C16.964 1.564 16.958 1.559 16.942 1.556ZM7.159 9.944C6.097 9.944 5.22 8.956 5.22 7.722C5.22 6.489 6.08 5.5 7.159 5.5C8.247 5.5 9.115 6.497 9.098 7.722C9.098 8.956 8.238 9.944 7.159 9.944ZM14.133 9.944C13.071 9.944 12.194 8.956 12.194 7.722C12.194 6.489 13.054 5.5 14.133 5.5C15.221 5.5 16.089 6.497 16.072 7.722C16.072 8.956 15.221 9.944 14.133 9.944Z"
                fill="currentColor"
              />
            </svg>
          </button>
        </div>
      </div>

      <!-- 移动端聊天组件容器（PC端复用） -->
      <div class="mobile-chat-wrapper">
        <div
          class="mobile-chat-container pc-adapted"
          :class="{ 'chat4-adapted': chatVersion === 'chat4' }"
        >
          <component
            :is="chatComponent"
            v-if="chatComponent"
            :characterId="characterId"
            :storyId="storyId"
            ref="mobileComponentRef"
            :key="`${characterId}-${storyId}-${shouldReloadComponent}`"
          />

          <!-- PC端专用底部输入组件 -->
          <!-- <div class="pc-chat-bottom-overlay" v-show="shouldShowPCChatBottom">
            <PCChatBottom @send-message="handleSendMessage" @toggle-history="handleToggleHistory" />
          </div> -->
        </div>
      </div>
    </div>

    <!-- 故事详情弹窗 -->
    <StoryDetailModal
      v-model:visible="showStoryDetailModal"
      :story-id="selectedStoryId"
      @play="handlePlayStory"
    />

    <!-- PC签到弹窗 -->
    <CheckinModal />

    <!-- PC登录弹窗 -->
    <LoginModal v-model:visible="loginModalVisible" :title="loginModalTitle" />

    <!-- 离开确认弹窗 -->
    <LeaveConfirmModal
      v-model:visible="showLeaveConfirmModal"
      @confirm="handleLeaveConfirm"
      @cancel="handleLeaveCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, defineAsyncComponent } from 'vue'
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router'
import { useStoryStore } from '@/store'
import { useUserStore } from '@/store/user'
import { useCheckinStore } from '@/store/checkin'
import { useChatResourcesStore } from '@/store/chat-resources'
import { useChatEventsStore } from '@/store/chat-events'
import { useChatUIStore } from '@/store/chat-ui'
import { useChatMessagesStore } from '@/store/chat-messages'

import StoryDetailModal from '@/pc/components/StoryDetailModal.vue'
import PCChatBottom from '@/pc/components/chat/PCChatBottom.vue'
import CheckinModal from '@/pc/components/CheckinModal.vue'
import LoginModal from '@/pc/components/LoginModal.vue'
import LeaveConfirmModal from '@/mobile/components/LeaveConfirmModal.vue'
import ResourceLoader from '@/shared/components/ResourceLoader.vue'
import type { Actor } from '@/api/stories'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'

// Props
defineProps<{
  characterId?: string
  storyId?: string
}>()

// Store
const route = useRoute()
const router = useRouter()
const storyStore = useStoryStore()
const userStore = useUserStore()
const checkinStore = useCheckinStore()
const chatResourcesStore = useChatResourcesStore()
const chatEventsStore = useChatEventsStore()
const chatUIStore = useChatUIStore()
const chatMessagesStore = useChatMessagesStore()

// 获取完整背景图（用于聊天区域和右侧区域）
const fullBackgroundImage = computed(() => {
  return (
    chatResourcesStore.backgroundImage ||
    storyStore.currentActor?.preview_url ||
    'https://cdn.magiclight.ai/assets/playshot/default-bg.jpg'
  )
})

// Refs
const mobileComponentRef = ref(null)
const shouldReloadComponent = ref(false)

// 故事详情弹窗相关状态
const showStoryDetailModal = ref(false)
const selectedStoryId = ref('')

// 菜单相关状态
const showCollapsedMenu = ref(false)

// 登录弹窗状态
const loginModalVisible = ref(false)
const loginModalTitle = ref('')

// 离开确认弹窗状态
const showLeaveConfirmModal = ref(false)

// 从环境变量获取网站标题和Logo URL
const websiteTitle = computed(() => import.meta.env.VITE_WEBSITE_TITLE || 'Playshot')
const logoUrl = computed(
  () => import.meta.env.VITE_LOGO_URL || 'https://cdn.magiclight.ai/assets/playshot/logo-v2.png'
)

// 根据路由参数确定使用哪个聊天组件版本
const chatVersion = computed(() => {
  const path = route.path
  if (path.startsWith('/chat4/')) {
    return 'chat4'
  } else if (path.startsWith('/chat3/')) {
    return 'chat3'
  } else if (path.startsWith('/chat2/')) {
    return 'chat2'
  } else {
    return 'chat'
  }
})

// 动态导入对应版本的聊天组件（移动端组件，PC端复用）
const chatComponent = computed(() => {
  // 使用shouldReloadComponent来强制重新计算
  shouldReloadComponent.value // 仅用于触发依赖收集，不实际使用值
  if (chatVersion.value === 'chat4') {
    return defineAsyncComponent(() => import('@/mobile/views/chat4/index.vue'))
  } else if (chatVersion.value === 'chat3') {
    return defineAsyncComponent(() => import('@/mobile/views/chat3/index.vue'))
  } else if (chatVersion.value === 'chat2') {
    return defineAsyncComponent(() => import('@/mobile/views/chat2/index.vue'))
  } else {
    return defineAsyncComponent(() => import('@/mobile/views/chat/index.vue'))
  }
})

// PC端底部输入组件显示逻辑（与移动端完全同步）
const shouldShowPCChatBottom = computed(() => {
  // 基础显示条件：不在overlay状态、不在结束状态、没有动画图片序列、输入框未被隐藏
  const basicConditions =
    !chatUIStore.overlay &&
    !chatUIStore.isEnding &&
    !chatResourcesStore.animatedImages?.length &&
    !chatUIStore.hideInput &&
    chatVersion.value !== 'chat' &&
    !chatResourcesStore.isPlayingVideo

  // 如果基础条件不满足，直接返回false
  if (!basicConditions) {
    return false
  }

  // Chat2版本的特殊条件
  if (chatVersion.value === 'chat2') {
    // 如果是legacy版本，检查更多条件
    if (chatUIStore.isLegacyVersion) {
      // 参考移动端Chat版本的显示条件
      const canShowChatInput =
        !chatMessagesStore.messageTypingPromise || chatMessagesStore.messageTypingPromise === null
      return basicConditions && canShowChatInput && !chatMessagesStore.isActorThinking
    }

    // 如果不是legacy版本，需要检查心灵感应是否完成
    const isTelepathyComplete = chatMessagesStore.heartValue >= 100
    return basicConditions && !isTelepathyComplete
  }

  // Chat4版本的特殊条件 - 直播场景不需要底部输入组件
  if (chatVersion.value === 'chat4') {
    return false // Chat4 是直播场景，不显示PC端底部输入组件
  }

  // Chat和Chat3版本的条件（参考移动端Chat版本）
  const canShowChatInput =
    !chatMessagesStore.messageTypingPromise || chatMessagesStore.messageTypingPromise === null

  return basicConditions && canShowChatInput && !chatMessagesStore.isActorThinking
})

// 菜单相关方法
const toggleCollapsedMenu = () => {
  showCollapsedMenu.value = !showCollapsedMenu.value
}

const handleMenuItemClick = (path: string) => {
  router.push(path)
  showCollapsedMenu.value = false
}

// 处理Logo点击
const handleLogoClick = () => {
  // 检查是否需要显示离开确认弹窗
  if (route.path.startsWith('/chat') && !chatEventsStore.isConfirmedLeave) {
    nextCallback = () => {
      console.log('用户确认离开，执行跳转')
      chatEventsStore.clearChat()
      router.push('/')

      // 上报聊天时长事件
      reportEvent(ReportEvent.ChatDuration, {
        characterId: route.params.actorId,
        userId: userStore.userInfo?.uuid,
        isInChat: true,
        actorId: route.params.actorId
      })
    }
    showLeaveConfirmModal.value = true
  } else {
    router.push('/')
  }
}

// 显示登录弹窗
const showLoginModal = (title?: string) => {
  loginModalTitle.value = title || 'Sign in to continue!'
  loginModalVisible.value = true
}

// 处理签到
const handleDailyReward = () => {
  if (checkinStore.canShowCheckin) {
    handleShowCheckin()
  } else {
    showLoginModal('Sign up to claim your rewards!')
  }
  showCollapsedMenu.value = false
}

// 显示签到弹窗
const handleShowCheckin = () => {
  reportEvent(ReportEvent.ClickProfilePageDailyTasks, {
    userId: userStore.userInfo?.uuid
  })
  // 显示签到弹窗而不是跳转页面
  checkinStore.showModal()
}

// 处理Discord
const handleDiscord = () => {
  // 上报事件
  reportEvent(ReportEvent.SocialButtonJoinClick, {
    type: 'discord'
  })
  // 打开Discord链接
  window.open('https://discord.gg/FdZJmU4a8x', '_blank')
  showCollapsedMenu.value = false
}

// 离开确认相关方法
let nextCallback: any = null

const handleLeaveConfirm = () => {
  showLeaveConfirmModal.value = false
  if (nextCallback) {
    // 清理聊天状态
    chatEventsStore.clearChat()
    nextCallback()

    // 上报聊天时长事件
    reportEvent(ReportEvent.ChatDuration, {
      characterId: route.params.actorId,
      userId: userStore.userInfo?.uuid,
      isInChat: true,
      actorId: route.params.actorId
    })
  }
}

const handleLeaveCancel = () => {
  showLeaveConfirmModal.value = false
  nextCallback = null
}

// 处理开始游戏
const handlePlayStory = (actor: Actor) => {
  // 上报事件
  reportEvent(ReportEvent.ClickToPlayInStoryIntro, {
    storyId: storyStore.currentStory?.id,
    actorId: actor.id
  })

  // 设置当前角色
  storyStore.setCurrentActor(actor)

  // 关闭弹窗
  showStoryDetailModal.value = false

  // 根据当前聊天版本构建路由
  const chatVersionPath = chatVersion.value
  const actorId = actor.id
  const storyId = storyStore.currentStory?.id

  // 导航到对应的聊天页面
  if (storyId) {
    router.push(`/${chatVersionPath}/${storyId}/${actorId}`).then(() => {
      // 切换shouldReloadComponent的值，触发组件重新加载
      shouldReloadComponent.value = !shouldReloadComponent.value
    })
  }
}

// PC端底部输入组件事件处理
const handleSendMessage = async (message: string) => {
  await chatEventsStore.sendMessage(message)
}

const handleToggleHistory = () => {
  // 通过ref调用移动端组件的历史记录功能
  if (mobileComponentRef.value && typeof mobileComponentRef.value.toggleHistory === 'function') {
    mobileComponentRef.value.toggleHistory()
  }
}

// 生命周期钩子
onMounted(async () => {
  // 添加PC聊天页面标识类
  document.body.classList.add('pc-chat-mode')

  // 添加一个全局CSS变量，用于在PC模式下覆盖ChatSection的计算逻辑
  document.documentElement.style.setProperty('--pc-chat-section-max-height', '80%')

  // 获取用户历史故事数据
  // await userStore.getUserPlayedStories()

  // 初始化签到信息（如果用户已登录且不是游客）
  if (userStore.isAuthenticated && !userStore.isGuest) {
    await checkinStore.fetchCheckinInfo()
  }
})

// 组件卸载前清理
onBeforeUnmount(() => {
  // 移除PC聊天页面标识类
  document.body.classList.remove('pc-chat-mode')

  // 移除全局CSS变量
  document.documentElement.style.removeProperty('--pc-chat-section-max-height')
})
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';
.pc-chat-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background-color: #000;
  position: relative;
  color: #fff;

  // 全屏聊天区域
  .chat-area {
    width: 100%;
    height: 100vh;
    position: relative;
    z-index: 1;

    // 背景图片
    .chat-area-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      opacity: 1;
      z-index: 0;
      filter: blur(32px);
    }

    // 顶部Logo
    .chat-logo {
      position: absolute;
      top: 32px;
      left: 40px;
      z-index: 10;
      cursor: pointer;
      transition: opacity 0.3s ease;

      &:hover {
        opacity: 0.8;
      }

      img {
        height: 31px;
        width: auto;
      }
    }

    // 菜单切换按钮
    .menu-toggle-btn {
      position: absolute;
      bottom: 32px;
      left: 32px;
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(20px);
      border: none;
      color: #fff;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      z-index: 20; // 确保在菜单之上

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.05);
      }

      // 当菜单展开时，按钮移到右边
      &.menu-expanded {
        left: calc(80px + 16px); // 菜单宽度 + 间距
        background: var(--bg-hover); // 使用主题悬停色

        &:hover {
          background: var(--accent-bg); // 使用主题强调色背景
        }

        // 图标旋转180度
        svg {
          transform: rotate(180deg);
        }
      }

      svg {
        width: 24px;
        height: 24px;
        transition: transform 0.3s ease; // 添加旋转过渡动画
      }
    }

    // 收起的菜单 - 适配亮色暗色主题
    .collapsed-menu {
      position: fixed;
      top: 0;
      left: 0;
      width: 80px; // 收起状态的宽度
      height: 100%;
      background-color: var(--sidebar-bg); // 使用主题变量
      backdrop-filter: blur(24px);
      border-right: 0.5px solid var(--border-color); // 使用主题边框色
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24px 0;
      animation: slideInLeft 0.3s ease-out;
      z-index: 15; // 高于聊天组件但低于模态框

      // 顶部Logo区域
      .menu-logo {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 32px;
        height: 32px;
        cursor: pointer;
        transition: opacity 0.3s ease;
        margin-bottom: 32px; // Logo下方间距

        &:hover {
          opacity: 0.8;
        }

        img {
          width: 32px;
          height: auto;
          max-height: 28px;
        }
      }

      // 菜单项区域 - 在顶部Logo下方
      .menu-items {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;

        .menu-item {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 58px;
          height: 50px;
          // background-color: var(--bg-tertiary); // 使用主题变量
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: var(--pc-sidebar-hover-bg); // 使用主题悬停色
          }

          &.active {
            background-color: var(--pc-sidebar-active-bg); // 使用主题激活色
            border-radius: 20px;

            svg {
              color: var(--pc-sidebar-active-text); // 激活状态使用强调色
            }
          }

          svg {
            width: 24px;
            height: 24px;
            color: var(--pc-sidebar-text); // 使用主题文字色
            transition: color 0.2s ease; // 添加颜色过渡
          }
        }
      }

      // 底部区域 - 固定在底部
      .menu-bottom {
        margin-top: auto; // 推到底部
        display: flex;
        flex-direction: column;
        gap: 12px;

        // 签到按钮
        .daily-reward-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 58px;
          height: 50px;
          background-color: var(--accent-bg); // 使用主题强调色背景
          border-radius: 20px;
          border: none;
          font-size: 24px;
          cursor: pointer;
          transition: all 0.2s ease;
          color: var(--text-primary); // 使用主题文字色

          &:hover {
            background-color: var(--accent-color); // 悬停时使用强调色
            opacity: 0.8;
          }
        }

        // Discord按钮
        .discord-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 58px;
          height: 50px;
          background-color: #6563ff; // Discord品牌色保持不变
          border-radius: 20px;
          border: none;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: #5451e6;
          }

          svg {
            width: 20px;
            height: 20px;
            color: white; // Discord图标保持白色
          }
        }
      }
    }

    // 移动端聊天组件容器（PC端复用）
    .mobile-chat-wrapper {
      width: 100%;
      height: 100%;
      position: relative;
      z-index: 5;
      display: flex;
      align-items: center;
      justify-content: center;

      .mobile-chat-container {
        position: relative;
        aspect-ratio: 9/16;
        height: 100vh; // 改为100vh，上下占满
        width: auto;
        max-width: calc(100vh * 9 / 16); // 相应调整最大宽度
        margin: 0 auto;
        z-index: 1;

        @supports not (aspect-ratio: 9/16) {
          width: calc(100vh * 9 / 16);
          max-width: calc(100vh * 9 / 16);
        }

        // PC端专用底部输入组件
        .pc-chat-bottom-overlay {
          position: absolute;
          bottom: 24px;
          left: 24px;
          right: 24px;
          z-index: 20;
          pointer-events: auto;
        }
      }
      // PC端适配样式
      .pc-adapted {
        // 隐藏移动端组件的某些元素

        :deep(.credit-display-container) {
          display: none !important;
        }

        :deep(.chat-top-bar) {
          .back-button {
            display: none;
          }
        }
        :deep(.chat-wrapper) {
          height: 100%;
        }
        :deep(.chat-container) {
          height: 100%;
        }

        // 修复ChatSection组件在PC端的定位问题
        :deep(.chat-section) {
          position: absolute !important;
          left: 0 !important;
          right: 0 !important;
          width: 100% !important;
          // bottom: 120px !important; // 为PC端底部输入组件预留空间
          // max-height: calc(100% - 120px) !important; // 限制最大高度，避免与底部输入重叠
          background: transparent;
          .input-section {
            padding-bottom: 0;
          }
          .sheet-container {
            position: absolute !important;
            bottom: 0 !important;
            left: 0 !important;
            right: 0 !important;
            width: 100% !important;
            height: 100% !important; // 确保占满可用空间
          }

          .task-tip-container {
            position: absolute !important;
            top: -84px !important;
            left: 0 !important;
            right: 0 !important;
            width: 100% !important;
          }
        }

        :deep(.chat-container),
        :deep(.message-list) {
          z-index: 2;
          // background-color: var(--pc-chat-area-overlay) !important;
          backdrop-filter: blur(10px);
        }

        // PC端底部输入区域样式覆盖 - 使用visibility隐藏保持布局

        // 确保消息容器不会超出边界
        :deep(.messages-wrapper) {
          height: 100% !important;
          overflow-y: auto !important;
          padding-bottom: 20px !important; // 适当的底部间距
        }
      }
    }
  }
}

// 动画
@keyframes thinking {
  0%,
  80%,
  100% {
    transform: scale(0.6);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

// 添加全局样式，确保在PC模式下ChatSection的计算逻辑被正确覆盖
:global(.pc-chat-mode) {
  // 覆盖ChatSection在PC模式下的计算逻辑
  --sheet-height: auto !important;

  // 确保在PC模式下，ChatSection的最大高度受限
  .chat-section {
    max-height: var(--pc-chat-section-max-height, 80%) !important;
  }
}
</style>
