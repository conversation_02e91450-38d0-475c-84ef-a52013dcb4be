<template>
  <div class="pc-profile-view">
    <div class="profile-container">
      <!-- 用户信息部分 -->
      <div class="user-info-section">
        <div class="user-info-card">
          <div class="user-avatar">
            <img
              :src="
                userStore.userInfo?.avatar_url ||
                'https://cdn.magiclight.ai/assets/playshot/default-avatar.png'
              "
              alt="avatar"
            />
          </div>
          <div class="user-details">
            <h2 class="username">{{ userStore.userInfo?.name }}</h2>
            <p class="uid">UID: {{ userStore.userInfo?.uuid?.slice(0, 7) }}</p>
          </div>
          <button class="settings-button" @click="handleSettings">personal settings</button>
        </div>
      </div>

      <!-- 内容标签页部分 -->
      <div class="content-section">
        <div class="tabs">
          <button
            class="tab"
            :class="{ active: activeTab === 'history' }"
            @click="handleTabClick('history')"
          >
            History
          </button>
          <button
            class="tab"
            :class="{ active: activeTab === 'like' }"
            @click="handleTabClick('like')"
          >
            Like ({{ userStore.userLikedStories.length }})
          </button>
        </div>

        <!-- 故事内容区域 -->
        <div class="story-grid-container">
          <template v-if="activeTab === 'history'">
            <template v-if="userStore.userPlayedStories.length === 0">
              <div class="no-data">
                <p>No browsing history available yet.</p>
              </div>
            </template>
            <template v-else>
              <VirtualStoryGrid
                :stories="userStore.userPlayedStories"
                @story-click="handleStoryClick"
                @image-loaded="handleImageLoaded"
                @subscription-change="handleSubscriptionChange"
                @need-login="showLoginModal('Sign up to subscribe!')"
                @need-email="showLoginModal('Please update your email to subscribe')"
              />
            </template>
          </template>
          <template v-if="activeTab === 'like'">
            <template v-if="userStore.userLikedStories.length === 0">
              <div class="no-data">
                <p>No Liked history yet.</p>
              </div>
            </template>
            <template v-else>
              <VirtualStoryGrid
                :stories="userStore.userLikedStories"
                @story-click="handleStoryClick"
                @image-loaded="handleImageLoaded"
                @subscription-change="handleSubscriptionChange"
                @need-login="showLoginModal('Sign up to subscribe!')"
                @need-email="showLoginModal('Please update your email to subscribe')"
              />
            </template>
          </template>
        </div>
      </div>
    </div>

    <!-- 故事详情弹窗 -->
    <StoryDetailModal
      v-model:visible="showStoryDetailModal"
      :story-id="selectedStoryId"
      @play="handlePlayStory"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeMount } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/user'
import { useStoryStore } from '@/store/story'
import { Story, Actor } from '@/api/stories'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import StoryDetailModal from '@/pc/components/StoryDetailModal.vue'
import VirtualStoryGrid from '@/pc/components/VirtualStoryGrid.vue'

// 定义组件名称
defineOptions({
  name: 'PCUserProfile'
})

const router = useRouter()
const userStore = useUserStore()
const storyStore = useStoryStore()

// 状态
const activeTab = ref('history')
const showStoryDetailModal = ref(false)
const selectedStoryId = ref('')

// 处理标签点击
const handleTabClick = async (tab: string) => {
  // 如果点击的是当前已激活的标签，则不执行任何操作
  if (activeTab.value === tab) return

  reportEvent(ReportEvent.ClickProfilePageTabClick, {
    userId: userStore.userInfo?.uuid,
    tab
  })
  activeTab.value = tab

  // 根据选中的标签刷新数据
  if (tab === 'history') {
    await userStore.getUserPlayedStories()
  } else if (tab === 'like') {
    await userStore.getUserLikedStories()
  }
}

// 处理设置按钮点击
const handleSettings = () => {
  reportEvent(ReportEvent.ClickProfilePageSettings, {
    userId: userStore.userInfo?.uuid
  })
  router.push('/user/settings')
}

// 处理故事点击
const handleStoryClick = (story: Story) => {
  reportEvent(ReportEvent.ClickProfilePageStoryClick, {
    userId: userStore.userInfo?.uuid,
    storyId: story.id,
    tabType: activeTab.value
  })
  storyStore.setCurrentStory(story)

  // 显示故事详情弹窗
  selectedStoryId.value = story.id
  showStoryDetailModal.value = true
}

// 处理开始游戏
const handlePlayStory = (actor: Actor) => {
  reportEvent(ReportEvent.ClickToPlayInStoryIntro, {
    storyId: storyStore.currentStory?.id,
    actorId: actor.id
  })

  // 关闭弹窗
  showStoryDetailModal.value = false
}

// 处理图片加载完成
const handleImageLoaded = (story: Story) => {
  // 可以在这里添加图片加载完成的逻辑，比如性能监控
  console.log('Image loaded for story:', story.id)
}

// 处理订阅状态变化
const handleSubscriptionChange = (updatedStory: Story) => {
  // 更新对应列表中的故事订阅状态
  if (activeTab.value === 'history') {
    const index = userStore.userPlayedStories.findIndex((s) => s.id === updatedStory.id)
    if (index !== -1) {
      userStore.userPlayedStories[index] = updatedStory
    }
  } else if (activeTab.value === 'like') {
    const index = userStore.userLikedStories.findIndex((s) => s.id === updatedStory.id)
    if (index !== -1) {
      userStore.userLikedStories[index] = updatedStory
    }
  }
}
// 显示登录弹窗
const showLoginModal = (message?: string) => {
  router.push('/login')
}

// 生命周期钩子
onBeforeMount(async () => {
  await Promise.all([
    userStore.getUserInfo(),
    userStore.getUserPlayedStories(),
    userStore.getUserLikedStories()
  ])
})

onMounted(() => {
  reportEvent(ReportEvent.ProfilePageView, {
    userId: userStore.userInfo?.uuid
  })
})
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.pc-profile-view {
  width: 100%;
  min-height: 100%;
  padding: 24px;
}

.profile-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  // max-width: 1200px;
  margin: 0 auto;
}

.user-info-section {
  margin-bottom: 24px;
}

.user-info-card {
  display: flex;
  align-items: center;
  background: var(--bg-card, rgba(255, 255, 255, 0.25));
  border-radius: 20px;
  padding: 24px;
  gap: 20px;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.user-details {
  flex: 1;

  .username {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary, rgba(0, 0, 0, 0.85));
    margin: 0 0 12px 0;
  }

  .uid {
    font-size: 14px;
    color: var(--text-secondary, rgba(0, 0, 0, 0.65));
    margin: 0;
  }
}

.settings-button {
  padding: 10px 24px;
  border-radius: 40px;
  background: transparent;
  border: 0.5px solid var(--text-secondary, rgba(0, 0, 0, 0.65));
  color: var(--text-secondary, rgba(0, 0, 0, 0.65));
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: var(--bg-hover);
  }
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tabs {
  display: flex;
  gap: 24px;
  border-bottom: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
  padding-bottom: 16px;

  .tab {
    background: none;
    border: none;
    color: var(--text-secondary, rgba(0, 0, 0, 0.45));
    font-size: 16px;
    font-weight: 500;
    padding: 0 0 8px 0;
    cursor: pointer;
    position: relative;

    &.active {
      color: var(--accent-color, #ca93f2);
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: -16px;
        left: 0;
        right: 0;
        height: 2px;
        background: var(--accent-color, #ca93f2);
      }
    }
  }
}

.story-grid-container {
  padding: 16px 0;
  min-height: 400px;

  .no-data {
    text-align: center;
    padding: 40px 0;

    p {
      color: var(--text-secondary);
      font-size: 16px;
      margin: 0;
    }
  }
}

// // 暗色模式适配
// :global(.dark-theme) {
//   .user-info-card {
//     background: rgba(255, 255, 255, 0.1);
//   }

//   .user-details {
//     .username {
//       color: #fff;
//     }

//     .uid {
//       color: rgba(255, 255, 255, 0.65);
//     }
//   }

//   .settings-button {
//     border-color: rgba(255, 255, 255, 0.65);
//     color: rgba(255, 255, 255, 0.65);

//     &:hover {
//       background: rgba(255, 255, 255, 0.1);
//     }
//   }

//   .tabs {
//     border-bottom-color: rgba(255, 255, 255, 0.1);

//     .tab {
//       color: rgba(255, 255, 255, 0.5);
//     }
//   }
// }
</style>
