/**
 * 地图相关类型定义
 */

import { Chat4SceneId } from './chat4-scene'

/**
 * 地图位置接口
 */
export interface MapLocation {
  /** 位置ID */
  id: string
  /** 位置名称 */
  name: string
  /** 位置描述 */
  description?: string
  /** 背景图片URL */
  image: string
  /** 位置坐标 */
  position: {
    x: number
    y: number
  }
  /** 是否锁定 */
  isLocked: boolean
  /** 解锁条件 */
  unlockCondition?: {
    /** 需要的好感度等级 */
    requiredLevel?: string
    /** 需要的心动值 */
    requiredHeartValue?: number
    /** 需要的金币 */
    requiredCoins?: number
  }
  /** 对应的meetup场景ID */
  meetupScene: Chat4SceneId
  /** 是否为新场景 */
  isNew?: boolean
}

/**
 * 地图配置接口
 */
export interface MapConfig {
  /** 地图背景图片 */
  backgroundImage: string
  /** 地图标题 */
  title?: string
  /** 地图位置列表 */
  locations: MapLocation[]
}

/**
 * 地图事件接口
 */
export interface MapLocationClickEvent {
  /** 点击的位置 */
  location: MapLocation
  /** 事件时间戳 */
  timestamp: number
}

/**
 * 默认地图配置
 */
export const DEFAULT_MAP_CONFIG: MapConfig = {
  backgroundImage:
    'https://images.unsplash.com/photo-1519904981063-b0cf448d479e?w=375&h=667&fit=crop',
  title: 'Choose Your Date Location',
  locations: [
    {
      id: 'swimming_pool',
      name: 'Swimming Pool',
      description: 'Enjoy a refreshing swim together',
      image:
        'https://images.unsplash.com/photo-1571902943202-507ec2618e8f?w=167&h=94&fit=crop',
      position: { x: 100, y: 150 }, // 调整为9:16比例下的位置
      isLocked: false,
      meetupScene: Chat4SceneId.MEETUP_POOL,
      isNew: false,
    },
    {
      id: 'coffee_shop',
      name: 'Coffee Shop',
      description: 'Have a cozy chat over coffee',
      image:
        'https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?w=167&h=94&fit=crop',
      position: { x: 20, y: 280 }, // 调整为9:16比例下的位置
      isLocked: false,
      meetupScene: Chat4SceneId.MEETUP_COFFEE,
      isNew: false,
    },
    {
      id: 'office',
      name: 'Office',
      description: 'Meet in a professional setting',
      image:
        'https://images.unsplash.com/photo-1497366216548-37526070297c?w=167&h=94&fit=crop',
      position: { x: 180, y: 420 }, // 调整为9:16比例下的位置
      isLocked: true,
      unlockCondition: {
        requiredLevel: 'level1',
        requiredHeartValue: 50,
      },
      meetupScene: Chat4SceneId.MEETUP_OFFICE,
      isNew: false,
    },
    {
      id: 'seaside',
      name: 'Seaside',
      description: 'Romantic walk by the ocean',
      image:
        'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?w=167&h=94&fit=crop',
      position: { x: 30, y: 550 }, // 调整为9:16比例下的位置
      isLocked: true,
      unlockCondition: {
        requiredLevel: 'level2',
        requiredHeartValue: 80,
      },
      meetupScene: Chat4SceneId.MEETUP_SEASIDE,
      isNew: true,
    },
  ],
}
