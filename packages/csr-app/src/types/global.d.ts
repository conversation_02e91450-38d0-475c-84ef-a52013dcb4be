// 构建时定义的全局变量
declare const __DEV__: boolean
declare const __TEST__: boolean

interface Window {
  // 第三方服务
  fbq: (event: string, ...args: any[]) => void
  gtag: (event: string, ...args: any[]) => void
  collectEvent: (event: string, ...args: any[]) => void

  // 环境标志
  __DEV_MODE__?: boolean
  __PROD_MODE__?: boolean
  __BUILD_TIME__?: string

  // 开发测试工具（仅开发环境）
  __versionManager?: any
  __versionManagerTest?: any
  __routePreloadDebug?: any

  // 动态API地址
  __DYNAMIC_API_HOST__?: string
}
