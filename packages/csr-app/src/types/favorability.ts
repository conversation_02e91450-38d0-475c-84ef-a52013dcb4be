/**
 * 好感度系统相关类型定义
 */

/**
 * 场景解锁条件
 */
export interface SceneUnlockCondition {
  /** 场景ID */
  scene_id: string
  /** 解锁要求类型 */
  requirement: 'coins' | 'heart_value'
  /** 解锁要求的数值 */
  value: number
}

/**
 * 显示场景解锁条件事件数据
 */
export interface ShowScenesUnlockConditionsEventData {
  event_type: 'show_scenes_unlock_conditions'
  timestamp: number
  data: {
    scene_conditions: SceneUnlockCondition[]
  }
}

/**
 * 等级信息
 */
export interface LevelInfo {
  /** 等级名称 */
  level: string
  /** 达到该等级所需的好感度值 */
  heart_value: number
}

/**
 * 显示等级信息事件数据
 */
export interface ShowLevelInfoEventData {
  event_type: 'show_level_info'
  timestamp: number
  data: {
    level_infos: LevelInfo[]
  }
}

/**
 * 好感度状态
 */
export interface FavorabilityState {
  /** 当前好感度值 */
  currentHeartValue: number
  /** 当前等级 */
  currentLevel: string
  /** 下一等级 */
  nextLevel?: string
  /** 升级到下一等级所需的好感度值 */
  nextLevelHeartValue?: number
  /** 距离下一等级还需要的好感度值 */
  heartValueLeft?: number
  /** 等级信息列表 */
  levelInfos: LevelInfo[]
  /** 场景解锁条件列表 */
  sceneConditions: SceneUnlockCondition[]
}

/**
 * 好感度抽屉组件的Props
 */
export interface FavorabilityDrawerProps {
  /** 是否显示抽屉 */
  visible: boolean
  /** 好感度状态 */
  favorabilityState: FavorabilityState
}

/**
 * 好感度抽屉组件的Emits
 */
export interface FavorabilityDrawerEmits {
  /** 更新显示状态 */
  'update:visible': [visible: boolean]
  /** 关闭抽屉 */
  'close': []
  /** 付费解锁 */
  'payment': [coins: number]
}

/**
 * 场景解锁状态
 */
export interface SceneUnlockStatus {
  /** 场景ID */
  sceneId: string
  /** 是否已解锁 */
  isUnlocked: boolean
  /** 解锁条件 */
  conditions: SceneUnlockCondition[]
  /** 如果未解锁，需要达到的等级（基于好感度要求） */
  requiredLevel?: string
  /** 如果未解锁，还需要的金币数量 */
  requiredCoins?: number
  /** 如果未解锁，还需要的好感度值 */
  requiredHeartValue?: number
}

/**
 * 场景解锁工具类
 */
export class SceneUnlockUtils {
  /**
   * 计算场景解锁状态
   */
  static calculateSceneUnlockStatus(
    sceneId: string,
    sceneConditions: SceneUnlockCondition[],
    levelInfos: LevelInfo[],
    currentHeartValue: number,
    currentCoins: number,
  ): SceneUnlockStatus {
    // 找到该场景的解锁条件
    const conditions = sceneConditions.filter(
      (condition) => condition.scene_id === sceneId,
    )

    if (conditions.length === 0) {
      // 没有解锁条件，默认已解锁
      return {
        sceneId,
        isUnlocked: true,
        conditions: [],
      }
    }

    let isUnlocked = true
    let requiredLevel: string | undefined
    let requiredCoins: number | undefined
    let requiredHeartValue: number | undefined

    for (const condition of conditions) {
      if (condition.requirement === 'coins') {
        if (currentCoins < condition.value) {
          isUnlocked = false
          requiredCoins = condition.value - currentCoins
        }
      } else if (condition.requirement === 'heart_value') {
        if (currentHeartValue < condition.value) {
          isUnlocked = false
          requiredHeartValue = condition.value - currentHeartValue
          // 根据好感度要求找到对应的等级
          requiredLevel = this.findRequiredLevel(condition.value, levelInfos)
        }
      }
    }

    return {
      sceneId,
      isUnlocked,
      conditions,
      requiredLevel,
      requiredCoins,
      requiredHeartValue,
    }
  }

  /**
   * 根据好感度值计算当前等级（统一的等级计算逻辑）
   */
  static calculateCurrentLevel(
    currentHeartValue: number,
    levelInfos: LevelInfo[],
  ): string {
    // 按好感度值排序
    const sortedLevels = [...levelInfos].sort(
      (a, b) => a.heart_value - b.heart_value,
    )

    let currentLevel = 'level0'

    // 找到当前心值对应的等级
    for (const level of sortedLevels) {
      if (currentHeartValue >= level.heart_value) {
        currentLevel = level.level
      } else {
        break
      }
    }

    return currentLevel
  }

  /**
   * 根据好感度值找到对应的等级（用于解锁条件）
   */
  static findRequiredLevel(
    requiredHeartValue: number,
    levelInfos: LevelInfo[],
  ): string | undefined {
    // 使用统一的等级计算逻辑
    return this.calculateCurrentLevel(requiredHeartValue, levelInfos)
  }

  /**
   * 检查等级是否解锁
   */
  static isLevelUnlocked(requiredLevel: string, currentLevel: string): boolean {
    // 提取等级数字
    const requiredLevelNum = parseInt(requiredLevel.replace('level', ''))
    const currentLevelNum = parseInt(currentLevel.replace('level', ''))

    // 当前等级大于等于要求等级即为解锁
    return currentLevelNum >= requiredLevelNum
  }

  /**
   * 格式化等级显示文本
   */
  static formatLevelText(level: string): string {
    // 将 "level0" 转换为 "Lv0", "level1" 转换为 "Lv1", 以此类推
    const levelNumber = level.replace('level', '')
    return `Lv${levelNumber}`
  }
}
