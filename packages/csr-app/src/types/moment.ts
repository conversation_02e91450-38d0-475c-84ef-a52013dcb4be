/**
 * 朋友圈相关类型定义
 */

/**
 * 朋友圈用户信息
 */
export interface MomentUser {
  /** 用户ID */
  id: string
  /** 用户名 */
  name: string
  /** 头像URL */
  avatar: string
  /** 个性签名 */
  signature?: string
}

/**
 * 朋友圈动态内容
 */
export interface MomentContent {
  /** 动态ID */
  id: string
  /** 文本内容 */
  text: string
  /** 来源URL */
  source_url: string
  /** 是否已点赞 */
  liked: boolean
  /** 评论列表 */
  comments: MomentComment[]
}

/**
 * 朋友圈动态
 */
export interface MomentPost {
  /** 动态ID */
  id?: string
  /** 发布用户 */
  user?: MomentUser
  /** 动态内容 */
  content: MomentContent
  /** 发布时间 */
  timestamp?: number
  /** 点赞数 */
  likeCount?: number
  /** 评论数 */
  commentCount?: number
  /** 当前用户是否已点赞 */
  isLiked?: boolean
  /** 是否展开显示全文 */
  isExpanded?: boolean
}

/**
 * 朋友圈评论
 */
export interface MomentComment {
  /** 评论内容 */
  text: string
  /** 评论者 */
  author: string
}

/**
 * 朋友圈数据
 */
export interface MomentData {
  /** 当前用户信息 */
  currentUser: MomentUser
  /** 动态列表 */
  posts: MomentPost[]
  /** 是否还有更多数据 */
  hasMore: boolean
  /** 下一页的游标 */
  nextCursor?: string
}

/**
 * 朋友圈API响应
 */
export interface MomentApiResponse {
  success: boolean
  data?: MomentData
  message?: string
}

/**
 * 点赞操作响应
 */
export interface LikeResponse {
  success: boolean
  isLiked: boolean
  likeCount: number
  message?: string
}

/**
 * 评论操作响应
 */
export interface CommentResponse {
  success: boolean
  comment?: MomentComment
  message?: string
}

/**
 * 后端返回的朋友圈事件数据
 */
export interface ShowMomentsEventData {
  moments: MomentPost[]
  personalized_signature?: string
}

/**
 * 朋友圈事件
 */
export interface ShowMomentsEvent {
  event_type: 'show_moments'
  timestamp: number
  data: ShowMomentsEventData
}
