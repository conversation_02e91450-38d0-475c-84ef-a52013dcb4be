/**
 * 场景相关类型定义
 */

/**
 * Chat4场景ID枚举
 * 定义Chat4中实际使用的场景ID
 */
export enum Chat4SceneId {
  /** 直播场景 - 显示好友请求弹窗 */
  LIVING = 'Living',
  /** 聊天场景 */
  PHONE = 'Phone',
  /** 视频通话场景 */
  VIDEO_CALL = 'Video',
  /** 监控场景 */
  MONITOR = 'Monitor',
  /** 地图场景 - 选择约会地点 */
  MAP = 'Map',
  /** 见面场景 */
  MEETUP = 'Meetup',
  /** 游泳池约会场景 */
  MEETUP_POOL = 'MeetupPool',
  /** 咖啡店约会场景 */
  MEETUP_COFFEE = 'MeetupCoffee',
  /** 办公室约会场景 */
  MEETUP_OFFICE = 'MeetupOffice',
  /** 海边约会场景 */
  MEETUP_SEASIDE = 'MeetupSeaside',
  /** 舞蹈场景 */
  DANCING = 'Dancing',
  /** 演唱会场景 */
  CONCERT = 'Concert',
  /** 打赏场景 */
  TIP = 'Tip',
  /** 游戏场景 */
  GAME = 'Game',
  /** 设置场景 */
  SETTINGS = 'Settings',
  /** 朋友圈场景 */
  MOMENT = 'Moment',
  /** 日记场景 */
  DIARY = 'Diary',
}

/**
 * 场景类型枚举
 * 定义不同的场景类型，用于根据场景执行不同的行为
 */
export enum SceneType {
  /** 直播场景 - 显示好友请求弹窗 */
  LIVING = 'Living',
  /** 聊天场景 */
  PHONE = 'Phone',
  /** 视频通话场景 */
  VIDEO_CALL = 'VideoCall',
  /** 监控场景 */
  MONITOR = 'Monitor',
  /** 地图场景 - 选择约会地点 */
  MAP = 'Map',
  /** 见面场景 */
  MEETUP = 'Meetup',
  /** 舞蹈场景 */
  DANCING = 'Dancing',
  /** 演唱会场景 */
  CONCERT = 'Concert',
  /** 打赏场景 */
  TIP = 'Tip',
  /** 游戏场景 */
  GAME = 'Game',
  /** 设置场景 */
  SETTINGS = 'Settings',
  /** 日记场景 */
  DIARY = 'Diary',
  /** 默认场景 */
  DEFAULT = 'default',
}

/**
 * Chat4场景状态类型
 * 用于UI状态管理
 */
export type Chat4SceneState =
  | Chat4SceneId.LIVING
  | Chat4SceneId.PHONE
  | Chat4SceneId.VIDEO_CALL
  | Chat4SceneId.MONITOR
  | Chat4SceneId.MAP
  | Chat4SceneId.MEETUP
  | Chat4SceneId.MEETUP_POOL
  | Chat4SceneId.MEETUP_COFFEE
  | Chat4SceneId.MEETUP_OFFICE
  | Chat4SceneId.MEETUP_SEASIDE
  | Chat4SceneId.DANCING
  | Chat4SceneId.CONCERT
  | Chat4SceneId.TIP
  | Chat4SceneId.GAME
  | Chat4SceneId.SETTINGS
  | Chat4SceneId.MOMENT
  | Chat4SceneId.DIARY

/**
 * 场景变化事件数据接口
 */
export interface SceneChangeEventData {
  /** 事件类型 */
  event_type: 'handle_scene_change'
  /** 时间戳 */
  timestamp: number
  /** 事件数据 */
  data: {
    /** 场景ID */
    scene_id: string
    /** 额外的场景数据 */
    [key: string]: any
  }
}

/**
 * 场景行为配置接口
 */
export interface SceneActionConfig {
  /** 场景类型 */
  sceneType: SceneType
  /** 行为类型 */
  actionType: string
  /** 行为参数 */
  params?: Record<string, any>
}

/**
 * 场景行为处理器接口
 */
export interface SceneActionHandler {
  /** 处理器名称 */
  name: string
  /** 支持的场景类型 */
  supportedScenes: SceneType[]
  /** 处理函数 */
  handler: (sceneId: string, timestamp: number, params?: any) => void
}

/**
 * 自定义事件详情接口
 */
export interface SceneCustomEventDetail {
  /** 场景ID */
  sceneId: string
  /** 时间戳 */
  timestamp: number
  /** 行为类型 */
  action: string
  /** 额外参数 */
  params?: Record<string, any>
}

/**
 * 场景映射配置
 * 用于将场景ID映射到场景类型
 */
export interface SceneMapping {
  /** 场景ID或模式 */
  pattern: string | RegExp
  /** 对应的场景类型 */
  sceneType: SceneType
  /** 描述 */
  description?: string
}

/**
 * Chat4场景映射配置
 */
export const CHAT4_SCENE_MAPPINGS: SceneMapping[] = [
  {
    pattern: 'Living',
    sceneType: SceneType.LIVING,
    description: 'Chat4直播场景',
  },
  {
    pattern: 'Phone',
    sceneType: SceneType.PHONE,
    description: 'Chat4聊天场景',
  },
  {
    pattern: 'VideoCall',
    sceneType: SceneType.VIDEO_CALL,
    description: 'Chat4视频通话场景',
  },
  {
    pattern: 'Monitor',
    sceneType: SceneType.MONITOR,
    description: 'Chat4监控场景',
  },
  {
    pattern: 'Map',
    sceneType: SceneType.MAP,
    description: 'Chat4地图场景',
  },
  {
    pattern: 'Meetup',
    sceneType: SceneType.MEETUP,
    description: 'Chat4见面场景',
  },
  {
    pattern: 'Dancing',
    sceneType: SceneType.DANCING,
    description: 'Chat4舞蹈场景',
  },
  {
    pattern: 'Concert',
    sceneType: SceneType.CONCERT,
    description: 'Chat4演唱会场景',
  },
  {
    pattern: 'Tip',
    sceneType: SceneType.TIP,
    description: 'Chat4打赏场景',
  },
  {
    pattern: 'Game',
    sceneType: SceneType.GAME,
    description: 'Chat4游戏场景',
  },
  {
    pattern: 'Settings',
    sceneType: SceneType.SETTINGS,
    description: 'Chat4设置场景',
  },
  {
    pattern: 'Diary',
    sceneType: SceneType.DIARY,
    description: 'Chat4日记场景',
  },
  {
    pattern: /living/i,
    sceneType: SceneType.LIVING,
    description: '直播场景，包含living关键词',
  },
  {
    pattern: /phone/i,
    sceneType: SceneType.PHONE,
    description: '聊天场景，包含phone关键词',
  },
  {
    pattern: /video/i,
    sceneType: SceneType.VIDEO_CALL,
    description: '视频场景，包含video关键词',
  },
  {
    pattern: /monitor/i,
    sceneType: SceneType.MONITOR,
    description: '监控场景，包含monitor关键词',
  },
  {
    pattern: /meetup/i,
    sceneType: SceneType.MEETUP,
    description: '见面场景，包含meetup关键词',
  },
  {
    pattern: /tip/i,
    sceneType: SceneType.TIP,
    description: '打赏场景，包含tip关键词',
  },
  {
    pattern: /diary/i,
    sceneType: SceneType.DIARY,
    description: '日记场景，包含diary关键词',
  },
]

/**
 * 默认场景映射配置（向后兼容）
 */
export const DEFAULT_SCENE_MAPPINGS: SceneMapping[] = CHAT4_SCENE_MAPPINGS

/**
 * 场景行为类型枚举
 */
export enum SceneActionType {
  /** 显示好友请求 */
  SHOW_FRIEND_REQUEST = 'show_friend_request',
  /** 聊天场景变化 */
  CHAT_SCENE_CHANGE = 'chat_scene_change',
  /** 默认场景变化 */
  DEFAULT_SCENE_CHANGE = 'default_scene_change',
  /** 显示通知 */
  SHOW_NOTIFICATION = 'show_notification',
  /** 更新UI状态 */
  UPDATE_UI_STATE = 'update_ui_state',
}

/**
 * 场景工具类
 */
export class SceneUtils {
  /**
   * 根据场景ID获取场景类型
   */
  static getSceneType(
    sceneId: string | null,
    mappings: SceneMapping[] = DEFAULT_SCENE_MAPPINGS,
  ): SceneType {
    if (!sceneId) {
      return SceneType.DEFAULT
    }

    for (const mapping of mappings) {
      if (typeof mapping.pattern === 'string') {
        if (sceneId.toLowerCase().includes(mapping.pattern.toLowerCase())) {
          return mapping.sceneType
        }
      } else if (mapping.pattern instanceof RegExp) {
        if (mapping.pattern.test(sceneId)) {
          return mapping.sceneType
        }
      }
    }

    return SceneType.DEFAULT
  }

  /**
   * 创建场景自定义事件
   */
  static createSceneEvent(
    eventName: string,
    detail: SceneCustomEventDetail,
  ): CustomEvent {
    return new CustomEvent(eventName, { detail })
  }

  /**
   * 验证场景变化事件数据
   */
  static validateSceneChangeEvent(data: any): data is SceneChangeEventData {
    return (
      data &&
      typeof data === 'object' &&
      data.event_type === 'handle_scene_change' &&
      typeof data.timestamp === 'number' &&
      data.data &&
      typeof data.data === 'object' &&
      typeof data.data.scene_id === 'string'
    )
  }
}

/**
 * Chat4场景工具类
 */
export class Chat4SceneUtils {
  /**
   * 判断是否为直播场景
   */
  static isLivingScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.LIVING
  }

  /**
   * 判断是否为聊天场景
   */
  static isPhoneScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.PHONE
  }

  /**
   * 判断是否为视频通话场景
   */
  static isVideoCallScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.VIDEO_CALL
  }

  /**
   * 判断是否为视频相关场景（包括视频通话）
   */
  static isVideoScene(sceneId: string | null): boolean {
    return Chat4SceneUtils.isVideoCallScene(sceneId)
  }

  /**
   * 判断是否为监控场景
   */
  static isMonitorScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.MONITOR
  }

  /**
   * 判断是否为地图场景
   */
  static isMapScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.MAP
  }

  /**
   * 判断是否为约会场景（包括所有约会子场景）
   */
  static isMeetupScene(sceneId: string | null): boolean {
    return (
      sceneId === Chat4SceneId.MEETUP ||
      sceneId === Chat4SceneId.MEETUP_POOL ||
      sceneId === Chat4SceneId.MEETUP_COFFEE ||
      sceneId === Chat4SceneId.MEETUP_OFFICE ||
      sceneId === Chat4SceneId.MEETUP_SEASIDE
    )
  }

  /**
   * 判断是否为基础约会场景
   */
  static isBaseMeetupScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.MEETUP
  }

  /**
   * 判断是否为舞蹈场景
   */
  static isDancingScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.DANCING
  }

  /**
   * 判断是否为演唱会场景
   */
  static isConcertScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.CONCERT
  }

  /**
   * 判断是否为打赏场景
   */
  static isTipScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.TIP
  }

  /**
   * 判断是否为朋友圈场景
   */
  static isMomentScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.MOMENT
  }

  /**
   * 判断是否为日记场景
   */
  static isDiaryScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.DIARY
  }

  /**
   * 获取场景的显示名称
   */
  static getSceneDisplayName(sceneId: string | null): string {
    switch (sceneId) {
      case Chat4SceneId.LIVING:
        return '直播间'
      case Chat4SceneId.PHONE:
        return '聊天室'
      case Chat4SceneId.VIDEO_CALL:
        return '视频通话'
      case Chat4SceneId.MONITOR:
        return '监控'
      case Chat4SceneId.MAP:
        return '地图'
      case Chat4SceneId.MEETUP:
        return '见面'
      case Chat4SceneId.MEETUP_POOL:
        return '游泳池约会'
      case Chat4SceneId.MEETUP_COFFEE:
        return '咖啡店约会'
      case Chat4SceneId.MEETUP_OFFICE:
        return '办公室约会'
      case Chat4SceneId.MEETUP_SEASIDE:
        return '海边约会'
      case Chat4SceneId.DANCING:
        return '舞蹈'
      case Chat4SceneId.CONCERT:
        return '演唱会'
      case Chat4SceneId.TIP:
        return '打赏'
      case Chat4SceneId.GAME:
        return '游戏'
      case Chat4SceneId.SETTINGS:
        return '设置'
      default:
        return '未知场景'
    }
  }

  /**
   * 获取场景的UI类型（用于UI状态管理）
   */
  static getSceneUIType(
    sceneId: string | null,
  ):
    | 'live'
    | 'chat'
    | 'video'
    | 'monitor'
    | 'map'
    | 'meetup'
    | 'dancing'
    | 'concert'
    | 'tip'
    | 'game'
    | 'settings'
    | 'diary'
    | 'unknown' {
    switch (sceneId) {
      case Chat4SceneId.LIVING:
        return 'live'
      case Chat4SceneId.PHONE:
        return 'chat'
      case Chat4SceneId.VIDEO_CALL:
        return 'video'
      case Chat4SceneId.MONITOR:
        return 'monitor'
      case Chat4SceneId.MAP:
        return 'map'
      case Chat4SceneId.MEETUP:
      case Chat4SceneId.MEETUP_POOL:
      case Chat4SceneId.MEETUP_COFFEE:
      case Chat4SceneId.MEETUP_OFFICE:
      case Chat4SceneId.MEETUP_SEASIDE:
        return 'meetup'
      case Chat4SceneId.DANCING:
        return 'dancing'
      case Chat4SceneId.CONCERT:
        return 'concert'
      case Chat4SceneId.TIP:
        return 'tip'
      case Chat4SceneId.GAME:
        return 'game'
      case Chat4SceneId.SETTINGS:
        return 'settings'
      case Chat4SceneId.DIARY:
        return 'diary'
      default:
        return 'unknown'
    }
  }

  /**
   * 根据UI类型获取场景ID
   */
  static getSceneIdFromUIType(uiType: string): Chat4SceneId {
    switch (uiType) {
      case 'live':
        return Chat4SceneId.LIVING
      case 'chat':
        return Chat4SceneId.PHONE
      case 'video':
        return Chat4SceneId.VIDEO_CALL
      case 'monitor':
        return Chat4SceneId.MONITOR
      case 'map':
        return Chat4SceneId.MAP
      case 'meetup':
        return Chat4SceneId.MEETUP
      case 'dancing':
        return Chat4SceneId.DANCING
      case 'concert':
        return Chat4SceneId.CONCERT
      case 'tip':
        return Chat4SceneId.TIP
      case 'game':
        return Chat4SceneId.GAME
      case 'settings':
        return Chat4SceneId.SETTINGS
      default:
        return Chat4SceneId.LIVING
    }
  }

  /**
   * 验证是否为有效的Chat4场景ID
   */
  static isValidChat4Scene(sceneId: string | null): sceneId is Chat4SceneState {
    return Object.values(Chat4SceneId).includes(sceneId as Chat4SceneId)
  }

  /**
   * 获取场景的导航类型（用于底部导航栏）
   */
  static getNavTypeFromScene(sceneId: string | null): string {
    switch (sceneId) {
      case Chat4SceneId.LIVING:
        return 'live'
      case Chat4SceneId.PHONE:
        return 'chat'
      case Chat4SceneId.VIDEO_CALL:
        return 'video'
      case Chat4SceneId.MONITOR:
        return 'monitor'
      case Chat4SceneId.MAP:
        return 'map'
      case Chat4SceneId.MEETUP:
        return 'meetup'
      case Chat4SceneId.TIP:
        return 'tip'
      default:
        return 'live'
    }
  }

  /**
   * 根据导航类型获取场景ID
   */
  static getSceneFromNavType(navType: string): Chat4SceneId {
    switch (navType) {
      case 'live':
        return Chat4SceneId.LIVING
      case 'tip':
        return Chat4SceneId.TIP
      case 'video':
        return Chat4SceneId.VIDEO_CALL
      case 'monitor':
        return Chat4SceneId.MONITOR
      case 'meetup':
        return Chat4SceneId.MEETUP
      default:
        return Chat4SceneId.PHONE
    }
  }
}
