declare module 'danmaku' {
  interface DanmakuOptions {
    container: HTMLElement
    media?: HTMLVideoElement | null
    comments?: DanmakuComment[]
    engine?: 'dom' | 'canvas'
    speed?: number
  }

  interface DanmakuComment {
    text: string
    style?: {
      fontSize?: string
      color?: string
      textShadow?: string
      fontFamily?: string
      fontWeight?: string | number
    }
    mode?: 'rtl' | 'ltr' | 'top' | 'bottom'
    render?: () => HTMLElement | HTMLCanvasElement
  }

  class Danmaku {
    constructor(options: DanmakuOptions)
    emit(comment: DanmakuComment): void
    clear(): void
    show(): void
    hide(): void
    destroy(): void
  }

  export = Danmaku
}

// 扩展CanvasRenderingContext2D类型
declare global {
  interface CanvasRenderingContext2D {
    roundRect?(x: number, y: number, width: number, height: number, radius: number): void
  }
}
