<template>
  <Teleport to="body">
    <Transition name="update-notification" appear>
      <div
        v-if="showNotification"
        class="update-notification-overlay"
        @click="handleOverlayClick"
      >
        <div class="update-notification-modal" @click.stop>
          <div class="notification-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
              <path
                d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"
                fill="currentColor"
              />
            </svg>
          </div>
          
          <div class="notification-content">
            <h3 class="notification-title">{{ title }}</h3>
            <p class="notification-message">{{ message }}</p>
            
            <div v-if="showDetails" class="notification-details">
              <div class="version-info">
                <span class="label">当前版本:</span>
                <span class="value">{{ currentVersion }}</span>
              </div>
              <div class="version-info">
                <span class="label">最新版本:</span>
                <span class="value">{{ latestVersion }}</span>
              </div>
            </div>
          </div>
          
          <div class="notification-actions">
            <button
              v-if="showLaterButton"
              class="btn btn-secondary"
              @click="handleLater"
            >
              稍后更新
            </button>
            <button
              class="btn btn-primary"
              @click="handleUpdate"
              :disabled="isUpdating"
            >
              <span v-if="isUpdating" class="loading-spinner"></span>
              {{ isUpdating ? '更新中...' : updateButtonText }}
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  visible?: boolean
  title?: string
  message?: string
  currentVersion?: string
  latestVersion?: string
  showDetails?: boolean
  showLaterButton?: boolean
  updateButtonText?: string
  autoUpdate?: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'update'): void
  (e: 'later'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  title: '发现新版本',
  message: '我们发布了新版本，包含性能优化和功能改进。',
  showDetails: true,
  showLaterButton: false,
  updateButtonText: '立即更新',
  autoUpdate: true
})

const emit = defineEmits<Emits>()

const isUpdating = ref(false)

const showNotification = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const handleOverlayClick = () => {
  if (!props.autoUpdate) {
    showNotification.value = false
  }
}

const handleUpdate = async () => {
  isUpdating.value = true
  
  try {
    emit('update')
    
    // 如果是自动更新模式，延迟一下再刷新页面
    if (props.autoUpdate) {
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    }
  } catch (error) {
    console.error('更新失败:', error)
    isUpdating.value = false
  }
}

const handleLater = () => {
  emit('later')
  showNotification.value = false
}
</script>

<style lang="less" scoped>
.update-notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.update-notification-modal {
  background: var(--color-bg-1);
  border-radius: 16px;
  padding: 32px;
  max-width: 400px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  text-align: center;
  border: 1px solid var(--color-border-2);
}

.notification-icon {
  color: var(--color-primary-6);
  margin-bottom: 20px;
  
  svg {
    animation: sparkle 2s ease-in-out infinite;
  }
}

.notification-content {
  margin-bottom: 24px;
}

.notification-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-1);
  margin: 0 0 12px 0;
}

.notification-message {
  font-size: 14px;
  color: var(--color-text-2);
  line-height: 1.5;
  margin: 0 0 16px 0;
}

.notification-details {
  background: var(--color-bg-2);
  border-radius: 8px;
  padding: 16px;
  text-align: left;
}

.version-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  
  &:not(:last-child) {
    margin-bottom: 8px;
  }
  
  .label {
    color: var(--color-text-3);
  }
  
  .value {
    color: var(--color-text-1);
    font-weight: 500;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  }
}

.notification-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 100px;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &.btn-primary {
    background: var(--color-primary-6);
    color: white;
    
    &:hover:not(:disabled) {
      background: var(--color-primary-5);
      transform: translateY(-1px);
    }
  }
  
  &.btn-secondary {
    background: var(--color-bg-3);
    color: var(--color-text-2);
    border: 1px solid var(--color-border-2);
    
    &:hover {
      background: var(--color-bg-4);
      color: var(--color-text-1);
    }
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

// 动画
@keyframes sparkle {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: scale(1.1) rotate(180deg);
    opacity: 0.8;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 过渡动画
.update-notification-enter-active,
.update-notification-leave-active {
  transition: all 0.3s ease;
}

.update-notification-enter-from,
.update-notification-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

.update-notification-enter-active .update-notification-modal,
.update-notification-leave-active .update-notification-modal {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.update-notification-enter-from .update-notification-modal,
.update-notification-leave-to .update-notification-modal {
  transform: scale(0.8) translateY(20px);
  opacity: 0;
}

// 移动端适配
@media (max-width: 768px) {
  .update-notification-overlay {
    padding: 16px;
  }
  
  .update-notification-modal {
    padding: 24px;
    max-width: none;
  }
  
  .notification-actions {
    flex-direction: column;
    
    .btn {
      width: 100%;
    }
  }
}
</style>
