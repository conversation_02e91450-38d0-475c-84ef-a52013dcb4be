<template>
  <!-- 优化版AB测试包裹器：预加载组件，避免虚拟滚动高度计算问题 -->
  <div class="story-card-ab-test-optimized">
    <!-- 根据AB测试结果显示对应组件 -->
    <StoryCard
      v-if="!storyCardDesignTest.isB"
      v-bind="$attrs"
      :story="story"
      :is-pc="isPc"
      :loading="loading"
      @click="handleClick"
      @image-loaded="handleImageLoaded"
      @image-error="handleImageError"
      @subscription-change="handleSubscriptionChange"
      @need-login="handleNeedLogin"
      @need-email="handleNeedEmail"
    />
    <StoryCardV2
      v-else
      v-bind="$attrs"
      :story="story"
      :is-pc="isPc"
      :loading="loading"
      @click="handleClick"
      @image-loaded="handleImageLoaded"
      @image-error="handleImageError"
      @subscription-change="handleSubscriptionChange"
      @need-login="handleNeedLogin"
      @need-email="handleNeedEmail"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import type { Story } from '@/api/stories'
import { useABTestStore } from '@/store/abtest'
import { trackABTestConversion } from '@/utils/abtest'
// 直接导入组件，避免异步加载导致的高度计算问题
import StoryCard from '@/shared/components/StoryCard.vue'
import StoryCardV2 from '@/shared/components/StoryCardV2.vue'

interface Props {
  story?: Story
  isPc?: boolean
  loading?: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'click', story: Story): void
  (e: 'image-loaded'): void
  (e: 'image-error'): void
  (e: 'subscription-change', story: Story): void
  (e: 'need-login'): void
  (e: 'need-email'): void
}>()

const abtestStore = useABTestStore()

// AB测试：故事卡片设计版本
const storyCardDesignTest = abtestStore.storyCardDesign

// 事件处理函数
const handleClick = (story: Story) => {
  // 上报AB测试转化事件
  trackABTestConversion('story_card_design', 'story_card_click', 1)
  emit('click', story)
}

const handleImageLoaded = () => {
  emit('image-loaded')
}

const handleImageError = () => {
  emit('image-error')
}

const handleSubscriptionChange = (story: Story) => {
  emit('subscription-change', story)
}

const handleNeedLogin = () => {
  emit('need-login')
}

const handleNeedEmail = () => {
  emit('need-email')
}

// 组件挂载时上报AB测试曝光事件 - 最大程度优化性能
onMounted(() => {
  // 使用更长的延迟，确保虚拟滚动完全稳定后再上报
  if ('requestIdleCallback' in window) {
    requestIdleCallback(
      () => {
        if (props.story?.id) {
          abtestStore.trackExposure('story_card_design', {
            component: 'StoryCardABTest',
            story_id: props.story.id,
            variant: storyCardDesignTest.variant,
            is_pc: props.isPc
          })
        }
      },
      { timeout: 10000 } // 进一步增加超时时间
    )
  } else {
    setTimeout(() => {
      if (props.story?.id) {
        abtestStore.trackExposure('story_card_design', {
          component: 'StoryCardABTest',
          story_id: props.story.id,
          variant: storyCardDesignTest.variant,
          is_pc: props.isPc
        })
      }
    }, 1000) // 使用更长的延迟
  }
})
</script>

<style lang="less" scoped>
.story-card-ab-test-optimized {
  // 确保包装组件完全透明，不影响布局
  width: 100%;
  height: 100%;
  display: contents; // 让包装组件在布局中"透明"
}

// 如果浏览器不支持 display: contents，使用备用方案
@supports not (display: contents) {
  .story-card-ab-test-optimized {
    display: block;
    width: 100%;
    height: auto;
    position: relative;
  }
}
</style>
