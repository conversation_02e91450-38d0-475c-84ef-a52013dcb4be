export interface MediaResource {
  type: 'image' | 'video'
  src: string
  scene?: string
  outfit?: string
}

export interface Character {
  id: number
  name: string
  thumbnail?: string
  mediaResources: MediaResource[]
}

export interface PreloadProgress {
  loaded: number
  total: number
  percentage: number
  currentResource: string
}

export type ProgressCallback = (progress: PreloadProgress) => void

/**
 * 预加载单个图片资源
 */
function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve()
    img.onerror = () => reject(new Error(`Failed to load image: ${src}`))
    img.src = src
  })
}

/**
 * 预加载单个视频资源
 */
function preloadVideo(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    video.preload = 'metadata'
    
    const onCanPlay = () => {
      cleanup()
      resolve()
    }
    
    const onError = () => {
      cleanup()
      reject(new Error(`Failed to load video: ${src}`))
    }
    
    const cleanup = () => {
      video.removeEventListener('canplaythrough', onCanPlay)
      video.removeEventListener('error', onError)
      video.remove()
    }
    
    video.addEventListener('canplaythrough', onCanPlay)
    video.addEventListener('error', onError)
    video.src = src
  })
}

/**
 * 预加载单个媒体资源
 */
async function preloadMediaResource(resource: MediaResource): Promise<void> {
  if (resource.type === 'image') {
    return preloadImage(resource.src)
  } else if (resource.type === 'video') {
    return preloadVideo(resource.src)
  }
  throw new Error(`Unsupported media type: ${resource.type}`)
}

/**
 * 从角色数组中提取所有唯一的媒体资源
 */
function extractAllMediaResources(characters: Character[]): MediaResource[] {
  const allResources: MediaResource[] = []
  const seenUrls = new Set<string>()
  
  characters.forEach(character => {
    // 添加缩略图
    if (character.thumbnail && !seenUrls.has(character.thumbnail)) {
      allResources.push({
        type: 'image',
        src: character.thumbnail
      })
      seenUrls.add(character.thumbnail)
    }
    
    // 添加媒体资源
    character.mediaResources.forEach(resource => {
      if (!seenUrls.has(resource.src)) {
        allResources.push(resource)
        seenUrls.add(resource.src)
      }
    })
  })
  
  return allResources
}

/**
 * 预加载所有角色的媒体资源
 */
export async function preloadCharacterResources(
  characters: Character[],
  onProgress?: ProgressCallback
): Promise<void> {
  const allResources = extractAllMediaResources(characters)
  const total = allResources.length
  let loaded = 0
  
  console.log(`开始预加载 ${total} 个媒体资源`)
  
  // 并发加载，但限制并发数量以避免过载
  const concurrencyLimit = 3
  const chunks: MediaResource[][] = []
  
  for (let i = 0; i < allResources.length; i += concurrencyLimit) {
    chunks.push(allResources.slice(i, i + concurrencyLimit))
  }
  
  for (const chunk of chunks) {
    const promises = chunk.map(async (resource) => {
      try {
        await preloadMediaResource(resource)
        loaded++
        
        const progress: PreloadProgress = {
          loaded,
          total,
          percentage: (loaded / total) * 100,
          currentResource: resource.src.split('/').pop() || resource.src
        }
        
        onProgress?.(progress)
        console.log(`已加载: ${progress.currentResource} (${loaded}/${total})`)
      } catch (error) {
        console.warn(`资源加载失败: ${resource.src}`, error)
        loaded++
        
        const progress: PreloadProgress = {
          loaded,
          total,
          percentage: (loaded / total) * 100,
          currentResource: resource.src.split('/').pop() || resource.src
        }
        
        onProgress?.(progress)
      }
    })
    
    await Promise.all(promises)
  }
  
  console.log('所有资源预加载完成')
}

/**
 * 带有最小显示时间的预加载器
 * 如果加载时间过短，则不显示加载器
 */
export async function preloadWithMinimumTime(
  characters: Character[],
  onProgress?: ProgressCallback,
  minimumShowTime: number = 800 // 最小显示时间（毫秒）
): Promise<boolean> {
  const startTime = Date.now()
  
  try {
    await preloadCharacterResources(characters, onProgress)
    
    const loadTime = Date.now() - startTime
    const shouldShow = loadTime >= minimumShowTime
    
    if (shouldShow && loadTime < minimumShowTime) {
      // 如果需要显示但加载时间不够，等待剩余时间
      const remainingTime = minimumShowTime - loadTime
      await new Promise(resolve => setTimeout(resolve, remainingTime))
    }
    
    return shouldShow
  } catch (error) {
    console.error('资源预加载失败:', error)
    return false
  }
}
