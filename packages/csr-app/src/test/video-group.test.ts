import { describe, it, expect, beforeEach, vi } from 'vitest'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { useChatResourcesStore } from '@/store/chat-resources'

// Mock audio manager
vi.mock('@/mobile/composables/useAudioManager', () => ({
  useAudioManager: () => ({
    stopTTS: vi.fn(),
    pauseBgm: vi.fn(),
    playBgm: vi.fn()
  })
}))

// Mock chat events store
vi.mock('@/store/chat-events', () => ({
  useChatEventsStore: () => ({
    sendMessage: vi.fn().mockResolvedValue(undefined),
    messageQueue: []
  })
}))

// Mock chat UI store
vi.mock('@/store/chat-ui', () => ({
  useChatUIStore: () => ({
    isInitializing: false,
    isLoading: false
  })
}))

describe('Video Group Functionality', () => {
  let store: ReturnType<typeof useChatResourcesStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    store = useChatResourcesStore()
  })

  it('should initialize video group state correctly', () => {
    expect(store.videoGroupQueue).toEqual([])
    expect(store.currentVideoGroupIndex).toBe(0)
    expect(store.isPlayingVideoGroup).toBe(false)
    expect(store.shouldStopVideoGroup).toBe(false)
    expect(store.pendingUserMessageForVideoGroup).toBe(null)
  })

  it('should handle user input during video group playback', () => {
    // 模拟视频组播放状态
    store.isPlayingVideoGroup = true
    
    const testMessage = 'Hello during video group'
    const result = store.handleUserInputDuringVideoGroup(testMessage)
    
    expect(result).toBe(true) // 表示消息被缓存
    expect(store.pendingUserMessageForVideoGroup).toBe(testMessage)
  })

  it('should not handle user input when not playing video group', () => {
    store.isPlayingVideoGroup = false
    
    const testMessage = 'Hello when not playing'
    const result = store.handleUserInputDuringVideoGroup(testMessage)
    
    expect(result).toBe(false) // 表示正常处理
    expect(store.pendingUserMessageForVideoGroup).toBe(null)
  })

  it('should stop video group correctly', () => {
    // 设置初始状态
    store.isPlayingVideoGroup = true
    store.shouldStopVideoGroup = false
    store.videoGroupQueue = [{ url: 'test.mp4' }]
    store.pendingUserMessageForVideoGroup = 'test message'
    
    store.stopVideoGroup()
    
    expect(store.shouldStopVideoGroup).toBe(true)
    expect(store.isPlayingVideoGroup).toBe(false)
  })

  it('should validate video group data correctly', async () => {
    // 测试无效数据
    const invalidData = { data: { video_group: null } }
    await store.handlePlayVideoGroupEvent(invalidData)
    expect(store.isPlayingVideoGroup).toBe(false)

    // 测试空数组
    const emptyData = { data: { video_group: [] } }
    await store.handlePlayVideoGroupEvent(emptyData)
    expect(store.isPlayingVideoGroup).toBe(false)
  })

  it('should clear video group state when clearing resources', () => {
    // 设置一些状态
    store.isPlayingVideoGroup = true
    store.videoGroupQueue = [{ url: 'test.mp4' }]
    store.currentVideoGroupIndex = 1
    store.pendingUserMessageForVideoGroup = 'test'
    
    store.clearResources()
    
    expect(store.isPlayingVideoGroup).toBe(false)
    expect(store.videoGroupQueue).toEqual([])
    expect(store.currentVideoGroupIndex).toBe(0)
    expect(store.pendingUserMessageForVideoGroup).toBe(null)
  })
})
