<template>
  <div class="video-group-demo">
    <h2>Video Group Demo</h2>
    
    <!-- 状态显示 -->
    <div class="status-panel">
      <h3>当前状态</h3>
      <p>视频组播放中: {{ chatResourcesStore.isPlayingVideoGroup }}</p>
      <p>当前视频索引: {{ chatResourcesStore.currentVideoGroupIndex }}</p>
      <p>视频组队列长度: {{ chatResourcesStore.videoGroupQueue.length }}</p>
      <p>缓存的用户消息: {{ chatResourcesStore.pendingUserMessageForVideoGroup || '无' }}</p>
    </div>

    <!-- 控制按钮 -->
    <div class="controls">
      <button @click="startVideoGroup" :disabled="chatResourcesStore.isPlayingVideoGroup">
        开始视频组播放
      </button>
      <button @click="stopVideoGroup" :disabled="!chatResourcesStore.isPlayingVideoGroup">
        停止视频组播放
      </button>
      <button @click="sendTestMessage">
        发送测试消息
      </button>
    </div>

    <!-- 用户输入 -->
    <div class="user-input">
      <h3>用户输入测试</h3>
      <input 
        v-model="userMessage" 
        placeholder="输入消息..." 
        @keyup.enter="handleUserInput"
      />
      <button @click="handleUserInput">发送消息</button>
    </div>

    <!-- 日志显示 -->
    <div class="logs">
      <h3>操作日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useChatResourcesStore } from '@/store/chat-resources'
import { useChat4Store } from '@/store/chat4'

const chatResourcesStore = useChatResourcesStore()
const chat4Store = useChat4Store()

const userMessage = ref('')
const logs = ref<string[]>([])

// 添加日志
const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift(`[${timestamp}] ${message}`)
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20)
  }
}

// 模拟视频组数据
const mockVideoGroup = [
  {
    url: 'https://static.playshot.ai/story/r18/1.mp4',
    is_background: false,
    min_watch_duration: 2,
    not_loop: false,
    is_blur: false,
    description: '测试视频1',
    tag: ''
  },
  {
    url: 'https://static.playshot.ai/story/r18/2.mp4',
    is_background: false,
    min_watch_duration: 2,
    not_loop: false,
    is_blur: false,
    description: '测试视频2',
    tag: ''
  },
  {
    url: 'https://static.playshot.ai/story/r18/3.mp4',
    is_background: false,
    min_watch_duration: 2,
    not_loop: false,
    is_blur: false,
    description: '测试视频3',
    tag: ''
  }
]

// 开始视频组播放
const startVideoGroup = async () => {
  addLog('开始播放视频组...')
  
  const eventData = {
    event_type: 'play_video_group',
    timestamp: Date.now(),
    data: {
      video_group: mockVideoGroup
    }
  }
  
  try {
    await chatResourcesStore.handlePlayVideoGroupEvent(eventData)
    addLog('视频组播放已开始')
  } catch (error) {
    addLog(`视频组播放失败: ${error}`)
  }
}

// 停止视频组播放
const stopVideoGroup = () => {
  addLog('停止视频组播放...')
  chatResourcesStore.stopVideoGroup()
  addLog('视频组播放已停止')
}

// 发送测试消息
const sendTestMessage = () => {
  const message = `测试消息 ${Date.now()}`
  addLog(`发送测试消息: ${message}`)
  
  if (chatResourcesStore.isPlayingVideoGroup) {
    const handled = chatResourcesStore.handleUserInputDuringVideoGroup(message)
    if (handled) {
      addLog('消息已缓存，等待视频播放完成')
    }
  } else {
    addLog('当前未播放视频组，消息正常处理')
  }
}

// 处理用户输入
const handleUserInput = () => {
  if (!userMessage.value.trim()) return
  
  const message = userMessage.value.trim()
  addLog(`用户输入: ${message}`)
  
  // 使用 Chat4 Store 的消息处理逻辑
  const shouldSendImmediately = chat4Store.addUserMessage(message)
  
  if (shouldSendImmediately) {
    addLog('消息将立即发送')
  } else {
    addLog('消息已缓存')
  }
  
  userMessage.value = ''
}

// 初始化日志
addLog('Video Group Demo 已加载')
</script>

<style scoped>
.video-group-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.status-panel {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.controls {
  margin-bottom: 20px;
}

.controls button {
  margin-right: 10px;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background: #007bff;
  color: white;
  cursor: pointer;
}

.controls button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.user-input {
  margin-bottom: 20px;
}

.user-input input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 10px;
  width: 200px;
}

.user-input button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background: #28a745;
  color: white;
  cursor: pointer;
}

.logs {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  padding: 4px 0;
  border-bottom: 1px solid #eee;
  font-family: monospace;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}
</style>
