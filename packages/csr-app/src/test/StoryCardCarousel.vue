<template>
  <div class="test-container">
    <h2>StoryCard 轮播功能测试</h2>
    
    <div class="test-grid">
      <!-- 测试1: 单张图片 -->
      <div class="test-item">
        <h3>单张图片</h3>
        <StoryCard :story="singleImageStory" />
      </div>
      
      <!-- 测试2: 多张图片轮播 -->
      <div class="test-item">
        <h3>多张图片轮播</h3>
        <StoryCard :story="multipleImagesStory" />
      </div>
      
      <!-- 测试3: 单个视频 -->
      <div class="test-item">
        <h3>单个视频</h3>
        <StoryCard :story="singleVideoStory" />
      </div>
      
      <!-- 测试4: 混合媒体轮播 -->
      <div class="test-item">
        <h3>混合媒体轮播</h3>
        <StoryCard :story="mixedMediaStory" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import StoryCard from '@/shared/components/StoryCard.vue'
import type { Story } from '@/api/stories'

// 测试数据
const singleImageStory = ref<Story>({
  id: '1',
  title: '单张图片测试',
  description: '这是一个单张图片的测试故事',
  preview_url: 'https://picsum.photos/300/500?random=1',
  bgm_url: '',
  actors: [],
  hot: 100,
  categories: ['测试'],
  coins: 0,
  is_fav: false,
  is_purchased: false,
  is_subscribed: false,
  status: 'normal',
  is_active_skill: false,
  is_support_face_swap: false,
  carousel_image_url: ['https://picsum.photos/300/500?random=1']
})

const multipleImagesStory = ref<Story>({
  id: '2',
  title: '多张图片轮播测试',
  description: '这是一个多张图片轮播的测试故事',
  preview_url: 'https://picsum.photos/300/500?random=2',
  bgm_url: '',
  actors: [],
  hot: 200,
  categories: ['测试', '轮播'],
  coins: 0,
  is_fav: false,
  is_purchased: false,
  is_subscribed: false,
  status: 'normal',
  is_active_skill: false,
  is_support_face_swap: false,
  carousel_image_url: [
    'https://picsum.photos/300/500?random=2',
    'https://picsum.photos/300/500?random=3',
    'https://picsum.photos/300/500?random=4'
  ]
})

const singleVideoStory = ref<Story>({
  id: '3',
  title: '单个视频测试',
  description: '这是一个单个视频的测试故事',
  preview_url: 'https://picsum.photos/300/500?random=5',
  bgm_url: '',
  actors: [],
  hot: 300,
  categories: ['测试', '视频'],
  coins: 0,
  is_fav: false,
  is_purchased: false,
  is_subscribed: false,
  status: 'normal',
  is_active_skill: false,
  is_support_face_swap: false,
  carousel_image_url: ['https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4']
})

const mixedMediaStory = ref<Story>({
  id: '4',
  title: '混合媒体轮播测试',
  description: '这是一个混合媒体轮播的测试故事',
  preview_url: 'https://picsum.photos/300/500?random=6',
  bgm_url: '',
  actors: [],
  hot: 400,
  categories: ['测试', '混合', '轮播'],
  coins: 0,
  is_fav: false,
  is_purchased: false,
  is_subscribed: false,
  status: 'normal',
  is_active_skill: false,
  is_support_face_swap: false,
  carousel_image_url: [
    'https://picsum.photos/300/500?random=6',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    'https://picsum.photos/300/500?random=7'
  ]
})
</script>

<style lang="less" scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-top: 20px;
}

.test-item {
  h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
    font-weight: 600;
  }
}
</style>
