<template>
  <div class="prompt-template-editor">
    <!-- 所有模板配置区域 -->
    <div v-if="templates.length > 0" class="templates-container">
      <div v-for="template in templates" :key="template.id" class="template-panel">
        <div class="template-header">
          <h3>{{ template.description }}</h3>
          <div class="template-scope-badge" :class="template.scope">
            {{ template.scope === 'global' ? '全局' : '场景' }}
          </div>
        </div>

        <div class="template-content">
          <!-- 变量编辑区域 - 只在有变量时显示 -->
          <div v-if="template.variables && template.variables.length > 0" class="variables-editor">
            <div
              v-for="variable in template.variables"
              :key="`${template.id}-${variable.key}`"
              class="variable-item"
            >
              <div class="variable-header">
                <label :for="`${template.id}-${variable.key}`">{{ variable.name }}</label>
                <div class="variable-type">{{ getVariableTypeDisplay(variable.type) }}</div>
              </div>
              <div class="variable-description">{{ variable.description }}</div>

              <!-- 根据变量类型显示不同的输入控件 -->
              <input
                v-if="variable.type === 'text'"
                :id="`${template.id}-${variable.key}`"
                type="text"
                :value="getVariableValue(template.id, variable.key)"
                @input="
                  (e) =>
                    updateVariableValue(
                      template.id,
                      variable.key,
                      (e.target as HTMLInputElement).value,
                      template
                    )
                "
                :placeholder="variable.default"
              />
              <textarea
                v-else-if="variable.type === 'textarea'"
                :id="`${template.id}-${variable.key}`"
                :value="getVariableValue(template.id, variable.key)"
                @input="
                  (e) =>
                    updateVariableValue(
                      template.id,
                      variable.key,
                      (e.target as HTMLTextAreaElement).value,
                      template
                    )
                "
                :placeholder="variable.default"
                rows="3"
              ></textarea>
              <select
                v-else-if="variable.type === 'select'"
                :id="`${template.id}-${variable.key}`"
                :value="getVariableValue(template.id, variable.key)"
                @change="
                  (e) =>
                    updateVariableValue(
                      template.id,
                      variable.key,
                      (e.target as HTMLSelectElement).value,
                      template
                    )
                "
              >
                <option v-for="option in getSelectOptions(variable)" :key="option" :value="option">
                  {{ option }}
                </option>
              </select>
              <input
                v-else
                :id="`${template.id}-${variable.key}`"
                type="text"
                :value="getVariableValue(template.id, variable.key)"
                @input="
                  (e) =>
                    updateVariableValue(
                      template.id,
                      variable.key,
                      (e.target as HTMLInputElement).value,
                      template
                    )
                "
                :placeholder="variable.default"
              />
            </div>
          </div>

          <!-- 预览区域 -->
          <div class="template-preview">
            <div class="preview-label">当前预览</div>
            <div class="preview-content">{{ getPreviewPrompt(template) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-else-if="isLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>正在加载模板...</p>
    </div>

    <!-- 无模板状态 -->
    <div v-else class="empty-state">
      <p>暂无可用模板</p>
    </div>

    <!-- 最终结果区域 -->
    <div class="result-section">
      <div class="result-header">
        <h3>最终提示词</h3>
        <div class="header-buttons">
          <!-- <button class="expand-button" @click="handleExpandClick">
            {{ isExpanded ? '收起' : '展开' }}
          </button> -->
          <button class="modal-button" @click="handleModalOpen">
            <svg viewBox="0 0 24 24" class="icon-fullscreen">
              <path
                fill="currentColor"
                d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"
              />
            </svg>
            全屏编辑
          </button>
        </div>
      </div>
      <textarea
        v-model="localValue"
        class="template-result"
        :class="{ expanded: isExpanded }"
        :rows="isExpanded ? 12 : 6"
        placeholder="手动编辑或从上方配置模板"
        @input="autoResize"
        ref="resultTextarea"
      ></textarea>

      <!-- 长文本编辑弹窗 -->
      <TextEditorModal
        v-model="showModal"
        title="编辑提示词"
        v-model:content="localValue"
        placeholder="输入提示词内容"
        @save="handleModalSave"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { getPromptTemplates, type PromptTemplate } from '@/api/prompt'
import TextEditorModal from './TextEditorModal.vue'

const props = defineProps<{
  modelValue: string
  scope?: string // 可选作用域过滤: 'global' 或 'scene'
}>()

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'template-selected': [template: PromptTemplate | null]
}>()

// 本地值，用于替代直接修改props
const localValue = ref(props.modelValue)

// 所有可用的模板
const templates = ref<PromptTemplate[]>([])
// 变量值的存储 - 改为嵌套结构，每个模板有自己的变量集合
const variableValues = ref<Record<string, Record<string, string>>>({})
// 加载状态
const isLoading = ref(true)
// 最终提示词区域展开状态
const isExpanded = ref(false)
// 最终提示词文本框引用
const resultTextarea = ref<HTMLTextAreaElement | null>(null)
// 弹窗显示状态
const showModal = ref(false)

// 处理弹窗保存事件
const handleModalSave = (value: string) => {
  // 当弹窗保存时更新本地值
  localValue.value = value
  // 关闭弹窗
  showModal.value = false
}

// 处理弹窗打开
const handleModalOpen = () => {
  console.log('点击全屏编辑按钮')
  showModal.value = true
}

// 自动调整文本框高度
const autoResize = (e: Event) => {
  const textarea = e.target as HTMLTextAreaElement
  if (textarea) {
    // 先将高度设为自动，让浏览器计算实际高度
    textarea.style.height = 'auto'
    // 然后设置为实际内容高度
    textarea.style.height = `${textarea.scrollHeight}px`
  }
}

// 监听 localValue 变化更新父组件
watch(localValue, (newValue) => {
  emit('update:modelValue', newValue)

  // 当内容变化时自动调整高度
  setTimeout(() => {
    if (resultTextarea.value) {
      resultTextarea.value.style.height = 'auto'
      resultTextarea.value.style.height = `${resultTextarea.value.scrollHeight}px`
    }
  }, 0)
})

// 监听 props.modelValue 变化更新本地值
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== localValue.value) {
      localValue.value = newValue
    }
  },
  { immediate: true }
)

// 从本地存储获取全局变量值
const getGlobalVariableValues = () => {
  const storedValues = localStorage.getItem('promptTemplateGlobalValues')
  return storedValues ? JSON.parse(storedValues) : {}
}

// 安全获取变量值，解决v-model中不能使用可选链的问题
const getVariableValue = (templateId: string, variableKey: string) => {
  // 确保模板id对应的对象存在
  if (!variableValues.value[templateId]) {
    variableValues.value[templateId] = {}
  }
  return variableValues.value[templateId][variableKey] || ''
}

// 更新变量值
const updateVariableValue = (
  templateId: string,
  variableKey: string,
  value: string,
  template: PromptTemplate
) => {
  // 确保模板id对应的对象存在
  if (!variableValues.value[templateId]) {
    variableValues.value[templateId] = {}
  }
  variableValues.value[templateId][variableKey] = value

  // 更新模板预览
  updateTemplatePrompt(template)
}

// 保存全局变量值到本地存储
const saveGlobalVariableValues = () => {
  const globalValues = getGlobalVariableValues()
  const updatedValues = { ...globalValues }

  // 遍历所有模板
  templates.value.forEach((template) => {
    // 只处理全局作用域的模板
    if (template.scope === 'global' && variableValues.value[template.id]) {
      // 如果模板有变量，保存该模板的所有变量值
      if (template.variables && template.variables.length > 0) {
        template.variables.forEach((variable) => {
          const value = variableValues.value[template.id]?.[variable.key]
          if (value) {
            updatedValues[variable.key] = value
          }
        })
      }
      // 如果模板没有变量，不需要特殊处理，因为没有变量需要保存
    }
  })

  localStorage.setItem('promptTemplateGlobalValues', JSON.stringify(updatedValues))
}

// 处理特定模板的预览内容
const getPreviewPrompt = (template: PromptTemplate) => {
  // 如果 variables 为空或不存在，直接返回原始 prompt
  if (!template.variables || template.variables.length === 0) {
    console.log(`Template ${template.name} has no variables, using original prompt`)
    return template.prompt
  }

  let result = template.prompt

  // 替换所有变量
  template.variables.forEach((variable) => {
    const value = getVariableValue(template.id, variable.key) || variable.default
    const pattern = new RegExp(`{{\\s*\\.${variable.key}\\s*}}`, 'g')
    result = result.replace(pattern, value)
  })

  return result
}

// 更新特定模板的变量值和预览
const updateTemplatePrompt = (template: PromptTemplate) => {
  // 通知父组件已选择模板
  emit('template-selected', template)

  // 如果是全局作用域，保存到本地存储
  if (template.scope === 'global') {
    saveGlobalVariableValues()
  }

  // 更新最终提示词 - 合并所有已配置模板的内容
  updateFinalPrompt()
}

// 更新最终提示词，合并所有已配置模板的内容
const updateFinalPrompt = () => {
  // 收集所有已配置的模板
  const configuredPrompts = templates.value
    .map((template) => {
      // 获取模板预览内容
      const promptText = getPreviewPrompt(template)
      // 只返回非空的内容
      return promptText.trim() ? promptText : null
    })
    .filter((text) => text !== null) // 过滤掉空内容

  // 将所有配置好的提示词组合成最终结果
  if (configuredPrompts.length > 0) {
    localValue.value = configuredPrompts.join('\n\n')
  }
}

// 获取提示词模板
const fetchPromptTemplates = async () => {
  isLoading.value = true
  try {
    const response = await getPromptTemplates()
    if (response.data.code === '0' && response.data.data) {
      let allTemplates = response.data.data.prompt_templates

      // 如果指定了scope，则按scope过滤
      if (props.scope) {
        allTemplates = allTemplates.filter((t) => t.scope === props.scope)
      }

      templates.value = allTemplates

      // 初始化变量值存储结构
      const globalValues = getGlobalVariableValues()

      templates.value.forEach((template) => {
        // 为每个模板创建变量值存储
        if (!variableValues.value[template.id]) {
          variableValues.value[template.id] = {}
        }

        // 如果模板有变量，设置变量默认值并加载全局变量
        if (template.variables && template.variables.length > 0) {
          template.variables.forEach((variable) => {
            // 如果是全局作用域变量且有存储值，使用存储的值
            if (template.scope === 'global' && globalValues[variable.key]) {
              variableValues.value[template.id][variable.key] = globalValues[variable.key]
            } else {
              // 否则使用默认值
              variableValues.value[template.id][variable.key] = variable.default
            }
          })
        } else {
          // 如果模板没有变量，确保变量值对象存在但为空
          console.log(`Template ${template.name} has no variables, initializing empty object`)
          variableValues.value[template.id] = {}
        }
      })

      // 初始化最终提示词
      if (templates.value.length > 0 && !localValue.value) {
        updateFinalPrompt()
      }
    }
  } catch (error) {
    console.error('获取提示词模板失败:', error)
  } finally {
    isLoading.value = false
  }
}

// 根据变量类型返回显示名称
const getVariableTypeDisplay = (type: string) => {
  const typeMap: Record<string, string> = {
    text: '文本',
    textarea: '多行文本',
    select: '选择项',
    number: '数字'
  }
  return typeMap[type] || '文本'
}

// 从变量默认值获取选择项列表
const getSelectOptions = (variable: any) => {
  // 如果默认值包含逗号，假设它是选项列表
  if (variable.default && variable.default.includes(',')) {
    return variable.default.split(',').map((item: string) => item.trim())
  }
  // 如果是select类型但没有明确的选项，至少提供默认值作为选项
  return [variable.default]
}

// 在组件挂载时获取模板
onMounted(() => {
  fetchPromptTemplates()

  // 初始化时自动调整文本框高度
  setTimeout(() => {
    if (resultTextarea.value) {
      resultTextarea.value.style.height = 'auto'
      resultTextarea.value.style.height = `${resultTextarea.value.scrollHeight}px`
    }
  }, 100)
})

// 尝试从当前值匹配模板
watch(
  () => props.modelValue,
  (newValue) => {
    if (!newValue || templates.value.length === 0) return

    // 尝试查找匹配当前值的模板
    const matchedTemplate = templates.value.find((template) => {
      // 检查模板是否是当前值的基础
      return newValue.includes(template.name) || newValue.includes(template.description)
    })

    if (matchedTemplate) {
      // 尝试解析变量值
      if (matchedTemplate.variables && matchedTemplate.variables.length > 0) {
        matchedTemplate.variables.forEach((variable) => {
          // 简单匹配，实际场景可能需要更复杂的正则
          const pattern = new RegExp(`{{\\s*\\.${variable.key}\\s*}}`, 'g')
          if (!pattern.test(newValue)) {
            // 如果变量在当前值中被替换了，尝试提取
            const simplePattern = variable.default
            if (newValue.includes(simplePattern)) {
              if (!variableValues.value[matchedTemplate.id]) {
                variableValues.value[matchedTemplate.id] = {}
              }
              variableValues.value[matchedTemplate.id][variable.key] = variable.default
            }
          }
        })
      }
      // 如果模板没有变量，不需要特殊处理，因为没有变量需要解析
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.prompt-template-editor {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;

  .templates-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .template-panel {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 16px;
    width: 100%;

    .template-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 12px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      margin-bottom: 16px;

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
      }
    }

    .template-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
  }

  .loading-state,
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 120px;
    width: 100%;
    color: rgba(255, 255, 255, 0.6);
  }

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top-color: #ca93f2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  .result-section {
    width: 100%;
    margin-top: 4px;
    padding-top: 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);

    .result-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
      }

      .header-buttons {
        display: flex;
        gap: 8px;
      }

      .expand-button {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        color: rgba(255, 255, 255, 0.8);
        padding: 4px 8px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.15);
          color: rgba(255, 255, 255, 0.95);
        }
      }

      .modal-button {
        display: flex;
        align-items: center;
        gap: 4px;
        background: rgba(202, 147, 242, 0.1);
        border: 1px solid rgba(202, 147, 242, 0.2);
        border-radius: 4px;
        color: rgba(202, 147, 242, 0.8);
        padding: 4px 8px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;

        .icon-fullscreen {
          width: 14px;
          height: 14px;
        }

        &:hover {
          background: rgba(202, 147, 242, 0.15);
          color: rgba(202, 147, 242, 0.95);
        }
      }
    }
  }

  .template-scope-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;

    &.global {
      background-color: rgba(0, 255, 0, 0.1);
      color: rgba(0, 255, 0, 0.8);
    }

    &.scene {
      background-color: rgba(0, 0, 255, 0.1);
      color: rgba(0, 0, 255, 0.8);
    }
  }

  .variables-editor {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .variable-item {
      .variable-header {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
        gap: 8px;

        label {
          display: block;
          font-size: 13px;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.8);
        }

        .variable-type {
          display: inline-block;
          padding: 2px 6px;
          border-radius: 4px;
          background-color: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.8);
          font-size: 12px;
        }
      }

      .variable-description {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.6);
        margin-bottom: 6px;
      }

      input,
      textarea,
      select {
        width: 100%;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: #fff;
        padding: 8px 10px;
        font-size: 13px;

        &:focus {
          border-color: #ca93f2;
          outline: none;
          box-shadow: 0 0 0 2px rgba(202, 147, 242, 0.2);
        }
      }

      textarea {
        min-height: 60px;
        resize: vertical;
      }

      select {
        appearance: none;
        background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 10px center;
        padding-right: 30px;
        cursor: pointer;

        &:hover {
          background-color: rgba(255, 255, 255, 0.08);
        }

        option {
          background-color: #2c2c2c;
          color: #fff;
        }
      }
    }
  }

  .template-preview {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    padding: 12px;
    margin-top: 8px;

    .preview-label {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.6);
      margin-bottom: 8px;
      font-weight: 600;
    }

    .preview-content {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.9);
      white-space: pre-wrap;
      word-break: break-word;
      line-height: 1.5;
      padding: 8px;
      background: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
    }
  }

  .template-result {
    width: 100%;
    min-height: 80px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    color: #fff;
    padding: 12px;
    font-size: 14px;
    resize: vertical;
    transition: all 0.3s ease;
    line-height: 1.5;
    max-width: 100%;

    &.expanded {
      min-height: 200px;
      width: 100%;
      max-width: 100%;
    }

    &:focus {
      border-color: #ca93f2;
      outline: none;
      box-shadow: 0 0 0 2px rgba(202, 147, 242, 0.2);
    }
  }
}
</style>
