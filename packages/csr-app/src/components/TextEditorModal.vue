<template>
  <div class="text-editor-modal" v-if="modelValue">
    <div class="modal-overlay" @click="closeModal"></div>
    <div class="modal-container">
      <div class="modal-header">
        <h3>{{ title }}</h3>
        <button class="close-button" @click="closeModal">
          <svg viewBox="0 0 24 24" class="icon-close">
            <path
              fill="currentColor"
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            />
          </svg>
        </button>
      </div>
      <div class="modal-body">
        <textarea
          v-model="localValue"
          class="text-editor"
          :placeholder="placeholder"
          @input="updateValue"
          ref="textareaRef"
        ></textarea>
      </div>
      <div class="modal-footer">
        <button class="cancel-button" @click="closeModal">取消</button>
        <button class="save-button" @click="saveAndClose">保存</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, nextTick } from 'vue'

const props = defineProps<{
  modelValue: boolean
  title?: string
  content: string
  placeholder?: string
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'update:content': [value: string]
  save: [value: string]
}>()

// 本地值，用于编辑
const localValue = ref(props.content)
const textareaRef = ref<HTMLTextAreaElement | null>(null)

// 监听内容变化
watch(
  () => props.content,
  (newValue) => {
    localValue.value = newValue
  }
)

// 关闭弹窗
const closeModal = () => {
  emit('update:modelValue', false)
}

// 保存并关闭
const saveAndClose = () => {
  emit('update:content', localValue.value)
  emit('save', localValue.value)
  closeModal()
}

// 实时更新内容
const updateValue = () => {
  emit('update:content', localValue.value)
}

// 弹窗打开时自动聚焦文本框
watch(
  () => props.modelValue,
  (isOpen) => {
    if (isOpen) {
      nextTick(() => {
        if (textareaRef.value) {
          textareaRef.value.focus()
          // 将光标移到文本末尾
          textareaRef.value.selectionStart = textareaRef.value.value.length
          textareaRef.value.selectionEnd = textareaRef.value.value.length
        }
      })
    }
  }
)

// 定义处理ESC键的函数
const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Escape' && props.modelValue) {
    closeModal()
  }
}

// 组件挂载时
onMounted(() => {
  // 添加ESC键关闭弹窗
  window.addEventListener('keydown', handleKeyDown)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyDown)
})
</script>

<style lang="less" scoped>
.text-editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;

  .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(3px);
  }

  .modal-container {
    position: relative;
    width: 80%;
    max-width: 900px;
    height: 80%;
    max-height: 700px;
    background: rgba(32, 0, 56, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    animation: modal-appear 0.2s ease-out;

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
      }

      .close-button {
        background: transparent;
        border: none;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: rgba(255, 255, 255, 0.6);
        border-radius: 4px;
        padding: 0;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.9);
        }

        .icon-close {
          width: 20px;
          height: 20px;
        }
      }
    }

    .modal-body {
      flex: 1;
      padding: 20px;
      overflow: hidden;

      .text-editor {
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: #fff;
        padding: 16px;
        font-size: 16px;
        line-height: 1.6;
        resize: none;

        &:focus {
          border-color: #ca93f2;
          outline: none;
          box-shadow: 0 0 0 2px rgba(202, 147, 242, 0.2);
        }
      }
    }

    .modal-footer {
      display: flex;
      justify-content: flex-end;
      padding: 16px 20px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      gap: 12px;

      button {
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s;

        &.cancel-button {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.8);

          &:hover {
            background: rgba(255, 255, 255, 0.15);
          }
        }

        &.save-button {
          background: rgba(202, 147, 242, 0.2);
          border: 1px solid rgba(202, 147, 242, 0.4);
          color: #ca93f2;

          &:hover {
            background: rgba(202, 147, 242, 0.3);
          }
        }
      }
    }
  }
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
