<template>
  <div v-if="showMonitor" class="route-performance-monitor">
    <div class="monitor-header">
      <h4>🚀 路由性能监控</h4>
      <button @click="toggleMonitor" class="toggle-btn">{{ isExpanded ? '收起' : '展开' }}</button>
    </div>

    <div v-if="isExpanded" class="monitor-content">
      <!-- 缓存状态 -->
      <div class="section">
        <h5>📦 缓存状态</h5>
        <div class="stats">
          <div class="stat-item">
            <span class="label">缓存组件数:</span>
            <span class="value">{{ cacheInfo.size }}</span>
          </div>
          <div class="stat-item">
            <span class="label">总加载次数:</span>
            <span class="value">{{ cacheInfo.stats.total }}</span>
          </div>
          <div class="stat-item">
            <span class="label">成功率:</span>
            <span class="value success">{{ successRate }}%</span>
          </div>
          <div class="stat-item">
            <span class="label">缓存命中:</span>
            <span class="value cached">{{ cacheInfo.stats.cached }}</span>
          </div>
        </div>
      </div>

      <!-- 预加载状态 -->
      <div class="section">
        <h5>⚡ 预加载状态</h5>
        <div class="preload-status">
          <div class="status-item">
            <span class="label">设备类型:</span>
            <span class="value">{{ preloadStatus.isMobile ? '移动端' : 'PC端' }}</span>
          </div>
          <div class="status-item">
            <span class="label">初始化状态:</span>
            <span :class="['value', preloadStatus.isInitialized ? 'success' : 'error']">
              {{ preloadStatus.isInitialized ? '已初始化' : '未初始化' }}
            </span>
          </div>
        </div>
      </div>

      <!-- 缓存的路由 -->
      <div class="section">
        <h5>📋 已缓存路由</h5>
        <div class="cached-routes">
          <div v-for="key in cacheInfo.keys.slice(0, 8)" :key="key" class="route-item">
            {{ key }}
          </div>
          <div v-if="cacheInfo.keys.length > 8" class="more-routes">
            +{{ cacheInfo.keys.length - 8 }} 更多...
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="section">
        <h5>🛠️ 操作</h5>
        <div class="actions">
          <button @click="refreshData" class="action-btn">刷新数据</button>
          <button @click="clearCache" class="action-btn danger">清除缓存</button>
          <button @click="preloadChatGroup" class="action-btn">预加载聊天组</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { getPreloadStatus, preloadRouteGroup } from '@/utils/route-preload-init'
import { getCacheInfo, clearComponentCache } from '@/utils/advanced-route-loader'

// 响应式数据
const showMonitor = ref(false)
const isExpanded = ref(false)
const cacheInfo = ref(getCacheInfo())
const preloadStatus = ref(getPreloadStatus())

// 计算属性
const successRate = computed(() => {
  const { total, success } = cacheInfo.value.stats
  return total > 0 ? Math.round((success / total) * 100) : 0
})

// 定时器
let updateTimer: number | null = null

// 方法
const toggleMonitor = () => {
  isExpanded.value = !isExpanded.value
}

const refreshData = () => {
  cacheInfo.value = getCacheInfo()
  preloadStatus.value = getPreloadStatus()
}

const clearCache = () => {
  clearComponentCache()
  refreshData()
  console.log('🧹 路由缓存已清除')
}

const preloadChatGroup = async () => {
  try {
    await preloadRouteGroup('chat')
    refreshData()
    console.log('✅ 聊天组预加载完成')
  } catch (error) {
    console.error('❌ 聊天组预加载失败:', error)
  }
}

// 生命周期
onMounted(() => {
  // 只在开发环境显示
  showMonitor.value = import.meta.env.DEV

  // 定时更新数据
  updateTimer = window.setInterval(refreshData, 5000)

  // 监听键盘快捷键 Ctrl+Shift+P 切换监控器
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.ctrlKey && event.shiftKey && event.key === 'P') {
      event.preventDefault()
      showMonitor.value = !showMonitor.value
    }
  }

  document.addEventListener('keydown', handleKeydown)

  // 清理函数
  onUnmounted(() => {
    if (updateTimer) {
      clearInterval(updateTimer)
    }
    document.removeEventListener('keydown', handleKeydown)
  })
})
</script>

<style scoped>
.route-performance-monitor {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 320px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 9999;
  font-size: 12px;
  backdrop-filter: blur(10px);
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.monitor-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.toggle-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
}

.toggle-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.monitor-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.section {
  margin-bottom: 16px;
}

.section h5 {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: #4caf50;
}

.stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.label {
  color: #ccc;
}

.value {
  font-weight: 600;
}

.value.success {
  color: #4caf50;
}

.value.cached {
  color: #2196f3;
}

.value.error {
  color: #f44336;
}

.preload-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.cached-routes {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.route-item {
  background: rgba(33, 150, 243, 0.2);
  color: #2196f3;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
}

.more-routes {
  background: rgba(255, 255, 255, 0.1);
  color: #ccc;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
}

.actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-btn {
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid #4caf50;
  color: #4caf50;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s;
}

.action-btn:hover {
  background: rgba(76, 175, 80, 0.3);
}

.action-btn.danger {
  background: rgba(244, 67, 54, 0.2);
  border-color: #f44336;
  color: #f44336;
}

.action-btn.danger:hover {
  background: rgba(244, 67, 54, 0.3);
}

/* 滚动条样式 */
.monitor-content::-webkit-scrollbar {
  width: 4px;
}

.monitor-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.monitor-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}
</style>
