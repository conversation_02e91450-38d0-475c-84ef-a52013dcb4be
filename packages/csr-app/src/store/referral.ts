import { defineStore } from 'pinia'

export const useReferralStore = defineStore('referral', {
  state: () => ({
    sourceCode: localStorage.getItem('source_code') || '',
    inviteCode: localStorage.getItem('invite_code') || ''
  }),
  actions: {
    setSourceCode(code: string) {
      this.sourceCode = code
      localStorage.setItem('source_code', code)
    },
    getSourceCode() {
      if (this.sourceCode && window?.collectEvent) {
        // @ts-ignore
        window.collectEvent('config', {
          kol_source: this.sourceCode
        })
      }
      return this.sourceCode
    },
    setInviteCode(code: string) {
      this.inviteCode = code
      localStorage.setItem('invite_code', code)
    },
    getInviteCode() {
      if (this.inviteCode && window?.collectEvent) {
        // @ts-ignore
        window.collectEvent('config', {
          kol_source: this.sourceCode
        })
      }
      return this.inviteCode
    }
  },
  persist: true
})
