import { defineStore } from 'pinia'
import type { RouteLocationNormalized } from 'vue-router'

interface AgeVerificationState {
  isAgeVerified: boolean
  showAgeVerificationModal: boolean
  targetRoute: RouteLocationNormalized | null
}

export const useAgeVerificationStore = defineStore('ageVerification', {
  state: (): AgeVerificationState => ({
    isAgeVerified: localStorage.getItem('isAgeVerified') === 'true',
    showAgeVerificationModal: false,
    targetRoute: null
  }),

  actions: {
    setAgeVerified() {
      this.isAgeVerified = true
      localStorage.setItem('isAgeVerified', 'true')
    },
    showModal() {
      this.showAgeVerificationModal = true
    },
    hideModal() {
      this.showAgeVerificationModal = false
    },
    setTargetRoute(route: RouteLocationNormalized) {
      this.targetRoute = route
    },

    reset() {
      this.isAgeVerified = false
      localStorage.removeItem('isAgeVerified')
      this.showAgeVerificationModal = false
      this.targetRoute = null
    }
  }
})
