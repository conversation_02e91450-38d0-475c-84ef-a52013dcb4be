import { defineStore } from 'pinia'

export const useGenderStore = defineStore('gender', {
  state: () => ({
    showModal: false,
    pendingAction: null as (() => Promise<void>) | null
  }),
  actions: {
    showGenderModal(action?: () => Promise<void>) {
      this.showModal = true
      if (action) {
        this.pendingAction = action
      }
    },
    hideGenderModal() {
      this.showModal = false
      this.pendingAction = null
    },
    async handleGenderSelect() {
      if (this.pendingAction) {
        await this.pendingAction()
        this.pendingAction = null
      }
      this.showModal = false
    }
  }
})
