import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getABTestVariant, trackABTestExposure } from '@/utils/abtest'
import { getExperimentConfig } from '@/config/abtest'
import type { ABTestVariant } from '@/utils/abtest'

/**
 * AB测试全局状态管理
 * 避免重复计算，提升性能
 */
export const useABTestStore = defineStore('abtest', () => {
  // 缓存所有实验的变体结果
  const experimentVariants = ref<Record<string, ABTestVariant>>({})

  // 曝光追踪记录，避免重复上报
  const exposureTracked = ref<Record<string, boolean>>({})

  /**
   * 获取实验变体（带缓存）
   */
  const getVariant = (experimentName: string): ABTestVariant | null => {
    // 如果已经缓存，直接返回
    if (experimentVariants.value[experimentName]) {
      return experimentVariants.value[experimentName]
    }

    // 获取实验配置
    const config = getExperimentConfig(experimentName)
    if (!config) {
      console.warn(`实验 "${experimentName}" 不存在`)
      return null
    }

    // 计算变体并缓存
    const variant = getABTestVariant(experimentName, config)
    experimentVariants.value[experimentName] = variant

    return variant
  }

  /**
   * 检查是否为指定变体
   */
  const isVariant = (experimentName: string, targetVariant: ABTestVariant): boolean => {
    const variant = getVariant(experimentName)
    return variant === targetVariant
  }

  /**
   * 上报曝光事件（防重复，优化性能）
   */
  const trackExposure = (experimentName: string, context?: Record<string, any>) => {
    const trackingKey = `${experimentName}_${JSON.stringify(context || {})}`

    // 如果已经上报过，跳过
    if (exposureTracked.value[trackingKey]) {
      return
    }

    // 标记为已上报
    exposureTracked.value[trackingKey] = true

    // 使用 requestIdleCallback 延迟上报，避免阻塞主线程
    if ('requestIdleCallback' in window) {
      requestIdleCallback(
        () => {
          trackABTestExposure(experimentName, context)
        },
        { timeout: 3000 }
      )
    } else {
      // 回退到 setTimeout
      setTimeout(() => {
        trackABTestExposure(experimentName, context)
      }, 50)
    }
  }

  /**
   * 批量初始化实验（页面加载时调用）
   */
  const initializeExperiments = (experimentNames: string[]) => {
    experimentNames.forEach((name) => {
      getVariant(name) // 触发缓存
    })
  }

  // 为常用实验创建计算属性
  const storyTagsDisplay = computed(() => ({
    variant: getVariant('story_tags_display'),
    isA: isVariant('story_tags_display', 'A'),
    isB: isVariant('story_tags_display', 'B'),
    isC: isVariant('story_tags_display', 'C'),
    isD: isVariant('story_tags_display', 'D')
  }))

  const mobileDefaultTheme = computed(() => ({
    variant: getVariant('mobile_default_theme'),
    isA: isVariant('mobile_default_theme', 'A'),
    isB: isVariant('mobile_default_theme', 'B'),
    isC: isVariant('mobile_default_theme', 'C'),
    isD: isVariant('mobile_default_theme', 'D')
  }))

  const storyCardDesign = computed(() => ({
    variant: getVariant('story_card_design'),
    isA: isVariant('story_card_design', 'A'),
    isB: isVariant('story_card_design', 'B'),
    isC: isVariant('story_card_design', 'C'),
    isD: isVariant('story_card_design', 'D')
  }))

  return {
    // 状态
    experimentVariants,
    exposureTracked,

    // 方法
    getVariant,
    isVariant,
    trackExposure,
    initializeExperiments,

    // 常用实验的计算属性
    storyTagsDisplay,
    mobileDefaultTheme,
    storyCardDesign
  }
})
