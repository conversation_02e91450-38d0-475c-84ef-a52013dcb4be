import { defineStore } from 'pinia'
import { ref, computed, toRaw } from 'vue'
import { nanoid } from 'nanoid'
import yaml from 'js-yaml'
import type {
  GameConfig,
  Scene,
  GameEvent,
  PreloadAsset,
  ActionHandler,
  GameEventType,
  GameEventPlot,
  Condition,
  ConditionValue,
  ConditionConfig,
  StoryGenerationParams
} from '@/types/editor'
import {
  getStoryList,
  type StoryInfo,
  createStory,
  updateStory,
  type UpdateStoryRequest,
  getStoryDetail,
  type StoryDetailResponse,
  generateStoryResources,
  getStoryStatus
} from '@/api/editor'
import type { Actor } from '@/api/stories'
import { getActorList } from '@/api/editor'
import { Message } from '@arco-design/web-vue'

export interface StoryConfig {
  project_id: string
  actor_id: string
  project_name: string
  description: string
  preview_url: string
  preview_video: string
  story_text: string
  bgm_url: string
  story_content: string
  status: string
  is_active_skill?: boolean
  skill_ids?: string[]
  is_support_face_swap?: boolean
}

export const useEditorStore = defineStore(
  'editor',
  () => {
    // 状态
    const gameConfig = ref<GameConfig>({
      preloads: [],
      scenes: []
    })

    const storyConfig = ref<StoryConfig>({
      project_id: '',
      actor_id: '',
      project_name: '',
      description: '',
      preview_url: '',
      preview_video: '',
      story_text: '',
      bgm_url: '',
      story_content: '',
      status: '',
      is_support_face_swap: false,
      is_active_skill: false
    })

    const currentSceneId = ref<string>('')
    const currentEventId = ref<string>('')
    const isModified = ref(false)

    const storyList = ref<StoryInfo[]>([])
    const loading = ref(false)

    const actors = ref<Actor[]>([])
    const selectedActor = ref<Actor | null>(null)

    const TOTAL_HEART_VALUE = 100
    const allocatableHeartValue = computed(() => {
      // 计算所有场景已分配的心值总和
      const totalAllocated = gameConfig.value.scenes.reduce((sum, scene) => {
        return sum + (scene.action_handlers?.[0]?.params.heart_value || 0)
      }, 0)

      // 返回剩余可分配的心值
      return TOTAL_HEART_VALUE - totalAllocated
    })

    // 使用 computed 来同步 storyConfig 和其他状态
    const storyName = computed({
      get: () => storyConfig.value.project_name,
      set: (val: string) => {
        storyConfig.value.project_name = val
        isModified.value = true
      }
    })

    const currentStoryId = computed({
      get: () => storyConfig.value.project_id,
      set: (val: string) => {
        storyConfig.value.project_id = val
      }
    })

    // 计算属性
    const currentScene = computed(() =>
      gameConfig.value.scenes.find((s) => s.id === currentSceneId.value)
    )

    const currentEvent = computed(() => {
      if (!currentScene.value) return null
      return currentScene.value.events.find((e) => e.id === currentEventId.value)
    })

    // Actions
    function addPreload(asset: PreloadAsset) {
      gameConfig.value.preloads.push(asset)
      isModified.value = true
    }

    function removePreload(url: string) {
      gameConfig.value.preloads = gameConfig.value.preloads.filter((p) => p.url !== url)
      isModified.value = true
    }

    function addScene(name: string): Scene {
      const scene: Scene = {
        id: nanoid(),
        name,
        events: []
      }
      gameConfig.value.scenes.push(scene)
      isModified.value = true
      return scene
    }

    function removeScene(sceneId: string) {
      // 从 scenes 数组中删除场景
      gameConfig.value.scenes = gameConfig.value.scenes.filter((s) => s.id !== sceneId)
      // 如果删除的是当前选中的场景，清空当前场景ID
      if (currentSceneId.value === sceneId) {
        currentSceneId.value = ''
        currentEventId.value = '' // 同时清空当前事件ID
      }
      isModified.value = true
    }

    function addEvent(sceneId: string, event: Partial<GameEvent>): GameEvent | null {
      const scene = gameConfig.value.scenes.find((s) => s.id === sceneId)
      if (scene) {
        const newEvent: GameEvent = {
          id: nanoid(),
          ...event
        } as GameEvent
        scene.events.push(newEvent)
        isModified.value = true
        return newEvent
      }
      return null
    }

    function addActionHandler(sceneId: string, handler: ActionHandler): ActionHandler | null {
      const scene = gameConfig.value.scenes.find((s) => s.id === sceneId)
      if (scene) {
        if (!scene.action_handlers) {
          scene.action_handlers = []
        }
        scene.action_handlers.push(handler)
        isModified.value = true
        return handler
      }
      return null
    }

    function updateActionHandler(sceneId: string, handler: ActionHandler) {
      const scene = gameConfig.value.scenes.find((s) => s.id === sceneId)
      if (scene && scene.action_handlers) {
        // Since we only support one action handler per scene, we can just replace the first one
        scene.action_handlers = [handler]
        isModified.value = true
      }
    }

    function updateEvent(sceneId: string, eventId: string, updates: Partial<GameEvent>) {
      const scene = gameConfig.value.scenes.find((s) => s.id === sceneId)
      if (scene) {
        const eventIndex = scene.events.findIndex((e) => e.id === eventId)
        if (eventIndex > -1) {
          scene.events[eventIndex] = {
            ...scene.events[eventIndex],
            ...updates
          }
          isModified.value = true
        }
      }
    }

    function updateScene(scene: Scene) {
      const index = gameConfig.value.scenes.findIndex((s) => s.id === scene.id)
      if (index > -1) {
        gameConfig.value.scenes[index] = scene
        isModified.value = true
      }
    }

    function removeEvent(sceneId: string, eventId: string) {
      const scene = gameConfig.value.scenes.find((s) => s.id === sceneId)
      if (scene) {
        scene.events = scene.events.filter((e) => e.id !== eventId)
        if (currentEventId.value === eventId) {
          currentEventId.value = ''
        }
        isModified.value = true
      }
    }

    function setCurrentScene(sceneId: string) {
      currentSceneId.value = sceneId
      currentEventId.value = ''
    }

    function setCurrentEvent(eventId: string) {
      if (!currentScene.value) return
      currentEventId.value = eventId
    }

    function exportToYAML(): string {
      // 先过滤掉 ID 为 '~' 的场景，再按照特殊场景排序
      const sortedScenes = gameConfig.value.scenes
        .filter((scene) => scene.id !== '~')
        .sort((a, b) => {
          // 确保 _BEGIN_ 在最前
          if (a.id === '_BEGIN_') return -1
          if (b.id === '_BEGIN_') return 1
          // 确保 _END_ 在最后
          if (a.id === '_END_') return 1
          if (b.id === '_END_') return -1
          return 0
        })

      // 处理场景之间的线性关系
      const processedScenes = sortedScenes.map((scene, index, array) => {
        // 处理 parent_id
        let parent_id
        if (scene.id === '_BEGIN_') {
          parent_id = '~'
        } else if (scene.id === '_END_') {
          // _END_ 的 parent_id 应该指向最后一个普通章节
          const lastNormalScene = array
            .slice(0, -1)
            .reverse()
            .find((s) => s.id !== '_BEGIN_' && s.id !== '_END_')
          parent_id = lastNormalScene?.id || '~'
        } else {
          // 普通章节的 parent_id 指向前一个章节
          const prevScene = array[index - 1]
          parent_id = prevScene?.id || '~'
        }
        const processedScene: {
          id: string
          name: string
          parent_id: string
          events: { type: GameEventType; plot: GameEventPlot }[]
          next_scene_id?: string
          conditions?: { expression: string; echo: string }[]
          action_handlers?: ActionHandler[]
          story_generation_params?: StoryGenerationParams
          scene_group?: string
        } = {
          id: scene.id,
          name: scene.name,
          parent_id,
          events: scene.events.map((event) => ({
            // 导出时不包含 id
            type: event.type,
            plot:
              event.type === 'show_chat_options'
                ? ({
                    ...event.plot,
                    options: event.plot.options?.map((option) => {
                      const { option_id, action, ...rest } = option
                      return rest
                    })
                  } as GameEventPlot)
                : event.type === 'message' &&
                  event.plot.content &&
                  typeof event.plot.content === 'object'
                ? {
                    ...event.plot,
                    content: {
                      ...event.plot.content,
                      text: event.plot.content.text?.trim() || ''
                    }
                  }
                : event.plot
          }))
        }
        // 只有当scene_group 有值时才添加
        if (scene.scene_group && scene.scene_group.trim() !== '') {
          processedScene.scene_group = scene.scene_group.trim()
        }
        // 只有当 next_scene_id 有值时才添加
        if (scene.next_scene_id && scene.next_scene_id.trim() !== '') {
          processedScene.next_scene_id = scene.next_scene_id.trim()
        }

        // 处理 conditions 属性，只有当 conditions 存在且不为空时才导出
        if (scene.conditions && scene.conditions.length > 0) {
          processedScene.conditions = scene.conditions.map((condition) => {
            const { expression, echo } = condition
            return { expression, echo }
          })
        }

        // 如果有 action_handlers，添加到导出数据中
        if (scene.action_handlers?.length) {
          processedScene.action_handlers = scene.action_handlers.map((handler) => {
            // 处理 streamer_tpl，移除开头和结尾的空格并在尾部添加换行符
            if (handler.params?.streamer_tpl) {
              handler.params.streamer_tpl = handler.params.streamer_tpl.trim() + '\n'
            }
            return {
              type: handler.type,
              params: handler.params
            }
          })
        }

        // 如果有 story_generation_params，添加到导出数据中
        if (scene.story_generation_params) {
          processedScene.story_generation_params = {
            plots: scene.story_generation_params.plots.map((plot) => ({
              sentence: plot.sentence || '',
              character: plot.character?.map((char) => ({
                name: char.name || '',
                lora_id: char.lora_id || '',
                clothes: char.clothes || ''
              })) || [
                {
                  name: '',
                  lora_id: '',
                  clothes: ''
                }
              ],
              location: plot.location || ''
            }))
          }
        }

        return processedScene
      })

      const exportConfig = {
        preloads: gameConfig.value.preloads,
        scenes: processedScenes
      }

      return yaml.dump(exportConfig, {
        indent: 2,
        lineWidth: -1,
        noRefs: true
      })
    }

    function importFromYAML(yamlString: string) {
      try {
        const imported = yaml.load(yamlString) as GameConfig

        const processedConfig = {
          preloads: imported.preloads || [],
          scenes: imported.scenes || []
        }
        console.log('processedConfig', processedConfig)
        gameConfig.value = processedConfig as GameConfig
        isModified.value = false
        return true
      } catch (error) {
        console.error('Failed to import YAML:', error)
        return false
      }
    }

    const fetchStoryList = async () => {
      loading.value = true
      try {
        const { data } = await getStoryList()
        storyList.value = data.data.project_infos || []
      } finally {
        loading.value = false
      }
    }

    const fetchActorList = async () => {
      try {
        const { data } = await getActorList()
        actors.value = data.data.actors.map((actor) => ({
          ...actor,
          subtitle: ''
        }))
      } catch (error) {
        console.error('Failed to fetch actors:', error)
        Message.error('获取角色列表失败')
      }
    }

    const setSelectedActor = (actor: Actor | null) => {
      selectedActor.value = actor
      if (actor) {
        storyConfig.value.actor_id = actor.id
        isModified.value = true
      }
    }

    const saveStory = async () => {
      if (!storyConfig.value.project_name) {
        throw new Error('请输入故事名称')
      }

      try {
        // 如果没有 project_id，先创建故事
        if (!storyConfig.value.project_id) {
          const { data } = await createStory(storyConfig.value.project_name)
          storyConfig.value.project_id = data.data.project_info.project_id
        }

        // 准备更新数据
        const updateData: UpdateStoryRequest = {
          project_id: storyConfig.value.project_id,
          project_name: storyConfig.value.project_name,
          description: storyConfig.value.description,
          preview_url: storyConfig.value.preview_url,
          preview_video: storyConfig.value.preview_video,
          story_text: storyConfig.value.story_text,
          bgm_url: storyConfig.value.bgm_url,
          story_content: exportToYAML(),
          ...(selectedActor.value?.id ? { actor_id: selectedActor.value.id } : {}), // 只有有值时才添加 actor_id
          is_active_skill: storyConfig.value.is_active_skill,
          ...(storyConfig.value.is_active_skill ? { skill_ids: storyConfig.value.skill_ids } : {}),
          ...(storyConfig.value.is_support_face_swap ? { is_support_face_swap: true } : {})
        }

        // 更新故事内容
        const { data } = await updateStory(updateData)
        if (data.code === '0') {
          isModified.value = false
          return true
        } else {
          throw new Error(data.message)
        }
      } catch (error) {
        console.error('Failed to save story:', error)
        throw error
      }
    }

    const updateStoryConfig = (config: Partial<StoryConfig>) => {
      Object.assign(storyConfig.value, config)
      isModified.value = true
    }

    // 重置所有状态
    const resetState = () => {
      gameConfig.value = {
        preloads: [],
        scenes: []
      }
      storyConfig.value = {
        project_id: '',
        actor_id: '',
        project_name: '',
        description: '',
        preview_url: '',
        preview_video: '',
        story_text: '',
        bgm_url: '',
        story_content: '',
        status: ''
      }
      currentSceneId.value = ''
      currentEventId.value = ''
      isModified.value = false
      selectedActor.value = null
    }

    // 加载故事详情
    const loadStoryDetail = async (projectId: string) => {
      try {
        loading.value = true
        const { data } = await getStoryDetail(projectId)
        const detail = data.data.project_info
        // 更新 storyConfig
        storyConfig.value = {
          project_id: detail.project_id,
          actor_id: detail.actor_id,
          project_name: detail.name,
          description: detail.description,
          preview_url: detail.preview_url,
          preview_video: detail.preview_video,
          story_text: detail.story_text,
          bgm_url: detail.bgm_url,
          story_content: detail.story_content,
          status: detail.status
        }

        // 如果有角色ID，设置选中的角色
        if (detail.actor_id) {
          // 确保已加载角色列表
          if (actors.value.length === 0) {
            await fetchActorList()
          }
          const actor = actors.value.find((a) => a.id === detail.actor_id)
          if (actor) {
            selectedActor.value = actor
          }
        }

        // 如果有 story_content，导入游戏配置
        if (detail.story_content) {
          importFromYAML(detail.story_content)
        }

        isModified.value = false
        return true
      } catch (error) {
        console.error('Failed to load story detail:', error)
        Message.error('加载故事详情失败')
        return false
      } finally {
        loading.value = false
      }
    }

    const generateResources = async () => {
      if (!storyConfig.value.project_id) {
        throw new Error('项目ID不能为空')
      }
      if (!gameConfig.value.daydream_user_id) {
        throw new Error('白日梦用户ID不能为空')
      }

      try {
        // 检查故事状态
        const { data: statusData } = await getStoryStatus(storyConfig.value.project_id)
        if (statusData.data.is_resources_generated) {
          throw new Error('该故事已经提交过素材生成任务')
        }

        // 提交生成任务
        const { data } = await generateStoryResources({
          project_id: storyConfig.value.project_id,
          brm_user_id: gameConfig.value.daydream_user_id
        })
        if (data.code === '0') {
          return true
        } else {
          throw new Error(data.message)
        }
      } catch (error) {
        console.error('Failed to generate resources:', error)
        throw error
      }
    }

    return {
      // State
      gameConfig,
      storyConfig,
      currentSceneId,
      currentEventId,
      isModified,
      storyList,
      loading,
      actors,
      selectedActor,
      storyName,
      currentStoryId,
      allocatableHeartValue,

      // Getters
      currentScene,
      currentEvent,

      // Actions
      addPreload,
      removePreload,
      addScene,
      removeScene,
      addEvent,
      addActionHandler,
      updateActionHandler,
      updateEvent,
      removeEvent,
      setCurrentScene,
      setCurrentEvent,
      exportToYAML,
      importFromYAML,
      fetchStoryList,
      fetchActorList,
      setSelectedActor,
      saveStory,
      updateStoryConfig,
      resetState,
      loadStoryDetail,
      updateScene,
      generateResources
    }
  },
  {
    persist: false
  }
)
