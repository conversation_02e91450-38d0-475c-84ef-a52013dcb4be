import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useStorage } from '@vueuse/core'
import { Overlay, Ending } from './chat-types'
import { useStoryStore } from '@/store/story'

export const useChatUIStore = defineStore('chatUI', () => {
  // 状态
  const isInitializing = ref(false)
  const isError = ref(false)
  const initialized = ref(false)
  const loading = ref(false)
  const streaming = ref(false)
  const disableInput = ref(false)
  const hideInput = ref(false)
  const inputPlaceholder = ref('')
  const isEnding = ref(false)
  const endings = ref<Ending | null>(null)
  const isOverlayBlocking = ref(false)
  const overlay = ref<Overlay | null>(null)
  const overlayResolve = ref<(() => void) | null>(null)
  const isRedirectToChat = ref(false)
  const isTaskTipCollapsed = ref(false)
  const isShowAlert = ref(false)
  const isLegacyVersion = ref(false)
  const isMuted = ref(false)
  const showEndingShare = ref(false)

  // Actions
  /**
   * 处理显示覆盖层事件
   */
  async function handleShowOverlayEvent(data: any) {
    overlay.value = data.data.overlay

    if (data.data.button) {
      overlay.value.button = data.data.button
    }
    isOverlayBlocking.value = true
    // 返回一个 Promise，只有当用户点击按钮后才会 resolve
    return new Promise<void>((resolve) => {
      overlayResolve.value = resolve
    })
  }

  /**
   * 处理覆盖层按钮点击
   */
  function handleOverlayButtonClick() {
    overlay.value = null
    isOverlayBlocking.value = false
    if (overlayResolve.value) {
      overlayResolve.value()
      overlayResolve.value = null
    }
  }

  /**
   * 处理显示结束事件
   */
  function handleShowEndingEvent(data: any) {
    const storyStore = useStoryStore()
    disableInput.value = true
    isEnding.value = true
    endings.value = {
      html: data.data.content.html
    }
    if (storyStore.currentStory.version === '3') return
    // 显示分享弹窗
    if (!storyStore.currentStory.is_active_skill) return
    showEndingShare.value = true
  }

  /**
   * 处理显示警告事件
   */
  function handleShowAlertEvent() {
    isShowAlert.value = true
  }

  /**
   * 设置初始化状态
   */
  function setInitializing(value: boolean) {
    isInitializing.value = value
  }

  /**
   * 设置错误状态
   */
  function setError(value: boolean) {
    isError.value = value
  }

  /**
   * 设置初始化完成状态
   */
  function setInitialized(value: boolean) {
    initialized.value = value
  }

  /**
   * 设置加载状态
   */
  function setLoading(value: boolean) {
    loading.value = value
  }

  /**
   * 设置流式传输状态
   */
  function setStreaming(value: boolean) {
    streaming.value = value
  }

  /**
   * 设置禁用输入状态
   */
  function setDisableInput(value: boolean) {
    disableInput.value = value
  }

  /**
   * 设置隐藏输入状态
   */
  function setHideInput(value: boolean) {
    hideInput.value = value
  }

  /**
   * 设置输入占位符
   */
  function setInputPlaceholder(value: string) {
    inputPlaceholder.value = value
  }

  /**
   * 设置静音状态
   */
  function setMuted(value: boolean) {
    isMuted.value = value
  }

  /**
   * 设置任务提示折叠状态
   */
  function setTaskTipCollapsed(value: boolean) {
    isTaskTipCollapsed.value = value
  }

  /**
   * 设置旧版本状态
   */
  function setLegacyVersion(value: boolean) {
    isLegacyVersion.value = value
  }

  /**
   * 清空UI状态
   */
  function clearUIState() {
    isEnding.value = false
    endings.value = null
    overlay.value = null
    isOverlayBlocking.value = false
    overlayResolve.value = null
    hideInput.value = false
    inputPlaceholder.value = ''
    isError.value = false
    isTaskTipCollapsed.value = false
    disableInput.value = false
  }

  return {
    // 状态
    isInitializing,
    isError,
    initialized,
    loading,
    streaming,
    disableInput,
    hideInput,
    inputPlaceholder,
    isEnding,
    endings,
    isOverlayBlocking,
    overlay,
    overlayResolve,
    isRedirectToChat,
    isTaskTipCollapsed,
    isShowAlert,
    isLegacyVersion,
    isMuted,
    showEndingShare,

    // Actions
    handleShowOverlayEvent,
    handleOverlayButtonClick,
    handleShowEndingEvent,
    handleShowAlertEvent,
    setInitializing,
    setError,
    setInitialized,
    setLoading,
    setStreaming,
    setDisableInput,
    setHideInput,
    setInputPlaceholder,
    setMuted,
    setTaskTipCollapsed,
    setLegacyVersion,
    clearUIState
  }
})
