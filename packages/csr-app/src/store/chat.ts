import { defineStore } from 'pinia'
import type { Message, ChatOptions, RatingForm, RatingHist<PERSON> } from '@/types/chat'
import { startChatSSE, sendMessageSSE, fetchVideo } from '@/services/chat'
import { generateTTS, submitRatingForm, getRatingHistory } from '@/api/chat'
import { v4 as uuidv4 } from 'uuid'
import { JSONParse } from '@/utils'
import { ref, computed, watch } from 'vue'
import { useUserStore } from '@/store/user'
import { useStoryStore } from '@/store/story'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import router from '@/router'
import { useRechargeStore } from './recharge'
import { useStorage } from '@vueuse/core'
import { useSkillStore } from '@/store/skill'
import { professionalVideoCache } from '@/utils/professionalVideoCache'
import { UserAvatar } from '@/api/user-character'
import { How<PERSON>, How<PERSON> } from 'howler'
import { useAudioManager } from '@/mobile/composables/useAudioManager'

// Create audio manager instance
const audioManager = useAudioManager()

interface BaseSSEData {
  event_type: string
  timestamp: number
  data: Record<string, any>
}
interface Task {
  task_id: number
  percent: number
  description: string
}

interface ChatState {
  messages: Message[]
  conversationId: string | null
  loading: boolean
  error: string | null
  currentActorId: string | null
  streaming: boolean
  videoUrl: string | null
  isPlayingVideo: boolean
  waitSeconds: number | null
  chatOptions: ChatOptions[] | null
  chatCard2: ChatOptions[] | null // 新交互的chat选项
  disableInput: boolean
  // backgroundImage: string | null
  actionOptions: ChatOptions[] | null
  isEnding: boolean
  messageQueue: BaseSSEData[]
  isProcessingQueue: boolean
  overlay: {
    text: string
    position: 'bottom' | 'top' | 'center'
    display_time: 'after' | 'before'
    button?: {
      icon: string
      text: string
      action: string
    }
  } | null
  isOverlayBlocking: boolean
  overlayResolve: (() => void) | null
  videoElementRef: HTMLVideoElement | null
  endings: { html: string } | null
  preloadCache: Set<string>
  paymentRequired: boolean
  bgmAudio: Howl | null
  bgmUrl: string | null
  isBgmPlaying: boolean
  isMuted: boolean
  isChatSectionVisible: boolean
  currentAudio: Howl | null
  isPlaying: boolean
  currentPlayingMessageId: string | null
  ttsLoading: boolean
  videoLoadingProgress: number
  isActorThinking: boolean
  isFirstVideo: boolean
  tasks: Task[]
  currentTaskId: number | null
  backgroundImageCouldBeFullScreen: boolean
  inputPlaceholder: string
  initialized: boolean
  animatedImagesMap: Record<string, string[]>
  backgroundImageMap: Record<string, string>
  backgroundVideoMap: Record<string, string>
  totalProgress: number
  onFaceDetect: boolean
  isShowUploadImage: boolean
  ttsCache: Map<string, string>
  hideInput: boolean
  audioInstances: Map<string, Howl>
  isRedirectToChat: boolean
  isError: boolean
  isInitializing: boolean
  heartValue: number
  messageTypingPromise: Promise<void> | null
  messageTypingResolve: ((value: void | PromiseLike<void>) => void) | null
  fullHeartValueAllowShowMessage: boolean
  isTaskTipCollapsed: boolean
  isShowAlert: boolean
  lastSceneTimestamp: number | null
  lastScene: string | null
  isLegacyVersion: boolean
  chatHistory: Message[]
  isShouldRestart: boolean
  hasNextMessage: boolean // 新增：记录下一个事件是否是消息
  hasNextVideo: boolean // 新增：记录下一个事件是否是视频
  messageDelayTime: number // 新增：消息延迟时间
  showEndingShare: boolean
  heartValueOptionSceneIds: string[]
  videoMinWatchDuration: number
  preloadingCount: number
  isResourceLoading: boolean
  userAvatar: null | { id?: string; url: string; type: 'predefined' | 'custom' }
  isVideoLoading: boolean
  currentVoiceId: string | null // 新增：当前使用的语音ID
  currentVoiceProvider: string | null // 新增：当前使用的语音提供商
  countdownInterval: ReturnType<typeof setInterval> | null
  timeoutId: ReturnType<typeof setTimeout> | null
}

interface EventHandler {
  [key: string]: (data: any) => Promise<void>
}

// Add new interface for preload data
interface PreloadItem {
  url: string
  type: 'video' | 'image'
}

// 预加载函数
async function preloadImage(url: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve()
    img.onerror = () => reject()
    img.src = url
  })
}

async function preloadVideo(url: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    video.preload = 'auto'
    video.oncanplaythrough = () => resolve()
    video.onerror = () => reject()
    video.src = url
  })
}

// 新增：从URL加载资源到Blob
async function fetchResourceAsBlob(url: string): Promise<Blob | null> {
  try {
    const response = await fetch(url)
    if (!response.ok) {
      console.warn(`Failed to fetch resource: ${url}`)
      return null
    }
    return await response.blob()
  } catch (error) {
    console.error(`Error fetching resource: ${url}`, error)
    return null
  }
}

// 新增：预加载并缓存视频资源
async function preloadAndCacheVideo(url: string): Promise<void> {
  try {
    // 先检查专业缓存中是否已存在
    const cachedBlob = await professionalVideoCache.getCachedVideo(url)
    if (cachedBlob) {
      return
    }

    // 获取视频资源并缓存
    const blob = await fetchResourceAsBlob(url)
    if (blob) {
      await professionalVideoCache.cacheVideo(url, blob)
      // console.log(`Video cached successfully: ${url}`)
    }
  } catch (error) {
    console.warn(`Failed to preload and cache video: ${url}`, error)
  }
}

// 新增：预加载并缓存图片资源
async function preloadAndCacheImage(url: string): Promise<void> {
  try {
    // 先预加载图片确保可加载
    await preloadImage(url)

    // 获取图片资源并缓存
    const blob = await fetchResourceAsBlob(url)
    if (blob) {
      await professionalVideoCache.cacheVideo(url, blob) // 复用专业缓存功能，因为底层存储机制相同
      // console.log(`Image cached successfully: ${url}`)
    }
  } catch (error) {
    console.warn(`Failed to preload and cache image: ${url}`, error)
  }
}

// Update the preload functions
async function preloadResources(urls: PreloadItem[]): Promise<void> {
  const preloadPromises = urls.map((item) => {
    if (item.type === 'video') {
      return preloadAndCacheVideo(item.url)
    } else {
      return preloadAndCacheImage(item.url)
    }
  })

  await Promise.allSettled(preloadPromises)
}

export const useChatStore = defineStore('chat', {
  state: (): ChatState => {
    // Create persistent storage with useStorage
    const persistedTasks = useStorage<Task[]>('chat-tasks', [])
    const persistedCurrentTaskId = useStorage<number | null>('chat-current-task-id', null)
    const persistedAnimatedImagesMap = useStorage<Record<string, string[]>>(
      'chat-animated-images-map',
      {}
    )
    const persistedBackgroundImageMap = useStorage<Record<string, string>>(
      'chat-background-image-map',
      {}
    )
    const persistedBackgroundVideoMap = useStorage<Record<string, string>>(
      'chat-background-video-map',
      {}
    )
    const persistedIsShouldRestart = useStorage<boolean>('chat-is-should-restart', true)
    const showEndingShare = useStorage<boolean>('chat-show-ending-share', false)
    return {
      messages: [],
      conversationId: null,
      loading: false,
      error: null,
      currentActorId: null,
      streaming: false,
      videoUrl: null,
      isPlayingVideo: false,
      waitSeconds: null,
      chatOptions: null,
      chatCard2: null,
      disableInput: false,
      // backgroundImage: null,
      actionOptions: null,
      isEnding: false,
      messageQueue: [],
      isProcessingQueue: false,
      overlay: null,
      isOverlayBlocking: false,
      overlayResolve: null,
      videoElementRef: null,
      endings: null,
      preloadCache: new Set(),
      paymentRequired: false,
      bgmAudio: null as Howl | null,
      bgmUrl: null,
      isBgmPlaying: false,
      isMuted: false,
      isChatSectionVisible: true,
      currentAudio: null as Howl | null,
      isPlaying: false,
      currentPlayingMessageId: null as string | null,
      ttsLoading: false,
      videoLoadingProgress: 0,
      isActorThinking: false,
      isFirstVideo: false,
      tasks: persistedTasks.value,
      currentTaskId: persistedCurrentTaskId.value,
      backgroundImageCouldBeFullScreen: false,
      inputPlaceholder: '',
      initialized: false,
      animatedImagesMap: persistedAnimatedImagesMap.value,
      backgroundImageMap: persistedBackgroundImageMap.value,
      backgroundVideoMap: persistedBackgroundVideoMap.value,
      totalProgress: 0,
      onFaceDetect: false,
      isShowUploadImage: false,
      ttsCache: new Map(),
      hideInput: false,
      audioInstances: new Map<string, Howl>(),
      isRedirectToChat: false,
      isError: false,
      isInitializing: false,
      heartValue: 0,
      messageTypingPromise: null as Promise<void> | null,
      messageTypingResolve: null as ((value: void | PromiseLike<void>) => void) | null,
      fullHeartValueAllowShowMessage: false,
      isTaskTipCollapsed: false,
      isShowAlert: false,
      lastSceneTimestamp: null as number | null,
      lastScene: null as string | null,
      isLegacyVersion: false,
      chatHistory: [],
      isShouldRestart: persistedIsShouldRestart.value,
      hasNextMessage: false, // 新增
      hasNextVideo: false, // 新增
      messageDelayTime: 300, // 新增：默认延迟时间
      showEndingShare: showEndingShare.value,
      heartValueOptionSceneIds: [],
      videoMinWatchDuration: 2,
      preloadingCount: 0,
      isResourceLoading: false,
      userAvatar: null,
      isVideoLoading: false,
      currentVoiceId: null, // 新增：当前使用的语音ID
      currentVoiceProvider: null, // 新增：当前使用的语音提供商
      countdownInterval: null as ReturnType<typeof setInterval> | null,
      timeoutId: null as ReturnType<typeof setTimeout> | null
    }
  },

  getters: {
    sortedMessages: (state) =>
      [...state.messages].sort(
        (a, b) => new Date(a.create_time).getTime() - new Date(b.create_time).getTime()
      ),
    hasPendingEvents: (state) => state.messageQueue.length > 0 || state.isProcessingQueue,
    animatedImages: (state) =>
      state.currentActorId ? state.animatedImagesMap[state.currentActorId] || [] : [],
    backgroundImage: (state) =>
      state.currentActorId ? state.backgroundImageMap[state.currentActorId] || null : null,
    backgroundVideo: (state) =>
      state.currentActorId ? state.backgroundVideoMap[state.currentActorId] || '' : ''
  },

  actions: {
    // Add watchers for persisted state in the store setup
    $onInit() {
      // Watch tasks changes
      watch(
        () => this.tasks,
        (newTasks) => {
          if (newTasks.length === 0) {
            useStorage('chat-tasks', []).value = []
          } else {
            useStorage('chat-tasks', []).value = newTasks
          }
        },
        { deep: true }
      )

      // Watch currentTaskId changes
      watch(
        () => this.currentTaskId,
        (newTaskId) => {
          useStorage('chat-current-task-id', null).value = newTaskId
        }
      )

      // Watch animatedImagesMap changes
      watch(
        () => this.animatedImagesMap,
        (newMap) => {
          if (Object.keys(newMap).length === 0) {
            useStorage('chat-animated-images-map', {}).value = {}
          } else {
            useStorage('chat-animated-images-map', {}).value = newMap
          }
        },
        { deep: true }
      )

      // Watch backgroundImageMap changes
      watch(
        () => this.backgroundImageMap,
        (newMap) => {
          if (Object.keys(newMap).length === 0) {
            useStorage('chat-background-image-map', {}).value = {}
          } else {
            useStorage('chat-background-image-map', {}).value = newMap
          }
        },
        { deep: true }
      )

      // Watch backgroundVideoMap changes
      watch(
        () => this.backgroundVideoMap,
        (newMap) => {
          if (Object.keys(newMap).length === 0) {
            useStorage('chat-background-video-map', {}).value = {}
          } else {
            useStorage('chat-background-video-map', {}).value = newMap
          }
        },
        { deep: true }
      )
      // Watch isShouldRestart changes
      watch(
        () => this.isShouldRestart,
        (newVal) => {
          useStorage('chat-is-should-restart', newVal).value = newVal
        }
      )
    },
    setShouldRestart(value: boolean) {
      this.isShouldRestart = value
    },

    // 提交评分表单
    async submitRatingForm(formData: RatingForm) {
      const { data } = await submitRatingForm(formData)
      return data.isOk
    },
    async handleHeartValueEvent(data: any) {
      this.heartValue = data.data.heart_value
      if (data.data.scene_ids && this.chatCard2?.length && this.heartValue === 100) {
        this.heartValueOptionSceneIds = data.data.scene_ids
        this.chatCard2 = this.chatCard2?.filter((option: ChatOptions) =>
          this.heartValueOptionSceneIds.includes(option.scene_id)
        )
        this.heartValueOptionSceneIds = []
      }

      this.fullHeartValueAllowShowMessage = data.data.is_allow_message
    },
    async handleDebugEvent(data: any) {
      if (!data.data.debug_messages) return

      const timestamp = new Date().toLocaleTimeString().replace(/:\d{2}\.\d{3}\s\w+/, '')
      let count = 0

      data.data.debug_messages.forEach((message: string) => {
        setTimeout(() => {
          console.log(
            '%c[' + timestamp + ']%c %c[DEBUG]%c ' + message,
            'color: #666; font-family: monospace;',
            '',
            'background: #000; color: #0f0; font-family: monospace; padding: 2px 4px; border-radius: 2px; text-shadow: 0 0 2px #0f0;',
            'color: #2196f3; font-family: monospace; text-shadow: 0 0 1px rgba(33,150,243,0.5);'
          )
        }, count * 100)
        count++
      })
    },
    async handleSceneTransitionEvent(data: any) {
      const chatStore = useChatStore()
      // 设置定时器
      const seconds = data.data?.seconds || 0

      // 定义倒计时函数
      let remainingSeconds = seconds
      if (seconds > 0) {
        this.countdownInterval = setInterval(() => {
          // 样式化输出倒计时
          const countdownStyle =
            'background: #ff5722; color: white; padding: 2px 5px; border-radius: 3px; font-weight: bold;'
          console.log(
            `%cScene transition in ${remainingSeconds} ${
              remainingSeconds === 1 ? 'second' : 'seconds'
            }...`,
            countdownStyle
          )

          remainingSeconds--

          // 当倒计时结束时，清除定时器
          if (remainingSeconds < 0) {
            clearInterval(this.countdownInterval)
            console.log(
              '%cScene transition starting now!',
              'background: #4CAF50; color: white; padding: 2px 5px; border-radius: 3px; font-weight: bold;'
            )
          }
        }, 1000)
        // 设置场景转换的触发器
        this.setTimeoutId = setTimeout(() => {
          // 发送一个空消息，触发场景切换
          chatStore.sendMessage('__NEXT_SCENE__', '__NEXT_SCENE__')
        }, seconds * 1000)
      } else {
        chatStore.sendMessage('', null, 'config', null, null, 0)
      }
    },
    async handleShowSceneEvent(data: any) {
      // 如果这个事件的场景跟lastScene不同，则是场景发生了变化，如果有计时器场景，则需要清除
      if (
        this.lastScene &&
        this.lastScene !== data.data.scene_id &&
        this.countdownInterval &&
        this.setTimeoutId
      ) {
        clearInterval(this.countdownInterval)
        clearTimeout(this.setTimeoutId)
      }
      const scene = data.data.scene_id
      if (scene) {
        const storyStore = useStoryStore()
        const currentTimestamp = Date.now()

        // 上报场景变化
        reportEvent(ReportEvent.SceneChange, {
          storyId: storyStore.currentStory?.id,
          actorId: storyStore.currentActor?.id,
          currentScene: scene
        })
        if (window.fbq) {
          window.fbq('trackCustom', 'SceneChange', {
            scene: scene,
            storyId: storyStore.currentStory?.id,
            actorId: storyStore.currentActor?.id
          })
        }

        // 如果有上一个场景，计算并上报耗时
        if (this.lastSceneTimestamp && this.lastScene && this.lastScene !== scene) {
          const duration = currentTimestamp - this.lastSceneTimestamp
          reportEvent(ReportEvent.SceneDuration, {
            storyId: storyStore.currentStory?.id,
            actorId: storyStore.currentActor?.id,
            fromScene: this.lastScene,
            toScene: scene,
            duration // 耗时，单位毫秒
          })
        }

        // 检查新场景是否有voice_config事件
        // 如果queue中没有voice_config事件，则重置currentVoiceId
        const hasVoiceConfig = this.messageQueue.some(
          (event) => event.event_type === 'voice_config'
        )

        if (!hasVoiceConfig && this.lastScene !== scene) {
          // 如果新场景没有语音配置事件，重置为默认语音
          this.currentVoiceId = null
          this.currentVoiceProvider = null
          console.log('Scene changed, reset voice_id and provider to default')
        }

        // 更新场景信息
        this.lastScene = scene
        this.lastSceneTimestamp = currentTimestamp
      }
    },
    async handleOverlayButtonClick() {
      this.overlay = null
      this.isOverlayBlocking = false
      if (this.overlayResolve) {
        this.overlayResolve()
        this.overlayResolve = null
      }
      await this.processEventQueue()
    },

    async handleMessageEvent(data: any) {
      const messageData = {
        ...data.data
      }

      // Guard clause: only process messages with text content
      if (!messageData.content.text) {
        return
      }

      if (messageData.sender_type !== 'actor') {
        this.messages.push(messageData)
        return
      }

      // Process text by removing asterisks and trimming
      const processedText = messageData.content.text.replace(/\*(.*?)\*/g, '').trim()

      // Set thinking state while processing
      this.isActorThinking = true

      // Prepare TTS if there's processed text, message ID, and valid actor ID
      if (processedText && messageData.id && this.currentActorId) {
        // Request TTS but don't auto-play - ActorMessage component controls playback
        const ttsPromise = audioManager.prepareTTS(
          processedText,
          messageData.id,
          this.currentActorId,
          this.currentVoiceId,
          this.currentVoiceProvider
        )

        // Wait for TTS to load or timeout after 5 seconds
        const timeoutPromise = new Promise((resolve) => setTimeout(resolve, 5000))
        await Promise.race([ttsPromise, timeoutPromise]).catch((error) =>
          console.error('Failed to prepare TTS:', error)
        )
      }
      // End thinking state regardless of TTS success
      this.isActorThinking = false

      // Create promise for typing animation
      this.messageTypingPromise = new Promise((resolve) => {
        this.messageTypingResolve = resolve
      })

      // Add message to the message list
      this.messages.push(messageData)

      // Guard clause: return if message is being skipped
      if (!this.messageTypingPromise) {
        return
      }

      // Wait for the configured delay before proceeding
      await new Promise((resolve) => setTimeout(resolve, this.messageDelayTime))
    },

    // 更新所有音频实例的静音状态
    updateAllAudioStates() {
      // 使用 audioManager 处理所有音频状态更新
      audioManager.updateAllAudioStates()
    },

    // 清理所有音频实例
    clearAudioInstances() {
      // 使用 audioManager 清理所有音频实例
      audioManager.cleanup()
    },

    // async playBgm(url?: string) {
    //   // 使用 audioManager 处理背景音乐播放
    //   // 如果有传入URL，直接使用传入的URL
    //   if (url) {
    //     console.log('playBgm called with URL:', url)
    //     const success = await audioManager.playBgm(url)
    //     if (success) {
    //       console.log('BGM started successfully with URL:', url)
    //     } else {
    //       console.warn('Failed to play BGM with URL:', url)
    //     }
    //     return
    //   }

    //   // 如果没有传入URL但有缓存的URL，使用缓存的URL
    //   if (this.bgmUrl) {
    //     console.log('playBgm using cached URL:', this.bgmUrl)
    //     const success = await audioManager.playBgm(this.bgmUrl)
    //     if (success) {
    //       console.log('BGM started successfully with cached URL:', this.bgmUrl)
    //     } else {
    //       console.warn('Failed to play BGM with cached URL:', this.bgmUrl)
    //     }
    //     return
    //   }

    //   console.log('No BGM URL available')
    // },

    pauseBgm() {
      // 使用 audioManager 处理背景音乐暂停
      audioManager.pauseBgm()
    },

    async handleShowImageEvent(data: any) {
      // 停止 TTS 播放
      audioManager.stopTTS()
      if (this.currentActorId && this.backgroundVideoMap[this.currentActorId]) {
        this.backgroundVideoMap[this.currentActorId] = ''
      }
      // 设置新背景图片
      if (this.currentActorId) {
        this.backgroundImageMap[this.currentActorId] = data.data.url
      }
      this.backgroundImageCouldBeFullScreen = data.data.is_fullscreen

      // 隐藏聊天区域
      this.isChatSectionVisible = false

      // if (data.data.is_fullscreen) {
      //   // 还需要等待背景图片完全加载
      //   await preloadImage(data.data.url)
      //   // 等待背景图片过渡完成（2秒加上额外缓冲时间
      //   await new Promise((resolve) => setTimeout(resolve, 2000))
      // }

      // 显示聊天区域
      this.isChatSectionVisible = true
    },

    async handleWaitEvent(data: any) {
      this.waitSeconds = data.data.seconds
      await new Promise((resolve) => setTimeout(resolve, data.data.seconds * 1000))
      this.waitSeconds = null
    },

    async handleShowTipsEvent(data: any) {
      const storyStore = useStoryStore()
      const tipsMessage: Message = {
        id: uuidv4(),
        msg_type: 'text',
        sender_type: 'tips',
        content: {
          html: data.data.content.html
        },
        create_time: data.data.create_time,
        sender: {
          avatar_url: '',
          name: ''
        }
      }
      this.messages.push(tipsMessage)
      reportEvent(ReportEvent.TaskTipExposure, {
        storyId: storyStore.currentStory?.id,
        actorId: storyStore.currentActor,
        tipId: data.data?.tip_id,
        tipText: data.data?.content?.html
      })
    },

    async handleShowChatOptionsEvent(data: any) {
      console.log(123123)
      const { style, options, allow_input, hide_input, input_placeholder } = data.data

      if (style !== 'card') {
        this.chatOptions = options
      } else {
        this.chatCard2 = options
      }
      console.log(this.chatOptions, 'chatOptionschatOptionschatOptions')
      this.disableInput = !allow_input
      this.hideInput = hide_input
      this.inputPlaceholder = input_placeholder
    },

    async handleShowActionOptionsEvent(data: any) {
      this.actionOptions = data.data.options
      this.disableInput = true
    },

    async handleShowEndingEvent(data: any) {
      const storyStore = useStoryStore()
      if (window.fbq) {
        window.fbq('trackCustom', 'GameEndClickRestartExposure', {
          storyId: storyStore.currentStory?.id,
          actorId: storyStore.currentActor
        })
      }
      reportEvent(ReportEvent.GameEndClickRestartExposure, {
        storyId: storyStore.currentStory?.id,
        actorId: storyStore.currentActor
      })
      this.disableInput = true
      this.chatOptions = null
      this.chatCard2 = null
      this.actionOptions = null
      this.messages = []
      // 如果有结束消息，添加到消息列表
      this.isEnding = true
      this.endings = {
        html: data.data.content.html
      }
      // 显示分享弹窗
      if (storyStore.currentStory.is_active_skill) {
        this.showEndingShare = true
      }
      await storyStore.checkHobbyCollectionStatus()
    },

    async handleShowOverlayEvent(data: any) {
      const storyStore = useStoryStore()
      this.overlay = data.data.overlay
      reportEvent(ReportEvent.ChatOverlayExposure, {
        storyId: storyStore.currentStory?.id,
        actorId: storyStore.currentActor
      })
      if (data.data.button) {
        this.overlay.button = data.data.button
      }
      this.isOverlayBlocking = true

      // 返回一个 Promise，只有当用户点击按钮后才会 resolve
      return new Promise<void>((resolve) => {
        this.overlayResolve = resolve
      })
    },

    async handleErrorEvent(data: any) {
      this.error = data.data.message || 'An error occurred'
    },

    async handlePreloadEvent(data: any) {
      if (Array.isArray(data.data)) {
        const resources = data.data.filter(
          (item: PreloadItem) => item.url && !this.preloadCache.has(item.url)
        )

        // Add URLs to cache first
        resources.forEach((item: PreloadItem) => {
          this.preloadCache.add(item.url)
        })

        // Then preload them
        await preloadResources(resources)
      }
    },

    async handleEvent(parsedData: BaseSSEData) {
      // Disable input when new events arrive
      if (parsedData.event_type !== 'preload') {
        this.disableInput = true
      }

      // 如果是资源相关事件，立即开始预加载，与队列处理并行
      if (
        parsedData.event_type === 'play_video' ||
        parsedData.event_type === 'show_image' ||
        parsedData.event_type === 'animated_images' ||
        parsedData.event_type === 'preload'
      ) {
        // 立即预加载资源，但不阻塞队列处理
        this.preloadEventResources(parsedData).catch((err) => {
          console.warn('Resource preloading failed:', err)
        })
      }

      // 所有事件都加入队列进行处理
      this.messageQueue.push(parsedData)
      this.messageQueue.sort((a, b) => a.timestamp - b.timestamp)
    },

    findNextMeaningfulEvent(events: BaseSSEData[], startIndex: number = 1): BaseSSEData | null {
      return (
        events
          .slice(startIndex)
          .find(
            (event) =>
              event.event_type === 'message' ||
              (event.event_type === 'play_video' && !event.data.is_background)
          ) || null
      )
    },

    updateNextEventFlags(nextEvent: BaseSSEData | null) {
      this.hasNextMessage = nextEvent?.event_type === 'message'
      this.hasNextVideo = nextEvent?.event_type === 'play_video' && !nextEvent.data.is_background
    },

    async triggerNextEvent() {
      if (this.messageQueue.length > 0) {
        const currentIndex = this.messageQueue.findIndex(
          (event) =>
            event.event_type === 'message' ||
            (event.event_type === 'play_video' && !event.data.is_background)
        )
        if (currentIndex !== -1) {
          // Remove all events up to and including the current one
          this.messageQueue.splice(0, currentIndex + 1)
          // Process the next event
          await this.processEventQueue()
        }
      }
    },

    async processEventQueue() {
      if (this.isProcessingQueue || this.messageQueue.length === 0) return

      try {
        this.isProcessingQueue = true
        this.disableInput = true

        // 新增：在处理队列前，预扫描队列中的资源并预加载
        await this.preloadQueuedResources()

        while (this.messageQueue.length > 0) {
          const currentEvent = this.messageQueue[0]
          // 只在处理消息事件时才需要查找下一个有意义的事件
          if (currentEvent.event_type === 'message') {
            const nextEvent = this.findNextMeaningfulEvent(this.messageQueue)
            this.updateNextEventFlags(nextEvent)
          }

          const eventHandlerMap: EventHandler = {
            message: this.handleMessageEvent,
            play_video: this.handlePlayVideoEvent,
            wait: this.handleWaitEvent,
            // show_tips: this.handleShowTipsEvent,
            show_chat_options: this.handleShowChatOptionsEvent,
            show_action_options: this.handleShowActionOptionsEvent,
            error: this.handleErrorEvent,
            show_image: this.handleShowImageEvent,
            show_ending: this.handleShowEndingEvent,
            show_overlay: this.handleShowOverlayEvent,
            payment_required: this.handlePaymentRequiredEvent,
            preload: this.handlePreloadEvent,
            play_audio: this.handlePlayAudioEvent,
            update_task_progress: this.handleUpdateTaskProgressEvent,
            update_user_coins: this.handleUpdateUserCoinsEvent,
            animated_images: this.handleAnimatedImagesEvent,
            update_total_progress: this.handleUpdateTotalProgressEvent,
            upload_image: this.handleUploadImageEvent,
            debug: this.handleDebugEvent,
            heart_value: this.handleHeartValueEvent,
            show_alert: this.handleShowAlertEvent,
            show_scene: this.handleShowSceneEvent,
            scene_transition: this.handleSceneTransitionEvent,
            voice_config: this.handleVoiceConfigEvent
          }

          const handler = eventHandlerMap[currentEvent.event_type]
          if (handler) {
            try {
              if (currentEvent.event_type === 'show_overlay') {
                // 移除当前事件
                this.messageQueue.shift()
                this.isProcessingQueue = false
                // 等待 overlay 处理完成
                await handler.call(this, currentEvent)
                // 重新开始处理队列
                if (this.messageQueue.length > 0) {
                  await this.processEventQueue()
                }
                return
              } else if (currentEvent.event_type === 'message') {
                // 消息事件特殊处理
                this.messageQueue.shift()

                // 处理消息事件
                await handler.call(this, currentEvent)

                // 等待打字效果完成
                if (this.messageTypingPromise) {
                  await this.messageTypingPromise
                }

                // 设置下一个事件的延迟时间
                if (this.messageTypingPromise === null) {
                  // 如果打字效果已完成，根据下一个事件类型设置延迟时间
                  if (this.hasNextMessage) {
                    this.messageDelayTime = 3000 // 如果下一个是消息，延迟更长
                  } else if (this.hasNextVideo) {
                    this.messageDelayTime = 3000 // 如果下一个是视频，延迟适中
                  } else {
                    this.messageDelayTime = 300 // 默认延迟
                  }

                  // 消息处理完后等待设定的延迟时间
                  await new Promise((resolve) => setTimeout(resolve, this.messageDelayTime))
                }

                // 在延迟之后，下一个消息处理之前重置状态
                this.hasNextMessage = false
                this.hasNextVideo = false
              } else {
                // 其他事件处理前先移除队列
                this.messageQueue.shift()
                await handler.call(this, currentEvent)
              }
            } catch (error) {
              console.error(`Error handling event ${currentEvent.event_type}:`, error)
              // 发生错误时也要移除当前事件，防止卡死
              if (this.messageQueue[0] === currentEvent) {
                this.messageQueue.shift()
              }
            }
          } else {
            console.warn(`Unhandled event type: ${currentEvent.event_type}`)
            this.messageQueue.shift()
          }
        }
      } finally {
        this.isProcessingQueue = false

        if (
          this.messageQueue.length === 0 &&
          !this.chatOptions?.length &&
          !this.actionOptions?.length
        ) {
          this.disableInput = false
        }

        // 如果还有未处理的事件，继续处理
        if (this.messageQueue.length > 0) {
          await this.processEventQueue()
        }
      }
    },

    // Add new methods for managing preload state
    startResourcePreloading() {
      this.preloadingCount++
      if (this.preloadingCount === 1) {
        this.isResourceLoading = true
      }
    },

    finishResourcePreloading() {
      if (this.preloadingCount > 0) {
        this.preloadingCount--
        if (this.preloadingCount === 0) {
          this.isResourceLoading = false
        }
      }
    },

    // Update preloadEventResources method
    async preloadEventResources(event: BaseSSEData): Promise<void> {
      if (!event || !event.event_type) {
        return
      }

      const needsPreloading =
        (event.event_type === 'play_video' && event.data?.url) ||
        (event.event_type === 'show_image' && event.data?.url) ||
        (event.event_type === 'animated_images' && Array.isArray(event.data?.urls)) ||
        (event.event_type === 'preload' && Array.isArray(event.data))

      if (!needsPreloading) {
        return
      }

      this.startResourcePreloading()

      try {
        if (event.event_type === 'play_video' && event.data.url) {
          await preloadAndCacheVideo(event.data.url)
        } else if (event.event_type === 'show_image' && event.data.url) {
          await preloadAndCacheImage(event.data.url)
        } else if (event.event_type === 'animated_images' && Array.isArray(event.data.urls)) {
          const promises = event.data.urls.map((url) => preloadAndCacheImage(url))
          await Promise.all(promises)
        } else if (event.event_type === 'preload' && Array.isArray(event.data)) {
          await preloadResources(event.data)
        }
      } catch (error) {
        console.error(`Error preloading resources for event ${event.event_type}:`, error)
      } finally {
        this.finishResourcePreloading()
      }
    },

    // Update preloadQueuedResources method
    async preloadQueuedResources() {
      if (this.messageQueue.length === 0) return

      const resourceEvents = this.messageQueue.filter(
        (event) =>
          (event.event_type === 'play_video' && event.data?.url) ||
          (event.event_type === 'show_image' && event.data?.url) ||
          (event.event_type === 'animated_images' && Array.isArray(event.data?.urls)) ||
          (event.event_type === 'preload' && Array.isArray(event.data))
      )

      if (resourceEvents.length === 0) return

      this.startResourcePreloading()

      try {
        const resourcePromises: Promise<void>[] = []

        for (const event of resourceEvents) {
          if (event.event_type === 'play_video' && event.data.url) {
            resourcePromises.push(preloadAndCacheVideo(event.data.url))
          } else if (event.event_type === 'show_image' && event.data.url) {
            resourcePromises.push(preloadAndCacheImage(event.data.url))
          } else if (event.event_type === 'animated_images' && Array.isArray(event.data.urls)) {
            for (const url of event.data.urls) {
              resourcePromises.push(preloadAndCacheImage(url))
            }
          } else if (event.event_type === 'preload' && Array.isArray(event.data)) {
            for (const item of event.data) {
              if (item.url && item.type) {
                if (item.type === 'video') {
                  resourcePromises.push(preloadAndCacheVideo(item.url))
                } else {
                  resourcePromises.push(preloadAndCacheImage(item.url))
                }
              }
            }
          }
        }

        const MAX_CONCURRENT = 5

        for (let i = 0; i < resourcePromises.length; i += MAX_CONCURRENT) {
          const batch = resourcePromises.slice(i, i + MAX_CONCURRENT)
          await Promise.allSettled(batch)
        }
      } finally {
        this.finishResourcePreloading()
      }
    },

    async initializeChat(actorId: string, storyId?: string) {
      this.initialized = false
      this.loading = true
      this.error = null
      this.currentActorId = actorId
      this.isInitializing = true
      const storyStore = useStoryStore()
      const skillStore = useSkillStore()
      const finalStoryId = storyId || storyStore.currentStory?.id || 'stepsister1'

      try {
        reportEvent(ReportEvent.InitializeChat, {
          storyId: finalStoryId,
          actorId: actorId
        })

        const params: {
          story_id: string
          game_config?: {
            skill_id: string
            numerical_values: {
              happiness: number
              intelligence: number
              strength: number
              wealth: number
            }
            user_avatar_url?: string
          }
        } = {
          story_id: finalStoryId
        }
        if (this.isShouldRestart && skillStore.selectedSkills.length > 0) {
          params.game_config = {
            skill_id: skillStore.selectedSkills[0].id,
            numerical_values: skillStore.totalAttributes
          }
        }
        if (this.userAvatar?.url) {
          // 确保 game_config 对象已存在，如果不存在则创建
          if (!params.game_config) {
            // @ts-ignore
            params.game_config = {
              user_avatar_url: this.userAvatar.url
            }
          }
          params.game_config.user_avatar_url = this.userAvatar.url
        }

        const events: any[] = []
        await startChatSSE(
          actorId,
          params,
          async (data) => {
            const parsedData = typeof data === 'string' ? JSONParse(data) : data
            if (parsedData) {
              events.push(parsedData)
            }
          },
          (error) => {
            this.isActorThinking = false
            this.error = 'Failed to initialize chat'
            const chatStore = useChatStore()
            chatStore.isError = true
          },
          async () => {
            // Stream closed callback
            this.isInitializing = false
            const processEvents = async (events: any[]) => {
              if (!this.isShouldRestart && events.length > 0) {
                // Handle redirect to chat scenario
                const findEvent = (type: string, optionsParams?: any): BaseSSEData | undefined =>
                  [...events].reverse().find((event) => {
                    if (type === 'play_video') {
                      return (
                        event.event_type === type &&
                        event.data.is_background === optionsParams.is_background
                      )
                    }
                    return event.event_type === type
                  })

                // 找到背景相关的事件
                const backgroundEvent = events.find((event) => {
                  if (event.event_type === 'play_video') {
                    return event.data.is_background === true
                  }
                  if (event.event_type === 'animated_images' && !this.isShouldRestart) {
                    // 如果是动画图片且不需要重启，将其转换为 show_image 事件
                    const lastImage = event.data.urls[event.data.urls.length - 1]
                    if (lastImage) {
                      event.event_type = 'show_image'
                      event.data = {
                        url: lastImage,
                        is_fullscreen: true
                      }
                    }
                    return true
                  }
                  return event.event_type === 'show_image'
                })

                const bgmEvent = events.find(
                  (event) =>
                    event.event_type === 'play_audio' &&
                    event.data.is_bgm === true &&
                    event.data.url
                )

                const lastTotalProgress = findEvent('update_total_progress')
                const lastTaskProgress = findEvent('update_task_progress')
                const lastHeartValue = findEvent('heart_value')
                const lastChatOptions = findEvent('show_chat_options')
                const isEnding = findEvent('show_ending')
                const lastVoiceConfig = findEvent('voice_config')

                // Process events in sequence
                const eventHandlers = []

                // 只处理找到的第一个背景事件
                if (backgroundEvent) {
                  eventHandlers.push(this.handleEvent(backgroundEvent))
                }

                if (isEnding) {
                  eventHandlers.push(this.handleEvent(isEnding))
                } else {
                  if (lastVoiceConfig) eventHandlers.push(this.handleEvent(lastVoiceConfig))
                  if (bgmEvent) eventHandlers.push(this.handleEvent(bgmEvent))
                  if (lastTotalProgress) eventHandlers.push(this.handleEvent(lastTotalProgress))
                  if (lastTaskProgress) eventHandlers.push(this.handleEvent(lastTaskProgress))
                  if (lastHeartValue) eventHandlers.push(this.handleEvent(lastHeartValue))
                  if (lastChatOptions) eventHandlers.push(this.handleEvent(lastChatOptions))
                }
                await Promise.all(eventHandlers)
              } else {
                // todo: 任务进度限制是前端计算出来的，且需要持久化处理，不然重定向进来任务level会变，后面需要后端处理
                this.tasks = []
                this.currentTaskId = null
                // Process all events sequentially
                await Promise.all(events.map((event) => this.handleEvent(event)))
              }

              await this.processEventQueue()
            }

            await processEvents(events)
            this.isRedirectToChat = false
          }
        )
      } catch (err) {
        this.error = 'Failed to initialize chat'
        console.error('Error initializing chat:', err)
      } finally {
        this.loading = false
        this.initialized = true
      }
    },

    async sendMessage(
      content: string,
      optionId?: string,
      msgType: 'text' | 'image' | 'config' = 'text',
      isTelepathyComplete: boolean = false,
      sceneId?: string,
      delay: number = 800 // 添加延迟参数，默认800ms
    ) {
      const userStore = useUserStore()
      const storyStore = useStoryStore()
      if (
        !this.currentActorId ||
        (!content.trim() && !isTelepathyComplete && msgType !== 'config') ||
        this.isActorThinking
      )
        return
      if (this.actionOptions?.length) {
        this.actionOptions = []
      }
      if (this.hideInput) {
        this.hideInput = false
      }
      const id = uuidv4()
      // 添加用户消息
      const userMessage: Message = {
        id,
        msg_type: msgType,
        sender_type: 'user',
        content: {
          text: content.trim(),
          media_url: msgType === 'image' ? content : null
        },
        create_time: new Date().toISOString(),
        sender: {
          avatar_url: '',
          name: 'You'
        }
      }
      if (
        msgType !== 'image' &&
        !['__CANCEL__', '__NEXT_SCENE__'].includes(content) &&
        msgType !== 'config'
      ) {
        this.messages.push(userMessage)
      }
      this.streaming = true
      this.isActorThinking = true
      this.loading = true
      if (optionId) {
        reportEvent(ReportEvent.SendChatMessage, {
          userId: userStore.userInfo?.uuid,
          message: content
        })
      }
      const handledOptionId = optionId || (this.chatOptions?.length ? '__NULL__' : null)
      try {
        const events: any[] = []
        const startTime = Date.now()
        sendMessageSSE(
          storyStore.currentStory?.id,
          this.currentActorId,
          {
            event_type: optionId ? 'action' : msgType === 'image' ? 'chat:image' : 'chat',
            timestamp: Date.now(),
            data: {
              text: msgType === 'image' ? null : content,
              option_id: handledOptionId,
              image: {
                url: msgType === 'image' ? content : null
              },
              scene_id: isTelepathyComplete ? sceneId || '' : null
            }
          },
          async (data) => {
            const parsedData = typeof data === 'string' ? JSONParse(data) : data
            if (parsedData) {
              events.push(parsedData)
            }
          },
          (error) => {
            this.isActorThinking = false
            this.error = 'Failed to send message'
            this.loading = false
          },
          async () => {
            // Stream closed callback
            const responseTime = Date.now() - startTime
            // 只有当服务器响应时间小于设置的延迟时间时，才需要额外等待
            if (responseTime < delay) {
              await new Promise((resolve) => setTimeout(resolve, delay - responseTime))
            }

            this.isActorThinking = false
            this.loading = false
            for (const event of events) {
              await this.handleEvent(event)
            }
            await this.processEventQueue()
          }
        )
      } catch (err) {
        this.isActorThinking = false
        this.loading = false
        console.error('Error sending message:', err)
        this.error = 'Failed to send message'
      } finally {
        if (this.chatOptions?.length) {
          this.chatOptions = []
        }
        this.inputPlaceholder = ''
        this.streaming = false
      }
    },

    async handlePaymentRequiredEvent(data: any) {
      const rechargeStore = useRechargeStore()
      rechargeStore.visible = true
      // 清空消息队列，因为需要先处理支付
      this.messageQueue = []
      this.isProcessingQueue = false
    },
    async handlePlayAudioEvent(data: any) {
      const { url, is_bgm } = data.data

      // Early return for invalid data
      if (!url) {
        console.error('Invalid audio data: missing URL')
        return
      }

      console.log('handlePlayAudioEvent:', { url, is_bgm })

      // 使用 audioManager 处理音频播放
      if (is_bgm) {
        // 如果当前 BGM URL 与新 URL 相同且正在播放，则跳过
        if (this.bgmUrl === url && this.isBgmPlaying) {
          console.log('BGM already playing with the same URL, skipping')
          return
        }

        // 设置 BGM URL
        this.bgmUrl = url

        // 尝试播放 BGM
        try {
          audioManager.playBgm(url)
        } catch (error) {
          console.error('Failed to play BGM:', error)
        }
      } else {
        audioManager.playSound(url)
      }
    },

    handleUpdateTaskProgressEvent(data: any) {
      const { task_id, percent, task_description } = data.data
      // 查找是否已存在该任务
      const existingTaskIndex = this.tasks.findIndex((task) => task.task_id === task_id)

      if (existingTaskIndex !== -1) {
        // 更新现有任务的进度
        this.tasks[existingTaskIndex].percent = percent
      } else {
        // 添加新任务
        this.tasks.push({
          task_id,
          percent,
          description: task_description
        })
      }

      // 更新当前任务ID
      this.currentTaskId = task_id
    },
    handleUpdateUserCoinsEvent(data: any) {
      const userStore = useUserStore()
      const { coins } = data.data
      userStore.userInfo.coins = coins
    },
    handleAnimatedImagesEvent(data: any) {
      // 如果不需要重启，直接返回，因为已经在 processEvents 中处理为 show_image 了
      if (!this.isShouldRestart) {
        return Promise.resolve()
      }

      const { urls } = data.data
      if (this.currentActorId) {
        this.backgroundVideoMap[this.currentActorId] = null
        this.animatedImagesMap[this.currentActorId] = urls
      }

      // 等待用户完成所有图片切换
      return new Promise<void>((resolve) => {
        // 如果没有图片，直接完成
        if (!urls || urls.length === 0) {
          resolve()
          return
        }

        // 监听器来检查 animatedImages 数组是否被清空
        const checkInterval = setInterval(() => {
          if (this.currentActorId && this.animatedImagesMap[this.currentActorId]?.length === 0) {
            clearInterval(checkInterval)
            resolve()
          }
        }, 100)
      })
    },
    handleUpdateTotalProgressEvent(data: any) {
      const { percent } = data.data
      this.totalProgress = percent
    },
    handleUploadImageEvent(data: any) {
      const { face_detect } = data.data
      this.onFaceDetect = face_detect
      this.isShowUploadImage = true
    },
    async completeMessageTyping(delay: number = 300, isSkipping: boolean = false) {
      if (this.messageTypingResolve) {
        if (delay > 0 && !isSkipping) {
          // Add delay to give users time to read, but skip if we're in a skip scenario
          await new Promise((resolve) => setTimeout(resolve, delay))
        }
        this.messageTypingResolve()
        this.messageTypingResolve = null
        this.messageTypingPromise = null

        // Reset message delay time when skipping
        if (isSkipping) {
          this.messageDelayTime = 0 // Reset to default delay
        } else {
          // 根据下一个事件类型设置不同的延迟时间
          if (this.hasNextMessage) {
            this.messageDelayTime = 3000 // 如果下一个是消息，延迟更长
          } else if (this.hasNextVideo) {
            this.messageDelayTime = 3000 // 如果下一个是视频，延迟适中
          } else {
            this.messageDelayTime = 300 // 默认延迟
          }
        }
      }
    },
    async handleShowAlertEvent() {
      this.isShowAlert = true
    },
    async handlePlayVideoEvent(data: any) {
      // 如果视频无效，则不播放
      if (!data.data.url) {
        return
      }
      // 背景视频
      if (data.data.is_background && this.currentActorId) {
        this.backgroundVideoMap[this.currentActorId] = data.data.url
        return
      }

      // Wait for message typing to complete if there's an ongoing typing
      if (this.messageTypingPromise && !data.data.is_background) {
        await this.messageTypingPromise
      }

      // 停止 TTS 播放
      audioManager.stopTTS()

      // 暂停 BGM
      if (this.bgmAudio && this.isBgmPlaying) {
        audioManager.pauseBgm()
      }

      let wasSkipped = false

      try {
        // 设置最小可跳过时间
        this.videoMinWatchDuration = data.data?.min_watch_duration || 2

        this.videoUrl = data.data.url
        this.isPlayingVideo = true

        if (this.isFirstVideo) {
          await new Promise((resolve) => {
            const checkInterval = setInterval(() => {
              if (!this.isFirstVideo) {
                clearInterval(checkInterval)
                resolve(true)
              }
            }, 100)
          })
        }

        await Promise.race([
          new Promise((resolve) => {
            if (this.videoElementRef) {
              this.videoElementRef.onended = () => resolve(true)
            } else {
              const checkInterval = setInterval(() => {
                if (this.videoElementRef) {
                  this.videoElementRef.onended = () => resolve(true)
                  clearInterval(checkInterval)
                }
              }, 100)
            }
          }),
          new Promise((resolve) => {
            watch(
              () => this.isPlayingVideo,
              (newVal) => {
                if (!newVal) {
                  wasSkipped = true
                  resolve(true)
                }
              },
              { immediate: true }
            )
          })
        ])
      } catch (error) {
        console.error('Error loading video:', error)
        this.error = 'Failed to load video'
      } finally {
        // 重置状态
        this.isPlayingVideo = false
        this.videoUrl = null
        this.isFirstVideo = false

        // 恢复 BGM 播放
        if (this.bgmAudio) {
          audioManager.playBgm()
        }
      }
    },
    // 处理语音配置事件
    async handleVoiceConfigEvent(data: any) {
      if (!data.data) return

      const { provider, voice_id } = data.data

      if (voice_id && provider) {
        console.log(`Setting voice configuration: provider=${provider}, voice_id=${voice_id}`)
        this.currentVoiceId = voice_id
        this.currentVoiceProvider = provider
      } else {
        // 如果未提供voice_id，则重置为默认
        this.currentVoiceId = null
        this.currentVoiceProvider = null
      }
    },
    setPaymentRequired() {
      this.paymentRequired = true
    },
    clearPaymentRequired() {
      this.paymentRequired = false
    },
    clearChat() {
      // 先停止音频相关
      if (this.bgmAudio) {
        this.bgmAudio.stop()
        this.bgmAudio = null
      }
      this.clearAudioInstances()

      // 重置当前语音ID
      this.currentVoiceId = null
      this.currentVoiceProvider = null

      // 先重置 store 的状态
      this.$reset()

      // 清空所有相关的状态
      this.tasks = []
      this.currentTaskId = null
      this.animatedImagesMap = {}
      this.backgroundImageMap = {}
      this.backgroundVideoMap = {}

      // 强制清理 localStorage
      localStorage.removeItem('chat-tasks')
      localStorage.removeItem('chat-current-task-id')
      localStorage.removeItem('chat-animated-images-map')
      localStorage.removeItem('chat-background-image-map')
      localStorage.removeItem('chat-background-video-map')
      this.lastSceneTimestamp = null
      this.lastScene = null
      this.setShouldRestart(true)
    },
    updateVideoLoadingProgress(progress: number) {
      this.videoLoadingProgress = Math.min(Math.max(0, progress), 100)
    },
    startVideoLoading() {
      this.isVideoLoading = true
      this.videoLoadingProgress = 0
    },
    finishVideoLoading() {
      this.isVideoLoading = false
      this.videoLoadingProgress = 100
    }
  },
  persist: false
})
