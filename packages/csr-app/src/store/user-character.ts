import { defineStore } from 'pinia'
import type { UserAvatar, CreateAvatarParams } from '@/api/user-character'
import {
  getAvatarList,
  createAvatar,
  deleteAvatar,
  getAvatarDetail as fetchAvatarDetail,
  getPredefinedAvatars
} from '@/api/user-character'
import { ref, computed } from 'vue'

interface ApiResponse<T> {
  code: string
  message: string
  data?: T
}

interface AvatarListResponse {
  user_avatars: UserAvatar[]
  total: number
}

interface AvatarResponse {
  user_avatar: UserAvatar
}

interface PreviewUrlResponse {
  preview_url: string[]
}

export const useUserAvatarStore = defineStore('user-avatar', () => {
  const currentAvatar = ref<UserAvatar | null>(null)
  const avatars = ref<UserAvatar[]>([])
  const predefinedAvatarUrls = ref<string[]>([])
  const total = ref(0)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const fakeProgress = ref<Record<string, number>>({})
  const hasVisited = ref(false)
  const unviewedFinishedAvatars = ref<Record<string, boolean>>({})

  const latestAvatars = computed(() =>
    [...avatars.value].sort(
      (a, b) => new Date(b.submitted_at).getTime() - new Date(a.submitted_at).getTime()
    )
  )

  const handleApiResponse = <T>(result: { data: ApiResponse<T> }) => {
    if (result.data.code === '0') {
      return result.data.data
    }
    throw new Error(result.data.message)
  }

  const withLoading = async <T>(operation: () => Promise<T>) => {
    loading.value = true
    error.value = null

    try {
      return await operation()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'An unexpected error occurred'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchAvatars = () =>
    withLoading(async () => {
      const result = await getAvatarList()
      const data = handleApiResponse<AvatarListResponse>(result)
      avatars.value = data.user_avatars
      if (avatars.value.length >= 0 && window?.collectEvent) {
        window.collectEvent('config', {
          has_avatar: avatars.value.length > 0
        })
      }
      // Clean up completed avatars' progress
      Object.keys(fakeProgress.value).forEach((id) => {
        const avatar = data.user_avatars.find((a) => a.id === id)
        if (!avatar || (avatar.status !== 'start' && avatar.status !== 'submitted')) {
          delete fakeProgress.value[id]
        }
      })

      total.value = data.total
    })

  const fetchPredefinedAvatars = () =>
    withLoading(async () => {
      try {
        // const result = await getPredefinedAvatars()
        // const data = handleApiResponse<PreviewUrlResponse>(result)
        // if (data && data.preview_url) {
        //   predefinedAvatarUrls.value = data.preview_url
        // }
        predefinedAvatarUrls.value = [
          'https://static.playshot.ai/static/images/user/character/yamato-example.png'
        ]
      } catch (err) {
        console.error('Failed to fetch predefined avatars:', err)
        // Fall back to empty array if the request fails
        predefinedAvatarUrls.value = []
      }
    })

  const createNewAvatar = (params: CreateAvatarParams) =>
    withLoading(async () => {
      const result = await createAvatar(params)
      const data = handleApiResponse<AvatarResponse>(result)
      avatars.value.unshift(data.user_avatar)
      // Initialize progress for new avatar
      fakeProgress.value[data.user_avatar.id] = 6
      total.value++
      return data.user_avatar
    })

  const removeAvatar = (id: string) =>
    withLoading(async () => {
      const result = await deleteAvatar(id)
      handleApiResponse<void>(result)
      const index = avatars.value.findIndex((a) => a.id === id)
      if (index > -1) {
        avatars.value.splice(index, 1)
        delete fakeProgress.value[id]
        total.value--
      }
    })

  const getAvatarDetail = (id: string) =>
    withLoading(async () => {
      const result = await fetchAvatarDetail(id)
      const data = handleApiResponse<AvatarResponse>(result)
      return data.user_avatar
    })

  const setCurrentAvatar = (avatar: UserAvatar) => {
    currentAvatar.value = avatar
  }

  const clearCurrentAvatar = () => {
    currentAvatar.value = null
  }

  const updateFakeProgress = (id: string, progress: number) => {
    fakeProgress.value[id] = progress
  }

  const initFakeProgress = (id: string) => {
    if (!fakeProgress.value[id]) {
      fakeProgress.value[id] = 0
    }
  }

  const markAsVisited = () => {
    hasVisited.value = true
    unviewedFinishedAvatars.value = {}
  }

  const addUnviewedAvatar = (avatarId: string) => {
    unviewedFinishedAvatars.value[avatarId] = true
  }

  return {
    // State
    currentAvatar,
    avatars,
    predefinedAvatarUrls,
    total,
    loading,
    error,
    fakeProgress,
    hasVisited,
    unviewedFinishedAvatars,

    // Getters
    latestAvatars,

    // Actions
    fetchAvatars,
    fetchPredefinedAvatars,
    createAvatar: createNewAvatar,
    removeAvatar,
    getAvatarDetail,
    setCurrentAvatar,
    clearCurrentAvatar,
    updateFakeProgress,
    initFakeProgress,
    markAsVisited,
    addUnviewedAvatar,
    persist: {
      storage: localStorage,
      pick: ['hasVisited', 'unviewedFinishedAvatars']
    }
  }
})
