import { defineStore } from 'pinia'
import { ref, watch } from 'vue'
import { useStorage } from '@vueuse/core'
import { Task } from './chat-types'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface'
import { useStoryStore } from '@/store/story'

export const useChatTasksStore = defineStore('chatTasks', () => {
  // 持久化存储
  const persistedTasks = useStorage<Task[]>('chat-tasks', [])
  const persistedCurrentTaskId = useStorage<number | null>('chat-current-task-id', null)

  // 状态
  const tasks = ref<Task[]>(persistedTasks.value)
  const currentTaskId = ref<number | null>(persistedCurrentTaskId.value)
  const totalProgress = ref(0)

  // 监听状态变化，更新持久化存储
  watch(
    () => tasks.value,
    (newTasks) => {
      if (newTasks.length === 0) {
        persistedTasks.value = []
      } else {
        persistedTasks.value = newTasks
      }
    },
    { deep: true }
  )

  watch(
    () => currentTaskId.value,
    (newTaskId) => {
      persistedCurrentTaskId.value = newTaskId
    }
  )

  // Actions
  /**
   * 处理更新任务进度事件
   */
  function handleUpdateTaskProgressEvent(data: any) {
    const { task_id, percent, task_description } = data.data
    // 查找是否已存在该任务
    const existingTaskIndex = tasks.value.findIndex((task) => task.task_id === task_id)

    if (existingTaskIndex !== -1) {
      // 更新现有任务的进度
      tasks.value[existingTaskIndex].percent = percent
    } else {
      // 添加新任务
      tasks.value.push({
        task_id,
        percent,
        description: task_description
      })
    }

    // 更新当前任务ID
    currentTaskId.value = task_id

    // 上报任务提示曝光事件
    const storyStore = useStoryStore()
    reportEvent(ReportEvent.TaskTipExposure, {
      storyId: storyStore.currentStory?.id,
      actorId: storyStore.currentActor,
      tipId: task_id,
      tipText: task_description
    })
  }

  /**
   * 处理更新总进度事件
   */
  function handleUpdateTotalProgressEvent(data: any) {
    const { percent } = data.data
    totalProgress.value = percent
  }

  /**
   * 清空任务
   */
  function clearTasks() {
    tasks.value = []
    currentTaskId.value = null
    totalProgress.value = 0
  }

  return {
    // 状态
    tasks,
    currentTaskId,
    totalProgress,

    // Actions
    handleUpdateTaskProgressEvent,
    handleUpdateTotalProgressEvent,
    clearTasks
  }
})
