import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getCheckinInfo, claimCheckinReward } from '@/api/checkin'
import type { CheckinResponse, ClaimCheckinResponse } from '@/interface/checkin'
import { Message } from '@/mobile/components/Message'
import { useUserStore } from './user'
import { useApi } from '@/composables/useApi'

export const useCheckinStore = defineStore(
  'checkin',
  () => {
    const { withLoading, loading, error } = useApi()
    const userStore = useUserStore()

    // State
    const currentDay = ref(1)
    const todayClaimed = ref(false)
    const visible = ref(false)

    // Getters
    const isAllDaysClaimed = computed(() => currentDay.value > 7)
    const canClaim = computed(() => !loading.value && !todayClaimed.value)
    const canShowCheckin = computed(() => userStore.isAuthenticated && !userStore.isGuest)

    // Utils
    const handleApiResponse = <T>(response: { isOk: boolean; message?: string; data: T }) => {
      if (response.isOk) {
        return response.data
      }
      throw new Error(response.message || 'API request failed')
    }

    // Day number conversion
    const getDayNumber = (key: string): number => {
      const dayMap: Record<string, number> = {
        day_one: 1,
        day_two: 2,
        day_three: 3,
        day_four: 4,
        day_five: 5,
        day_six: 6,
        day_seven: 7
      }
      return dayMap[key] || 0
    }

    // Actions
    const fetchCheckinInfo = () =>
      withLoading(async () => {
        const response = await getCheckinInfo()
        const { is_sign_in, sign_in_count } = handleApiResponse<CheckinResponse['data']>(response)

        // sign_in_count 代表已经签到的天数
        // 如果今天已签到，currentDay 保持在当前天数
        // 如果今天未签到，currentDay 应该是下一天
        currentDay.value = is_sign_in ? sign_in_count : sign_in_count + 1
        todayClaimed.value = is_sign_in

        // 如果已经签满7天，确保 currentDay 不会超过8
        if (currentDay.value > 7) {
          currentDay.value = 8
        }
      })

    const claimDailyReward = () =>
      withLoading(async () => {
        if (todayClaimed.value) return false
        const response = await claimCheckinReward()
        const data = handleApiResponse<ClaimCheckinResponse['data']>(response)
        todayClaimed.value = true
        Message.success('Reward claimed successfully!')
        // Update user info
        userStore.setUserInfo({
          ...data.user
        })
        return true
      })

    const showModal = () => {
      visible.value = true
    }

    const hideModal = () => {
      visible.value = false
    }

    const reset = () => {
      currentDay.value = 1
      todayClaimed.value = false
    }

    return {
      // State
      currentDay,
      todayClaimed,
      loading,
      error,
      visible,

      // Getters
      isAllDaysClaimed,
      canClaim,
      canShowCheckin,

      // Actions
      getDayNumber,
      fetchCheckinInfo,
      claimDailyReward,
      showModal,
      hideModal,
      reset
    }
  },
  {
    persist: false
  }
)
