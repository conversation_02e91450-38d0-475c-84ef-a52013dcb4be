import { createPinia } from 'pinia'
import piniaPluginPersistedstate, { createPersistedState } from 'pinia-plugin-persistedstate'

import StoreReset from '@/store/storeReset'
export { useChatStore } from './chat'
export { useActorStore } from './character'
export { useUserStore } from './user'
export { useStoryStore } from './story'
export { useRechargeStore } from './recharge'
export { useSysConfigStore } from './sysconfig'
export { useReferralStore } from './referral'
export { useTasksStore } from './tasks'
export { useABTestStore } from './abtest'

const pinia = createPinia()
pinia.use(
  createPersistedState({
    auto: false
  })
)
pinia.use(StoreReset)
export default pinia
