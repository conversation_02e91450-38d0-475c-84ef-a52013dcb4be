import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
  getTasksList,
  completePlayGameTask,
  completeShareGameTask,
  completeCreateStoryTask,
  completeInviteFriendTask,
  getInviteCode
} from '@/api/tasks'
import type {
  TaskItem,
  TaskData,
  CompleteTaskResponse,
  InviteCodeResponse
} from '@/interface/tasks'
import { Message } from '@/mobile/components/Message'
import { useUserStore } from './user'
import { useApi } from '@/composables/useApi'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'

// 任务奖励配置（钻石数量）
const TASK_REWARDS = {
  play_game: 10,
  share: 10,
  create: 10,
  invite: 10
}

export const useTasksStore = defineStore(
  'tasks',
  () => {
    const { withLoading, loading, error } = useApi()
    const userStore = useUserStore()

    // State
    const tasks = ref<TaskItem[]>([])
    const rawTasksData = ref<Record<string, TaskData> | null>(null)
    const inviteCode = ref<string | null>(null)
    const isLoadingInviteCode = ref(false)

    // Getters
    const pendingTasks = computed(() => tasks.value.filter((task) => !task.is_done))
    const completedTasks = computed(() => tasks.value.filter((task) => task.is_done))
    const playGameTask = computed(() => tasks.value.find((task) => task.type === 'play_game'))
    const shareGameTask = computed(() => tasks.value.find((task) => task.type === 'share'))
    const createStoryTask = computed(() => tasks.value.find((task) => task.type === 'create'))
    const inviteFriendTask = computed(() => tasks.value.find((task) => task.type === 'invite'))
    const allTasksCompleted = computed(
      () => tasks.value.length > 0 && tasks.value.every((task) => task.is_done)
    )

    // 将后端返回的任务数据转换为前端使用的格式
    const transformTasksData = (data: Record<string, TaskData>): TaskItem[] => {
      const result: TaskItem[] = []

      // 处理播放游戏任务
      if (data.play_game_task) {
        result.push({
          id: 'play_game',
          type: 'play_game',
          name: data.play_game_task.name,
          description: data.play_game_task.description,
          count: data.play_game_task.count,
          limit: data.play_game_task.limit,
          is_done: data.play_game_task.is_done,
          reward: data.play_game_task.coins || TASK_REWARDS.play_game
        })
      }

      // 处理分享游戏任务
      if (data.share_task) {
        result.push({
          id: 'share',
          type: 'share',
          name: data.share_task.name,
          description: data.share_task.description,
          count: data.share_task.count,
          limit: data.share_task.limit,
          is_done: data.share_task.is_done,
          reward: data.share_task.coins || TASK_REWARDS.share
        })
      }

      // 处理创建故事任务
      // if (data.create_task) {
      //   result.push({
      //     id: 'create',
      //     type: 'create',
      //     name: data.create_task.name,
      //     description: data.create_task.description,
      //     count: data.create_task.count,
      //     limit: data.create_task.limit,
      //     is_done: data.create_task.is_done,
      //     reward: data.create_task.coins || TASK_REWARDS.create
      //   })
      // }

      // 处理邀请好友任务
      if (data.invite_task) {
        result.push({
          id: 'invite',
          type: 'invite',
          name: data.invite_task.name,
          description: data.invite_task.description,
          count: data.invite_task.count,
          limit: data.invite_task.limit,
          is_done: data.invite_task.is_done,
          reward: data.invite_task.coins || TASK_REWARDS.invite
        })
      }

      return result
    }

    // 通用任务完成处理函数
    const handleTaskCompletion = async (
      taskType: keyof typeof TASK_REWARDS,
      apiCall: () => Promise<CompleteTaskResponse>,
      reportEventType?: ReportEvent,
      errorMessage = 'Failed to complete task'
    ) => {
      if (reportEventType) {
        reportEvent(reportEventType, {
          userId: userStore.userInfo?.uuid
        })
      }

      try {
        const response = await apiCall()

        if (response.code === '0') {
          // 更新用户信息
          if (response.data.user) {
            // 确保类型兼容
            const { uuid, name, email, avatar_url, status, plan, coins } = response.data.user
            const gender = response.data.user.gender as 'male' | 'female' | 'unknown'
            const role = response.data.user.role as 'guest' | 'normal' | 'admin'

            userStore.setUserInfo({
              uuid,
              name,
              email,
              avatar_url,
              status,
              plan,
              coins,
              role,
              gender
            })
          }

          // 刷新任务列表
          await fetchTasks()

          // 获取任务的实际奖励值
          const task = tasks.value.find((t) => t.type === taskType)
          const rewardAmount = task ? task.reward : TASK_REWARDS[taskType]

          Message.success(`Task completed! You've earned ${rewardAmount} diamonds!`)
          return true
        } else {
          throw new Error(response.message || errorMessage)
        }
      } catch (err) {
        console.error(`${errorMessage}:`, err)
        Message.error('Failed to complete task')
        return false
      }
    }

    // Actions
    const fetchTasks = () =>
      withLoading(async () => {
        try {
          const response = await getTasksList()

          if (response.code === '0') {
            rawTasksData.value = response.data
            tasks.value = transformTasksData(response.data)
            return true
          } else {
            throw new Error(response.message || 'Failed to fetch tasks')
          }
        } catch (err) {
          console.error('Failed to fetch tasks:', err)
          Message.error('Failed to fetch tasks')
          return false
        }
      })

    const completePlayGame = () =>
      withLoading(() =>
        handleTaskCompletion(
          'play_game',
          completePlayGameTask,
          ReportEvent.DailyTasksPlayGameClick,
          'Failed to complete play game task'
        )
      )

    const completeShareGame = () =>
      withLoading(() =>
        handleTaskCompletion(
          'share',
          completeShareGameTask,
          ReportEvent.DailyTasksShareGameClick,
          'Failed to complete share game task'
        )
      )

    const completeCreateStory = () =>
      withLoading(() =>
        handleTaskCompletion(
          'create',
          completeCreateStoryTask,
          undefined,
          'Failed to complete create story task'
        )
      )

    const completeInviteFriend = () =>
      withLoading(() =>
        handleTaskCompletion(
          'invite',
          completeInviteFriendTask,
          ReportEvent.DailyTasksInviteFriendClick,
          'Failed to complete invite friend task'
        )
      )

    // 获取邀请码
    const fetchInviteCode = async () => {
      // 如果已经有邀请码，直接返回
      if (inviteCode.value) {
        return inviteCode.value
      }

      isLoadingInviteCode.value = true

      try {
        const response = await getInviteCode()

        if (response.code === '0') {
          inviteCode.value = response.data.exclusive_invite_code
          return inviteCode.value
        } else {
          throw new Error(response.message || 'Failed to get invite code')
        }
      } catch (err) {
        console.error('Failed to get invite code:', err)
        Message.error('Failed to get invite code')
        return null
      } finally {
        isLoadingInviteCode.value = false
      }
    }

    // 复制邀请码到剪贴板
    const copyInviteCodeToClipboard = async () => {
      const code = inviteCode.value || (await fetchInviteCode())

      if (!code) {
        Message.error('Failed to get invite code')
        return false
      }

      try {
        await navigator.clipboard.writeText(code)
        Message.success('Invite code copied to clipboard!')
        return true
      } catch (err) {
        console.error('Failed to copy invite code:', err)
        Message.error('Failed to copy invite code')
        return false
      }
    }

    const reset = () => {
      tasks.value = []
      rawTasksData.value = null
    }

    return {
      // State
      tasks,
      rawTasksData,
      loading,
      error,
      inviteCode,
      isLoadingInviteCode,

      // Getters
      pendingTasks,
      completedTasks,
      playGameTask,
      shareGameTask,
      createStoryTask,
      inviteFriendTask,
      allTasksCompleted,

      // Actions
      fetchTasks,
      completePlayGame,
      completeShareGame,
      completeCreateStory,
      completeInviteFriend,
      fetchInviteCode,
      copyInviteCodeToClipboard,
      reset
    }
  },
  {
    persist: false
  }
)
