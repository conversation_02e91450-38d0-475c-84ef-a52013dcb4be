/**
 * 部署环境配置
 */

import { getStagingUrls } from '../../config/server'

export interface DeploymentConfig {
  // 主应用URL
  mainAppUrl: string
  // CSR应用URL
  csrAppUrl: string
  // 是否在微前端环境中
  isMicroFrontend: boolean
}

// 环境配置
const configs: Record<string, DeploymentConfig> = {
  // 开发环境
  development: {
    mainAppUrl: 'http://localhost:3000',
    csrAppUrl: 'http://localhost:5173',
    isMicroFrontend: true
  },

  // 测试环境 - 混合部署 (Nuxt用IP，CSR用域名)
  staging: (() => {
    const urls = getStagingUrls()
    return {
      mainAppUrl: urls.nuxtUrl, // Nuxt主应用通过IP访问
      csrAppUrl: urls.csrUrl, // CSR应用通过域名访问
      isMicroFrontend: true
    }
  })(),

  // 生产环境 - playshot.ai
  production: {
    mainAppUrl: 'https://playshot.ai',
    csrAppUrl: 'https://chat.playshot.ai',
    isMicroFrontend: true
  },

  // 生产环境 - reelplay.ai
  'production-reelplay': {
    mainAppUrl: 'https://reelplay.ai',
    csrAppUrl: 'https://chat.reelplay.ai',
    isMicroFrontend: true
  },

  // 测试环境 - 使用dev子域名
  'production-dev': {
    mainAppUrl: 'https://dev.playshot.ai',
    csrAppUrl: 'https://dev.chat.playshot.ai',
    isMicroFrontend: true
  }
}

// 获取当前环境
export function getCurrentEnvironment(): string {
  if (typeof window === 'undefined') {
    return process.env.NODE_ENV || 'development'
  }

  const hostname = window.location.hostname
  const protocol = window.location.protocol

  // 开发环境
  if (hostname.includes('localhost') || hostname === '127.0.0.1') {
    return 'development'
  }

  // 测试环境 - CSR应用通过dev.chat.playshot.ai访问
  if (hostname === 'dev.chat.playshot.ai') {
    return 'production-dev'
  }

  // 生产环境 - chat.playshot.ai
  if (hostname === 'chat.playshot.ai') {
    return 'production'
  }

  // 生产环境 - chat.reelplay.ai
  if (hostname === 'chat.reelplay.ai') {
    return 'production-reelplay'
  }

  // 默认返回staging，因为测试环境可能有各种配置
  return 'staging'
}

// 获取当前环境配置
export function getDeploymentConfig(): DeploymentConfig {
  const env = getCurrentEnvironment()
  return configs[env] || configs.development
}

// 检测是否在微前端环境中
export function isInMicroFrontendEnvironment(): boolean {
  if (typeof window === 'undefined') return false

  // 检查是否在iframe中
  const inIframe = window.self !== window.top

  // 检查URL是否来自主应用
  const config = getDeploymentConfig()
  const isFromMainApp = document.referrer.includes(config.mainAppUrl)

  return inIframe && (isFromMainApp || config.isMicroFrontend)
}
