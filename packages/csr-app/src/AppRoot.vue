<template>
  <a-config-provider :update-at-scroll="true">
    <!-- Conditionally render either AppPC or AppMobile based on device type and route -->
    <AppPC v-if="shouldShowPCComponent" />
    <AppMobile v-else />

    <!-- 路由性能监控组件 (仅开发环境)
    <RoutePerformanceMonitor /> -->

    <!-- 版本更新通知 -->
    <!-- <UpdateNotification
      v-model:visible="showUpdateNotification"
      :current-version="updateInfo.currentVersion"
      :latest-version="updateInfo.latestVersion"
      :auto-update="true"
      @update="handleUpdate"
      @later="handleUpdateLater"
    /> -->

    <!-- PWA安装提示 -->
    <PWAInstallPrompt />
  </a-config-provider>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref, onUnmounted, watch } from 'vue'
  import { useRoute } from 'vue-router'
  import { useStateSync } from '@/composables/useStateSync'
  import AppPC from './AppPC.vue'
  import AppMobile from './AppMobile.vue'
  import PWAInstallPrompt from '@/components/PWAInstallPrompt.vue'
  import RoutePerformanceMonitor from '@/components/RoutePerformanceMonitor.vue'
  import UpdateNotification from '@/shared/components/UpdateNotification.vue'
  import { useRouteAdaptation } from '@/composables/useRouteAdaptation'
  import { useReferralStore } from '@/store/referral'
  import { useChatEventsStore } from '@/store/chat-events'
  import { reportEvent } from './utils'
  import { ReportEvent } from './interface'
  import { useDeviceDetection } from '@/composables/useDeviceDetection'
  import { useThemeStore } from '@/store/theme'
  import { useSysConfigStore } from '@/store/sysconfig'
  import { useUserStore } from '@/store/user'
  import { useChatStore } from '@/store/chat'
  import { sendUserLoginStatusEvent } from '@/utils/googleAnalytics'

  const route = useRoute()
  const referralStore = useReferralStore()
  const themeStore = useThemeStore()
  const sysConfigStore = useSysConfigStore()
  const userStore = useUserStore()
  const chatStore = useChatStore()
  const chatEventsStore = useChatEventsStore()

  // 使用状态同步
  const { isInMicroFrontend } = useStateSync()

  const { isDesktop } = useDeviceDetection()

  // 版本更新相关状态
  const showUpdateNotification = ref(false)
  const updateInfo = ref({
    currentVersion: '',
    latestVersion: '',
    onUpdate: null as (() => void) | null,
    onLater: null as (() => void) | null,
  })

  // 检查当前路由是否是PC路由
  const isPCRoute = computed(() => {
    return route.path.startsWith('/pc')
  })

  // 根据设备类型和路由决定是否显示PC组件
  const shouldShowPCComponent = computed(() => {
    // 如果是PC路由，始终使用PC组件
    if (isPCRoute.value) return true

    // 如果是桌面设备，使用PC组件
    if (isDesktop.value) return true

    // 其他情况使用移动端组件
    return false
  })

  // 版本更新处理函数
  const handleVersionUpdateEvent = (event: Event) => {
    const customEvent = event as CustomEvent
    const { newVersion, currentVersion, onUpdate, onLater } = customEvent.detail

    updateInfo.value = {
      currentVersion: currentVersion.version,
      latestVersion: newVersion.version,
      onUpdate,
      onLater,
    }

    showUpdateNotification.value = true
  }

  // 立即检查URL参数（在setup阶段，确保在子组件初始化前执行）
  if (typeof window !== 'undefined') {
    const urlParams = new URLSearchParams(window.location.search)

    // Check for source_code in URL parameters
    const sourceCode = urlParams.get('source')
    if (sourceCode) {
      referralStore.setSourceCode(sourceCode)
    }

    // Check for invite_code in URL parameters
    const inviteCode = urlParams.get('invite')
    if (inviteCode) {
      referralStore.setInviteCode(inviteCode)
    }

    // Check for restart parameter in URL parameters
    const restartParam = urlParams.get('restart')
    if (restartParam === '1' || restartParam === 'true') {
      chatStore.setShouldRestart(true)
      // 同时设置 chatEventsStore 的 restart 状态
      chatEventsStore.setShouldRestart(true)
    } else if (restartParam === '0' || restartParam === 'false') {
      chatStore.setShouldRestart(false)
      // 同时设置 chatEventsStore 的 restart 状态
      chatEventsStore.setShouldRestart(false)
    }

    reportEvent(ReportEvent.ReelPlayFirstVisit, {
      sourceCode: sourceCode || '',
      inviteCode: inviteCode || '',
    })
  }

  // Detect and store source_code from URL parameters
  onMounted(() => {
    // 初始化主题 - 确保在DOM加载后立即执行
    setTimeout(() => {
      themeStore.initTheme()
      // themeStore.listenForSystemThemeChanges()
    }, 0)
    sysConfigStore.fetchConfigs()

    // 监听版本更新事件
    window.addEventListener(
      'version-update-available',
      handleVersionUpdateEvent,
    )

    // 检查是否有source_code用于事件收集
    if (referralStore.getSourceCode() && window?.collectEvent) {
      window.collectEvent('config', {
        kol_source: referralStore.getSourceCode(),
      })
    }
  })

  onUnmounted(() => {
    // 清理事件监听器
    window.removeEventListener(
      'version-update-available',
      handleVersionUpdateEvent,
    )
  })

  let lastReportedLoginState = ref(false)

  watch(
    () => userStore.userInfo?.role,
    (newVal) => {
      if (!newVal) return
      const isLoggedIn = newVal === 'normal' || newVal === 'admin'

      // 避免重复上报
      if (lastReportedLoginState.value === isLoggedIn) return
      lastReportedLoginState.value = isLoggedIn

      // 发送用户登录状态事件
      sendUserLoginStatusEvent(isLoggedIn)
    },
    { immediate: false },
  )

  // Route adaptation
  useRouteAdaptation()
</script>

<style lang="less">
  #app {
    position: relative;
    width: 100%;
    height: calc(var(--vh, 1vh) * 100);
    font-family: var(--font-family);
  }
</style>
