export interface CheckinResponse {
  isOk: boolean
  message?: string
  data: {
    is_sign_in: boolean
    sign_in_count: number
  }
}

export interface ClaimCheckinResponse {
  isOk: boolean
  message?: string
  data: {
    message: string
    user: {
      avatar_url: string
      coins: number
      email: string
      gender: 'male' | 'female' | 'unknown'
      name: string
      plan: 'free' | 'basic'
      role: 'guest' | 'normal'
      status: 'guest' | 'normal'
      uuid: string
    }
  }
}
