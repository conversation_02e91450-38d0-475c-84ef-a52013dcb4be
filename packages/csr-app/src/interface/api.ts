export interface CommonQuery {
  pageSize?: number
  page?: number
}

export interface ProjectQuery {
  pageSize?: number
  page?: number
  accessControl?: number
}
export interface ResponseData<T = unknown> {
  isOk: boolean
  code: number
  message: string
  data: T
}

export interface ListResult<T> {
  total: number
  data: T[]
}

export enum ListStatus {
  loading = 'loading',
  success = 'success',
  error = 'error'
}

export interface ListInfo {
  status: ListStatus
  total: number
}

export interface Action {
  id: string
  name: string
}
