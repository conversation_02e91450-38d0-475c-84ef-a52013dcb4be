export interface TaskData {
  name: string
  count: number
  limit: number
  is_done: boolean
  description: string
  coins: number
}

export interface TasksResponse {
  code: string
  message: string
  data: {
    play_game_task: TaskData
    share_task: TaskData
    create_task: TaskData
    invite_task: TaskData
    [key: string]: TaskData
  }
}

export interface TaskItem {
  id: string
  name: string
  description: string
  count: number
  limit: number
  is_done: boolean
  type: 'play_game' | 'share' | 'create' | 'invite' | string
  reward: number
}

export interface CompleteTaskResponse {
  code: string
  message: string
  data: {
    user: {
      uuid: string
      name: string
      email: string
      avatar_url: string
      status: string
      plan: string
      gender: string
      coins: number
      role: string
    }
  }
}

export interface InviteCodeResponse {
  code: string
  message: string
  data: {
    exclusive_invite_code: string
  }
}
