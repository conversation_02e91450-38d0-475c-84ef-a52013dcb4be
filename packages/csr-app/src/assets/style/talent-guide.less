// Custom styles for <PERSON>.js 引导样式
.shepherd-element.talent-guide-step {
  background: rgba(31, 0, 56, 0.95);
  border: 1px solid rgba(202, 147, 242, 0.3);
  border-radius: 16px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5);
  color: #fff;
  max-width: 85%;
  padding: 0;
  z-index: 10000;
  overflow: visible;

  /* 定位在屏幕底部 */
  position: fixed !important;
  bottom: 120px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
}

.shepherd-element .shepherd-header {
  display: none;
}

.shepherd-element .shepherd-text {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.4;
  padding: 16px 16px 8px;
  text-align: center;
}

.shepherd-element .shepherd-footer {
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  padding: 0 16px 16px;
  justify-content: flex-end;
  margin-top: -4px;
  .shepherd-button {
    display: flex;
    width: 100px;
    height: 40px;
    padding: 10px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
    color: #ca93f2;
    font-size: 16px;
    font-weight: 500;
    line-height: 150%;
    border-radius: 42px;
    border: 1px solid #ca93f2;
  }
}

.shepherd-element .shepherd-button {
  background: none;
  border: none;
  border-radius: 20px;
  font-size: 15px;
  font-weight: 600;
  margin-right: 0;
  padding: 8px 16px;
  transition: all 0.2s ease;
}

.shepherd-element .talent-guide-button.talent-guide-next,
.shepherd-element .talent-guide-button.talent-guide-done {
  background: linear-gradient(180deg, #d6cafe 0%, #ca93f2 100%);
  color: #1f0038;
  border-radius: 32px;
  padding: 8px 20px;
  min-width: 100px;
  font-weight: 700;
}

.shepherd-element .talent-guide-button.talent-guide-skip,
.shepherd-element .talent-guide-button.talent-guide-back {
  background: rgba(202, 147, 242, 0.2);
  border: 1px solid rgba(202, 147, 242, 0.5);
  color: #fff;
  display: none; /* 隐藏跳过按钮 */
}

.shepherd-element .shepherd-cancel-icon {
  color: rgba(255, 255, 255, 0.7);
  font-size: 24px;
  transition: color 0.2s ease;
}

.shepherd-element .shepherd-cancel-icon:hover {
  color: #fff;
}

/* 隐藏箭头 */
.shepherd-arrow {
  display: none;
}

.shepherd-element.shepherd-has-title .shepherd-content .shepherd-header .shepherd-cancel-icon {
  margin-top: 0;
}

// 高亮元素样式
.shepherd-highlighted,
.guide-highlight {
  animation: highlight-pulse 2s infinite;
  box-shadow:
    0 0 0 4px rgba(218, 255, 150, 0.9),
    0 0 30px rgba(218, 255, 150, 0.7) !important;
  border-radius: 8px;
  will-change: box-shadow;
  position: relative;
  z-index: 9999 !important;
}

// 高亮效果动画
@keyframes highlight-pulse {
  0% {
    box-shadow:
      0 0 0 4px rgba(218, 255, 150, 0.7),
      0 0 20px 5px rgba(218, 255, 150, 0.4);
  }
  50% {
    box-shadow:
      0 0 0 4px rgba(218, 255, 150, 0.9),
      0 0 30px 10px rgba(218, 255, 150, 0.6);
  }
  100% {
    box-shadow:
      0 0 0 4px rgba(218, 255, 150, 0.7),
      0 0 20px 5px rgba(218, 255, 150, 0.4);
  }
}

// 手指指针样式
.guide-hand-pointer {
  position: fixed !important;
  width: 40px;
  height: 40px;
  z-index: 10001 !important;
  pointer-events: none;
  will-change: transform, opacity;
  transform-origin: center center;
  visibility: visible !important;
  display: flex !important;
  align-items: center;
  justify-content: center;
  opacity: 1 !important;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.4));
  transition:
    top 0.2s ease-out,
    left 0.2s ease-out;
}

.guide-hand-pointer img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 0 8px rgba(202, 147, 242, 0.9));
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  transition: transform 0.2s ease-out;
}

// 滑动轨迹样式
.swipe-trail {
  position: fixed !important;
  z-index: 10000 !important;
  pointer-events: none;
  visibility: visible !important;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  opacity: 0.7 !important;
}

// 增强点击动画效果
@keyframes tap-animation {
  0%,
  100% {
    transform: translateY(0) scale(1);
    filter: drop-shadow(0 0 8px rgba(202, 147, 242, 0.8));
  }
  50% {
    transform: translateY(10px) scale(0.9);
    filter: drop-shadow(0 0 12px rgba(202, 147, 242, 1));
  }
}

// 属性点数分配动画增强
@keyframes pointer-tap-down {
  0%,
  100% {
    transform: var(--rotate-scale) translateY(0);
    filter: drop-shadow(0 0 8px rgba(202, 147, 242, 0.8));
  }
  50% {
    transform: var(--rotate-scale) translateY(6px);
    filter: drop-shadow(0 0 12px rgba(202, 147, 242, 1));
  }
}

// 引导高亮元素样式
.guide-highlight {
  animation: highlight-pulse 2s infinite !important;
  transition: all 0.3s ease-in-out !important;
}

// 轮播卡片高亮样式
.carousel .skill-card.active.highlighted,
.carousel .skill-card.highlighted {
  box-shadow:
    0 0 0 4px rgba(218, 255, 150, 0.9),
    0 0 30px 10px rgba(218, 255, 150, 0.6) !important;
  animation: highlight-pulse 2s infinite;
  z-index: 11 !important;
  position: relative !important;

  // 使用伪元素创建高亮效果，不影响原卡片位置
  &::before {
    content: '';
    position: absolute;
    top: -5px;
    right: -5px;
    bottom: -5px;
    left: -5px;
    border-radius: inherit;
    box-shadow:
      0 0 0 4px rgba(218, 255, 150, 0.9),
      0 0 30px 10px rgba(218, 255, 150, 0.6);
    animation: highlight-pulse 2s infinite;
    z-index: -1;
    pointer-events: none;
  }

  // 确保内容在高亮框内
  & > * {
    position: relative;
    z-index: 1;
  }
}

// 修改highlight-pulse动画，使用伪元素实现
@keyframes highlight-pulse-overlay {
  0% {
    opacity: 0.7;
    box-shadow:
      0 0 0 4px rgba(218, 255, 150, 0.7),
      0 0 20px 5px rgba(218, 255, 150, 0.4);
  }
  50% {
    opacity: 1;
    box-shadow:
      0 0 0 4px rgba(218, 255, 150, 0.9),
      0 0 30px 10px rgba(218, 255, 150, 0.6);
  }
  100% {
    opacity: 0.7;
    box-shadow:
      0 0 0 4px rgba(218, 255, 150, 0.7),
      0 0 20px 5px rgba(218, 255, 150, 0.4);
  }
}

// 小屏幕适配
@media (max-width: 350px) {
  .shepherd-element.talent-guide-step {
    max-width: 95%;

    .shepherd-text {
      font-size: 14px;
      padding: 12px;
    }

    .shepherd-footer {
      padding: 0 12px 12px;
    }
  }
}

// 内置蒙层样式
.shepherd-modal-overlay-container {
  opacity: 0.7;
  transition: all 0.3s ease;
}

// 确保在蒙层上可见的被高亮元素
.shepherd-modal-is-visible .shepherd-highlight-element {
  z-index: auto !important;
}

// 确保蒙层上的高亮元素可点击
.shepherd-modal-is-visible {
  .shepherd-highlight-element,
  .shepherd-target,
  .shepherd-element,
  .guide-highlight,
  .guide-card-highlight {
    pointer-events: auto !important;
  }
}

// 修复控制按钮在蒙层上的可见性和可点击性
.attribute-controls .control-btn.highlighted,
.attribute-controls .control-btn.guide-highlight,
.attribute-controls .control-btn.shepherd-highlighted {
  z-index: 9999 !important;
  position: relative !important;
  pointer-events: auto !important;
}

// 确认按钮高亮样式增强
.confirm-btn.guide-highlight,
.confirm-btn.shepherd-highlighted {
  box-shadow:
    0 0 0 4px rgba(218, 255, 150, 0.9),
    0 0 30px 10px rgba(218, 255, 150, 0.6),
    0 4px 20px rgba(202, 147, 242, 0.8) !important;
  z-index: 9999 !important;
}

// 属性卡片高亮样式
.attribute-card.guide-highlight,
.attribute-grid.guide-highlight {
  z-index: 9999 !important;
  position: relative !important;
}

// 轮播卡片高亮样式（不影响位置）
.carousel .skill-card.guide-card-highlight {
  // 仅使用后置伪元素添加光晕效果，不修改卡片自身属性
  &::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    bottom: -2px;
    left: -2px;
    border-radius: inherit;
    box-shadow: 0 0 15px 5px rgba(218, 255, 150, 0.5);
    animation: highlight-outer-glow 2s infinite;
    z-index: -1;
    pointer-events: none;
    opacity: 0.8;
  }

  // 添加内部高亮效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border: 2px solid rgba(218, 255, 150, 0.7);
    border-radius: inherit;
    z-index: 1;
    pointer-events: none;
  }

  // 确保内容在高亮之上
  & > * {
    position: relative;
    z-index: 2;
  }
}

// 确保高亮效果在卡片切换过程中也可见
.carousel .skill-card.active.guide-card-highlight,
.carousel .skill-card.prev-card.guide-card-highlight,
.carousel .skill-card.next-card.guide-card-highlight {
  // 使用更安全的方式处理动画，不改变卡片原有的transform
  &::after,
  &::before {
    animation: highlight-outer-glow 2s infinite;
  }
}

// 避免与普通的highlight类冲突
.carousel .skill-card.highlighted {
  // 清理特定于高亮类的样式，使用guide-card-highlight类代替
  box-shadow: none !important;
  animation: none !important;

  &::before,
  &::after {
    display: none !important;
  }
}

// 非侵入式的光晕动画
@keyframes highlight-outer-glow {
  0%,
  100% {
    opacity: 0.6;
    box-shadow: 0 0 15px 5px rgba(218, 255, 150, 0.4);
  }
  50% {
    opacity: 0.9;
    box-shadow: 0 0 20px 8px rgba(218, 255, 150, 0.7);
  }
}
