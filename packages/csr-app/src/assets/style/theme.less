/* 主题变量 */

:root {
  /* 暗色主题变量 - 默认 */
  --bg-primary: #180430;
  --bg-secondary: #290e40;
  --bg-tertiary: rgba(255, 255, 255, 0.05);
  --bg-card: rgba(255, 255, 255, 0.08);
  --bg-hover: rgba(255, 255, 255, 0.1);

  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --text-tertiary: rgba(255, 255, 255, 0.5);

  --border-color: rgba(255, 255, 255, 0.1);
  --divider-color: rgba(255, 255, 255, 0.05);

  --accent-color: #ca93f2;
  --accent-hover: #b87de0;
  --accent-bg: rgba(202, 147, 242, 0.1);

  --shadow-color: rgba(0, 0, 0, 0.2);

  --sidebar-bg: #290e40;
  --header-bg: rgba(24, 4, 48, 0.95);

  /* 钻石数量颜色 */
  --coins-color: #daff96;

  --border-color: rgba(255, 255, 255, 0.1);

  /* PC布局变量 */
  --pc-top-header-bg: #2a1b42;
  --pc-sidebar-active-bg: rgba(202, 147, 242, 0.2);
  --pc-sidebar-hover-bg: rgba(255, 255, 255, 0.1);
  --pc-sidebar-active-text: #ca93f2;
  --pc-sidebar-text: rgba(255, 255, 255, 0.7);
  --pc-sidebar-icon-size: 20px;
  --pc-sidebar-item-radius: 20px;
  --pc-sidebar-item-padding: 15px 20px;

  /* PC聊天界面变量 */
  --pc-chat-sidebar-bg: rgba(31, 0, 56, 0.8);
  --pc-chat-area-bg: rgba(31, 0, 56, 0.5);
  --pc-chat-area-overlay: rgba(31, 0, 56, 0.4);
  --pc-chat-message-actor-bg: rgba(245, 230, 255, 0.9);
  --pc-chat-message-actor-border: #e0b6ff;
  --pc-chat-message-actor-text: #1f0038;
  --pc-chat-message-user-bg: rgba(76, 51, 96, 0.9);
  --pc-chat-message-user-border: #754f93;
  --pc-chat-message-user-text: #ffffff;
  --pc-chat-name-tag-actor-bg: linear-gradient(135deg, #ca93f2, #ba9eff);
  --pc-chat-name-tag-user-bg: linear-gradient(135deg, #7f6f8c, #754f93);

  /* 页面背景色变量 - 默认暗色主题 */
  --page-bg-color: #180430;
}

/* 确保变量在全局范围内可用 */
body {
  &.pc-mode {
    background-color: var(--bg-primary);
    color: var(--text-primary);
  }
}

/* 亮色主题变量 */
body.light-theme {
  --bg-primary: #f8f9fa;
  --bg-secondary: #ffffff;
  --bg-tertiary: rgba(0, 0, 0, 0.05);
  --bg-card: #ffffff;
  --bg-hover: rgba(0, 0, 0, 0.05);

  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;

  --border-color: rgba(0, 0, 0, 0.1);
  --divider-color: rgba(0, 0, 0, 0.05);

  --accent-color: #ca93f2;
  --accent-hover: #7d3c98;
  --accent-bg: rgba(142, 68, 173, 0.1);

  --shadow-color: rgba(0, 0, 0, 0.1);

  --sidebar-bg: #ffffff;
  --header-bg: rgba(255, 255, 255, 0.95);

  /* 钻石数量颜色 - 亮色模式下使用更深的绿色以提高对比度 */
  --coins-color: #2d7d32;

  /* 奖励数量颜色 - 亮色模式下使用紫色 */
  --reward-amount-color: #ca93f2;

  --border-color: rgba(0, 0, 0, 0.1);

  /* PC布局变量 */
  --pc-top-header-bg: #ffffff;
  --pc-sidebar-active-bg: rgba(202, 147, 242, 0.2);
  --pc-sidebar-hover-bg: rgba(0, 0, 0, 0.05);
  --pc-sidebar-active-text: #ca93f2;
  --pc-sidebar-text: #666666;
  --pc-sidebar-icon-size: 20px;
  --pc-sidebar-item-radius: 20px;
  --pc-sidebar-item-padding: 15px 20px;

  /* PC聊天界面变量 */
  --pc-chat-sidebar-bg: #f5f0ff;
  --pc-chat-area-bg: rgba(255, 255, 255, 0.9);
  --pc-chat-area-overlay: rgba(255, 255, 255, 0.4);
  --pc-chat-message-actor-bg: #f5f0ff;
  --pc-chat-message-actor-border: #e0b6ff;
  --pc-chat-message-actor-text: #333333;
  --pc-chat-message-user-bg: #8e44ad;
  --pc-chat-message-user-border: #7d3c98;
  --pc-chat-message-user-text: #ffffff;
  --pc-chat-name-tag-actor-bg: linear-gradient(135deg, #ca93f2, #ba9eff);
  --pc-chat-name-tag-user-bg: linear-gradient(135deg, #8e44ad, #7d3c98);

  /* 筛选组件变量 */
  --filter-dropdown-bg: transparent;
  --filter-dropdown-hover-bg: #f5f0ff;
  --filter-dropdown-text: #1f0038;
  --filter-dropdown-border: #1f0038;

  --filter-option-hover-bg: #f5f0ff;
  --filter-option-active-bg: #f5f0ff;
  --filter-option-active-text: #1f0038;

  --tag-bg: #ffffff;
  --tag-hover-bg: #f5f0ff;
  --tag-text: rgba(0, 0, 0, 0.65);
  --tag-border: rgba(0, 0, 0, 0.65);
  --tag-active-bg: rgba(202, 147, 242, 0.5);
  --tag-active-text: rgba(0, 0, 0, 0.85);
  --tag-active-border: #ca93f2;

  /* 移动端专用变量 */
  --mobile-bg-primary: #f8f9fa;
  --mobile-bg-secondary: #ffffff;
  --mobile-bg-gradient-start: #f0f0f0;
  --mobile-bg-gradient-end: #ffffff;
  --mobile-app-bg: #ffffff;
  --mobile-menu-bg: #ffffff;
  --mobile-header-bg: rgba(255, 255, 255, 0.95);
  --mobile-input-bg: rgba(0, 0, 0, 0.05);
  --mobile-input-border: rgba(0, 0, 0, 0.1);
  --mobile-button-bg: rgba(0, 0, 0, 0.05);

  /* 页面背景色变量 - 亮色主题 */
  --page-bg-color: #f8f9fa;

  /* 确保卡片在亮色主题下有边框 */
  .story-card {
    // border: 1px solid rgba(0, 0, 0, 0.1);
  }
}

/* 确保暗色主题明确设置 */
body.dark-theme {
  --bg-primary: #180430;
  --bg-secondary: #290e40;
  --bg-tertiary: rgba(255, 255, 255, 0.05);
  --bg-card: rgba(255, 255, 255, 0.08);
  --bg-hover: rgba(255, 255, 255, 0.1);

  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --text-tertiary: rgba(255, 255, 255, 0.5);

  --border-color: rgba(255, 255, 255, 0.1);
  --divider-color: rgba(255, 255, 255, 0.05);

  --accent-color: #ca93f2;
  --accent-hover: #b87de0;
  --accent-bg: rgba(202, 147, 242, 0.1);

  --shadow-color: rgba(0, 0, 0, 0.2);

  --sidebar-bg: #290e40;
  --header-bg: rgba(24, 4, 48, 0.95);

  /* 钻石数量颜色 - 暗色模式下使用亮绿色 */
  --coins-color: #daff96;

  /* 奖励数量颜色 - 暗色模式下使用亮绿色 */
  --reward-amount-color: #daff96;

  --border-color: rgba(255, 255, 255, 0.1);

  /* PC布局变量 */
  --pc-top-header-bg: #2a1b42;
  --pc-sidebar-active-bg: rgba(202, 147, 242, 0.2);
  --pc-sidebar-hover-bg: rgba(255, 255, 255, 0.1);
  --pc-sidebar-active-text: #ca93f2;
  --pc-sidebar-text: rgba(255, 255, 255, 0.7);
  --pc-sidebar-icon-size: 20px;
  --pc-sidebar-item-radius: 20px;
  --pc-sidebar-item-padding: 15px 20px;

  /* PC聊天界面变量 */
  --pc-chat-sidebar-bg: rgba(31, 0, 56, 0.8);
  --pc-chat-area-bg: rgba(31, 0, 56, 0.5);
  --pc-chat-area-overlay: rgba(31, 0, 56, 0.4);
  --pc-chat-message-actor-bg: rgba(245, 230, 255, 0.9);
  --pc-chat-message-actor-border: #e0b6ff;
  --pc-chat-message-actor-text: #1f0038;
  --pc-chat-message-user-bg: rgba(76, 51, 96, 0.9);
  --pc-chat-message-user-border: #754f93;
  --pc-chat-message-user-text: #ffffff;
  --pc-chat-name-tag-actor-bg: linear-gradient(135deg, #ca93f2, #ba9eff);
  --pc-chat-name-tag-user-bg: linear-gradient(135deg, #7f6f8c, #754f93);

  /* 筛选组件变量 */
  --filter-dropdown-bg: transparent;
  --filter-dropdown-hover-bg: #3a0066;
  --filter-dropdown-text: #ffffff;
  --filter-dropdown-border: #ca93f2;

  --filter-option-hover-bg: #3a0066;
  --filter-option-active-bg: #3a0066;
  --filter-option-active-text: #ffffff;

  --tag-bg: #290e40;
  --tag-hover-bg: #3a0066;
  --tag-text: rgba(255, 255, 255, 0.65);
  --tag-border: rgba(255, 255, 255, 0.65);
  --tag-active-bg: rgba(202, 147, 242, 0.5);
  --tag-active-text: rgba(255, 255, 255, 0.85);
  --tag-active-border: #ca93f2;

  /* 移动端专用变量 - 暗色主题 */
  --mobile-bg-primary: #180430;
  --mobile-bg-secondary: #290e40;
  --mobile-bg-gradient-start: #2b1b2f;
  --mobile-bg-gradient-end: #1a1021;
  --mobile-app-bg: #1f0038;
  --mobile-menu-bg: #1f0038;
  --mobile-header-bg: rgba(24, 4, 48, 0.95);
  --mobile-input-bg: rgba(204, 213, 255, 0.05);
  --mobile-input-border: rgba(184, 196, 255, 0.1);
  --mobile-button-bg: rgba(255, 255, 255, 0.1);

  /* 页面背景色变量 - 暗色主题 */
  --page-bg-color: #180430;
}

/* 主题过渡效果 */
body {
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

/* 全局应用主题变量 */
body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}
