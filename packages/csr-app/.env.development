# 开发/测试环境配置
VITE_API_HOST=https://api-test.zhijianyuzhou.com
VITE_APP_TITLE=PlayShot.AI - Development
VITE_APP_ENV=development

# 微前端配置
# 本地开发时使用 localhost，部署到测试环境时使用域名
VITE_MAIN_APP_URL=http://localhost:3000
VITE_CSR_APP_URL=http://localhost:5173

# 调试配置
VITE_DEBUG=true
VITE_ENABLE_MOCK=false

# 品牌配置
VITE_APP_NAME=PlayShot
VITE_WEBSITE_TITLE=PlayShot
VITE_LOGO_URL=https://static.playshot.ai/static/images/logo/playshot_logo.png
VITE_ICON_URL=https://cdn.magiclight.ai/assets/playshot/playshot-icon.png
