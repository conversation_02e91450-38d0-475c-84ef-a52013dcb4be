import { mergeConfig, loadEnv } from 'vite'
import baseConfig from './vite.config.base'
import { createHtmlPlugin } from 'vite-plugin-html'
import configVisualizerPlugin from './plugin/visualizer'
import configArcoResolverPlugin from './plugin/arcoResolver'
import createManifestPlugin from './plugin/manifest'
import { injectFacebookPixelEuropeAndAmerica } from './plugins/injectFacebookPixel'
import { injectByteDanceAnalytics } from './plugins/injectByteDanceAnalytics'
import { optimizeFontLoading } from './plugins/optimizeFontLoading'
import { createDefaultDynamicApiHostPlugin } from './plugin/dynamicApiHost'
import { createProductionCompressPlugin } from './plugin/compress'
import { createProdPerformancePlugins } from './plugin/performance'
import { createLightCdnPlugin } from './plugin/cdn'
import {
  createAdvancedPreloadPlugin,
  createCriticalPathPlugin,
} from './plugin/advanced-preload'
import {
  createDynamicImportPlugin,
  createBundleOptimizationPlugin,
} from './plugin/module-federation'
import { createPWAPlugin } from './plugin/pwa'
import { resolve } from 'path'

// Load environment variables for ReelPlay production
const env = loadEnv('prod.reelplay', resolve(process.cwd()))

export default mergeConfig(
  {
    mode: 'prod',
    base: '/',
    define: {
      // 生产环境变量定义
      __DEV__: false,
      __TEST__: false,
    },
    plugins: [
      configVisualizerPlugin(),
      configArcoResolverPlugin(),
      createManifestPlugin(),
      createDefaultDynamicApiHostPlugin(env.VITE_API_HOST),

      // CDN外部化 - 暂时禁用，专注于其他优化
      // createLightCdnPlugin(),

      // 高级预加载优化
      createAdvancedPreloadPlugin({
        strategy: 'adaptive',
        networkAware: true,
        criticalResources: [
          '/src/main.ts',
          '/src/AppRoot.vue',
          '/src/router/index.ts',
        ],
        routePreload: {
          '/stories': [
            '/src/mobile/views/stories/index.vue',
            '/src/pc/views/stories/index.vue',
          ],
          '/chat': [
            '/src/mobile/views/chat/index.vue',
            '/src/pc/views/chat/index.vue',
          ],
        },
      }),

      // 关键路径优化
      createCriticalPathPlugin(),

      // 动态导入优化
      createDynamicImportPlugin(),

      // Bundle分析优化 - 生产环境不生成报告文件
      createBundleOptimizationPlugin({ enableReport: false }),

      // PWA插件
      createPWAPlugin(env),

      createHtmlPlugin({
        inject: {
          data: {
            title: env.VITE_WEBSITE_TITLE || 'ReelPlay',
            description:
              'Experience immersive AI-powered interactive stories and characters. Create your own adventures with advanced AI technology.',
            iconUrl:
              env.VITE_ICON_URL ||
              'https://cdn.magiclight.ai/assets/playshot/playshot-icon.png',
          },
        },
      }),
      // 字体加载优化插件 - 减少对LCP的影响
      optimizeFontLoading(),
      // 优化后的第三方脚本插件 - 减少对LCP的影响
      injectFacebookPixelEuropeAndAmerica(),
      injectByteDanceAnalytics(),
      // 压缩插件
      ...createProductionCompressPlugin(),
      // 性能优化插件
      ...createProdPerformancePlugins(),
    ],
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            // UI库分包
            'arco': ['@arco-design/web-vue'],

            // Vue生态系统
            'vue-utils': ['@vueuse/core', 'vue-i18n'],

            // 第三方服务 (Stripe 已移至 shared-payment)

            // 媒体和动画
            'media': ['howler', 'swiper', 'canvas-confetti'],

            // 工具库
            'utils': ['lodash-es', 'dayjs', 'nanoid', 'uuid'],

            // 可视化库
            'visualization': [
              '@antv/x6',
              '@antv/x6-vue-shape',
              'motion',
              'animejs',
            ],
          },
        },
      },
      chunkSizeWarningLimit: 2000,
      // 启用更激进的压缩
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true, // 生产环境移除console
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.info', 'console.debug'],
          passes: 3, // 增加压缩次数
        },
        mangle: {
          safari10: true,
          toplevel: true, // 更激进的变量名混淆
        },
        format: {
          comments: false,
        },
      },
    },
  },
  baseConfig,
)
