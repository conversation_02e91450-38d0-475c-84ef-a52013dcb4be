import { mergeConfig } from 'vite'
import baseConfig from './vite.config.base'
import { createHtmlPlugin } from 'vite-plugin-html'
import configVisualizerPlugin from './plugin/visualizer'
import configArcoResolverPlugin from './plugin/arcoResolver'
import { injectFacebookPixelEuropeAndAmerica } from './plugins/injectFacebookPixel'
import { injectByteDanceAnalytics } from './plugins/injectByteDanceAnalytics'
import { createProductionCompressPlugin } from './plugin/compress'
import { createProdPerformancePlugins } from './plugin/performance'
export default mergeConfig(
  {
    mode: 'pre',
    base: '/',
    plugins: [
      configVisualizerPlugin(),
      configArcoResolverPlugin(),

      createHtmlPlugin({
        inject: {
          data: {
            title: 'PlayShot',
            description: '',
            iconUrl: 'https://cdn.magiclight.ai/assets/playshot/playshot-icon.png'
          }
        }
      }),
      injectFacebookPixelEuropeAndAmerica(),
      injectByteDanceAnalytics(),
      // 压缩插件
      ...createProductionCompressPlugin(),
      // 性能优化插件
      ...createProdPerformancePlugins()
    ],
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            // 移除已通过CDN加载的库: vue, vue-router, pinia, @arco-design/web-vue
            // 保留需要本地打包的库
            'vue-utils': ['@vueuse/core', 'vue-i18n']
          }
        }
      },
      chunkSizeWarningLimit: 2000
    }
  },
  baseConfig
)
