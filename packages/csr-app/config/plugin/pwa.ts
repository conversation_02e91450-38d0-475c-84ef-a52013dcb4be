import { VitePWA } from 'vite-plugin-pwa'

export function createPWAPlugin(env: Record<string, any> = {}) {
  // 从环境变量获取应用信息
  const appName = env.VITE_APP_NAME || 'PlayShot'
  const iconUrl = env.VITE_ICON_URL || '/icon.svg'

  // 根据应用名称选择本地图标
  const localIconPath = appName.toLowerCase() === 'reelplay' ? '/reelplay.svg' : '/playshot.svg'

  // 检测是否为开发环境
  const isDev = process.env.NODE_ENV === 'development' || env.MODE === 'development'

  return VitePWA({
    registerType: 'autoUpdate',
    workbox: {
      globPatterns: ['**/*.{js,css,html,ico,png,svg,woff2}'],
      runtimeCaching: [
        {
          urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
          handler: 'CacheFirst',
          options: {
            cacheName: 'google-fonts-cache',
            expiration: {
              maxEntries: 10,
              maxAgeSeconds: 60 * 60 * 24 * 365 // 1 year
            }
          }
        },
        {
          urlPattern: /^https:\/\/fonts\.gstatic\.com\/.*/i,
          handler: 'CacheFirst',
          options: {
            cacheName: 'gstatic-fonts-cache',
            expiration: {
              maxEntries: 10,
              maxAgeSeconds: 60 * 60 * 24 * 365 // 1 year
            }
          }
        },
        {
          urlPattern: /^https:\/\/static\.playshot\.ai\/.*/i,
          handler: 'StaleWhileRevalidate',
          options: {
            cacheName: 'static-cache',
            expiration: {
              maxEntries: 50,
              maxAgeSeconds: 60 * 60 * 24 * 30 // 30 days
            }
          }
        }
      ]
    },
    includeAssets: ['favicon.ico'],
    manifest: {
      name: `${appName}`,
      short_name: appName,
      description: `Experience immersive AI-powered interactive stories and characters with ${appName}`,
      theme_color: '#ca93f2',
      background_color: '#180430',
      display: 'standalone',
      orientation: 'portrait-primary',
      scope: '/',
      start_url: '/',
      categories: ['entertainment', 'games'],
      lang: 'en',
      dir: 'ltr',
      icons: [
        {
          src: '/favicon.ico',
          sizes: '48x48',
          type: 'image/x-icon'
        },
        {
          src: localIconPath,
          sizes: 'any',
          type: 'image/svg+xml',
          purpose: 'any'
        },
        {
          src: localIconPath,
          sizes: 'any',
          type: 'image/svg+xml',
          purpose: 'maskable'
        }
      ]
    },
    devOptions: {
      enabled: !isDev // 开发环境禁用PWA，避免缓存问题
    }
  })
}

export default createPWAPlugin
