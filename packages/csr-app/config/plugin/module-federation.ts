/**
 * 模块联邦优化插件
 * 实现微前端架构，进一步减少主bundle大小
 */
import type { Plugin } from 'vite'

export interface ModuleFederationConfig {
  name: string
  remotes?: Record<string, string>
  exposes?: Record<string, string>
  shared?: Record<string, any>
}

/**
 * 创建模块联邦插件
 */
export function createModuleFederationPlugin(config: ModuleFederationConfig): Plugin {
  return {
    name: 'vite-plugin-module-federation',
    config(viteConfig) {
      // 配置共享模块
      if (config.shared) {
        viteConfig.build = viteConfig.build || {}
        viteConfig.build.rollupOptions = viteConfig.build.rollupOptions || {}
        viteConfig.build.rollupOptions.external = viteConfig.build.rollupOptions.external || []

        const external = Array.isArray(viteConfig.build.rollupOptions.external)
          ? viteConfig.build.rollupOptions.external
          : []

        Object.keys(config.shared).forEach((module) => {
          if (!external.includes(module)) {
            external.push(module)
          }
        })

        viteConfig.build.rollupOptions.external = external
      }
    },
    generateBundle() {
      // 生成模块联邦运行时
      const federationRuntime = `
        (function() {
          // 模块联邦运行时
          window.__FEDERATION__ = {
            name: '${config.name}',
            remotes: ${JSON.stringify(config.remotes || {})},
            shared: ${JSON.stringify(config.shared || {})},
            
            // 动态导入远程模块
            async importRemote(remoteName, moduleName) {
              const remoteUrl = this.remotes[remoteName];
              if (!remoteUrl) {
                throw new Error('Remote not found: ' + remoteName);
              }
              
              // 加载远程模块
              const remoteModule = await import(remoteUrl + '/' + moduleName);
              return remoteModule;
            },
            
            // 共享模块管理
            getSharedModule(name) {
              return this.shared[name];
            }
          };
        })();
      `

      this.emitFile({
        type: 'asset',
        fileName: 'federation-runtime.js',
        source: federationRuntime.trim()
      })
    }
  }
}

/**
 * 创建动态导入优化插件
 */
export function createDynamicImportPlugin(): Plugin {
  return {
    name: 'vite-plugin-dynamic-import-optimization',
    generateBundle(_options, bundle) {
      // 生成动态导入优化脚本
      const dynamicImportScript = `
        (function() {
          // 动态导入优化
          const originalImport = window.__vitePreload;
          
          // 智能预加载策略
          const preloadCache = new Map();
          const loadingPromises = new Map();
          
          // 优化的动态导入函数
          window.__vitePreload = function(baseModule, deps, importerUrl) {
            // 检查缓存
            const cacheKey = deps.join(',');
            if (preloadCache.has(cacheKey)) {
              return preloadCache.get(cacheKey);
            }
            
            // 检查是否正在加载
            if (loadingPromises.has(cacheKey)) {
              return loadingPromises.get(cacheKey);
            }
            
            // 网络状态检测
            const connection = navigator.connection;
            const isSlowNetwork = connection && (
              connection.effectiveType === 'slow-2g' || 
              connection.effectiveType === '2g'
            );
            
            // 根据网络状况调整并发数
            const maxConcurrent = isSlowNetwork ? 2 : 6;
            
            // 批量加载依赖
            const loadPromise = Promise.all(
              deps.slice(0, maxConcurrent).map(dep => {
                return new Promise((resolve, reject) => {
                  const link = document.createElement('link');
                  link.rel = 'modulepreload';
                  link.href = dep;
                  link.onload = resolve;
                  link.onerror = reject;
                  document.head.appendChild(link);
                });
              })
            ).then(() => {
              // 加载剩余依赖
              if (deps.length > maxConcurrent) {
                return Promise.all(
                  deps.slice(maxConcurrent).map(dep => {
                    return new Promise((resolve, reject) => {
                      const link = document.createElement('link');
                      link.rel = 'modulepreload';
                      link.href = dep;
                      link.onload = resolve;
                      link.onerror = reject;
                      document.head.appendChild(link);
                    });
                  })
                );
              }
            }).then(() => {
              // 执行原始导入
              return originalImport ? originalImport(baseModule, deps, importerUrl) : Promise.resolve();
            });
            
            // 缓存Promise
            loadingPromises.set(cacheKey, loadPromise);
            
            // 完成后缓存结果
            loadPromise.then(result => {
              preloadCache.set(cacheKey, Promise.resolve(result));
              loadingPromises.delete(cacheKey);
              return result;
            }).catch(error => {
              loadingPromises.delete(cacheKey);
              throw error;
            });
            
            return loadPromise;
          };
          
          // 路由级别的代码分割优化
          const routeCache = new Map();
          
          window.__optimizedRouteImport = function(routePath) {
            if (routeCache.has(routePath)) {
              return routeCache.get(routePath);
            }
            
            const importPromise = import(routePath).then(module => {
              // 预加载相关组件
              if (module.default && module.default.__preloadDeps) {
                module.default.__preloadDeps.forEach(dep => {
                  const link = document.createElement('link');
                  link.rel = 'modulepreload';
                  link.href = dep;
                  document.head.appendChild(link);
                });
              }
              return module;
            });
            
            routeCache.set(routePath, importPromise);
            return importPromise;
          };
          
          // 组件级别的懒加载优化
          window.__optimizedComponentImport = function(componentPath, priority = 'normal') {
            const importPromise = import(componentPath);
            
            // 根据优先级决定预加载策略
            if (priority === 'high') {
              importPromise.then(module => {
                // 立即预加载相关依赖
                if (module.default && module.default.__asyncDeps) {
                  module.default.__asyncDeps.forEach(dep => {
                    import(dep).catch(() => {}); // 静默失败
                  });
                }
              });
            }
            
            return importPromise;
          };
        })();
      `

      this.emitFile({
        type: 'asset',
        fileName: 'dynamic-import-optimization.js',
        source: dynamicImportScript.trim()
      })
    },
    transformIndexHtml(html) {
      // 注入动态导入优化脚本
      const scriptTag = '<script src="/dynamic-import-optimization.js"></script>'
      return html.replace('</head>', `${scriptTag}\n</head>`)
    }
  }
}

/**
 * 创建Bundle分析和优化插件
 */
export function createBundleOptimizationPlugin(options: { enableReport?: boolean } = {}): Plugin {
  return {
    name: 'vite-plugin-bundle-optimization',
    generateBundle(_options, bundle) {
      // 分析bundle并生成优化建议
      const analysis = {
        totalSize: 0,
        chunks: [] as any[],
        recommendations: [] as string[]
      }

      Object.entries(bundle).forEach(([fileName, chunk]) => {
        if (chunk.type === 'chunk') {
          const size = chunk.code.length
          analysis.totalSize += size
          analysis.chunks.push({
            name: fileName,
            size,
            modules: Object.keys(chunk.modules || {}),
            isEntry: chunk.isEntry,
            isDynamicEntry: chunk.isDynamicEntry
          })
        }
      })

      // 生成优化建议
      analysis.chunks.sort((a, b) => b.size - a.size)

      const largeChunks = analysis.chunks.filter((chunk) => chunk.size > 500000) // 500KB
      if (largeChunks.length > 0) {
        analysis.recommendations.push(
          `发现 ${largeChunks.length} 个大型chunk (>500KB)，建议进一步拆分`
        )
      }

      const totalSizeMB = analysis.totalSize / (1024 * 1024)
      if (totalSizeMB > 2) {
        analysis.recommendations.push(`总bundle大小 ${totalSizeMB.toFixed(2)}MB，建议启用CDN外部化`)
      }

      // 只在开发环境或明确启用时输出分析报告
      if (options.enableReport || process.env.NODE_ENV === 'development') {
        this.emitFile({
          type: 'asset',
          fileName: 'bundle-optimization-report.json',
          source: JSON.stringify(analysis, null, 2)
        })
      }

      // 控制台输出
      console.log('\n📊 Bundle优化分析:')
      console.log(`总大小: ${totalSizeMB.toFixed(2)}MB`)
      console.log(`Chunk数量: ${analysis.chunks.length}`)
      if (analysis.recommendations.length > 0) {
        console.log('\n💡 优化建议:')
        analysis.recommendations.forEach((rec) => console.log(`  - ${rec}`))
      }
    }
  }
}
