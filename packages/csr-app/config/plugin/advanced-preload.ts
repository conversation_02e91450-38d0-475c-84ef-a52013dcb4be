/**
 * 高级预加载优化插件
 * 实现智能预加载、关键路径优化、资源优先级管理
 */
import type { Plugin } from 'vite'

export interface PreloadConfig {
  // 关键资源预加载
  criticalResources: string[]
  // 路由预加载配置
  routePreload: {
    [route: string]: string[]
  }
  // 预加载策略
  strategy: 'aggressive' | 'conservative' | 'adaptive'
  // 网络感知
  networkAware: boolean
}

const defaultConfig: PreloadConfig = {
  criticalResources: ['/src/main.ts', '/src/App.vue', '/src/router/index.ts'],
  routePreload: {
    '/stories': [
      '/src/mobile/views/stories/index.vue',
      '/src/pc/views/stories/index.vue',
      '/src/shared/components/StoryCard.vue'
    ],
    '/chat': ['/src/mobile/views/chat/index.vue', '/src/pc/views/chat/index.vue']
  },
  strategy: 'adaptive',
  networkAware: true
}

export function createAdvancedPreloadPlugin(config: Partial<PreloadConfig> = {}): Plugin {
  const finalConfig = { ...defaultConfig, ...config }

  return {
    name: 'vite-plugin-advanced-preload',
    generateBundle(_options, bundle) {
      // 生成智能预加载脚本
      const preloadScript = `
        (function() {
          // 网络状态检测
          const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
          const isSlowNetwork = connection && (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g');
          const isSaveData = connection && connection.saveData;
          
          // 设备性能检测
          const isLowEndDevice = navigator.hardwareConcurrency <= 2 || navigator.deviceMemory <= 2;
          
          // 预加载策略选择
          const strategy = '${finalConfig.strategy}';
          const networkAware = ${finalConfig.networkAware};
          
          // 决定是否进行预加载
          function shouldPreload() {
            if (!networkAware) return true;
            if (isSaveData) return false;
            if (strategy === 'conservative' && (isSlowNetwork || isLowEndDevice)) return false;
            if (strategy === 'adaptive' && isSlowNetwork && isLowEndDevice) return false;
            return true;
          }
          
          // 预加载函数
          function preloadResource(href, as, type) {
            if (!shouldPreload()) return;
            
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = href;
            link.as = as;
            if (type) link.type = type;
            link.crossOrigin = 'anonymous';
            
            // 错误处理
            link.onerror = function() {
              console.warn('Failed to preload:', href);
            };
            
            document.head.appendChild(link);
          }
          
          // 预连接到关键域名
          function preconnectDomains() {
            const domains = [
              'https://cdn.jsdelivr.net',
              'https://unpkg.com',
              'https://fonts.googleapis.com',
              'https://fonts.gstatic.com',
              'https://cdn.magiclight.ai',
              'https://static.playshot.ai'
            ];
            
            domains.forEach(function(domain) {
              const link = document.createElement('link');
              link.rel = 'preconnect';
              link.href = domain;
              link.crossOrigin = 'anonymous';
              document.head.appendChild(link);
            });
          }
          
          // 关键资源预加载
          function preloadCriticalResources() {
            const criticalResources = ${JSON.stringify(finalConfig.criticalResources)};
            
            criticalResources.forEach(function(resource) {
              if (resource.endsWith('.js') || resource.endsWith('.ts')) {
                preloadResource(resource, 'script', 'text/javascript');
              } else if (resource.endsWith('.css')) {
                preloadResource(resource, 'style', 'text/css');
              } else if (resource.endsWith('.vue')) {
                preloadResource(resource, 'script', 'text/javascript');
              }
            });
          }
          
          // 路由预加载
          function setupRoutePreload() {
            const routePreload = ${JSON.stringify(finalConfig.routePreload)};
            
            // 监听路由变化
            function handleRouteChange() {
              const currentPath = window.location.pathname;
              const resources = routePreload[currentPath];
              
              if (resources && shouldPreload()) {
                resources.forEach(function(resource) {
                  preloadResource(resource, 'script', 'text/javascript');
                });
              }
            }
            
            // 监听 popstate 事件
            window.addEventListener('popstate', handleRouteChange);
            
            // 监听链接点击
            document.addEventListener('click', function(e) {
              const link = e.target.closest('a');
              if (link && link.href && link.href.startsWith(window.location.origin)) {
                const path = new URL(link.href).pathname;
                const resources = routePreload[path];
                
                if (resources && shouldPreload()) {
                  // 延迟预加载，避免阻塞当前操作
                  setTimeout(function() {
                    resources.forEach(function(resource) {
                      preloadResource(resource, 'script', 'text/javascript');
                    });
                  }, 100);
                }
              }
            });
          }
          

          
          // 初始化
          function init() {
            // 立即执行的优化
            preconnectDomains();
            
            // DOM加载完成后执行
            if (document.readyState === 'loading') {
              document.addEventListener('DOMContentLoaded', function() {
                preloadCriticalResources();
                setupRoutePreload();
              });
            } else {
              preloadCriticalResources();
              setupRoutePreload();
            }
          }
          
          init();
        })();
      `

      // 生成预加载脚本文件
      this.emitFile({
        type: 'asset',
        fileName: 'advanced-preload.js',
        source: preloadScript.trim()
      })
    },
    transformIndexHtml(html) {
      // 在head中注入预加载脚本
      const preloadScriptTag = '<script src="/advanced-preload.js" defer></script>'
      return html.replace('</head>', `${preloadScriptTag}\n</head>`)
    }
  }
}

/**
 * 创建关键路径优化插件
 */
export function createCriticalPathPlugin(): Plugin {
  return {
    name: 'vite-plugin-critical-path',
    transformIndexHtml(html) {
      // 优化关键渲染路径
      const optimizations = `
        <script>
          // 关键路径优化
          (function() {
            // 预解析DNS
            const dnsPrefetchDomains = [
              '//cdn.jsdelivr.net',
              '//unpkg.com',
              '//fonts.gstatic.com'
            ];
            
            dnsPrefetchDomains.forEach(function(domain) {
              const link = document.createElement('link');
              link.rel = 'dns-prefetch';
              link.href = domain;
              document.head.appendChild(link);
            });
            
            // 优化首屏渲染
            const observer = new PerformanceObserver(function(list) {
              const entries = list.getEntries();
              entries.forEach(function(entry) {
                if (entry.entryType === 'largest-contentful-paint') {
                  console.log('LCP:', entry.startTime);
                }
              });
            });
            
            try {
              observer.observe({ entryTypes: ['largest-contentful-paint'] });
            } catch (e) {
              // 浏览器不支持
            }
          })();
        </script>
      `

      return html.replace('<head>', '<head>' + optimizations)
    }
  }
}
