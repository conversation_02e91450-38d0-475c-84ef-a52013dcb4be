import { Plugin } from 'vite'
import { copyFileSync, existsSync, mkdirSync } from 'fs'
import { resolve, dirname } from 'path'

/**
 * Vite 插件：处理 SEO 相关文件的复制和优化
 * 包括 robots.txt, sitemap.xml 等文件
 */
export default function createSEOPlugin(): Plugin {
  return {
    name: 'vite-plugin-seo',
    apply: 'build',
    generateBundle() {
      // 确保输出目录存在
      const outputDir = resolve(process.cwd(), 'dist')
      if (!existsSync(outputDir)) {
        mkdirSync(outputDir, { recursive: true })
      }

      // 复制 robots.txt
      const robotsSource = resolve(process.cwd(), 'public/robots.txt')
      const robotsTarget = resolve(outputDir, 'robots.txt')
      
      if (existsSync(robotsSource)) {
        copyFileSync(robotsSource, robotsTarget)
        console.log('✅ robots.txt copied to dist/')
      } else {
        console.warn('⚠️  robots.txt not found in public/ directory')
      }

      // 复制 sitemap.xml
      const sitemapSource = resolve(process.cwd(), 'public/sitemap.xml')
      const sitemapTarget = resolve(outputDir, 'sitemap.xml')
      
      if (existsSync(sitemapSource)) {
        copyFileSync(sitemapSource, sitemapTarget)
        console.log('✅ sitemap.xml copied to dist/')
      } else {
        console.warn('⚠️  sitemap.xml not found in public/ directory')
      }

      // 可以在这里添加更多 SEO 相关文件的处理
      // 例如：manifest.json, favicon.ico 等
    },
    
    configureServer(server) {
      // 开发环境下也提供这些文件的访问
      server.middlewares.use('/robots.txt', (req, res, next) => {
        const robotsPath = resolve(process.cwd(), 'public/robots.txt')
        if (existsSync(robotsPath)) {
          res.setHeader('Content-Type', 'text/plain')
          const fs = require('fs')
          res.end(fs.readFileSync(robotsPath, 'utf-8'))
        } else {
          next()
        }
      })

      server.middlewares.use('/sitemap.xml', (req, res, next) => {
        const sitemapPath = resolve(process.cwd(), 'public/sitemap.xml')
        if (existsSync(sitemapPath)) {
          res.setHeader('Content-Type', 'application/xml')
          const fs = require('fs')
          res.end(fs.readFileSync(sitemapPath, 'utf-8'))
        } else {
          next()
        }
      })
    }
  }
}

/**
 * 生成动态 sitemap 的辅助函数
 * 可以根据路由或数据动态生成 sitemap 内容
 */
export function generateDynamicSitemap(routes: string[], baseUrl: string): string {
  const currentDate = new Date().toISOString().split('T')[0]
  
  const urlEntries = routes.map(route => {
    const priority = route === '/' ? '1.0' : 
                    route.includes('/stories') ? '0.9' : 
                    route.includes('/user') ? '0.6' : '0.5'
    
    const changefreq = route === '/' ? 'daily' : 
                      route.includes('/stories') ? 'daily' : 
                      route.includes('/user') ? 'monthly' : 'monthly'

    return `  <url>
    <loc>${baseUrl}${route}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>${changefreq}</changefreq>
    <priority>${priority}</priority>
  </url>`
  }).join('\n')

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlEntries}
</urlset>`
}
