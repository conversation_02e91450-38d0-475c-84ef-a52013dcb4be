/**
 * CSS极致优化插件
 * 实现Critical CSS提取、未使用CSS移除、CSS压缩等功能
 */
import type { Plugin } from 'vite'

export interface CSSOptimizationConfig {
  // Critical CSS配置
  criticalCSS: {
    enabled: boolean
    inlineThreshold: number // 内联阈值(字节)
    extractPath: string
  }
  // 未使用CSS移除
  purgeCSS: {
    enabled: boolean
    safelist: string[]
    blocklist: string[]
  }
  // CSS压缩配置
  minification: {
    enabled: boolean
    removeComments: boolean
    removeUnusedRules: boolean
    mergeRules: boolean
  }
}

const defaultConfig: CSSOptimizationConfig = {
  criticalCSS: {
    enabled: true,
    inlineThreshold: 14336, // 14KB
    extractPath: 'critical.css'
  },
  purgeCSS: {
    enabled: true,
    safelist: [
      // Vue相关类名
      /^v-/,
      /^vue-/,
      // Arco Design类名
      /^arco-/,
      // 动态类名
      /^theme-/,
      /^mode-/,
      // 动画类名
      /^animate-/,
      /^transition-/
    ],
    blocklist: []
  },
  minification: {
    enabled: true,
    removeComments: true,
    removeUnusedRules: true,
    mergeRules: true
  }
}

export function createCSSOptimizationPlugin(config: Partial<CSSOptimizationConfig> = {}): Plugin {
  const finalConfig = { ...defaultConfig, ...config }

  return {
    name: 'vite-plugin-css-optimization',
    generateBundle(options, bundle) {
      // 处理CSS文件
      Object.entries(bundle).forEach(([fileName, chunk]) => {
        if (chunk.type === 'asset' && fileName.endsWith('.css')) {
          const cssContent = chunk.source.toString()
          
          // 提取Critical CSS
          if (finalConfig.criticalCSS.enabled) {
            const criticalCSS = extractCriticalCSS(cssContent)
            
            if (criticalCSS.length < finalConfig.criticalCSS.inlineThreshold) {
              // 生成内联Critical CSS
              this.emitFile({
                type: 'asset',
                fileName: 'critical-inline.css',
                source: criticalCSS
              })
            } else {
              // 生成独立的Critical CSS文件
              this.emitFile({
                type: 'asset',
                fileName: finalConfig.criticalCSS.extractPath,
                source: criticalCSS
              })
            }
          }
          
          // 优化主CSS文件
          if (finalConfig.minification.enabled) {
            const optimizedCSS = optimizeCSS(cssContent, finalConfig.minification)
            chunk.source = optimizedCSS
          }
        }
      })
    },
    transformIndexHtml(html) {
      // 注入Critical CSS内联脚本
      if (finalConfig.criticalCSS.enabled) {
        const criticalCSSScript = `
          <script>
            // Critical CSS 动态加载
            (function() {
              // 检测是否需要加载Critical CSS
              function loadCriticalCSS() {
                const criticalLink = document.createElement('link');
                criticalLink.rel = 'stylesheet';
                criticalLink.href = '/critical.css';
                criticalLink.media = 'all';
                document.head.appendChild(criticalLink);
              }
              
              // 检测首屏内容
              function detectAboveFold() {
                const viewportHeight = window.innerHeight;
                const criticalElements = document.querySelectorAll('header, nav, .hero, .banner, .above-fold');
                
                return criticalElements.length > 0;
              }
              
              // 延迟加载非关键CSS
              function loadNonCriticalCSS() {
                const links = document.querySelectorAll('link[rel="preload"][as="style"]');
                links.forEach(function(link) {
                  link.rel = 'stylesheet';
                });
              }
              
              // 初始化
              if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', function() {
                  if (detectAboveFold()) {
                    loadCriticalCSS();
                  }
                  // 延迟加载非关键CSS
                  setTimeout(loadNonCriticalCSS, 100);
                });
              } else {
                if (detectAboveFold()) {
                  loadCriticalCSS();
                }
                loadNonCriticalCSS();
              }
            })();
          </script>
        `
        
        return html.replace('</head>', criticalCSSScript + '\n</head>')
      }
      
      return html
    }
  }
}

/**
 * 提取Critical CSS
 */
function extractCriticalCSS(cssContent: string): string {
  const criticalSelectors = [
    // 基础元素
    'html', 'body', '*',
    // 布局相关
    '#app', '.app', '.container', '.wrapper',
    // 首屏组件
    '.header', '.nav', '.hero', '.banner', '.above-fold',
    // 加载状态
    '.loading', '.spinner', '.skeleton',
    // 字体相关
    '@font-face',
    // 关键动画
    '@keyframes spin', '@keyframes loading', '@keyframes fadeIn'
  ]
  
  const criticalRules: string[] = []
  
  // 简单的CSS解析和提取
  const rules = cssContent.split('}')
  
  rules.forEach(rule => {
    const trimmedRule = rule.trim()
    if (!trimmedRule) return
    
    // 检查是否为关键选择器
    const isCritical = criticalSelectors.some(selector => {
      if (selector.startsWith('@')) {
        return trimmedRule.includes(selector)
      }
      return trimmedRule.includes(selector)
    })
    
    if (isCritical) {
      criticalRules.push(trimmedRule + '}')
    }
  })
  
  return criticalRules.join('\n')
}

/**
 * CSS优化
 */
function optimizeCSS(cssContent: string, config: CSSOptimizationConfig['minification']): string {
  let optimized = cssContent
  
  if (config.removeComments) {
    // 移除注释
    optimized = optimized.replace(/\/\*[\s\S]*?\*\//g, '')
  }
  
  if (config.removeUnusedRules) {
    // 移除明显未使用的规则（简单实现）
    const unusedSelectors = [
      '.unused-class',
      '.debug-',
      '.test-'
    ]
    
    unusedSelectors.forEach(selector => {
      const regex = new RegExp(`[^}]*${selector}[^{]*{[^}]*}`, 'g')
      optimized = optimized.replace(regex, '')
    })
  }
  
  if (config.mergeRules) {
    // 合并相同的规则（简单实现）
    optimized = mergeSimilarRules(optimized)
  }
  
  // 压缩空白
  optimized = optimized
    .replace(/\s+/g, ' ')
    .replace(/;\s*}/g, '}')
    .replace(/{\s*/g, '{')
    .replace(/}\s*/g, '}')
    .replace(/,\s*/g, ',')
    .replace(/:\s*/g, ':')
    .replace(/;\s*/g, ';')
    .trim()
  
  return optimized
}

/**
 * 合并相似规则
 */
function mergeSimilarRules(cssContent: string): string {
  // 简单的规则合并实现
  const ruleMap = new Map<string, string[]>()
  
  // 解析CSS规则
  const rules = cssContent.match(/[^}]+{[^}]*}/g) || []
  
  rules.forEach(rule => {
    const [selector, declarations] = rule.split('{')
    const cleanDeclarations = declarations.replace('}', '').trim()
    
    if (ruleMap.has(cleanDeclarations)) {
      ruleMap.get(cleanDeclarations)!.push(selector.trim())
    } else {
      ruleMap.set(cleanDeclarations, [selector.trim()])
    }
  })
  
  // 重建CSS
  const mergedRules: string[] = []
  ruleMap.forEach((selectors, declarations) => {
    if (selectors.length > 1) {
      // 合并选择器
      mergedRules.push(`${selectors.join(',')}{${declarations}}`)
    } else {
      mergedRules.push(`${selectors[0]}{${declarations}}`)
    }
  })
  
  return mergedRules.join('')
}

/**
 * 创建CSS分割插件
 */
export function createCSSSplitPlugin(): Plugin {
  return {
    name: 'vite-plugin-css-split',
    generateBundle(_options, bundle) {
      Object.entries(bundle).forEach(([fileName, chunk]) => {
        if (chunk.type === 'asset' && fileName.endsWith('.css')) {
          const cssContent = chunk.source.toString()
          
          // 分割CSS为多个小文件
          if (cssContent.length > 100000) { // 100KB
            const parts = splitCSS(cssContent, 50000) // 50KB per part
            
            parts.forEach((part, index) => {
              this.emitFile({
                type: 'asset',
                fileName: `style-${index + 1}.css`,
                source: part
              })
            })
            
            // 移除原始大文件
            delete bundle[fileName]
          }
        }
      })
    }
  }
}

/**
 * 分割CSS
 */
function splitCSS(cssContent: string, maxSize: number): string[] {
  const parts: string[] = []
  const rules = cssContent.split('}')
  
  let currentPart = ''
  
  rules.forEach(rule => {
    const ruleWithBrace = rule.trim() + '}'
    
    if (currentPart.length + ruleWithBrace.length > maxSize) {
      if (currentPart) {
        parts.push(currentPart)
        currentPart = ruleWithBrace
      }
    } else {
      currentPart += ruleWithBrace
    }
  })
  
  if (currentPart) {
    parts.push(currentPart)
  }
  
  return parts
}
