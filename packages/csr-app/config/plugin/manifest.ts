import type { Plugin } from 'vite'

export function createManifestPlugin(): Plugin {
  return {
    name: 'vite-plugin-dynamic-manifest',
    generateBundle(options, bundle) {
      // 添加版本控制和缓存破坏机制
      const buildTime = Date.now()
      const version = process.env.npm_package_version || '1.0.0'
      // 从环境变量获取配置
      const appName = process.env.VITE_APP_NAME || 'ReelPlay'
      const websiteTitle = process.env.VITE_WEBSITE_TITLE || 'ReelPlay'

      // 根据不同产品配置不同的 manifest
      const manifestConfig = {
        ReelPlay: {
          name: 'ReelPlay - AI Interactive Stories',
          short_name: 'ReelPlay',
          description: 'Experience immersive AI-powered interactive stories and characters',
          start_url: '/',
          display: 'standalone',
          background_color: '#180430',
          theme_color: '#ca93f2',
          orientation: 'portrait-primary',
          categories: ['entertainment', 'games'],
          lang: 'en',
          dir: 'ltr',
          icons: [
            {
              src: 'https://cdn.magiclight.ai/assets/playshot/playshot-icon.png',
              sizes: '192x192',
              type: 'image/png',
              purpose: 'any maskable'
            },
            {
              src: 'https://cdn.magiclight.ai/assets/playshot/playshot-icon.png',
              sizes: '512x512',
              type: 'image/png',
              purpose: 'any maskable'
            }
          ]
        },
        PlayShot: {
          name: 'PlayShot - AI Interactive Stories',
          short_name: 'PlayShot',
          description: 'Experience immersive AI-powered interactive stories and characters',
          start_url: '/',
          display: 'standalone',
          background_color: '#180430',
          theme_color: '#ca93f2',
          orientation: 'portrait-primary',
          categories: ['entertainment', 'games'],
          lang: 'en',
          dir: 'ltr',
          icons: [
            {
              src: 'https://static.playshot.ai/static/images/icon/playshot.png',
              sizes: '192x192',
              type: 'image/png',
              purpose: 'any maskable'
            },
            {
              src: 'https://static.playshot.ai/static/images/icon/playshot.png',
              sizes: '512x512',
              type: 'image/png',
              purpose: 'any maskable'
            }
          ]
        }
      }

      // 选择对应的配置
      const manifest =
        manifestConfig[appName as keyof typeof manifestConfig] || manifestConfig.ReelPlay

      // 添加版本信息到 manifest
      const manifestWithVersion = {
        ...manifest,
        version,
        build_time: buildTime,
        // 添加缓存破坏参数到图标URL
        icons:
          manifest.icons?.map((icon) => ({
            ...icon,
            src: `${icon.src}`
          })) || []
      }

      // PWA插件已生成manifest.webmanifest，这里不再生成manifest.json
      // this.emitFile({
      //   type: 'asset',
      //   fileName: 'manifest.json',
      //   source: JSON.stringify(manifestWithVersion, null, 2)
      // })

      // 生成版本信息文件，用于客户端检查更新
      this.emitFile({
        type: 'asset',
        fileName: 'version.json',
        source: JSON.stringify(
          {
            version,
            buildTime,
            timestamp: buildTime
          },
          null,
          2
        )
      })
    }
  }
}

export default createManifestPlugin
