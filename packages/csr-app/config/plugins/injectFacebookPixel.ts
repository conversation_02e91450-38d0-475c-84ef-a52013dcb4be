import type { Plugin } from 'vite'

export function injectFacebookPixelSouthEastAsia(): Plugin {
  const pixelCode = `
    <!-- Meta Pixel Code -->
    <script>
      // 优化 Facebook Pixel 加载策略，减少对LCP的影响
      (function() {
        var loaded = false;
        var loadFacebookPixel = function() {
          if (loaded) return;
          loaded = true;

          var loadScript = function() {
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '1752769811937814');
            fbq('track', 'PageView');
          };

          if (window.requestIdleCallback) {
            requestIdleCallback(loadScript, { timeout: 5000 });
          } else {
            setTimeout(loadScript, 2000);
          }
        };

        // 监听用户交互，延迟加载广告脚本
        var events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        var onUserInteraction = function() {
          setTimeout(loadFacebookPixel, 1000); // 交互后1秒再加载
          events.forEach(function(event) {
            document.removeEventListener(event, onUserInteraction, true);
          });
        };

        events.forEach(function(event) {
          document.addEventListener(event, onUserInteraction, true);
        });

        // 备用方案：5秒后自动加载
        setTimeout(loadFacebookPixel, 5000);
      })();
    </script>
    <noscript><img height="1" width="1" style="display:none"
      src="https://www.facebook.com/tr?id=962852842668557&ev=PageView&noscript=1"
    /></noscript>
    <!-- End Meta Pixel Code -->
  `.trim()

  return {
    name: 'vite-plugin-inject-facebook-pixel',
    transformIndexHtml(html) {
      return html.replace('</head>', `${pixelCode}\n</head>`)
    }
  }
}

export function injectFacebookPixelEuropeAndAmerica(): Plugin {
  const pixelCode = `
    <!-- Meta Pixel Code -->
    <script>
      // 优化 Facebook Pixel 加载策略，减少对LCP的影响
      (function() {
        var loaded = false;
        var loadFacebookPixel = function() {
          if (loaded) return;
          loaded = true;

          var loadScript = function() {
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '1752769811937814');
            fbq('track', 'PageView');
          };

          if (window.requestIdleCallback) {
            requestIdleCallback(loadScript, { timeout: 5000 });
          } else {
            setTimeout(loadScript, 2000);
          }
        };

        // 监听用户交互，延迟加载广告脚本
        var events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        var onUserInteraction = function() {
          setTimeout(loadFacebookPixel, 1000); // 交互后1秒再加载
          events.forEach(function(event) {
            document.removeEventListener(event, onUserInteraction, true);
          });
        };

        events.forEach(function(event) {
          document.addEventListener(event, onUserInteraction, true);
        });

        // 备用方案：5秒后自动加载
        setTimeout(loadFacebookPixel, 5000);
      })();
    </script>
    <noscript><img height="1" width="1" style="display:none"
      src="https://www.facebook.com/tr?id=962852842668557&ev=PageView&noscript=1"
    /></noscript>
    <!-- End Meta Pixel Code -->
  `.trim()

  return {
    name: 'vite-plugin-inject-facebook-pixel',
    transformIndexHtml(html) {
      return html.replace('</head>', `${pixelCode}\n</head>`)
    }
  }
}

export function injectFacebookTest(): Plugin {
  const pixelCode = `
    <!-- Meta Pixel Code -->
    <script>
      // 优化 Facebook Pixel 加载策略，减少对LCP的影响
      (function() {
        var loaded = false;
        var loadFacebookPixel = function() {
          if (loaded) return;
          loaded = true;

          var loadScript = function() {
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '1283954289296073');
            fbq('track', 'PageView');
          };

          if (window.requestIdleCallback) {
            requestIdleCallback(loadScript, { timeout: 5000 });
          } else {
            setTimeout(loadScript, 2000);
          }
        };

        // 监听用户交互，延迟加载广告脚本
        var events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        var onUserInteraction = function() {
          setTimeout(loadFacebookPixel, 1000); // 交互后1秒再加载
          events.forEach(function(event) {
            document.removeEventListener(event, onUserInteraction, true);
          });
        };

        events.forEach(function(event) {
          document.addEventListener(event, onUserInteraction, true);
        });

        // 备用方案：5秒后自动加载
        setTimeout(loadFacebookPixel, 5000);
      })();
    </script>
    <noscript><img height="1" width="1" style="display:none"
      src="https://www.facebook.com/tr?id=1283954289296073&ev=PageView&noscript=1"
    /></noscript>
    <!-- End Meta Pixel Code -->
  `.trim()

  return {
    name: 'vite-plugin-inject-facebook-pixel',
    transformIndexHtml(html) {
      return html.replace('</head>', `${pixelCode}\n</head>`)
    }
  }
}
