import { Plugin } from 'vite'

/**
 * Vite 插件：注入 Ahrefs Analytics 脚本
 */
export function injectAhrefsAnalytics(): Plugin {
  return {
    name: 'inject-ahrefs-analytics',
    transformIndexHtml(html) {
      // 优化 Ahrefs Analytics 加载策略，减少对LCP的影响
      const ahrefsScript = `
  <!-- Ahrefs Analytics -->
  <script>
    // 优化 Ahrefs Analytics 加载策略
    (function() {
      var loaded = false;
      var loadAhrefsAnalytics = function() {
        if (loaded) return;
        loaded = true;

        var loadScript = function() {
          var ahrefs_analytics_script = document.createElement('script');
          ahrefs_analytics_script.async = true;
          ahrefs_analytics_script.src = 'https://analytics.ahrefs.com/analytics.js';
          ahrefs_analytics_script.setAttribute('data-key', 'oirXsj/lzpGcx5E/hUma6w');
          document.getElementsByTagName('head')[0].appendChild(ahrefs_analytics_script);
        };

        if (window.requestIdleCallback) {
          requestIdleCallback(loadScript, { timeout: 7000 });
        } else {
          setTimeout(loadScript, 4000);
        }
      };

      // 监听用户交互，延迟加载广告脚本
      var events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
      var onUserInteraction = function() {
        setTimeout(loadAhrefsAnalytics, 2000); // 交互后2秒再加载
        events.forEach(function(event) {
          document.removeEventListener(event, onUserInteraction, true);
        });
      };

      events.forEach(function(event) {
        document.addEventListener(event, onUserInteraction, true);
      });

      // 备用方案：7秒后自动加载
      setTimeout(loadAhrefsAnalytics, 7000);
    })();
  </script>`

      // 在 </head> 标签前插入脚本
      return html.replace('</head>', `${ahrefsScript}\n</head>`)
    }
  }
}
