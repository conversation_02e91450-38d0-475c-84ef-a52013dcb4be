/**
 * 故事内容增强器
 * 将缓存的故事内容与用户状态结合
 */

import type { Story } from './useApi'

export interface StoryContent {
  id: string
  title: string
  description: string
  cover_url: string
  preview_url: string
  carousel_image_url?: string[] // 添加轮播图片字段
  category: string
  tags: string[]
  created_at: string
  updated_at: string
  // 添加 Story 接口中的必需字段
  status: 'preparing' | 'normal' | 'admin_only'
}

export interface EnhancedStory extends Story {
  // 用户相关的增强字段
  is_liked?: boolean
  is_bookmarked?: boolean
  user_rating?: number
  view_count?: number
  bookmark_count?: number
}

export interface StoriesContentResponse {
  stories: StoryContent[]
  total: number
  page: number
  page_size: number
  source: string
  timestamp: number
  error?: string
}

export const useStoriesEnhancer = () => {
  /**
   * 获取缓存的故事内容
   */
  const getCachedStoriesContent = async (
    params: {
      sort?: string
      category?: string
    } = {}
  ): Promise<StoriesContentResponse> => {
    try {
      const queryParams = new URLSearchParams()
      if (params.sort) queryParams.set('sort', params.sort)
      if (params.category) queryParams.set('category', params.category)

      const url = `/api/stories-content${
        queryParams.toString() ? '?' + queryParams.toString() : ''
      }`

      return await $fetch<StoriesContentResponse>(url)
    } catch (error: any) {
      console.error('Failed to get cached stories content:', error)
      return {
        stories: [],
        total: 0,
        page: 1,
        page_size: 100,
        source: 'error',
        timestamp: Date.now(),
        error: error.message
      }
    }
  }

  /**
   * 获取用户对故事的状态数据
   * 直接返回空对象，不调用任何API
   */
  const getUserStoriesState = async (
    _storyIds: string[],
    _userToken: string
  ): Promise<
    Record<
      string,
      {
        is_liked?: boolean
        is_bookmarked?: boolean
        user_rating?: number
      }
    >
  > => {
    // 直接返回空对象，不调用API
    return {}
  }

  /**
   * 将故事内容与用户状态结合
   */
  const enrichStoriesWithUserState = async (
    storiesContent: StoriesContentResponse,
    userToken?: string
  ): Promise<{
    stories: EnhancedStory[]
    total: number
    page: number
    page_size: number
    source: string
    timestamp: number
  }> => {
    const { stories } = storiesContent

    // 如果没有用户token，直接返回基础内容
    if (!userToken || !stories.length) {
      return {
        ...storiesContent,
        stories: stories.map((story) => ({
          ...story,
          is_liked: false,
          is_bookmarked: false,
          user_rating: undefined
        }))
      }
    }

    try {
      // 获取用户对这些故事的状态
      const storyIds = stories.map((story) => story.id)
      const userStates = await getUserStoriesState(storyIds, userToken)

      // 合并内容和状态
      const enhancedStories: EnhancedStory[] = stories.map((story) => ({
        ...story,
        ...userStates[story.id],
        // 设置默认值
        is_liked: userStates[story.id]?.is_liked || false,
        is_bookmarked: userStates[story.id]?.is_bookmarked || false,
        user_rating: userStates[story.id]?.user_rating || undefined
      }))

      return {
        ...storiesContent,
        stories: enhancedStories,
        source: `${storiesContent.source}+user-state`
      }
    } catch (error) {
      console.error('Failed to enrich stories with user state:', error)

      // 出错时返回基础内容
      return {
        ...storiesContent,
        stories: stories.map((story) => ({
          ...story,
          is_liked: false,
          is_bookmarked: false,
          user_rating: undefined
        }))
      }
    }
  }

  /**
   * 获取完整的故事数据（内容 + 用户状态）
   */
  const getEnhancedStories = async (
    params: {
      sort?: string
      page?: number
      page_size?: number
      category?: string
      userToken?: string
    } = {}
  ): Promise<{
    stories: EnhancedStory[]
    total: number
    page: number
    page_size: number
    source: string
    timestamp: number
  }> => {
    // 1. 获取缓存的故事内容
    const storiesContent = await getCachedStoriesContent({
      sort: params.sort,
      category: params.category
    })

    // 2. 与用户状态结合
    return await enrichStoriesWithUserState(storiesContent, params.userToken)
  }

  return {
    getCachedStoriesContent,
    getUserStoriesState,
    enrichStoriesWithUserState,
    getEnhancedStories
  }
}
