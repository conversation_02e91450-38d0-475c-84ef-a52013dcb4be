// Nuxt 3 自动导入，无需手动导入

export interface PageSeoConfig {
  pageTitle: string
  customDescription?: string
  useAutoDescription?: boolean
}

export const usePageSeo = (config: PageSeoConfig) => {
  const { brandingConfig } = useBranding()
  const brand = brandingConfig.value

  // 获取页面描述
  const getPageDescription = (): string => {
    // 优先使用自定义描述
    if (config.customDescription) {
      return config.customDescription
    }

    // 如果没有启用自动描述，返回默认描述
    if (!config.useAutoDescription) {
      return `${config.pageTitle} - ${brand.websiteTitle}`
    }

    // 自动描述的后备方案（主要用于非法律页面）
    return `${config.pageTitle} - ${brand.websiteTitle}`
  }

  // 设置SEO meta标签
  const setupSeo = () => {
    const title = `${config.pageTitle} - ${brand.websiteTitle}`
    const description = getPageDescription()

    useSeoMeta({
      title,
      description,
      ogTitle: title,
      ogDescription: description,
      ogType: 'website',
      ogUrl: brand.siteUrl,
      ogImage: brand.ogImageUrl,
      twitterCard: 'summary_large_image',
      twitterTitle: title,
      twitterDescription: description,
      twitterImage: brand.ogImageUrl
    })
  }

  // 设置SEO（在服务端和客户端都会执行）
  setupSeo()

  return {
    setupSeo,
    getPageDescription
  }
}
