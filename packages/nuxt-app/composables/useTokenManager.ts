/**
 * 统一Token生命周期管理
 * 负责token的获取、验证、刷新和用户类型检测
 */

export type UserType = 'guest' | 'user' | 'admin'

export interface TokenStatus {
  status: 'none' | 'valid' | 'expired' | 'invalid'
  token: string | null
  userType: UserType | null
  needsRefresh: boolean
}

export interface TokenUserInfo {
  uuid: string
  id?: string
  name?: string
  email?: string
  avatar_url?: string
  coins?: number
  role?: string
  gender?: string
  is_guest?: boolean
}

export const useTokenManager = () => {
  const config = useRuntimeConfig()
  const baseURL = config.public.apiBase

  // Token存储键
  const TOKEN_KEY = 'token'
  const REFRESH_TOKEN_KEY = 'refreshToken'
  const USER_INFO_KEY = 'userInfo'
  const USER_TYPE_KEY = 'userType'
  const TOKEN_TIMESTAMP_KEY = 'tokenTimestamp'

  /**
   * 获取现有token（按优先级顺序）
   */
  const getExistingToken = (): string | null => {
    // 优先级：localStorage > cookie > SSR cookie
    if (import.meta.client) {
      const localToken = localStorage.getItem(TOKEN_KEY)
      if (localToken) return localToken
    }

    // 检查常规cookie（仅在有效上下文中）
    try {
      const tokenCookie = useCookie<string | null>('auth_token', {
        default: () => null
      })
      if (tokenCookie.value) {
        // 将cookie token同步到localStorage（仅在客户端）
        if (import.meta.client) {
          localStorage.setItem(TOKEN_KEY, tokenCookie.value)
          localStorage.setItem(TOKEN_TIMESTAMP_KEY, Date.now().toString())
        }
        return tokenCookie.value
      }
    } catch {
      // 在无效上下文中忽略cookie操作
    }

    // 检查SSR设置的cookie（向后兼容）
    if (import.meta.client) {
      const ssrTokenCookie = useCookie('ssr_access_token')
      if (ssrTokenCookie.value) {
        // 将SSR token同步到localStorage
        localStorage.setItem(TOKEN_KEY, ssrTokenCookie.value)
        localStorage.setItem(TOKEN_TIMESTAMP_KEY, Date.now().toString())
        // 清除cookie避免重复使用
        const token = ssrTokenCookie.value
        ssrTokenCookie.value = null
        return token
      }
    }

    return null
  }

  /**
   * 获取存储的用户信息
   */
  const getStoredUserInfo = (): TokenUserInfo | null => {
    // 优先从localStorage读取
    if (import.meta.client) {
      const userInfoStr = localStorage.getItem(USER_INFO_KEY)
      if (userInfoStr) {
        try {
          return JSON.parse(userInfoStr)
        } catch (error) {
          console.error('Failed to parse stored userInfo:', error)
          localStorage.removeItem(USER_INFO_KEY)
        }
      }
    }

    // 从cookie读取（仅在有效上下文中）
    try {
      const userInfoCookie = useCookie<string | null>('auth_user_info', {
        default: () => null
      })

      if (userInfoCookie.value) {
        try {
          const userInfo = JSON.parse(userInfoCookie.value)
          // 同步到localStorage（仅在客户端）
          if (import.meta.client) {
            localStorage.setItem(USER_INFO_KEY, userInfoCookie.value)
          }
          return userInfo
        } catch {
          userInfoCookie.value = null
        }
      }
    } catch {
      // 在无效上下文中忽略cookie操作
    }

    return null
  }

  /**
   * 检测用户类型
   */
  const detectUserType = (userInfo: TokenUserInfo | null): UserType => {
    if (!userInfo) return 'guest'

    // 检查是否是管理员
    if (userInfo.role === 'admin' || userInfo.role === 'super_admin') {
      return 'admin'
    }

    // 检查是否是游客
    if (userInfo.is_guest === true || !userInfo.email) {
      return 'guest'
    }

    // 默认为普通用户
    return 'user'
  }

  /**
   * 验证token有效性
   */
  const validateToken = async (
    token: string
  ): Promise<{
    valid: boolean
    userInfo: TokenUserInfo | null
    userType: UserType
  }> => {
    try {
      const response = await $fetch<{
        code: string
        message: string
        data: { user: TokenUserInfo }
      }>(`${baseURL}/api/v1/user.whoami`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.code === '0' && response.data?.user) {
        const userInfo = response.data.user
        const userType = detectUserType(userInfo)

        // 更新存储的用户信息和类型
        if (import.meta.client) {
          localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
          localStorage.setItem(USER_TYPE_KEY, userType)
        }

        return {
          valid: true,
          userInfo,
          userType
        }
      } else {
        return {
          valid: false,
          userInfo: null,
          userType: 'guest'
        }
      }
    } catch (error) {
      console.error('Token validation failed:', error)
      return {
        valid: false,
        userInfo: null,
        userType: 'guest'
      }
    }
  }

  /**
   * 检查token状态
   */
  const checkTokenStatus = async (): Promise<TokenStatus> => {
    const token = getExistingToken()

    if (!token) {
      return {
        status: 'none',
        token: null,
        userType: null,
        needsRefresh: false
      }
    }

    // 在服务端，跳过时间戳检查，直接验证 token
    if (import.meta.server) {
      const isValid = await validateToken(token)
      const userInfo = getStoredUserInfo()
      const userType = detectUserType(userInfo)

      return {
        status: isValid ? 'valid' : 'expired',
        token: isValid ? token : null,
        userType: isValid ? userType : null,
        needsRefresh: !isValid
      }
    }

    // 客户端：检查token时间戳，如果太旧则需要验证
    const timestamp = localStorage.getItem(TOKEN_TIMESTAMP_KEY)
    const tokenAge = timestamp ? Date.now() - parseInt(timestamp) : Infinity
    const maxAge = 30 * 60 * 1000 // 30分钟

    if (tokenAge < maxAge) {
      // Token较新，使用缓存的用户类型
      const cachedUserType = (localStorage.getItem(USER_TYPE_KEY) as UserType) || 'guest'
      return {
        status: 'valid',
        token,
        userType: cachedUserType,
        needsRefresh: false
      }
    }

    // Token较旧，需要验证
    const validation = await validateToken(token)

    if (validation.valid) {
      // 更新时间戳
      if (import.meta.client) {
        localStorage.setItem(TOKEN_TIMESTAMP_KEY, Date.now().toString())
      }

      return {
        status: 'valid',
        token,
        userType: validation.userType,
        needsRefresh: false
      }
    } else {
      return {
        status: 'expired',
        token,
        userType: 'guest',
        needsRefresh: true
      }
    }
  }

  /**
   * 游客注册
   */
  const registerGuest = async (): Promise<string | null> => {
    try {
      const deviceId = 'device_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now()

      const response = await $fetch<{
        code: string
        message: string
        data: {
          auth: { access_token: string; refresh_token: string }
          user: TokenUserInfo
        }
      }>(`${baseURL}/api/v1/user-guest.sign-up`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: {
          device_id: deviceId,
          gender: 'unknown'
        }
      })

      if (response.code === '0' && response.data?.auth?.access_token) {
        const { auth, user } = response.data

        // 存储token和用户信息
        setTokens(auth.access_token, auth.refresh_token)
        setUserInfo(user, 'guest')

        return auth.access_token
      } else {
        return null
      }
    } catch (error) {
      console.error('Guest registration failed:', error)
      return null
    }
  }

  /**
   * 刷新token或重新注册
   */
  const refreshOrReregister = async (): Promise<string | null> => {
    // 获取refresh token（优先localStorage，然后cookie）
    let refreshToken: string | null = null
    if (import.meta.client) {
      refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY)
    }

    if (!refreshToken) {
      try {
        const refreshTokenCookie = useCookie<string | null>('auth_refresh_token', {
          default: () => null
        })
        refreshToken = refreshTokenCookie.value
      } catch {
        // 在无效上下文中忽略cookie操作
      }
    }

    if (refreshToken) {
      // 尝试刷新token
      try {
        const response = await $fetch<{
          code: string
          data: { auth: { access_token: string; refresh_token: string } }
        }>(`${baseURL}/api/v1/auth.refresh`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: {
            refresh_token: refreshToken
          }
        })

        if (response.code === '0' && response.data?.auth?.access_token) {
          const { auth } = response.data

          if (import.meta.client) {
            localStorage.setItem(TOKEN_KEY, auth.access_token)
            localStorage.setItem(REFRESH_TOKEN_KEY, auth.refresh_token)
            localStorage.setItem(TOKEN_TIMESTAMP_KEY, Date.now().toString())
          }

          return auth.access_token
        }
      } catch (error) {
        console.error('Token refresh failed:', error)
        // 静默处理刷新失败
      }
    }

    // 刷新失败，清除旧数据并重新注册游客
    if (import.meta.client) {
      localStorage.removeItem(TOKEN_KEY)
      localStorage.removeItem(REFRESH_TOKEN_KEY)
      localStorage.removeItem(USER_INFO_KEY)
      localStorage.removeItem(USER_TYPE_KEY)
      localStorage.removeItem(TOKEN_TIMESTAMP_KEY)
    }

    return await registerGuest()
  }

  /**
   * 确保有效token（核心方法）
   */
  const ensureValidToken = async (): Promise<{
    token: string | null
    userType: UserType
    isNewToken: boolean
  }> => {
    const tokenStatus = await checkTokenStatus()

    switch (tokenStatus.status) {
      case 'valid':
        return {
          token: tokenStatus.token,
          userType: tokenStatus.userType!,
          isNewToken: false
        }

      case 'expired': {
        const refreshedToken = await refreshOrReregister()
        return {
          token: refreshedToken,
          userType: refreshedToken ? 'guest' : 'guest',
          isNewToken: true
        }
      }

      case 'none':
      default:
        return {
          token: await registerGuest(),
          userType: 'guest',
          isNewToken: true
        }
    }
  }

  /**
   * 设置tokens（用于登录成功后同步）
   */
  const setTokens = (accessToken: string, refreshToken: string) => {
    if (import.meta.client) {
      localStorage.setItem(TOKEN_KEY, accessToken)
      localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken)
      localStorage.setItem(TOKEN_TIMESTAMP_KEY, Date.now().toString())
    }

    // 尝试设置到cookie（仅在有效上下文中）
    try {
      const accessTokenCookie = useCookie<string | null>('auth_token', {
        default: () => null,
        maxAge: 60 * 60 * 24 * 7, // 7天
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax'
      })

      const refreshTokenCookie = useCookie<string | null>('auth_refresh_token', {
        default: () => null,
        maxAge: 60 * 60 * 24 * 30, // 30天
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax'
      })

      accessTokenCookie.value = accessToken
      refreshTokenCookie.value = refreshToken
    } catch {
      // 在无效上下文中忽略cookie操作，只使用localStorage
    }
  }

  /**
   * 设置用户信息（用于登录成功后同步）
   */
  const setUserInfo = (userInfo: any, userType: UserType) => {
    if (import.meta.client) {
      localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
      localStorage.setItem(USER_TYPE_KEY, userType)
    }

    // 尝试设置到cookie（仅在有效上下文中）
    try {
      const userInfoCookie = useCookie<string | null>('auth_user_info', {
        default: () => null,
        maxAge: 60 * 60 * 24 * 7, // 7天
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax'
      })

      const userTypeCookie = useCookie<string | null>('auth_user_type', {
        default: () => null,
        maxAge: 60 * 60 * 24 * 7, // 7天
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax'
      })

      userInfoCookie.value = JSON.stringify(userInfo)
      userTypeCookie.value = userType
    } catch {
      // 在无效上下文中忽略cookie操作
    }
  }

  /**
   * 清除所有认证数据
   */
  const clearAuth = () => {
    // 清除localStorage
    if (import.meta.client) {
      localStorage.removeItem(TOKEN_KEY)
      localStorage.removeItem(REFRESH_TOKEN_KEY)
      localStorage.removeItem(USER_INFO_KEY)
      localStorage.removeItem(USER_TYPE_KEY)
      localStorage.removeItem(TOKEN_TIMESTAMP_KEY)
    }

    // 尝试清除cookies（仅在有效上下文中）
    try {
      const accessTokenCookie = useCookie<string | null>('auth_token', {
        default: () => null
      })
      const refreshTokenCookie = useCookie<string | null>('auth_refresh_token', {
        default: () => null
      })
      const userInfoCookie = useCookie<string | null>('auth_user_info', {
        default: () => null
      })
      const userTypeCookie = useCookie<string | null>('auth_user_type', {
        default: () => null
      })

      accessTokenCookie.value = null
      refreshTokenCookie.value = null
      userInfoCookie.value = null
      userTypeCookie.value = null
    } catch {
      // 在无效上下文中忽略cookie操作
    }
  }

  return {
    // 核心方法
    ensureValidToken,
    checkTokenStatus,
    validateToken,

    // 辅助方法
    getExistingToken,
    getStoredUserInfo,
    detectUserType,
    registerGuest,
    setTokens,
    setUserInfo,
    clearAuth,

    // 常量
    TOKEN_KEY,
    USER_INFO_KEY,
    USER_TYPE_KEY
  }
}
