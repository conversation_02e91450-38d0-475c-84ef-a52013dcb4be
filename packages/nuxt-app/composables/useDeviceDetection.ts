import { ref, computed, onMounted, onUnmounted } from 'vue'
import isMobile from 'ismobilejs'

/**
 * 设备检测 Composable for Nuxt
 * 提供设备类型检测功能，包括PC/移动设备识别和开发者工具模拟设备检测
 * 支持SSR和CSR环境
 */
export function useDeviceDetection() {
  // 设备类型状态
  const isDesktop = ref(false)
  const isMobileDevice = ref(false)
  const isTablet = ref(false)
  const isPhone = ref(false)
  const isEmulated = ref(false) // 是否为开发者工具模拟的移动设备
  const screenWidth = ref(0)
  const screenHeight = ref(0)

  // 检测设备类型
  const detectDeviceType = () => {
    // 只在客户端执行
    if (import.meta.client) {
      const mobileInfo = isMobile()

      // 更新屏幕尺寸
      screenWidth.value = window.innerWidth
      screenHeight.value = window.innerHeight

      // 检测是否为移动设备
      isMobileDevice.value = mobileInfo.any
      isTablet.value = mobileInfo.tablet
      isPhone.value = mobileInfo.phone

      // 检测是否为桌面设备
      // 条件：非移动设备 OR 屏幕宽度大于1024px
      isDesktop.value = !isMobileDevice.value || window.innerWidth >= 1024

      // 检测是否为开发者工具模拟的移动设备
      isEmulated.value = detectEmulatedDevice()
    } else {
      // SSR环境默认值
      isDesktop.value = true
      isMobileDevice.value = false
      isTablet.value = false
      isPhone.value = false
      isEmulated.value = false
      screenWidth.value = 1920
      screenHeight.value = 1080
    }
  }

  // 检测是否为开发者工具模拟的移动设备
  const detectEmulatedDevice = () => {
    if (!import.meta.client) return false

    const mobileInfo = isMobile()

    // 如果userAgent表明是移动设备，但屏幕宽度大于768px，可能是开发者工具模拟的
    if (mobileInfo.any && window.innerWidth >= 768) {
      return true
    }

    // 检查是否存在常见的开发者工具模拟标志
    const ua = navigator.userAgent.toLowerCase()
    return ua.includes('emulator') || ua.includes('responsive')
  }

  // 计算最终的设备类型
  const deviceType = computed(() => {
    if (isEmulated.value) return 'mobile' // 如果是模拟的移动设备，按移动设备处理
    if (isTablet.value) return 'tablet'
    if (isPhone.value) return 'mobile'
    return 'desktop'
  })

  // 是否应该使用PC布局
  const shouldUsePCLayout = computed(() => {
    return (
      deviceType.value === 'desktop' || (deviceType.value === 'tablet' && screenWidth.value >= 1024)
    )
  })

  // 是否应该使用移动端布局
  const shouldUseMobileLayout = computed(() => {
    return !shouldUsePCLayout.value
  })

  // 响应式断点
  const breakpoints = computed(() => ({
    xs: screenWidth.value < 576,
    sm: screenWidth.value >= 576 && screenWidth.value < 768,
    md: screenWidth.value >= 768 && screenWidth.value < 992,
    lg: screenWidth.value >= 992 && screenWidth.value < 1200,
    xl: screenWidth.value >= 1200,
    mobile: screenWidth.value < 768,
    tablet: screenWidth.value >= 768 && screenWidth.value < 1024,
    desktop: screenWidth.value >= 1024
  }))

  // 监听窗口大小变化
  const handleResize = () => {
    detectDeviceType()
  }

  // 初始化设备检测
  const initDeviceDetection = () => {
    detectDeviceType()

    if (import.meta.client) {
      // 添加设备类型到body class
      document.body.classList.toggle('is-desktop', isDesktop.value)
      document.body.classList.toggle('is-mobile', isMobileDevice.value)
      document.body.classList.toggle('is-tablet', isTablet.value)
      document.body.classList.toggle('is-phone', isPhone.value)
      document.body.classList.toggle('is-emulated', isEmulated.value)

      // 添加设备类型属性
      document.body.setAttribute('data-device-type', deviceType.value)
      document.body.setAttribute('data-screen-width', screenWidth.value.toString())
    }
  }

  onMounted(() => {
    initDeviceDetection()
    if (import.meta.client) {
      window.addEventListener('resize', handleResize)
    }
  })

  onUnmounted(() => {
    if (import.meta.client) {
      window.removeEventListener('resize', handleResize)
    }
  })

  return {
    // 基础设备检测
    isDesktop,
    isMobileDevice,
    isTablet,
    isPhone,
    isEmulated,
    deviceType,

    // 屏幕尺寸
    screenWidth,
    screenHeight,

    // 布局决策
    shouldUsePCLayout,
    shouldUseMobileLayout,

    // 响应式断点
    breakpoints,

    // 方法
    detectDeviceType,
    initDeviceDetection
  }
}

// 服务端渲染时的设备检测
export function detectDeviceFromUserAgent(userAgent?: string): {
  isDesktop: boolean
  isMobileDevice: boolean
  isTablet: boolean
  isPhone: boolean
  deviceType: 'desktop' | 'mobile' | 'tablet'
} {
  if (!userAgent) {
    return {
      isDesktop: true,
      isMobileDevice: false,
      isTablet: false,
      isPhone: false,
      deviceType: 'desktop'
    }
  }

  const mobileInfo = isMobile(userAgent)
  const isMobileDevice = mobileInfo.any
  const isTablet = mobileInfo.tablet
  const isPhone = mobileInfo.phone
  const isDesktop = !isMobileDevice

  let deviceType: 'desktop' | 'mobile' | 'tablet' = 'desktop'
  if (isTablet) deviceType = 'tablet'
  else if (isPhone) deviceType = 'mobile'

  return {
    isDesktop,
    isMobileDevice,
    isTablet,
    isPhone,
    deviceType
  }
}
