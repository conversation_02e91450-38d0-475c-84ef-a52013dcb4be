/**
 * 故事数据管理 Composable
 */

import type { Story, Category } from './useApi'

export const useStories = () => {
  const api = useApi()

  // 响应式状态
  const stories = ref<Story[]>([])
  const categories = ref<Category[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 筛选状态
  const selectedSort = ref('popular')
  const selectedCategories = ref<string[]>([])

  // 分页状态（为后续分页做准备）
  const currentPage = ref(1)
  const pageSize = ref(12)
  const total = ref(0)

  /**
   * 获取故事列表
   */
  const fetchStories = async (refresh = false) => {
    if (refresh) {
      currentPage.value = 1
    }

    loading.value = true
    error.value = null

    try {
      const response = await api.fetchStories({
        category_ids: selectedCategories.value.length > 0 ? selectedCategories.value : undefined,
        sort: selectedSort.value,
        page: currentPage.value,
        page_size: pageSize.value
      })

      if (response.code === '0') {
        if (refresh || currentPage.value === 1) {
          stories.value = response.data.stories || []
        } else {
          // 分页加载时追加数据
          stories.value.push(...(response.data.stories || []))
        }

        // 如果API返回总数，更新total
        if ('total' in response.data && typeof response.data.total === 'number') {
          total.value = response.data.total
        }
      } else {
        error.value = response.message
        console.error('获取故事列表失败:', response.message)
      }
    } catch (err: unknown) {
      error.value = err instanceof Error ? err.message : '网络错误'
      console.error('获取故事列表异常:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取分类列表
   */
  const fetchCategories = async () => {
    try {
      const response = await api.fetchCategories()

      if (response.code === '0') {
        categories.value = response.data.category || []
      } else {
        console.error('获取分类列表失败:', response.message)
      }
    } catch (err: unknown) {
      console.error('获取分类列表异常:', err)
    }
  }

  /**
   * 更新排序方式
   */
  const updateSort = async (sort: string) => {
    selectedSort.value = sort
    await fetchStories(true)
  }

  /**
   * 更新分类筛选
   */
  const updateCategories = async (categoryIds: string[]) => {
    selectedCategories.value = categoryIds
    await fetchStories(true)
  }

  /**
   * 切换分类选择
   */
  const toggleCategory = async (categoryId: string) => {
    const index = selectedCategories.value.indexOf(categoryId)
    if (index === -1) {
      selectedCategories.value.push(categoryId)
    } else {
      selectedCategories.value.splice(index, 1)
    }
    await fetchStories(true)
  }

  /**
   * 加载更多（分页）
   */
  const loadMore = async () => {
    if (loading.value) return

    currentPage.value += 1
    await fetchStories(false)
  }

  /**
   * 刷新数据
   */
  const refresh = async () => {
    await fetchStories(true)
  }

  /**
   * 获取故事详情
   */
  const getStoryDetail = async (storyId: string) => {
    try {
      const response = await api.fetchStoryDetail(storyId)

      if (response.code === '0') {
        return response.data.story
      } else {
        throw new Error(response.message)
      }
    } catch (err: unknown) {
      console.error('获取故事详情失败:', err)
      throw err
    }
  }

  /**
   * 切换收藏状态
   */
  const toggleFavorite = async (storyId: string, currentStatus: boolean) => {
    try {
      const response = currentStatus
        ? await api.removeFavorite(storyId)
        : await api.addFavorite(storyId)

      if (response.code === '0') {
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (err: unknown) {
      console.error('切换收藏状态失败:', err)
      throw err
    }
  }

  /**
   * 初始化数据
   */
  const initialize = async () => {
    await Promise.all([fetchCategories(), fetchStories(true)])
  }

  // 计算属性
  const hasMore = computed(() => {
    if (total.value === 0) return false
    return stories.value.length < total.value
  })

  const filteredStories = computed(() => {
    // 如果需要前端额外筛选，可以在这里实现
    return stories.value
  })

  // 可用的标签（从分类中提取）
  const availableTags = computed(() => {
    const tags: { id: string; name: string }[] = []

    categories.value.forEach((category) => {
      if (category.subcategories) {
        category.subcategories.forEach((sub) => {
          tags.push({
            id: sub.id,
            name: sub.name
          })
        })
      } else {
        tags.push({
          id: category.id,
          name: category.name
        })
      }
    })

    return tags
  })

  return {
    // 状态
    stories: readonly(stories),
    categories: readonly(categories),
    loading: readonly(loading),
    error: readonly(error),

    // 筛选状态
    selectedSort: readonly(selectedSort),
    selectedCategories: readonly(selectedCategories),

    // 分页状态
    currentPage: readonly(currentPage),
    pageSize: readonly(pageSize),
    total: readonly(total),

    // 计算属性
    hasMore,
    filteredStories,
    availableTags,

    // 方法
    fetchStories,
    fetchCategories,
    getStoryDetail,
    toggleFavorite,
    updateSort,
    updateCategories,
    toggleCategory,
    loadMore,
    refresh,
    initialize
  }
}
