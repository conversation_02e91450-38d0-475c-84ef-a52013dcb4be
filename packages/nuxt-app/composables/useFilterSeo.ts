export function useFilterSeo() {
  const { brandingConfig } = useBranding()

  // 生成标签页面的title
  const generateTagTitle = (selectedTags: string[], tagNames: string[]) => {
    if (tagNames.length === 0) return brandingConfig.value.websiteTitle

    // 单个标签：Most Popular Idol Otome Game - ReelPlay AI
    // 多个标签：Most Popular Idol Party Otome Game - ReelPlay AI
    const tagsText = tagNames
      .map((name) => name.charAt(0).toUpperCase() + name.slice(1).toLowerCase())
      .join(' ')

    return `${tagsText} Otome Game - ${brandingConfig.value.websiteTitle}`
  }

  // 生成标签页面的description
  const generateTagDescription = (
    selectedTags: string[],
    tagNames: string[],
    sortType: string = 'popular',
    firstStoryDescription?: string
  ) => {
    if (tagNames.length === 0) {
      // 无标签时的排序描述
      let sortPrefix = ''
      switch (sortType) {
        case 'newest':
          sortPrefix = 'Newest '
          break
        case 'popular':
          sortPrefix = 'Most Popular '
          break
        default:
          sortPrefix = 'The Best Fun '
      }

      const baseDesc = `${sortPrefix}Otome Game`
      const storyDesc = firstStoryDescription ? ` ${firstStoryDescription}` : ''
      const fullDesc = `The Best Fun ${baseDesc}${storyDesc}`

      return fullDesc.length > 100 ? fullDesc.substring(0, 100) : fullDesc
    }

    // 有标签时的描述：The Best Fun + 排序前缀 + 标签名称 + Otome Game + 第一个故事描述
    let sortPrefix = ''
    switch (sortType) {
      case 'newest':
        sortPrefix = 'Newest '
        break
      case 'popular':
        sortPrefix = 'Most Popular '
        break
      default:
        sortPrefix = ''
    }

    const tagsText = tagNames
      .map((name) => name.charAt(0).toUpperCase() + name.slice(1).toLowerCase())
      .join(' ')

    const baseDesc = `The Best Fun ${sortPrefix}${tagsText} Otome Game`
    const storyDesc = firstStoryDescription ? ` ${firstStoryDescription}` : ''
    const fullDesc = `${baseDesc}${storyDesc}`

    // 限制最大100个字符
    return fullDesc.length > 100 ? fullDesc.substring(0, 100) : fullDesc
  }

  // 生成完整的SEO配置
  const generateFilterSeo = (params: {
    selectedTags: string[]
    tagNames: string[]
    sortType?: string
    hasFilters: boolean
    firstStoryDescription?: string
  }) => {
    const {
      selectedTags,
      tagNames,
      sortType = 'popular',
      hasFilters,
      firstStoryDescription
    } = params

    // 如果没有任何筛选，使用默认首页SEO
    if (!hasFilters) {
      return {
        title: `Try Best Romance Visual Novels,Mobile Otome Games,Anime Dating Sims & pc Virtual Date Games for Free on ${brandingConfig.value.websiteTitle}`,
        description: `Spark your dream romance at ${brandingConfig.value.websiteTitle}! Play free Romance visual novels,Anime dating sims, Mobile Best otome games &Pc virtual date games. choose your path, and fall in love with every click!`,
        keywords: `otome games, dating sims, visual novels, virtual dating games, ${brandingConfig.value.websiteTitle}, free otome games, romance games, dating simulation, interactive romance, AI dating games`
      }
    }

    // 有筛选时的SEO
    const title = generateTagTitle(selectedTags, tagNames)
    const description = generateTagDescription(
      selectedTags,
      tagNames,
      sortType,
      firstStoryDescription
    )

    // 生成关键词
    const baseKeywords = `otome games, dating sims, visual novels, virtual dating games, ${brandingConfig.value.websiteTitle}`
    const tagKeywords = tagNames.map((name) => `${name.toLowerCase()} otome game`).join(', ')
    const sortKeywords = sortType === 'popular' ? 'popular otome games' : 'newest otome games'
    const keywords = `${baseKeywords}, ${sortKeywords}${tagKeywords ? ', ' + tagKeywords : ''}`

    return {
      title,
      description,
      keywords
    }
  }

  return {
    generateFilterSeo,
    generateTagTitle,
    generateTagDescription
  }
}
