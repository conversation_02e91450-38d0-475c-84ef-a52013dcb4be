import { detectProjectTypeWithConfig } from 'shared-payment'

/**
 * 获取支付配置，支持调试环境变量
 */
export function usePaymentConfig() {
  const config = useRuntimeConfig()
  
  // 从 runtime config 中获取支付提供商
  const paymentProvider = config.public.paymentProvider as string
  
  // 使用带配置的检测函数
  const projectType = detectProjectTypeWithConfig(paymentProvider)
  
  console.log('usePaymentConfig - 检测到的支付提供商:', paymentProvider)
  console.log('usePaymentConfig - 检测到的项目类型:', projectType)
  
  return {
    paymentProvider,
    projectType
  }
}