/**
 * 智能数据获取管理器
 * 根据用户类型和缓存状态智能获取数据，避免重复请求
 */

import type { UserType } from './useTokenManager'
import type { Story } from './useApi'

export interface DataContext {
  isSSR: boolean
  userType: UserType
  token: string | null
  isNewToken: boolean
}

export interface StoriesData {
  stories: Story[]
  source: 'ssr' | 'cache' | 'api'
  userType: UserType
  timestamp: number
}

export const useDataManager = () => {
  const config = useRuntimeConfig()
  const baseURL = config.public.apiBase
  const { ensureValidToken } = useTokenManager()

  // 缓存键
  const STORIES_CACHE_KEY = 'stories_cache'
  const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  /**
   * 获取缓存的故事数据
   */
  const getCachedStories = (userType: UserType): StoriesData | null => {
    if (!import.meta.client) return null

    try {
      const cacheKey = `${STORIES_CACHE_KEY}_${userType}`
      const cachedStr = localStorage.getItem(cacheKey)

      if (cachedStr) {
        const cached: StoriesData = JSON.parse(cachedStr)
        const isExpired = Date.now() - cached.timestamp > CACHE_DURATION

        if (!isExpired && cached.userType === userType) {
          return cached
        } else {
          // 缓存过期，清除
          localStorage.removeItem(cacheKey)
        }
      }
    } catch (error) {
      console.error('读取故事缓存失败:', error)
    }

    return null
  }

  /**
   * 缓存故事数据
   */
  const setCachedStories = (stories: Story[], userType: UserType, source: 'ssr' | 'api') => {
    if (!import.meta.client) return

    try {
      const cacheKey = `${STORIES_CACHE_KEY}_${userType}`
      const cacheData: StoriesData = {
        stories,
        source,
        userType,
        timestamp: Date.now()
      }

      localStorage.setItem(cacheKey, JSON.stringify(cacheData))
    } catch (error) {
      console.error('缓存故事数据失败:', error)
    }
  }

  /**
   * 从API获取故事数据
   */
  const fetchStoriesFromAPI = async (
    token: string,
    userType: UserType,
    params?: {
      category_ids?: string[]
      sort?: string
      page?: number
      page_size?: number
    }
  ): Promise<Story[]> => {
    try {
      const response = await $fetch<{
        code: string
        message: string
        data: { stories: Story[] }
      }>(`${baseURL}/api/v1/story.list`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: params || {}
      })

      if (response.code === '0' && response.data?.stories) {
        return response.data.stories
      } else {
        return []
      }
    } catch (error) {
      console.error('从API获取故事数据失败:', error)
      return []
    }
  }

  /**
   * 获取SSR数据
   */
  const getSSRStories = (): Story[] => {
    // 在客户端可以通过 nuxtApp 获取SSR数据
    if (import.meta.client) {
      const nuxtApp = useNuxtApp()
      return (nuxtApp.ssrContext?.seoStories as Story[]) || []
    }
    return []
  }

  /**
   * 智能获取故事数据（核心方法）
   */
  const getOptimalStories = async (params?: {
    category_ids?: string[]
    sort?: string
    page?: number
    page_size?: number
    forceRefresh?: boolean
  }): Promise<StoriesData> => {
    const { forceRefresh = false } = params || {}

    // 1. 确保有效token
    const { token, userType, isNewToken } = await ensureValidToken()

    if (!token) {
      return {
        stories: [],
        source: 'api',
        userType: 'guest',
        timestamp: Date.now()
      }
    }

    // 2. 如果不强制刷新，尝试使用缓存
    if (!forceRefresh) {
      const cached = getCachedStories(userType)
      if (cached) {
        return cached
      }
    }

    // 3. 检查是否可以使用SSR数据
    if (import.meta.client && userType === 'guest' && !isNewToken) {
      const ssrStories = getSSRStories()
      if (ssrStories.length > 0) {
        const ssrData: StoriesData = {
          stories: ssrStories,
          source: 'ssr',
          userType: 'guest',
          timestamp: Date.now()
        }

        // 缓存SSR数据
        setCachedStories(ssrStories, 'guest', 'ssr')
        return ssrData
      }
    }

    // 4. 从API获取新数据
    const stories = await fetchStoriesFromAPI(token, userType, params)

    // 5. 缓存新数据
    setCachedStories(stories, userType, 'api')

    return {
      stories,
      source: 'api',
      userType,
      timestamp: Date.now()
    }
  }

  /**
   * 根据用户类型获取个性化数据
   */
  const getPersonalizedStories = async (
    baseStories: Story[],
    userType: UserType,
    token: string
  ): Promise<Story[]> => {
    if (userType === 'guest') {
      // 游客直接返回基础数据
      return baseStories
    }

    try {
      if (userType === 'user') {
        // 普通用户：获取个人偏好和订阅状态
        const response = await $fetch<{
          code: string
          data: { stories: Story[] }
        }>(`${baseURL}/api/v1/story.list`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: {
            include_user_data: true // 包含用户相关数据
          }
        })

        if (response.code === '0' && response.data?.stories) {
          return response.data.stories
        }
      } else if (userType === 'admin') {
        // 管理员：获取包含管理员专属内容的完整数据
        const response = await $fetch<{
          code: string
          data: { stories: Story[] }
        }>(`${baseURL}/api/v1/story.list`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: {
            include_admin_content: true // 包含管理员内容
          }
        })

        if (response.code === '0' && response.data?.stories) {
          return response.data.stories
        }
      }
    } catch (error) {
      console.error('从API获取个性化故事数据失败:', error)
      // 静默处理错误
    }

    // 失败时返回基础数据
    return baseStories
  }

  /**
   * 清除所有缓存
   */
  const clearAllCache = () => {
    if (!import.meta.client) return

    const userTypes: UserType[] = ['guest', 'user', 'admin']
    userTypes.forEach((userType) => {
      const cacheKey = `${STORIES_CACHE_KEY}_${userType}`
      localStorage.removeItem(cacheKey)
    })
  }

  /**
   * 预加载数据（用于性能优化）
   */
  const preloadStories = async (userType: UserType) => {
    const cached = getCachedStories(userType)
    if (!cached) {
      // 后台预加载，不阻塞UI
      getOptimalStories().catch(() => {
        // 静默处理预加载错误
      })
    }
  }

  return {
    // 核心方法
    getOptimalStories,
    getPersonalizedStories,

    // 缓存管理
    getCachedStories,
    setCachedStories,
    clearAllCache,

    // 性能优化
    preloadStories,

    // 辅助方法
    fetchStoriesFromAPI,
    getSSRStories
  }
}
