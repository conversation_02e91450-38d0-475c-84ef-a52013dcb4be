import { computed } from 'vue'

export interface BrandingConfig {
  appName: string
  websiteTitle: string
  logoUrl: string
  faviconUrl: string
  cdnUrl: string
  staticUrl: string
  siteUrl: string
  description: string
  keywords: string
  ogImageUrl: string
  supportEmail: string
}

export const useBranding = () => {
  const config = useRuntimeConfig()

  const appName = computed(() => config.public.appName || 'ReelPlay')
  const websiteTitle = computed(() => config.public.websiteTitle || 'ReelPlay')

  const isReelPlay = computed(() => {
    const name = appName.value.toLowerCase()
    return name.includes('reel') || name === 'reelplay'
  })

  const isPlayShot = computed(() => {
    const name = appName.value.toLowerCase()
    return name.includes('play') || name === 'playshot'
  })

  const brandingConfig = computed<BrandingConfig>(() => {
    if (isReelPlay.value) {
      return {
        appName: 'ReelPlay',
        websiteTitle: 'ReelPlay',
        logoUrl:
          config.public.logoUrl ||
          `${
            (config.public.cdnUrl as string) || 'https://static.reelplay.ai'
          }/static/images/logo/reelplay_logo.png`,
        faviconUrl: `${
          (config.public.cdnUrl as string) || 'https://static.reelplay.ai'
        }/static/images/icon/playshot-icon.png`,
        cdnUrl: (config.public.cdnUrl as string) || 'https://static.reelplay.ai',
        staticUrl:
          (config.public.staticUrl as string) ||
          (config.public.cdnUrl as string) ||
          'https://static.reelplay.ai',
        siteUrl: 'https://reelplay.ai',
        description:
          'Experience immersive AI-powered interactive stories and characters with ReelPlay',
        keywords: 'AI stories, interactive fiction, AI characters, choose your adventure, ReelPlay',
        ogImageUrl: 'https://cdn.magiclight.ai/assets/playshot/og-image.jpg',
        supportEmail: config.public.supportEmail || '<EMAIL>'
      }
    } else {
      return {
        appName: 'PlayShot',
        websiteTitle: 'PlayShot',
        logoUrl:
          config.public.logoUrl ||
          `${
            (config.public.cdnUrl as string) || 'https://static.playshot.ai'
          }/static/images/logo/playshot_logo.png`,
        faviconUrl: `${
          (config.public.cdnUrl as string) || 'https://static.playshot.ai'
        }/static/images/icon/playshot.png`,
        cdnUrl: (config.public.cdnUrl as string) || 'https://static.playshot.ai',
        staticUrl:
          (config.public.staticUrl as string) ||
          (config.public.cdnUrl as string) ||
          'https://static.playshot.ai',
        siteUrl: 'https://playshot.ai',
        description:
          'Experience immersive AI-powered interactive stories and characters with PlayShot',
        keywords: 'AI stories, interactive fiction, AI characters, choose your adventure, PlayShot',
        ogImageUrl: `${
          (config.public.cdnUrl as string) || 'https://static.playshot.ai'
        }/static/images/og-image.jpg`,
        supportEmail: config.public.supportEmail || '<EMAIL>'
      }
    }
  })

  // Favicon 文件配置
  const faviconFile = computed(() => {
    return isReelPlay.value ? '/reelplay.svg' : '/playshot.svg'
  })

  // SEO Meta Tags
  const seoMeta = computed(() => {
    const brand = brandingConfig.value
    const title = `${brand.websiteTitle} - AI-Powered Interactive Stories`
    const favicon = faviconFile.value

    return {
      title,
      meta: [
        { name: 'description', content: brand.description },
        { name: 'keywords', content: brand.keywords },
        // Open Graph
        { property: 'og:title', content: title },
        { property: 'og:description', content: brand.description },
        { property: 'og:type', content: 'website' },
        { property: 'og:url', content: brand.siteUrl },
        { property: 'og:image', content: brand.ogImageUrl },
        { property: 'og:site_name', content: brand.websiteTitle },
        // Twitter Card
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:title', content: title },
        { name: 'twitter:description', content: brand.description },
        { name: 'twitter:image', content: brand.ogImageUrl },
        // Apple Web App
        { name: 'apple-mobile-web-app-title', content: brand.appName }
      ],
      link: [
        { rel: 'canonical', href: brand.siteUrl },
        // 简化 favicon 配置 - 使用 SVG
        { rel: 'icon', type: 'image/svg+xml', href: favicon },
        { rel: 'shortcut icon', href: favicon }
      ]
    }
  })

  return {
    appName,
    websiteTitle,
    isReelPlay,
    isPlayShot,
    brandingConfig,
    seoMeta
  }
}
