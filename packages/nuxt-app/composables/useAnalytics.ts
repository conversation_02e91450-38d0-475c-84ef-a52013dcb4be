/**
 * Google Analytics 配置和事件跟踪
 * 根据品牌动态配置不同的 GA ID
 */

export const useAnalytics = () => {
  const { brandingConfig } = useBranding()
  
  // 根据品牌获取 GA ID
  const getGAId = () => {
    const appName = brandingConfig.value.appName.toLowerCase()
    return appName.includes('reel') ? 'G-7J3CFG8D6T' : 'G-BCD38QPPKH'
  }

  // 页面浏览事件
  const trackPageView = (pagePath?: string) => {
    if (import.meta.client && window.gtag) {
      window.gtag('config', getGAId(), {
        page_path: pagePath || window.location.pathname
      })
    }
  }

  // 自定义事件跟踪
  const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
    if (import.meta.client && window.gtag) {
      window.gtag('event', eventName, {
        ...parameters,
        brand: brandingConfig.value.appName
      })
    }
  }

  // 故事相关事件
  const trackStoryEvent = (action: string, storyId: string, storyTitle?: string) => {
    trackEvent('story_interaction', {
      action,
      story_id: storyId,
      story_title: storyTitle
    })
  }

  // 用户行为事件
  const trackUserEvent = (action: string, details?: Record<string, any>) => {
    trackEvent('user_action', {
      action,
      ...details
    })
  }

  // 支付相关事件
  const trackPaymentEvent = (action: string, amount?: number, currency?: string) => {
    trackEvent('payment', {
      action,
      value: amount,
      currency: currency || 'USD'
    })
  }

  return {
    getGAId,
    trackPageView,
    trackEvent,
    trackStoryEvent,
    trackUserEvent,
    trackPaymentEvent
  }
}

// 类型声明
declare global {
  interface Window {
    gtag: (...args: any[]) => void
  }
}
