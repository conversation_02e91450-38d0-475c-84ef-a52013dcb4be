/**
 * 游戏结构化数据生成器
 * 为聊天页面生成 VideoGame 类型的 JSON-LD 结构化数据
 */

import type { Story, Actor } from './useApi'

export interface GameStructuredData {
  '@context': string
  '@type': string
  name: string
  url: string
  description: string
  applicationCategory: string
  operatingSystem: string
  genre: string[]
  author: {
    '@type': string
    name: string
  }
  image: string[]
  datePublished: string
  publisher: {
    '@type': string
    name: string
  }
  aggregateRating: {
    '@type': string
    ratingValue: string
    ratingCount: string
  }
  offers: {
    '@type': string
    price: string
    priceCurrency: string
    availability: string
    url: string
  }
}

export const useGameStructuredData = () => {
  const { brandingConfig } = useBranding()

  /**
   * 生成游戏结构化数据
   */
  const generateGameStructuredData = (
    storyDetail: Story | null,
    selectedActor: Actor | null,
    chatType: 'chat' | 'chat2' | 'chat3' | 'chat4',
    storyId: string,
    characterId: string
  ): GameStructuredData | null => {
    if (!storyDetail) return null

    const brand = brandingConfig.value
    const baseUrl = brand.siteUrl
    
    // 构建游戏名称
    const gameName = selectedActor?.name 
      ? `${storyDetail.title} - Chat with ${selectedActor.name}`
      : storyDetail.title

    // 构建游戏URL
    const gameUrl = `${baseUrl}/${chatType}/${storyId}/${characterId}`

    // 构建描述
    const description = selectedActor?.name
      ? `An immersive otome visual novel web game featuring ${selectedActor.name} from ${storyDetail.title}. ${storyDetail.description || 'Experience romance and story choices in this interactive dating simulation.'}`
      : `An immersive otome visual novel web game: ${storyDetail.title}. ${storyDetail.description || 'Experience romance and story choices in this interactive dating simulation.'}`

    // 构建图片数组
    const images: string[] = []
    if (selectedActor?.avatar_url) {
      images.push(selectedActor.avatar_url)
    }
    if (storyDetail.preview_url) {
      images.push(storyDetail.preview_url)
    }
    if (storyDetail.carousel_image_url && storyDetail.carousel_image_url.length > 0) {
      images.push(...storyDetail.carousel_image_url)
    }
    // 如果没有图片，使用默认 OG 图片
    if (images.length === 0) {
      images.push(brand.ogImageUrl)
    }

    // 构建游戏类型标签
    const genres = ['Visual Novel', 'Otome Game', 'Dating Sim']
    if (storyDetail.tags && storyDetail.tags.length > 0) {
      // 添加故事标签，但避免重复
      storyDetail.tags.forEach(tag => {
        const normalizedTag = tag.charAt(0).toUpperCase() + tag.slice(1).toLowerCase()
        if (!genres.includes(normalizedTag)) {
          genres.push(normalizedTag)
        }
      })
    }

    // 计算价格
    const price = storyDetail.coins ? (storyDetail.coins * 0.01).toFixed(2) : '0'

    // 生成发布日期（使用创建时间或默认日期）
    const datePublished = storyDetail.created_at 
      ? new Date(storyDetail.created_at).toISOString().split('T')[0]
      : '2025-06-15'

    // 生成评分（基于热度或默认值）
    const ratingValue = storyDetail.hot 
      ? Math.min(5.0, Math.max(3.0, storyDetail.hot / 20)).toFixed(1)
      : '4.7'
    
    const ratingCount = storyDetail.play_count?.toString() || '320'

    return {
      '@context': 'https://schema.org',
      '@type': 'VideoGame',
      name: gameName,
      url: gameUrl,
      description: description.substring(0, 300), // 限制描述长度
      applicationCategory: 'Game',
      operatingSystem: 'Web',
      genre: genres,
      author: {
        '@type': 'Organization',
        name: brand.websiteTitle
      },
      image: images,
      datePublished,
      publisher: {
        '@type': 'Organization',
        name: brand.websiteTitle
      },
      aggregateRating: {
        '@type': 'AggregateRating',
        ratingValue,
        ratingCount
      },
      offers: {
        '@type': 'Offer',
        price,
        priceCurrency: 'USD',
        availability: 'https://schema.org/InStock',
        url: gameUrl
      }
    }
  }

  /**
   * 设置结构化数据到页面头部
   */
  const setGameStructuredData = (structuredData: GameStructuredData | null) => {
    if (!structuredData) return

    useHead({
      script: [
        {
          type: 'application/ld+json',
          innerHTML: JSON.stringify(structuredData, null, 2)
        }
      ]
    })
  }

  return {
    generateGameStructuredData,
    setGameStructuredData
  }
}
