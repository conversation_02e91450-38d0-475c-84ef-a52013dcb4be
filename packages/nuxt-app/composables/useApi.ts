/**
 * API请求封装 - 基于Nuxt的$fetch
 */

export interface ApiResponse<T = any> {
  code: string
  message: string
  data: T
}

export interface Story {
  id: string
  title: string
  description: string
  preview_url: string
  bgm_url?: string
  actors?: Actor[]
  hot?: number
  categories?: string[]
  coins?: number
  is_fav?: boolean
  is_purchased?: boolean
  badge?: string
  preview_video_url?: string
  is_subscribed?: boolean
  status: 'preparing' | 'normal' | 'admin_only'
  is_active_skill?: boolean
  is_support_face_swap?: boolean
  version?: string
  author?: string
  tags?: string[]
  carousel_image_url?: string[]
  like_count?: number
  play_count?: number
  created_at?: string
}

export interface Actor {
  id: string
  name: string
  avatar_url: string
  description?: string
}

export interface Category {
  id: string
  name: string
  parent_id: string
  level: number
  subcategories?: SubCategory[]
}

export interface SubCategory {
  id: string
  name: string
  parent_id: string
  level: number
}

export interface StoriesResponse {
  stories: Story[]
}

export interface StoryCategoriesResponse {
  category: Category[]
}

// Checkin related interfaces
export interface CheckinResponse {
  isOk: boolean
  message?: string
  data: {
    is_sign_in: boolean
    sign_in_count: number
  }
}

export interface ClaimCheckinResponse {
  isOk: boolean
  message?: string
  data: {
    message: string
    user: {
      avatar_url: string
      coins: number
      email: string
      gender: 'male' | 'female' | 'unknown'
      name: string
      plan: 'free' | 'basic'
      role: 'guest' | 'normal'
      status: 'guest' | 'normal'
      uuid: string
    }
  }
}

// Tasks related interfaces
export interface TaskData {
  name: string
  count: number
  limit: number
  is_done: boolean
  description: string
  coins: number
}

export interface TasksResponse {
  code: string
  message: string
  data: {
    play_game_task: TaskData
    share_task: TaskData
    create_task: TaskData
    invite_task: TaskData
    [key: string]: TaskData
  }
}

export interface TaskItem {
  id: string
  name: string
  description: string
  count: number
  limit: number
  is_done: boolean
  type: 'play_game' | 'share' | 'create' | 'invite' | string
  reward: number
}

export interface CompleteTaskResponse {
  code: string
  message: string
  data: {
    user: {
      uuid: string
      name: string
      email: string
      avatar_url: string
      status: string
      plan: string
      gender: string
      coins: number
      role: string
    }
  }
}

export interface InviteCodeResponse {
  code: string
  message: string
  data: {
    exclusive_invite_code: string
  }
}

// System config interfaces
export interface CheckInCoinsPerDay {
  day_one: number
  day_two: number
  day_three: number
  day_four: number
  day_five: number
  day_six: number
  day_seven: number
}

export interface SysConfigListResponse {
  checkin: {
    check_in_coins_per_day: CheckInCoinsPerDay
  }
  user_hobby_collection: Record<string, object>
}

// Payment related interfaces
export interface PriceExtra {
  background_url: string
  discount_percent: number
}

export interface PriceItem {
  id: string
  name: string
  amount: number
  coins: number
  period: string
  extra: PriceExtra
}

export interface PriceListResponse {
  code: string
  message: string
  data: PriceItem[]
}

export interface CheckoutCreateParams {
  price_id: string
  success_url: string
  cancel_url: string
}

export interface CheckoutCreateResponse {
  code: string
  message: string
  data: {
    session_id: string
  }
}

// Chat history interfaces
export interface ChatHistoryResponse {
  isOk: boolean
  data: {
    history: Array<{
      id: string
      content: any
      type: string
      created_at: string
    }>
  }
}

/**
 * 创建API请求实例
 */
export const useApi = () => {
  const config = useRuntimeConfig()
  const baseURL = config.public.apiBase
  const { ensureValidToken } = useTokenManager()

  /**
   * 通用API请求方法
   */
  const apiRequest = async <T>(
    endpoint: string,
    options: {
      method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
      body?: Record<string, unknown> | FormData | string
      query?: Record<string, string | number | boolean>
      headers?: Record<string, string>
      requireAuth?: boolean
    } = {}
  ): Promise<ApiResponse<T>> => {
    const { method = 'GET', body, query, headers = {}, requireAuth = true } = options

    // 如果需要认证，确保已认证
    let authToken: string | null = null
    if (requireAuth) {
      const { token } = await ensureValidToken()

      if (!token) {
        return {
          code: 'AUTH_ERROR',
          message: 'Authentication failed',
          data: null as T
        }
      }
      authToken = token
    }

    try {
      // 合并认证头和自定义头
      const authHeaders: Record<string, string> = {}
      if (requireAuth && authToken) {
        authHeaders.Authorization = `Bearer ${authToken}`
      }

      const requestHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        ...authHeaders,
        ...headers
      }

      const response = await $fetch<ApiResponse<T>>(`${baseURL}${endpoint}`, {
        method,
        body,
        query,
        headers: requestHeaders
      })

      return response
    } catch (error: any) {
      console.error('API请求失败:', error)

      // 返回标准错误格式
      return {
        code: 'ERROR',
        message: error?.message || 'Network error',
        data: null as T
      }
    }
  }

  /**
   * 获取故事列表
   */
  const fetchStories = async (params?: {
    category_ids?: string[]
    sort?: string
    page?: number
    page_size?: number
  }) => {
    return apiRequest<StoriesResponse>('/api/v1/story.list', {
      method: 'POST',
      body: params
    })
  }

  /**
   * 获取故事分类
   */
  const fetchCategories = async () => {
    return apiRequest<StoryCategoriesResponse>('/api/v1/category.list', {
      method: 'GET'
    })
  }

  /**
   * 获取故事详情
   */
  const fetchStoryDetail = async (storyId: string) => {
    return apiRequest<{ story: Story; is_discount_user: boolean }>('/api/v1/story.get', {
      method: 'GET',
      query: { id: storyId },
      requireAuth: true // 使用现有的认证机制
    })
  }

  /**
   * 订阅故事
   */
  const subscribeStory = async (storyId: string, email?: string) => {
    return apiRequest<{ is_subscribed: boolean }>('/api/v1/user-subscribe.create', {
      method: 'POST',
      body: {
        story_id: storyId,
        email
      }
    })
  }

  /**
   * 取消订阅故事
   */
  const unsubscribeStory = async (storyId: string) => {
    return apiRequest<{ is_subscribed: boolean }>('/api/v1/user-subscribe.cancel', {
      method: 'POST',
      body: {
        story_id: storyId
      }
    })
  }

  /**
   * 添加收藏
   */
  const addFavorite = async (storyId: string) => {
    return apiRequest<{ is_favorited: boolean }>('/api/v1/favorite.create', {
      method: 'POST',
      body: {
        story_id: storyId
      }
    })
  }

  /**
   * 移除收藏
   */
  const removeFavorite = async (storyId: string) => {
    return apiRequest<{ is_favorited: boolean }>('/api/v1/favorite.delete', {
      method: 'POST',
      body: {
        story_id: storyId
      }
    })
  }

  /**
   * 获取用户游戏历史
   */
  const fetchUserPlayedStories = async () => {
    return apiRequest<{ stories: Story[] }>('/api/v1/game-history.list', {
      method: 'GET',
      requireAuth: true
    })
  }

  /**
   * 获取用户喜欢的故事
   */
  const fetchUserLikedStories = async () => {
    return apiRequest<{ stories: Story[] }>('/api/v1/favorite.list', {
      method: 'GET',
      requireAuth: true
    })
  }

  /**
   * 获取用户信息
   */
  const fetchUserInfo = async () => {
    return apiRequest<{
      user: {
        uuid: string
        name: string
        email: string
        avatar_url: string
        status: string
        plan: string
        coins: number
        role: string
        gender: string
        create_time: string
      }
    }>('/api/v1/user.whoami', {
      method: 'GET',
      requireAuth: true
    })
  }

  /**
   * 更新用户信息
   */
  const updateUserInfo = async (data: { name?: string; gender?: string; avatar_url?: string }) => {
    return apiRequest<{
      user: {
        uuid: string
        name: string
        email: string
        avatar_url: string
        status: string
        plan: string
        coins: number
        role: string
        gender: string
        create_time: string
      }
    }>('/api/v1/user-info.update', {
      method: 'POST',
      body: data,
      requireAuth: true
    })
  }

  /**
   * Get checkin info
   */
  const getCheckinInfo = async () => {
    return apiRequest<CheckinResponse['data']>('/api/v1/user-checkin.get', {
      method: 'GET'
    })
  }

  /**
   * Claim checkin reward
   */
  const claimCheckinReward = async () => {
    return apiRequest<ClaimCheckinResponse['data']>('/api/v1/user-checkin.create', {
      method: 'POST'
    })
  }

  /**
   * Get tasks list
   */
  const getTasksList = async () => {
    return apiRequest<TasksResponse['data']>('/api/v1/daily-tasks.list', {
      method: 'GET'
    })
  }

  /**
   * Complete play game task
   */
  const completePlayGameTask = async () => {
    return apiRequest<CompleteTaskResponse['data']>('/api/v1/daily-tasks.play-game', {
      method: 'POST'
    })
  }

  /**
   * Complete share game task
   */
  const completeShareGameTask = async () => {
    return apiRequest<CompleteTaskResponse['data']>('/api/v1/daily-tasks.share-game', {
      method: 'POST'
    })
  }

  /**
   * Complete create story task
   */
  const completeCreateStoryTask = async () => {
    return apiRequest<CompleteTaskResponse['data']>('/api/v1/daily-tasks.create-story', {
      method: 'POST'
    })
  }

  /**
   * Complete invite friend task
   */
  const completeInviteFriendTask = async () => {
    return apiRequest<CompleteTaskResponse['data']>('/api/v1/daily-tasks.invite-friend', {
      method: 'POST'
    })
  }

  /**
   * Get invite code
   */
  const getInviteCode = async () => {
    return apiRequest<InviteCodeResponse['data']>('/api/v1/daily-tasks.invite-code', {
      method: 'GET'
    })
  }

  /**
   * Get system config list
   */
  const getSysConfigList = async () => {
    return apiRequest<SysConfigListResponse>('/api/v1/sysconfig.list', {
      method: 'GET'
    })
  }

  /**
   * Get price list for payments
   */
  const getPriceList = async () => {
    return apiRequest<PriceListResponse['data']>('/api/v1/price.list', {
      method: 'GET'
    })
  }

  /**
   * Create checkout session for Stripe payment
   */
  const createCheckoutSession = async (params: CheckoutCreateParams) => {
    return apiRequest<CheckoutCreateResponse['data']>('/api/v1/checkout.create', {
      method: 'POST',
      body: params as Record<string, unknown>
    })
  }

  /**
   * Get user chat history
   */
  const getUserChatHistory = async (storyId: string, actorId: string, version?: string) => {
    const apiUrl =
      version === '3'
        ? '/api/v1/actor/chat/reasoning/game.history'
        : '/api/v1/actor/chat/game.history'

    const params =
      version === '3' ? { story_id: storyId } : { story_id: storyId, actor_id: actorId }

    return apiRequest<ChatHistoryResponse>(`${apiUrl}`, {
      method: 'POST',
      body: params
    })
  }

  return {
    apiRequest,
    fetchStories,
    fetchCategories,
    fetchStoryDetail,
    subscribeStory,
    unsubscribeStory,
    addFavorite,
    removeFavorite,
    fetchUserPlayedStories,
    fetchUserLikedStories,
    fetchUserInfo,
    updateUserInfo,
    // Checkin APIs
    getCheckinInfo,
    claimCheckinReward,
    // Tasks APIs
    getTasksList,
    completePlayGameTask,
    completeShareGameTask,
    completeCreateStoryTask,
    completeInviteFriendTask,
    getInviteCode,
    // System config APIs
    getSysConfigList,
    // Payment APIs
    getPriceList,
    createCheckoutSession,
    // Chat history APIs
    getUserChatHistory
  }
}
