/**
 * CDN 工具函数
 * 提供动态 CDN 地址构建功能
 */

export const useCdn = () => {
  const { brandingConfig } = useBranding()

  /**
   * 构建 CDN URL
   * @param path 资源路径，可以以 / 开头或不以 / 开头
   * @returns 完整的 CDN URL
   */
  const getCdnUrl = (path: string): string => {
    const cdnBase = brandingConfig.value.cdnUrl
    const cleanPath = path.startsWith('/') ? path : `/${path}`
    return `${cdnBase}${cleanPath}`
  }

  /**
   * 构建静态资源 URL
   * @param path 资源路径
   * @returns 完整的静态资源 URL
   */
  const getStaticUrl = (path: string): string => {
    const staticBase = brandingConfig.value.staticUrl
    const cleanPath = path.startsWith('/') ? path : `/${path}`
    return `${staticBase}${cleanPath}`
  }

  /**
   * 获取品牌相关的图片 URL
   */
  const getBrandImageUrl = (imageName: string): string => {
    return getCdnUrl(`/static/images/${imageName}`)
  }

  /**
   * 获取图标 URL
   */
  const getIconUrl = (iconName: string): string => {
    return getCdnUrl(`/static/images/icon/${iconName}`)
  }

  /**
   * 预定义的常用图标配置
   * 统一管理所有图标地址，避免硬编码
   */
  const icons = {
    // 应用图标
    appIcon: computed(() => getIconUrl('playshot-icon.png')),
    favicon: computed(() => getIconUrl('playshot-icon.png')),

    // 功能图标
    diamond: computed(() => getIconUrl('diamond.png')),
    coin: computed(() => getIconUrl('diamond.png')), // 钻石和金币使用同一个图标

    // 默认头像
    defaultAvatar: computed(() => getIconUrl('default-avatar.png')),

    // 其他常用图标
    logo: computed(() => brandingConfig.value.logoUrl)
  }

  return {
    getCdnUrl,
    getStaticUrl,
    getBrandImageUrl,
    getIconUrl,
    icons,
    // 直接暴露配置值
    cdnUrl: computed(() => brandingConfig.value.cdnUrl),
    staticUrl: computed(() => brandingConfig.value.staticUrl)
  }
}
