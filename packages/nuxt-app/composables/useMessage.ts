/**
 * Message 通知管理器
 * 提供全局的消息通知功能
 */

export interface MessageOptions {
  duration?: number
  closable?: boolean
}

export type MessageType = 'success' | 'error' | 'info' | 'warning'

// 全局消息实例引用
let messageInstance: any = null
let instancePromise: Promise<any> | null = null
let instanceResolve: ((instance: any) => void) | null = null

/**
 * 设置消息实例
 * 由 AppMessage 组件调用
 */
export const setMessageInstance = (instance: any) => {
  messageInstance = instance
  if (instanceResolve) {
    instanceResolve(instance)
    instanceResolve = null
    instancePromise = null
  }
}

/**
 * 清除消息实例
 */
export const clearMessageInstance = () => {
  messageInstance = null
  instancePromise = null
  instanceResolve = null
}

/**
 * 获取消息实例（支持等待）
 */
const getMessageInstance = async (): Promise<any> => {
  if (messageInstance) {
    return messageInstance
  }

  // 如果实例不存在，等待实例注册
  if (!instancePromise) {
    instancePromise = new Promise((resolve) => {
      instanceResolve = resolve
      // 设置超时，避免无限等待
      setTimeout(() => {
        if (instanceResolve) {
          console.warn(
            'Message instance registration timeout. Make sure AppMessage component is mounted.'
          )
          instanceResolve(null)
          instanceResolve = null
          instancePromise = null
        }
      }, 5000) // 5秒超时
    })
  }

  return instancePromise
}

/**
 * useMessage composable
 * 提供消息通知的便捷方法
 */
export const useMessage = () => {
  /**
   * 显示成功消息
   */
  const success = async (content: string, options: MessageOptions = {}) => {
    const instance = await getMessageInstance()
    if (instance) {
      return instance.success(content, options.duration)
    }
    return null
  }

  /**
   * 显示错误消息
   */
  const error = async (content: string, options: MessageOptions = {}) => {
    const instance = await getMessageInstance()
    if (instance) {
      return instance.error(content, options.duration)
    }
    return null
  }

  /**
   * 显示信息消息
   */
  const info = async (content: string, options: MessageOptions = {}) => {
    const instance = await getMessageInstance()
    if (instance) {
      return instance.info(content, options.duration)
    }
    return null
  }

  /**
   * 显示警告消息
   */
  const warning = async (content: string, options: MessageOptions = {}) => {
    const instance = await getMessageInstance()
    if (instance) {
      return instance.warning(content, options.duration)
    }
    return null
  }

  /**
   * 清除所有消息
   */
  const clear = async () => {
    const instance = await getMessageInstance()
    if (instance) {
      instance.clear()
    }
  }

  return {
    success,
    error,
    info,
    warning,
    clear
  }
}

/**
 * 全局消息方法（可直接调用）
 */
export const message = {
  success: async (content: string, duration?: number) => {
    const instance = await getMessageInstance()
    if (instance) {
      return instance.success(content, duration)
    }
    return null
  },

  error: async (content: string, duration?: number) => {
    const instance = await getMessageInstance()
    if (instance) {
      return instance.error(content, duration)
    }
    return null
  },

  info: async (content: string, duration?: number) => {
    const instance = await getMessageInstance()
    if (instance) {
      return instance.info(content, duration)
    }
    return null
  },

  warning: async (content: string, duration?: number) => {
    const instance = await getMessageInstance()
    if (instance) {
      return instance.warning(content, duration)
    }
    return null
  },

  clear: async () => {
    const instance = await getMessageInstance()
    if (instance) {
      instance.clear()
    }
  }
}
