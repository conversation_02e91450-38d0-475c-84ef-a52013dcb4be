import { createPersistedState } from 'pinia-plugin-persistedstate'
import type { Pinia } from 'pinia'

export default defineNuxtPlugin(({ $pinia }) => {
  ;($pinia as Pinia).use(
    createPersistedState({
      auto: false, // 不自动持久化所有store，需要手动配置
      storage: {
        getItem: (key: string) => {
          if (import.meta.client) {
            return localStorage.getItem(key)
          }
          return null
        },
        setItem: (key: string, value: string) => {
          if (import.meta.client) {
            localStorage.setItem(key, value)
          }
        },
        removeItem: (key: string) => {
          if (import.meta.client) {
            localStorage.removeItem(key)
          }
        }
      }
    })
  )
})
