#!/bin/bash

# Nuxt应用快速上传脚本
# 使用方法: ./upload-to-server.sh [staging|production] [server-alias]

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用方法
show_usage() {
    echo "使用方法: $0 [staging|production] [server-alias]"
    echo ""
    echo "参数:"
    echo "  staging      上传测试环境配置"
    echo "  production   上传生产环境配置"
    echo "  server-alias SSH配置别名 (可选，默认: myserver)"
    echo ""
    echo "示例:"
    echo "  $0 staging myserver"
    echo "  $0 staging"
    echo "  $0 production myserver"
}

# 检查参数
if [ $# -lt 1 ]; then
    log_error "缺少环境参数"
    show_usage
    exit 1
fi

ENVIRONMENT=$1
SERVER_INFO=${2:-"myserver"}  # 默认使用 myserver

# 验证环境参数
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    log_error "无效的环境参数: $ENVIRONMENT"
    show_usage
    exit 1
fi

# 设置目标目录 (使用用户主目录避免权限问题)
TARGET_DIR="~/nuxt-app"

log_info "🚀 开始上传 Nuxt 应用到 $ENVIRONMENT 环境..."
log_info "目标服务器: $SERVER_INFO"
log_info "目标目录: $TARGET_DIR"

# 测试 SSH 连接
log_info "测试 SSH 连接..."
if ! ssh -o ConnectTimeout=10 "$SERVER_INFO" "echo 'SSH连接成功'" >/dev/null 2>&1; then
    log_error "无法连接到服务器 $SERVER_INFO"
    log_error "请检查:"
    log_error "1. SSH 配置是否正确"
    log_error "2. 服务器是否可达"
    log_error "3. SSH 密钥是否配置"
    exit 1
fi
log_success "SSH 连接测试通过"

# 检查必要文件
log_info "检查本地文件..."

# 检测是否在 monorepo 环境中
if [ -f "../../turbo.json" ] || [ -f "../../pnpm-workspace.yaml" ]; then
    log_info "🔍 检测到 Monorepo 环境，切换到根目录"
    cd ../..

    required_files=(
        "package.json"
        "pnpm-lock.yaml"
        "pnpm-workspace.yaml"
        "turbo.json"
        "packages/nuxt-app/package.json"
        "packages/nuxt-app/nuxt.config.ts"
        "packages/nuxt-app/Dockerfile"
        "packages/nuxt-app/quick-deploy.sh"
        ".env.$ENVIRONMENT"
        "packages/nuxt-app/config/server.ts"
    )
else
    log_info "🔍 检测到独立部署环境"
    required_files=(
        "package.json"
        "nuxt.config.ts"
        "Dockerfile"
        "quick-deploy.sh"
        ".env.$ENVIRONMENT"
        "config/server.ts"
    )
fi

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        log_error "缺少必要文件: $file"
        exit 1
    fi
done

log_success "本地文件检查完成"

# 创建临时排除文件
EXCLUDE_FILE=$(mktemp)
cat > "$EXCLUDE_FILE" << EOF
node_modules/
.nuxt/
.output/
dist/
.data/
logs/
*.log
.DS_Store
.env.local
.env.*.local
.turbo/
.git/
packages/csr-app/node_modules/
packages/nuxt-app/node_modules/
EOF

log_info "创建远程目录..."
ssh "$SERVER_INFO" "mkdir -p $TARGET_DIR" || {
    log_error "无法创建目录 $TARGET_DIR"
    log_warning "尝试使用 sudo 创建目录..."

    # 尝试使用 sudo 创建目录
    if ssh "$SERVER_INFO" "sudo mkdir -p /opt/nuxt-app && sudo chown \$(whoami):\$(whoami) /opt/nuxt-app"; then
        TARGET_DIR="/opt/nuxt-app"
        log_success "使用 sudo 成功创建目录 $TARGET_DIR"
    else
        log_warning "无法在 /opt 目录创建，使用用户主目录"
        TARGET_DIR="~/nuxt-app"
        ssh "$SERVER_INFO" "mkdir -p $TARGET_DIR" || {
            log_error "无法创建目录，请检查服务器权限"
            rm -f "$EXCLUDE_FILE"
            exit 1
        }
    fi
}

# 上传文件
log_info "上传文件到服务器..."
rsync -avz --progress \
    --exclude-from="$EXCLUDE_FILE" \
    --delete \
    ./ "$SERVER_INFO:$TARGET_DIR/" || {
    log_error "文件上传失败"
    rm -f "$EXCLUDE_FILE"
    exit 1
}

# 清理临时文件
rm -f "$EXCLUDE_FILE"

log_success "文件上传完成"

# 设置脚本权限
log_info "设置脚本执行权限..."
if [ -f "packages/nuxt-app/quick-deploy.sh" ]; then
    # Monorepo 环境
    ssh "$SERVER_INFO" "cd $TARGET_DIR && chmod +x packages/nuxt-app/quick-deploy.sh" || {
        log_warning "设置脚本权限失败，请手动执行: chmod +x packages/nuxt-app/quick-deploy.sh"
    }
else
    # 独立部署环境
    ssh "$SERVER_INFO" "cd $TARGET_DIR && chmod +x quick-deploy.sh" || {
        log_warning "设置脚本权限失败，请手动执行: chmod +x quick-deploy.sh"
    }
fi

# 显示后续步骤
echo ""
log_success "🎉 上传完成！"
echo ""
echo "📋 后续步骤:"
echo "1. 登录服务器:"
echo "   ssh $SERVER_INFO"
echo ""
echo "2. 进入应用目录:"
echo "   cd $TARGET_DIR"
echo ""
echo "3. 部署应用:"
echo "   ./quick-deploy.sh $ENVIRONMENT"
echo ""
echo "4. 访问应用:"
echo "   http://118.145.199.7:3000  (测试环境)"
echo ""

# 询问是否自动部署
read -p "是否立即部署应用? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "开始自动部署..."
    ssh "$SERVER_INFO" "cd $TARGET_DIR && ./quick-deploy.sh $ENVIRONMENT" || {
        log_error "自动部署失败，请手动执行部署命令"
        exit 1
    }
    
    log_success "🚀 部署完成！"
    echo ""
    echo "🌐 应用访问地址:"
    echo "   http://118.145.199.7:3000"
else
    log_info "跳过自动部署，请手动执行部署命令"
fi

log_success "✨ 所有操作完成！"
