import { defineStore } from 'pinia'

interface ThemeState {
  isDarkTheme: boolean
}

export const useThemeStore = defineStore('theme', {
  state: (): ThemeState => ({
    isDarkTheme: true // 默认暗色主题
  }),

  getters: {
    currentTheme: (state) => state.isDarkTheme ? 'dark' : 'light'
  },

  actions: {
    toggleTheme() {
      this.isDarkTheme = !this.isDarkTheme
      this.applyTheme()
      this.saveTheme()
    },

    setTheme(isDark: boolean) {
      this.isDarkTheme = isDark
      this.applyTheme()
      this.saveTheme()
    },

    applyTheme() {
      if (import.meta.client) {
        document.body.classList.toggle('light-theme', !this.isDarkTheme)
        document.body.classList.toggle('dark-theme', this.isDarkTheme)
      }
    },

    saveTheme() {
      if (import.meta.client) {
        localStorage.setItem('theme', this.isDarkTheme ? 'dark' : 'light')
      }
    },

    initTheme() {
      if (import.meta.client) {
        const savedTheme = localStorage.getItem('theme')
        if (savedTheme) {
          this.isDarkTheme = savedTheme === 'dark'
        }
        this.applyTheme()
      }
    }
  }
})
