import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { TaskItem, TaskData } from '~/composables/useApi'

// 任务奖励配置（钻石数量）
const TASK_REWARDS = {
  play_game: 10,
  share: 10,
  create: 10,
  invite: 10
}

export const useTasksStore = defineStore(
  'tasks',
  () => {
    const {
      getTasksList,
      completePlayGameTask,
      completeShareGameTask,
      completeCreateStoryTask,
      completeInviteFriendTask,
      getInviteCode
    } = useApi()
    const userStore = useUserStore()

    // State
    const tasks = ref<TaskItem[]>([])
    const rawTasksData = ref<Record<string, TaskData> | null>(null)
    const inviteCode = ref<string | null>(null)
    const isLoadingInviteCode = ref(false)
    const loading = ref(false)
    const error = ref<string | null>(null)

    // Getters
    const pendingTasks = computed(() => tasks.value.filter((task) => !task.is_done))
    const completedTasks = computed(() => tasks.value.filter((task) => task.is_done))
    const playGameTask = computed(() => tasks.value.find((task) => task.type === 'play_game'))
    const shareGameTask = computed(() => tasks.value.find((task) => task.type === 'share'))
    const createStoryTask = computed(() => tasks.value.find((task) => task.type === 'create'))
    const inviteFriendTask = computed(() => tasks.value.find((task) => task.type === 'invite'))
    const allTasksCompleted = computed(
      () => tasks.value.length > 0 && tasks.value.every((task) => task.is_done)
    )

    // Utils
    const handleApiResponse = <T>(response: { code: string; message?: string; data: T }) => {
      if (response.code === '0') {
        return response.data
      }
      throw new Error(response.message || 'API request failed')
    }

    // Transform raw task data to TaskItem format
    const transformTaskData = (rawData: Record<string, TaskData>): TaskItem[] => {
      const taskItems: TaskItem[] = []

      Object.entries(rawData).forEach(([key, taskData]) => {
        if (typeof taskData === 'object' && taskData !== null) {
          const taskType = key.replace('_task', '')
          const reward = TASK_REWARDS[taskType as keyof typeof TASK_REWARDS] || taskData.coins || 0

          taskItems.push({
            id: key,
            name: taskData.name,
            description: taskData.description,
            count: taskData.count,
            limit: taskData.limit,
            is_done: taskData.is_done,
            type: taskType,
            reward
          })
        }
      })

      return taskItems
    }

    // Actions
    const fetchTasks = async (force = false) => {
      // 如果正在加载或已有数据且不强制刷新，则跳过
      if (loading.value || (!force && tasks.value.length > 0)) {
        console.log('Tasks already loaded or loading, skipping...')
        return
      }

      loading.value = true
      error.value = null

      try {
        const response = await getTasksList()
        const data = handleApiResponse(response)

        rawTasksData.value = data
        tasks.value = transformTaskData(data)
      } catch (err) {
        error.value = err instanceof Error ? err.message : 'Failed to fetch tasks'
        throw err
      } finally {
        loading.value = false
      }
    }

    const completePlayGame = async () => {
      try {
        const response = await completePlayGameTask()
        const data = handleApiResponse(response)

        // Update user info
        if (data.user) {
          userStore.setUserInfo(data.user as any)
        }

        // Refresh tasks
        await fetchTasks()
        return true
      } catch (err) {
        error.value = err instanceof Error ? err.message : 'Failed to complete play game task'
        throw err
      }
    }

    const completeShareGame = async () => {
      try {
        const response = await completeShareGameTask()
        const data = handleApiResponse(response)

        // Update user info
        if (data.user) {
          userStore.setUserInfo(data.user as any)
        }

        // Refresh tasks
        await fetchTasks()
        return true
      } catch (err) {
        error.value = err instanceof Error ? err.message : 'Failed to complete share game task'
        throw err
      }
    }

    const completeCreateStory = async () => {
      try {
        const response = await completeCreateStoryTask()
        const data = handleApiResponse(response)

        // Update user info
        if (data.user) {
          userStore.setUserInfo(data.user as any)
        }

        // Refresh tasks
        await fetchTasks()
        return true
      } catch (err) {
        error.value = err instanceof Error ? err.message : 'Failed to complete create story task'
        throw err
      }
    }

    const completeInviteFriend = async () => {
      try {
        const response = await completeInviteFriendTask()
        const data = handleApiResponse(response)

        // Update user info
        if (data.user) {
          userStore.setUserInfo(data.user as any)
        }

        // Refresh tasks
        await fetchTasks()
        return true
      } catch (err) {
        error.value = err instanceof Error ? err.message : 'Failed to complete invite friend task'
        throw err
      }
    }

    const fetchInviteCode = async () => {
      if (inviteCode.value) return inviteCode.value

      isLoadingInviteCode.value = true

      try {
        const response = await getInviteCode()
        const data = handleApiResponse(response)

        inviteCode.value = data.exclusive_invite_code
        return inviteCode.value
      } catch (err) {
        error.value = err instanceof Error ? err.message : 'Failed to fetch invite code'
        throw err
      } finally {
        isLoadingInviteCode.value = false
      }
    }

    const copyInviteCodeToClipboard = async () => {
      const code = await fetchInviteCode()
      if (code) {
        try {
          const url = `${window.location.origin}?invite=${code}`
          await navigator.clipboard.writeText(url)
          return true
        } catch (err) {
          console.error('Failed to copy invite code:', err)
          return false
        }
      }
      return false
    }

    const reset = () => {
      tasks.value = []
      rawTasksData.value = null
      inviteCode.value = null
      error.value = null
    }

    return {
      // State
      tasks,
      rawTasksData,
      loading,
      error,
      inviteCode,
      isLoadingInviteCode,

      // Getters
      pendingTasks,
      completedTasks,
      playGameTask,
      shareGameTask,
      createStoryTask,
      inviteFriendTask,
      allTasksCompleted,

      // Actions
      fetchTasks,
      completePlayGame,
      completeShareGame,
      completeCreateStory,
      completeInviteFriend,
      fetchInviteCode,
      copyInviteCodeToClipboard,
      reset
    }
  },
  {
    persist: false
  }
)
