import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { CheckInCoinsPerDay } from '~/composables/useApi'

export const useSysConfigStore = defineStore(
  'sysconfig',
  () => {
    const { getSysConfigList } = useApi()

    // State
    const checkInCoinsPerDay = ref<CheckInCoinsPerDay | null>(null)
    const userHobbyCollection = ref<Record<string, object> | null>(null)
    const loading = ref(false)
    const error = ref<string | null>(null)

    // Utils
    const handleApiResponse = <T>(response: { code: string; message?: string; data: T }) => {
      if (response.code === '0') {
        return response.data
      }
      throw new Error(response.message || 'API request failed')
    }

    // Actions
    const fetchConfigs = async (force = false) => {
      // 如果正在加载或已有数据且不强制刷新，则跳过
      if (loading.value || (!force && checkInCoinsPerDay.value !== null)) {
        console.log('System configs already loaded or loading, skipping...')
        return
      }

      loading.value = true
      error.value = null

      try {
        const response = await getSysConfigList()
        const data = handleApiResponse(response)

        checkInCoinsPerDay.value = data.checkin.check_in_coins_per_day
        userHobbyCollection.value = data.user_hobby_collection
      } catch (err) {
        error.value = err instanceof Error ? err.message : 'Failed to fetch system configs'
        throw err
      } finally {
        loading.value = false
      }
    }

    const getCheckInCoins = (day: number) => {
      if (!checkInCoinsPerDay.value) return 0

      const dayMap: Record<number, keyof CheckInCoinsPerDay> = {
        1: 'day_one',
        2: 'day_two',
        3: 'day_three',
        4: 'day_four',
        5: 'day_five',
        6: 'day_six',
        7: 'day_seven'
      }

      const key = dayMap[day]
      return key ? checkInCoinsPerDay.value[key] : 0
    }

    const reset = () => {
      checkInCoinsPerDay.value = null
      userHobbyCollection.value = null
      error.value = null
    }

    return {
      // State
      checkInCoinsPerDay,
      userHobbyCollection,
      loading,
      error,

      // Actions
      fetchConfigs,
      getCheckInCoins,
      reset
    }
  },
  {
    persist: false
  }
)
