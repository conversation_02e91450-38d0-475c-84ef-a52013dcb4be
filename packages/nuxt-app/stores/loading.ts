import { defineStore } from 'pinia'

interface LoadingState {
  isLoading: boolean
  message: string
  type: 'payment' | 'general' | 'redirect'
}

export const useLoadingStore = defineStore('loading', {
  state: (): LoadingState => ({
    isLoading: false,
    message: '',
    type: 'general'
  }),

  actions: {
    showLoading(message: string = 'Loading...', type: 'payment' | 'general' | 'redirect' = 'general') {
      this.isLoading = true
      this.message = message
      this.type = type
    },

    hideLoading() {
      this.isLoading = false
      this.message = ''
      this.type = 'general'
    },

    showPaymentLoading(message: string = 'Processing payment...') {
      this.showLoading(message, 'payment')
    },

    showRedirectLoading(message: string = 'Redirecting to payment...') {
      this.showLoading(message, 'redirect')
    }
  }
})