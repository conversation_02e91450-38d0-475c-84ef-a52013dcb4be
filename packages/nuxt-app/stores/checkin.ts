import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useCheckinStore = defineStore(
  'checkin',
  () => {
    const { getCheckinInfo, claimCheckinReward } = useApi()
    const userStore = useUserStore()

    // State
    const currentDay = ref(1)
    const todayClaimed = ref(false)
    const visible = ref(false)
    const loading = ref(false)
    const error = ref<string | null>(null)

    // Getters
    const isAllDaysClaimed = computed(() => currentDay.value > 7)
    const canClaim = computed(() => !loading.value && !todayClaimed.value)
    const canShowCheckin = computed(() => userStore.isAuthenticated && !userStore.isGuest)

    // Utils
    const handleApiResponse = <T>(response: { code: string; message?: string; data: T }) => {
      if (response.code === '0') {
        return response.data
      }
      throw new Error(response.message || 'API request failed')
    }

    // Day number conversion
    const getDayNumber = (key: string): number => {
      const dayMap: Record<string, number> = {
        day_one: 1,
        day_two: 2,
        day_three: 3,
        day_four: 4,
        day_five: 5,
        day_six: 6,
        day_seven: 7
      }
      return dayMap[key] || 0
    }

    // Actions
    const fetchCheckinInfo = async (force = false) => {
      // 如果正在加载或已有数据且不强制刷新，则跳过
      if (loading.value || (!force && currentDay.value > 1)) {
        console.log('Checkin info already loaded or loading, skipping...')
        return
      }

      loading.value = true
      error.value = null

      try {
        const response = await getCheckinInfo()
        const { is_sign_in, sign_in_count } = handleApiResponse(response)

        // sign_in_count 代表已经签到的天数
        // 如果今天已签到，currentDay 保持在当前天数
        // 如果今天未签到，currentDay 应该是下一天
        currentDay.value = is_sign_in ? sign_in_count : sign_in_count + 1
        todayClaimed.value = is_sign_in

        // 如果已经签满7天，确保 currentDay 不会超过8
        if (currentDay.value > 7) {
          currentDay.value = 8
        }
      } catch (err) {
        error.value = err instanceof Error ? err.message : 'Failed to fetch checkin info'
        throw err
      } finally {
        loading.value = false
      }
    }

    const claimDailyReward = async () => {
      if (todayClaimed.value) return false

      loading.value = true
      error.value = null

      try {
        const response = await claimCheckinReward()
        const data = handleApiResponse(response)

        todayClaimed.value = true

        // Update user info
        if (data.user) {
          userStore.setUserInfo({
            ...data.user
          })
        }

        return true
      } catch (err) {
        error.value = err instanceof Error ? err.message : 'Failed to claim reward'
        throw err
      } finally {
        loading.value = false
      }
    }

    const showModal = () => {
      visible.value = true
    }

    const hideModal = () => {
      visible.value = false
    }

    const reset = () => {
      currentDay.value = 1
      todayClaimed.value = false
      error.value = null
    }

    return {
      // State
      currentDay,
      todayClaimed,
      loading,
      error,
      visible,

      // Getters
      isAllDaysClaimed,
      canClaim,
      canShowCheckin,

      // Actions
      getDayNumber,
      fetchCheckinInfo,
      claimDailyReward,
      showModal,
      hideModal,
      reset
    }
  },
  {
    persist: false
  }
)
