/**
 * 缓存的故事内容API
 * 只返回故事的基础内容，不涉及用户状态
 */

export default defineCachedEventHandler(
  async (event) => {
    const config = useRuntimeConfig()
    const query = getQuery(event)

    try {
      // 构建请求参数（支持排序、分类和标签）
      const params: any = {
        sort: query.sort || 'popular'
      }

      // 处理分类参数
      if (query.category) {
        params.category_ids = [query.category]
      }

      // 处理标签参数 - 将标签名称转换为分类ID
      if (query.tags) {
        try {
          // 首先获取所有分类数据来进行标签名称到ID的转换
          const categoriesResponse = await $fetch<{
            code: string
            data: { category: any[] }
          }>(`${config.public.apiBase}/api/v1/category.list`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            }
          })

          if (categoriesResponse.code === '0' && categoriesResponse.data.category) {
            const tagNames =
              typeof query.tags === 'string'
                ? query.tags.split(',').map((t) => t.trim())
                : Array.isArray(query.tags)
                ? query.tags
                : [query.tags]

            const tagIds: string[] = []

            // 在所有分类中查找标签
            for (const category of categoriesResponse.data.category) {
              if (category?.subcategories) {
                for (const subcategory of category.subcategories) {
                  if (
                    tagNames.some(
                      (name) =>
                        subcategory.name.toLowerCase() === name.toLowerCase() ||
                        subcategory.id === name
                    )
                  ) {
                    tagIds.push(subcategory.id)
                  }
                }
              }
            }

            // 如果找到了标签ID，添加到参数中
            if (tagIds.length > 0) {
              params.category_ids = (params.category_ids || []).concat(tagIds)
            }
          }
        } catch (error) {
          console.error('Failed to resolve tag names to IDs:', error)
        }
      }

      // 使用一个固定的游客token来获取公共故事内容
      const serverGuestToken = await getServerGuestToken()

      if (!serverGuestToken) {
        throw new Error('Failed to get server guest token')
      }

      // 获取故事内容
      const response = await $fetch<{
        code: string
        message: string
        data: {
          stories: any[]
          total: number
          page: number
          page_size: number
        }
      }>(`${config.public.apiBase}/api/v1/story.list`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${serverGuestToken}`
        },
        body: params
      })

      if (response.code !== '0' || !response.data) {
        throw new Error(response.message || 'Failed to fetch stories')
      }

      // 只返回故事的基础内容，移除用户相关的状态
      const cleanStories = response.data.stories.map((story) => ({
        id: story.id,
        title: story.title,
        description: story.description,
        cover_url: story.preview_url, // 使用 preview_url 作为封面
        preview_url: story.preview_url,
        carousel_image_url: story.carousel_image_url || [], // 添加轮播图片字段
        category: story.category,
        tags: story.tags || [],
        created_at: story.create_time, // 注意字段名是 create_time
        updated_at: story.updated_at,
        status: story.status || 'normal',
        // 保留公共的统计数据
        like_count: story.likes, // 注意字段名是 likes
        play_count: story.played_count, // 注意字段名是 played_count
        hot: story.hot,
        // 添加其他有用的字段
        badge: story.badge,
        coins: story.coins,
        bgm_url: story.bgm_url,
        preview_video_url: story.preview_video_url,
        actors: story.actors || [],
        categories: story.categories || []
        // 移除用户相关的状态字段
        // is_liked, is_bookmarked 等会在客户端根据用户token重新获取
      }))

      const result = {
        stories: cleanStories,
        total: response.data.total,
        page: response.data.page,
        page_size: response.data.page_size,
        source: 'server-cache',
        timestamp: Date.now()
      }

      return result
    } catch (error: any) {
      console.error('Stories content cache error:', error)

      // 返回空数据而不是抛出错误，避免缓存穿透
      return {
        stories: [],
        total: 0,
        page: 1,
        page_size: 12,
        source: 'error-fallback',
        error: error.message,
        timestamp: Date.now()
      }
    }
  },
  {
    maxAge: 60 * 30, // 30分钟缓存
    name: 'stories-content-cache',
    getKey: (event) => {
      const query = getQuery(event)
      const tagsKey = query.tags
        ? Array.isArray(query.tags)
          ? query.tags.join(',')
          : query.tags
        : 'none'
      const cacheKey = `stories-content:${query.sort || 'popular'}:${
        query.category || 'all'
      }:${tagsKey}`
      return cacheKey
    }
  }
)

/**
 * 获取服务端专用的游客token
 * 这个token只在服务端使用，用于获取公共内容
 */
async function getServerGuestToken(): Promise<string | null> {
  const config = useRuntimeConfig()

  try {
    const deviceId = 'server_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9)

    const response = await $fetch<{
      code: string
      message: string
      data: {
        auth: {
          access_token: string
          refresh_token: string
        }
        user: any
      }
    }>(`${config.public.apiBase}/api/v1/user-guest.sign-up`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: {
        device_id: deviceId,
        gender: 'unknown'
      }
    })

    if (response.code === '0' && response.data?.auth?.access_token) {
      return response.data.auth.access_token
    }

    return null
  } catch (error) {
    console.error('Failed to get server guest token:', error)
    return null
  }
}
