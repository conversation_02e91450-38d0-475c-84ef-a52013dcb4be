/**
 * 动态生成 reelsitemap.xml
 * 展示游客可以访问的网址
 */

export default defineCachedEventHandler(
  async (event) => {
    try {
      // 获取基础URL
      const baseUrl = getBaseUrl()

      // 静态页面URL列表（游客可访问）
      const staticUrls = [
        '/', // 首页
        '/terms', // 服务条款
        '/privacy', // 隐私政策
        '/complaints', // 投诉页面
        '/content-removal', // 内容移除
        '/record-keeping', // 记录保存
        '/404' // 404页面
      ]

      // 获取动态故事页面URL
      const storyUrls = await getStoryUrls()

      // 合并所有URL
      const allUrls = [...staticUrls, ...storyUrls]

      // 生成sitemap XML
      const sitemap = generateSitemapXml(allUrls, baseUrl)

      // 设置响应头
      setHeader(event, 'Content-Type', 'application/xml; charset=utf-8')
      setHeader(
        event,
        'Cache-Control',
        'public, max-age=3600, s-maxage=7200, stale-while-revalidate=86400'
      )

      return sitemap
    } catch (error: any) {
      console.error('Sitemap generation error:', error)

      // 返回基础sitemap，避免完全失败
      const baseUrl = getBaseUrl()
      const basicSitemap = generateSitemapXml(['/'], baseUrl)

      setHeader(event, 'Content-Type', 'application/xml; charset=utf-8')
      return basicSitemap
    }
  },
  {
    maxAge: 60 * 60, // 1小时缓存
    name: 'reelsitemap-cache',
    getKey: () => 'reelsitemap'
  }
)

/**
 * 获取基础URL
 */
function getBaseUrl(): string {
  // 根据环境返回对应的域名
  const isDev = process.env.NODE_ENV === 'development'
  const isStaging = process.env.DEPLOYMENT_ENV === 'staging'

  if (isDev) {
    // 在开发环境中，使用实际的端口
    return 'http://localhost:3000'
  } else if (isStaging) {
    return 'https://reelplay.ai'
  } else {
    return 'https://playshot.ai'
  }
}

/**
 * 获取所有公开故事的URL列表
 */
async function getStoryUrls(): Promise<string[]> {
  try {
    // 使用现有的服务端guest token获取故事列表
    const serverGuestToken = await getServerGuestToken()

    if (!serverGuestToken) {
      console.warn('Failed to get server guest token for sitemap')
      return []
    }

    const config = useRuntimeConfig()

    // 获取故事列表（优化：只获取必要的字段和数量）
    const allStories: any[] = []
    let page = 1
    const pageSize = 50 // 减少页面大小以提高性能
    let hasMore = true
    const maxPages = 20 // 增加最大页数限制，但减少每页数量
    const maxStories = 1000 // 设置最大故事数量限制

    while (hasMore && page <= maxPages && allStories.length < maxStories) {
      try {
        const response = await $fetch<{
          code: string
          message: string
          data: {
            stories: any[]
            total: number
            page: number
            page_size: number
          }
        }>(`${config.public.apiBase}/api/v1/story.list`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${serverGuestToken}`
          },
          body: {
            sort: 'popular', // 使用热门排序，确保重要内容优先
            page,
            page_size: pageSize
          },
          timeout: 5000 // 5秒超时
        })

        if (response.code === '0' && response.data?.stories) {
          // 过滤并添加故事
          const validStories = response.data.stories.filter(
            (story) => story.id && story.status === 'normal' && story.id.toString().length > 0
          )

          allStories.push(...validStories)

          // 检查是否还有更多数据
          hasMore =
            response.data.stories.length === pageSize &&
            allStories.length < response.data.total &&
            allStories.length < maxStories
          page++

          // 添加小延迟避免API压力
          if (hasMore) {
            await new Promise((resolve) => setTimeout(resolve, 100))
          }
        } else {
          console.warn(`Story API returned error on page ${page}:`, response.message)
          hasMore = false
        }
      } catch (pageError) {
        console.error(`Failed to fetch stories page ${page}:`, pageError)
        // 单页失败不影响整体，继续下一页
        page++
        if (page > maxPages) {
          hasMore = false
        }
      }
    }

    console.log(`Sitemap: Retrieved ${allStories.length} stories for sitemap`)

    // 生成故事页面URL，确保ID有效
    return allStories.map((story) => `/story/${story.id}`).filter((url) => url !== '/story/') // 过滤掉无效的URL
  } catch (error) {
    console.error('Failed to fetch stories for sitemap:', error)
    return []
  }
}

/**
 * 获取服务端专用的游客token
 * 复用 stories-content.get.ts 中的逻辑
 */
async function getServerGuestToken(): Promise<string | null> {
  const config = useRuntimeConfig()

  try {
    const deviceId = 'sitemap_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9)

    const response = await $fetch<{
      code: string
      message: string
      data: {
        auth: {
          access_token: string
          refresh_token: string
        }
        user: any
      }
    }>(`${config.public.apiBase}/api/v1/user-guest.sign-up`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: {
        device_id: deviceId,
        gender: 'unknown'
      }
    })

    if (response.code === '0' && response.data?.auth?.access_token) {
      return response.data.auth.access_token
    }

    return null
  } catch (error) {
    console.error('Failed to get server guest token:', error)
    return null
  }
}

/**
 * 生成sitemap XML内容
 */
function generateSitemapXml(urls: string[], baseUrl: string): string {
  const currentDate = new Date().toISOString().split('T')[0]

  const urlEntries = urls
    .map((url) => {
      // 根据URL类型设置优先级和更新频率
      const priority = getPriority(url)
      const changefreq = getChangeFreq(url)
      const lastmod = getLastMod(url, currentDate)

      return `  <url>
    <loc>${baseUrl}${url}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>${changefreq}</changefreq>
    <priority>${priority}</priority>
  </url>`
    })
    .join('\n')

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlEntries}
</urlset>`
}

/**
 * 根据URL获取优先级
 */
function getPriority(url: string): string {
  if (url === '/') return '1.0'
  if (url.startsWith('/story/')) return '0.8'
  if (['/terms', '/privacy'].includes(url)) return '0.6'
  if (['/complaints', '/content-removal', '/record-keeping'].includes(url)) return '0.4'
  return '0.5'
}

/**
 * 根据URL获取更新频率
 */
function getChangeFreq(url: string): string {
  if (url === '/') return 'daily'
  if (url.startsWith('/story/')) return 'weekly'
  if (['/terms', '/privacy', '/complaints', '/content-removal', '/record-keeping'].includes(url))
    return 'monthly'
  return 'monthly'
}

/**
 * 根据URL获取最后修改时间
 */
function getLastMod(url: string, currentDate: string): string {
  // 对于首页，使用当前日期
  if (url === '/') return currentDate

  // 对于其他页面，可以使用当前日期或固定日期
  // 在实际应用中，可以从数据库获取真实的修改时间
  return currentDate
}
