/**
 * 动态生成 robots.txt
 * 根据品牌配置显示对应的 sitemap
 */

export default defineEventHandler((event) => {
  const config = useRuntimeConfig()
  const appName = config.public.appName || 'PlayShot'

  // 品牌判断逻辑
  const isReelPlay = appName.toLowerCase().includes('reel')

  // 根据品牌确定域名和 sitemap
  const domain = isReelPlay ? 'https://reelplay.ai' : 'https://playshot.ai'

  const robotsContent = `# Robots.txt for ${isReelPlay ? 'ReelPlay' : 'PlayShot'}
# Generated automatically based on brand configuration

User-agent: *
Allow: /

# Allow access to important pages
Allow: /story/*
Allow: /about
Allow: /privacy
Allow: /terms
Allow: /refund
Allow: /complaints
Allow: /content-removal
Allow: /record-keeping
Allow: /daily-tasks

# Allow user pages (but some may require authentication)
Allow: /user/login
Allow: /user/profile
Allow: /user/settings
Allow: /user/coins
Allow: /recharge-success

# Allow access to static assets
Allow: /assets/*
Allow: /_nuxt/*
Allow: /favicon.ico
Allow: /*.svg
Allow: /*.png
Allow: /*.jpg
Allow: /*.jpeg
Allow: /*.webp

# Allow access to sitemaps
Allow: /reelsitemap.xml
Allow: /sitemap.xml

# Disallow admin or sensitive areas
Disallow: /api/
Disallow: /_nuxt/builds/
Disallow: /admin/

# Disallow chat pages (interactive content, not suitable for indexing)
Disallow: /chat/
Disallow: /chat2/
Disallow: /chat3/
Disallow: /chat4/

# Disallow user-specific pages that require authentication
Disallow: /user/social-callback

# Crawl delay (optional - be respectful to servers)
Crawl-delay: 1

# Sitemap location
Sitemap: ${domain}/reelsitemap.xml`

  // 设置正确的 Content-Type
  setHeader(event, 'Content-Type', 'text/plain')

  return robotsContent
})
