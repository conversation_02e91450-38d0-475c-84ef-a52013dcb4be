#!/bin/bash

# Nuxt应用本地构建并部署脚本
# 使用方法: ./build-and-deploy.sh [staging|production]

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -eq 0 ]; then
    log_error "缺少环境参数"
    echo "使用方法: $0 [staging|production]"
    exit 1
fi

ENVIRONMENT=$1

# 验证环境参数
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    log_error "无效的环境参数: $ENVIRONMENT"
    echo "使用方法: $0 [staging|production]"
    exit 1
fi

log_info "🚀 开始本地构建并部署 Nuxt 应用到 $ENVIRONMENT 环境..."

# 检查必要工具
if ! command -v pnpm &> /dev/null; then
    log_error "pnpm 未安装"
    exit 1
fi

if ! command -v rsync &> /dev/null; then
    log_error "rsync 未安装，请先安装: brew install rsync (macOS) 或 apt-get install rsync (Linux)"
    exit 1
fi

# 服务器配置
SERVER_ALIAS="myserver"
SERVER_PATH="/home/<USER>/nuxt-app-$ENVIRONMENT"

# 设置环境变量
export NODE_ENV=$ENVIRONMENT
export DEPLOYMENT_ENV=$ENVIRONMENT

# 检查环境文件
ENV_FILE="../../.env.$ENVIRONMENT"
if [ ! -f "$ENV_FILE" ]; then
    log_warning "⚠️  环境文件 $ENV_FILE 不存在，使用默认配置"
fi

# 清理旧的构建产物
log_info "🧹 清理旧的构建产物..."
rm -rf .output
rm -rf .nuxt

# 本地构建应用
log_info "📦 本地构建应用..."
if [ -f "../../turbo.json" ]; then
    log_info "使用 Turbo 构建..."
    cd ../..
    pnpm turbo run build:$ENVIRONMENT --filter=nuxt-app
    cd packages/nuxt-app
else
    log_info "使用 pnpm 直接构建..."
    pnpm build:$ENVIRONMENT
fi

# 检查构建产物
if [ ! -d ".output" ]; then
    log_error "构建失败，未找到 .output 目录"
    exit 1
fi

log_success "✅ 本地构建完成"

# 创建部署包
log_info "📦 创建部署包..."
DEPLOY_DIR="deploy-$ENVIRONMENT-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$DEPLOY_DIR"

# 复制必要文件到部署目录
cp -r .output "$DEPLOY_DIR/"
cp package.json "$DEPLOY_DIR/"
cp Dockerfile "$DEPLOY_DIR/"

# 如果环境文件存在，也复制过去
if [ -f "$ENV_FILE" ]; then
    cp "$ENV_FILE" "$DEPLOY_DIR/.env"
fi

# 创建服务器端部署脚本
cat > "$DEPLOY_DIR/server-deploy.sh" << 'EOF'
#!/bin/bash

set -e

ENVIRONMENT=$1
if [ -z "$ENVIRONMENT" ]; then
    echo "错误: 缺少环境参数"
    exit 1
fi

echo "🚀 开始在服务器上部署 Nuxt 应用..."

# 停止现有容器
echo "🛑 停止现有容器..."
docker stop "nuxt-app-$ENVIRONMENT" > /dev/null 2>&1 || true
docker rm "nuxt-app-$ENVIRONMENT" > /dev/null 2>&1 || true

# 清理旧镜像
echo "🗑️  清理旧镜像..."
docker rmi "nuxt-app:$ENVIRONMENT" > /dev/null 2>&1 || true

# 构建 Docker 镜像
echo "🐳 构建 Docker 镜像..."
docker build -t "nuxt-app:$ENVIRONMENT" \
    --build-arg NODE_ENV=$ENVIRONMENT \
    --build-arg DEPLOYMENT_ENV=$ENVIRONMENT \
    .

# 启动容器
echo "🚀 启动容器..."
DOCKER_CMD="docker run -d \
  --name nuxt-app-$ENVIRONMENT \
  --restart unless-stopped \
  -p 3000:3000 \
  -e NODE_ENV=$ENVIRONMENT \
  -e DEPLOYMENT_ENV=$ENVIRONMENT \
  -e NITRO_PORT=3000 \
  -e NITRO_HOST=0.0.0.0"

# 如果环境文件存在，添加环境文件参数
if [ -f ".env" ]; then
    DOCKER_CMD="$DOCKER_CMD --env-file .env"
fi

DOCKER_CMD="$DOCKER_CMD nuxt-app:$ENVIRONMENT"

eval $DOCKER_CMD

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 健康检查
echo "🔍 执行健康检查..."
if curl -f http://localhost:3000/ > /dev/null 2>&1; then
    echo "✅ 部署成功!"
    echo ""
    echo "🌐 访问地址: http://*************:3000"
    echo ""
    echo "📊 容器状态:"
    docker ps --filter "name=nuxt-app-$ENVIRONMENT"
else
    echo "❌ 部署失败 - 服务未响应"
    echo ""
    echo "📋 查看日志:"
    docker logs --tail=20 "nuxt-app-$ENVIRONMENT"
    exit 1
fi

echo "🎉 Nuxt 应用部署完成!"
EOF

chmod +x "$DEPLOY_DIR/server-deploy.sh"

log_success "✅ 部署包创建完成: $DEPLOY_DIR"

# 上传到服务器
log_info "📤 上传部署包到服务器..."
ssh "$SERVER_ALIAS" "mkdir -p $SERVER_PATH" || {
    log_error "无法连接到服务器或创建目录"
    exit 1
}

rsync -avz --delete "$DEPLOY_DIR/" "$SERVER_ALIAS:$SERVER_PATH/" || {
    log_error "上传失败"
    exit 1
}

log_success "✅ 上传完成"

# 在服务器上执行部署
log_info "🚀 在服务器上执行部署..."
ssh "$SERVER_ALIAS" "cd $SERVER_PATH && ./server-deploy.sh $ENVIRONMENT" || {
    log_error "服务器部署失败"
    exit 1
}

# 清理本地临时文件
log_info "🧹 清理本地临时文件..."
rm -rf "$DEPLOY_DIR"

log_success "🎉 部署完成!"
echo ""
echo "🌐 访问地址: http://*************:3000"
echo "📋 部署信息:"
echo "   环境: $ENVIRONMENT"
echo "   服务器: $SERVER_ALIAS"
echo "   路径: $SERVER_PATH"
echo "   构建方式: 本地构建 + 服务器部署"
