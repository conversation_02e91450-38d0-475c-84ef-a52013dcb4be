<template>
  <div class="layout-container">
    <NuxtRouteAnnouncer />
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
    <ClientOnly>
      <GlobalLoading />
      <AppMessage />
      <RechargeModal />
    </ClientOnly>
  </div>
</template>

<script setup>
// 使用品牌配置
const { seoMeta, brandingConfig } = useBranding()

// 全局SEO配置
useHead({
  htmlAttrs: {
    lang: 'en'
  },
  ...seoMeta.value,
  link: [
    ...seoMeta.value.link,
    // 动态预连接 CDN 资源
    { rel: 'preconnect', href: brandingConfig.value.cdnUrl },
    { rel: 'dns-prefetch', href: brandingConfig.value.cdnUrl },
    // 字体预连接优化
    { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
    { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' }
  ]
})

// 获取分析工具
const { trackPageView } = useAnalytics()

// 监听路由变化，跟踪页面浏览
const router = useRouter()
router.afterEach((to) => {
  // 跟踪页面浏览
  trackPageView(to.fullPath)
})

// 立即初始化主题和用户状态，避免闪烁
if (import.meta.client) {
  // 初始化主题
  const savedTheme = localStorage.getItem('theme')
  const isDarkTheme = savedTheme ? savedTheme === 'dark' : true

  document.body.classList.toggle('light-theme', !isDarkTheme)
  document.body.classList.toggle('dark-theme', isDarkTheme)

  // 立即初始化用户状态
  const userStore = useUserStore()
  userStore.initFromStorage()
}
</script>
