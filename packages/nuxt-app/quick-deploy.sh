#!/bin/bash

# Nuxt应用快速部署脚本
# 使用方法: ./quick-deploy.sh [staging|production]

set -e  # 遇到错误立即退出

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -eq 0 ]; then
    log_error "缺少环境参数"
    echo "使用方法: $0 [staging|production]"
    exit 1
fi

ENVIRONMENT=$1

# 验证环境参数
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    log_error "无效的环境参数: $ENVIRONMENT"
    echo "使用方法: $0 [staging|production]"
    exit 1
fi

log_info "🚀 快速部署 Nuxt 应用到 $ENVIRONMENT 环境..."

# 设置环境变量
export NODE_ENV=$ENVIRONMENT
export DEPLOYMENT_ENV=$ENVIRONMENT

# 一键部署
log_info "停止现有容器..."
docker stop nuxt-app-$ENVIRONMENT > /dev/null 2>&1 || true
docker rm nuxt-app-$ENVIRONMENT > /dev/null 2>&1 || true

log_info "构建 Docker 镜像..."
docker build -t nuxt-app:$ENVIRONMENT .

log_info "启动容器..."
docker run -d \
  --name nuxt-app-$ENVIRONMENT \
  --restart unless-stopped \
  -p 3000:3000 \
  --env-file .env.$ENVIRONMENT \
  nuxt-app:$ENVIRONMENT

log_info "等待服务启动..."
sleep 15

# 简单健康检查
if curl -f http://localhost:3000/ > /dev/null 2>&1; then
    log_success "✅ 部署成功!"
    echo ""
    echo "🌐 访问地址:"
    echo "   http://*************:3000"
    echo ""
    echo "📊 容器状态:"
    docker ps --filter "name=nuxt-app-$ENVIRONMENT"
else
    log_error "❌ 部署失败 - 服务未响应"
    echo ""
    echo "📋 查看日志:"
    docker logs --tail=20 nuxt-app-$ENVIRONMENT
    exit 1
fi

log_success "🎉 快速部署完成!"
