// 主题变量定义
:root {
  // 基础颜色
  --accent-color: #ca93f2;
  --accent-hover: #b87de8;
  --accent-bg: rgba(202, 147, 242, 0.2);
  
  // 文本颜色
  --text-primary: rgba(255, 255, 255, 0.9);
  --text-secondary: rgba(255, 255, 255, 0.7);
  --text-tertiary: rgba(255, 255, 255, 0.5);
  --text-on-accent: rgba(0, 0, 0, 0.85);
  
  // 背景颜色
  --bg-primary: #1f0038;
  --bg-secondary: rgba(255, 255, 255, 0.1);
  --bg-tertiary: rgba(255, 255, 255, 0.05);
  --bg-hover: rgba(255, 255, 255, 0.1);
  
  // 移动端特定背景
  --mobile-app-bg: #1f0038;
  --mobile-bg-gradient-start: #1f0038;
  
  // 边框颜色
  --border-color: rgba(255, 255, 255, 0.1);
  
  // 阴影
  --shadow-color: rgba(0, 0, 0, 0.3);
  
  // 移动端输入框
  --mobile-input-bg: rgba(255, 255, 255, 0.1);
  --mobile-input-border: rgba(255, 255, 255, 0.2);
  
  // 成功、错误、警告颜色
  --success-color: #52c41a;
  --success-bg: rgba(82, 196, 26, 0.1);
  --error-color: #ff4d4f;
  --error-bg: rgba(255, 77, 79, 0.1);
  --info-color: #1890ff;
  --info-bg: rgba(24, 144, 255, 0.1);
  --warning-color: #faad14;
  --warning-bg: rgba(250, 173, 20, 0.1);
}

// 亮色主题
.light-theme {
  // 文本颜色
  --text-primary: rgba(0, 0, 0, 0.85);
  --text-secondary: rgba(0, 0, 0, 0.65);
  --text-tertiary: rgba(0, 0, 0, 0.45);
  --text-on-accent: rgba(255, 255, 255, 0.9);
  
  // 背景颜色
  --bg-primary: #ffffff;
  --bg-secondary: rgba(0, 0, 0, 0.05);
  --bg-tertiary: rgba(0, 0, 0, 0.03);
  --bg-hover: rgba(0, 0, 0, 0.05);
  
  // 移动端特定背景
  --mobile-app-bg: #f5f5f5;
  --mobile-bg-gradient-start: #f5f5f5;
  
  // 边框颜色
  --border-color: rgba(0, 0, 0, 0.1);
  
  // 阴影
  --shadow-color: rgba(0, 0, 0, 0.1);
  
  // 移动端输入框
  --mobile-input-bg: rgba(0, 0, 0, 0.03);
  --mobile-input-border: rgba(0, 0, 0, 0.1);
  
  // 成功、错误、警告颜色
  --success-bg: #f6ffed;
  --error-bg: #fff2f0;
  --info-bg: #e6f7ff;
  --warning-bg: #fffbe6;
}

// 暗色主题
.dark-theme {
  // 保持默认的暗色主题变量
}

// 全局样式
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

// 移动端视口高度修复
@supports (height: 100dvh) {
  .mobile-full-height {
    height: 100dvh;
  }
}

@supports not (height: 100dvh) {
  .mobile-full-height {
    height: calc(var(--vh, 1vh) * 100);
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

// 选择文本样式
::selection {
  background: var(--accent-color);
  color: var(--text-on-accent);
}

// 焦点样式
:focus-visible {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

// 按钮重置
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

// 输入框重置
input, textarea {
  border: none;
  background: none;
  font-family: inherit;
  color: inherit;
}

input:focus, textarea:focus {
  outline: none;
}

// 链接样式
a {
  color: var(--accent-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

// 图片样式
img {
  max-width: 100%;
  height: auto;
}

// 响应式断点
@media (max-width: 768px) {
  .mobile-only {
    display: block;
  }
  
  .desktop-only {
    display: none;
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none;
  }
  
  .desktop-only {
    display: block;
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(100%);
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.z-10 {
  z-index: 10;
}

.z-50 {
  z-index: 50;
}

.z-100 {
  z-index: 100;
}

.z-1000 {
  z-index: 1000;
}
