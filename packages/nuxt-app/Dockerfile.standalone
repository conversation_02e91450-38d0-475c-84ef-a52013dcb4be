# Nuxt应用独立部署 Dockerfile
FROM dockerpull.pw/node:18-alpine AS base

# 安装pnpm并配置国内镜像源
RUN npm install -g pnpm && \
    pnpm config set registry https://registry.npmmirror.com

# 设置工作目录
WORKDIR /app

# 复制package文件 (独立部署模式)
COPY package.json ./
COPY pnpm-lock.yaml* ./

# 安装依赖
RUN pnpm install --no-frozen-lockfile

# 复制源代码
COPY . .

# 构建应用
ARG NODE_ENV=production
ARG DEPLOYMENT_ENV=production
ENV NODE_ENV=$NODE_ENV
ENV DEPLOYMENT_ENV=$DEPLOYMENT_ENV

RUN pnpm build

# 生产阶段
FROM dockerpull.pw/node:18-alpine AS production

# 安装pnpm并配置国内镜像源
RUN npm install -g pnpm && \
    pnpm config set registry https://registry.npmmirror.com

# 设置工作目录
WORKDIR /app

# 复制构建产物和必要文件
COPY --from=base /app/.output ./
COPY --from=base /app/package.json ./

# 设置环境变量
ENV NODE_ENV=production
ENV NITRO_PORT=3000
ENV NITRO_HOST=0.0.0.0

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["node", "server/index.mjs"]
