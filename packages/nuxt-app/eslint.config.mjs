// @ts-check
import withNuxt from './.nuxt/eslint.config.mjs'

export default withNuxt({
  rules: {
    // 放宽一些规则以减少错误
    '@typescript-eslint/no-explicit-any': 'off', // 完全关闭 any 类型检查
    '@typescript-eslint/no-unused-vars': [
      'error',
      {
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_'
      }
    ],
    '@typescript-eslint/unified-signatures': 'warn',
    '@typescript-eslint/no-extraneous-class': 'warn',
    '@typescript-eslint/ban-ts-comment': 'warn',
    'no-prototype-builtins': 'warn',
    'no-self-assign': 'warn',
    'vue/no-v-html': 'warn',
    'vue/require-default-prop': 'warn',
    'vue/no-required-prop-with-default': 'warn'
  }
})
