/**
 * Chat4 Landing Page Configuration
 * 配置不同角色的落地页路由和故事信息
 * Chat4 面向女性用户，使用清新温柔的设计风格
 */

export interface Chat4LandingConfig {
  storyId: string
  storyQuote?: string
  characterKey: string // For reporting events (e.g. 'elliot', 'alex')
  otherStoryIds: string[]
  showStoryIntro?: boolean
  characterName: string
  characterAvatar?: string
  backgroundImage?: string
  themeColor?: string // 角色专属主题色（紫色/粉色系）
}

interface StoryConfig {
  id: string
  quote: string
  characterName?: string
  characterAvatar?: string
  backgroundImage?: string
  themeColor?: string
}

interface Chat4CharacterConfig {
  key: string
  name: string
  storyId: {
    test: string | StoryConfig
    prod: string | StoryConfig
  }
  showStoryIntro?: boolean
  characterAvatar?: string
  backgroundImage?: string
  themeColor?: string // 角色专属主题色
}

// 环境判断
const isTest = process.env.NODE_ENV === 'development'

// Chat4 专用男性角色配置（面向女性用户） - 使用真实API数据
export const chat4CharacterConfigs: Chat4CharacterConfig[] = [
  {
    key: 'elliot-vesper',
    name: '<PERSON> Vesper',
    storyId: {
      test: {
        id: '5ffc96ac-41ac-4dde-b96d-e01d93a0e5c6',
        quote:
          'After losing both parents, I found my calling in helping others heal their emotional wounds.',
        characterName: '<PERSON> Vesper',
        characterAvatar:
          'https://static.reelplay.ai/story/Elliot/image/avatar-1.png',
        themeColor: '#b59c87',
      },
      prod: {
        id: '5ffc96ac-41ac-4dde-b96d-e01d93a0e5c6',
        quote:
          'After losing both parents, I found my calling in helping others heal their emotional wounds.',
        characterName: 'Elliot Vesper',
        characterAvatar:
          'https://static.reelplay.ai/story/Elliot/image/avatar-1.png',
        themeColor: '#b59c87',
      },
    },
    characterAvatar:
      'https://static.reelplay.ai/story/Elliot/image/avatar-1.png',
    themeColor: '#b59c87',
    showStoryIntro: true,
  },
  {
    key: 'finn-harper',
    name: 'Finn Harper',
    storyId: {
      test: {
        id: 'cc19c6d7-1c6c-48a6-b9d0-d1212ff7de3b',
        quote:
          'I want everyone to feel that fitness is a love letter to themselves.',
        characterName: 'Finn Harper',
        characterAvatar:
          'https://static.reelplay.ai/story/Finn/image/avatar.png',
        themeColor: '#fde047',
      },
      prod: {
        id: 'cc19c6d7-1c6c-48a6-b9d0-d1212ff7de3b',
        quote:
          'I want everyone to feel that fitness is a love letter to themselves.',
        characterName: 'Finn Harper',
        characterAvatar:
          'https://static.reelplay.ai/story/Finn/image/avatar.png',
        themeColor: '#fde047',
      },
    },
    characterAvatar: 'https://static.reelplay.ai/story/Finn/image/avatar.png',
    themeColor: '#fde047',
    showStoryIntro: true,
  },
  {
    key: 'jude-carver',
    name: 'Jude "Blaze" Carver',
    storyId: {
      test: {
        id: '435eb313-f4ad-4750-b2bf-f2784df8094b',
        quote:
          "I ride to the Brooklyn Bridge at night, staring at the river, pondering life's meaning.",
        characterName: 'Jude "Blaze" Carver',
        characterAvatar:
          'https://static.reelplay.ai/story/Jude/image/avatar.png',
        themeColor: '#d97757',
      },
      prod: {
        id: '435eb313-f4ad-4750-b2bf-f2784df8094b',
        quote:
          "I ride to the Brooklyn Bridge at night, staring at the river, pondering life's meaning.",
        characterName: 'Jude "Blaze" Carver',
        characterAvatar:
          'https://static.reelplay.ai/story/Jude/image/avatar.png',
        themeColor: '#d97757',
      },
    },
    characterAvatar: 'https://static.reelplay.ai/story/Jude/image/avatar.png',
    themeColor: '#d97757',
    showStoryIntro: true,
  },
  {
    key: 'abner-rivera',
    name: 'Abner "Spark" Rivera',
    storyId: {
      test: {
        id: '578a7073-1cb2-4633-bd98-7f19e829eef3',
        quote:
          'Roguishly charming and humorous, but I crave understanding while being cautious in relationships.',
        characterName: 'Abner "Spark" Rivera',
        characterAvatar:
          'https://static.reelplay.ai/story/Abner/image/avatar.png',
        themeColor: '#dc2626',
      },
      prod: {
        id: '578a7073-1cb2-4633-bd98-7f19e829eef3',
        quote:
          'Roguishly charming and humorous, but I crave understanding while being cautious in relationships.',
        characterName: 'Abner "Spark" Rivera',
        characterAvatar:
          'https://static.reelplay.ai/story/Abner/image/avatar.png',
        themeColor: '#dc2626',
      },
    },
    characterAvatar: 'https://static.reelplay.ai/story/Abner/image/avatar.png',
    themeColor: '#dc2626',
    showStoryIntro: true,
  },
  {
    key: 'theodore-vandermeer',
    name: 'Theodore Vandermeer',
    storyId: {
      test: {
        id: 'c20dce6c-99b9-4cbd-ae9d-9cf89b632a09',
        quote:
          'Life is a stage, and I was born for the spotlight. Elegantly ruthless, yet protective.',
        characterName: 'Theodore Vandermeer',
        characterAvatar:
          'https://static.reelplay.ai/story/Theo/image/avatar.png',
        themeColor: '#7c3aed',
      },
      prod: {
        id: 'c20dce6c-99b9-4cbd-ae9d-9cf89b632a09',
        quote:
          'Life is a stage, and I was born for the spotlight. Elegantly ruthless, yet protective.',
        characterName: 'Theodore Vandermeer',
        characterAvatar:
          'https://static.reelplay.ai/story/Theo/image/avatar.png',
        themeColor: '#7c3aed',
      },
    },
    characterAvatar: 'https://static.reelplay.ai/story/Theo/image/avatar.png',
    themeColor: '#7c3aed',
    showStoryIntro: true,
  },
  {
    key: 'ethan-carter',
    name: 'Ethan Carter',
    storyId: {
      test: {
        id: 'df8131bc-2727-417c-8b79-4f0bafc539dc',
        quote:
          'My strict prosecutor persona clashes with my soft, sensitive inner self.',
        characterName: 'Ethan Carter',
        characterAvatar:
          'https://static.reelplay.ai/story/Ethan/image/avatar.png',
        themeColor: '#1f2937',
      },
      prod: {
        id: 'df8131bc-2727-417c-8b79-4f0bafc539dc',
        quote:
          'My strict prosecutor persona clashes with my soft, sensitive inner self.',
        characterName: 'Ethan Carter',
        characterAvatar:
          'https://static.reelplay.ai/story/Ethan/image/avatar.png',
        themeColor: '#1f2937',
      },
    },
    characterAvatar: 'https://static.reelplay.ai/story/Ethan/image/avatar.png',
    themeColor: '#1f2937',
    showStoryIntro: true,
  },
]

// 获取当前环境的 storyId
const getStoryId = (config: Chat4CharacterConfig) => {
  const storyId = config.storyId[isTest ? 'test' : 'prod']
  return typeof storyId === 'string' ? storyId : storyId.id
}

// 获取故事配置
const getStoryConfig = (config: Chat4CharacterConfig) => {
  const storyConfig = config.storyId[isTest ? 'test' : 'prod']
  return typeof storyConfig === 'string' ? null : storyConfig
}

// 获取所有 storyIds
const getAllStoryIds = () =>
  chat4CharacterConfigs.map((config) => getStoryId(config))

// 生成最终配置
export const chat4LandingConfigs: Record<string, Chat4LandingConfig> =
  chat4CharacterConfigs.reduce(
    (acc, config) => {
      const currentStoryId = getStoryId(config)
      const storyConfig = getStoryConfig(config)
      const allStoryIds = getAllStoryIds()

      acc[config.key] = {
        storyId: currentStoryId,
        storyQuote: storyConfig?.quote,
        characterKey: config.key,
        characterName: storyConfig?.characterName || config.name,
        characterAvatar: storyConfig?.characterAvatar || config.characterAvatar,
        backgroundImage: storyConfig?.backgroundImage || config.backgroundImage,
        themeColor: storyConfig?.themeColor || config.themeColor,
        otherStoryIds: allStoryIds.filter((id) => id !== currentStoryId),
        ...(config.showStoryIntro ? { showStoryIntro: true } : {}),
      }

      return acc
    },
    {} as Record<string, Chat4LandingConfig>,
  )

// 获取配置的辅助函数
export const getChat4Config = (
  characterKey: string,
): Chat4LandingConfig | null => {
  return chat4LandingConfigs[characterKey] || null
}

// 获取所有可用的角色键
export const getAvailableCharacterKeys = (): string[] => {
  return Object.keys(chat4LandingConfigs)
}

// 获取主要故事（第一个配置的故事）
export const getMainStoryConfig = (): Chat4LandingConfig | null => {
  const firstKey = getAvailableCharacterKeys()[0]
  return firstKey ? getChat4Config(firstKey) : null
}
