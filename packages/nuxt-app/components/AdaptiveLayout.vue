<template>
  <div class="adaptive-layout">
    <!-- PC端布局 - 使用CSS媒体查询控制显示 -->
    <div class="pc-layout">
      <slot name="pc" :device-info="deviceInfo">
        <!-- 默认PC端内容 -->
        <slot :device-info="deviceInfo" />
      </slot>
    </div>

    <!-- 移动端布局 - 使用CSS媒体查询控制显示 -->
    <div class="mobile-layout">
      <slot name="mobile" :device-info="deviceInfo">
        <!-- 默认移动端内容 -->
        <slot :device-info="deviceInfo" />
      </slot>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue'

// Props
const props = defineProps({
  // 强制使用特定布局
  forceLayout: {
    type: String,
    validator: (value) => ['pc', 'mobile', 'auto'].includes(value),
    default: 'auto'
  }
})

// 简化的设备检测状态
const screenWidth = ref(0)
const isMounted = ref(false)

// 计算是否应该使用PC布局（仅用于JavaScript增强）
const shouldUsePCLayout = computed(() => {
  if (props.forceLayout === 'pc') return true
  if (props.forceLayout === 'mobile') return false

  // 在客户端且已挂载后，使用屏幕宽度判断
  if (isMounted.value && import.meta.client) {
    return screenWidth.value >= 1024
  }

  // 默认返回false，让CSS媒体查询控制显示
  return false
})

// 简化的设备信息
const deviceInfo = computed(() => ({
  screenWidth: screenWidth.value,
  shouldUsePCLayout: shouldUsePCLayout.value,
  isMounted: isMounted.value
}))

// 更新屏幕宽度
const updateScreenWidth = () => {
  if (import.meta.client) {
    screenWidth.value = window.innerWidth
  }
}

onMounted(() => {
  isMounted.value = true
  updateScreenWidth()

  if (import.meta.client) {
    window.addEventListener('resize', updateScreenWidth)
  }
})

// 暴露给父组件
defineExpose({
  deviceInfo,
  shouldUsePCLayout
})
</script>

<style scoped>
.adaptive-layout {
  width: 100%;
  height: 100%;
}

.pc-layout {
  width: 100%;
  height: 100%;
  min-height: 100vh;
}

.mobile-layout {
  width: 100%;
  height: 100%;
  min-height: 100vh;
}

/* 自适应布局容器 */
.adaptive-layout {
  width: 100%;
  height: 100%;
}

.pc-layout,
.mobile-layout {
  width: 100%;
  height: 100%;
}

/* CSS媒体查询控制显示 - 避免SSR闪现 */
@media (max-width: 1023px) {
  .pc-layout {
    display: none !important;
  }
}

@media (min-width: 1024px) {
  .mobile-layout {
    display: none !important;
  }
}
</style>
