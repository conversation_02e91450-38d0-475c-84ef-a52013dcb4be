<template>
  <aside class="sidebar" :class="{ collapsed }">
    <nav class="nav-menu">
      <div
        class="nav-item"
        :class="{ active: $route.path === '/' || $route.path === '/stories' }"
        @click="$router.push('/')"
      >
        <Icon name="lucide:home" :size="20" />
        <span v-show="!collapsed">Home</span>
      </div>
      <div
        class="nav-item"
        :class="{ active: $route.path === '/user/profile' }"
        @click="
          !userStore.isLoggedIn || userStore.isGuest
            ? showAuthModal()
            : $router.push('/user/profile')
        "
      >
        <Icon name="lucide:user" :size="20" />
        <span v-show="!collapsed">Account</span>
      </div>
    </nav>

    <div class="sidebar-bottom">
      <!-- 每日奖励按钮 -->
      <button
        class="daily-reward-btn"
        type="button"
        :aria-label="
          userStore.isLoggedIn && !userStore.isGuest
            ? 'Claim daily reward'
            : 'Sign up to claim daily rewards'
        "
        @click="handleDailyReward"
      >
        <span role="img" aria-label="Gift emoji">🎁</span>
        <span v-if="!collapsed" class="reward-text" :class="{ 'collapsed-text': collapsed }"
          >Daily Reward</span
        >
      </button>

      <!-- Discord链接 (仅ReelPlay) -->
      <div v-if="appName === 'ReelPlay'" class="join-discord">
        <a
          href="https://discord.gg/FdZJmU4a8x"
          target="_blank"
          rel="noopener noreferrer"
          class="discord-btn"
          @click="handleDiscord"
        >
          <svg class="discord-icon" viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"
            />
          </svg>
          <span v-if="!collapsed" :class="{ 'collapsed-text': collapsed }">Join Our Discord</span>
          <svg
            v-if="!collapsed"
            class="arrow-icon"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <polyline points="9 18 15 12 9 6" />
          </svg>
        </a>
      </div>

      <!-- 收起/展开按钮 -->
      <button
        class="sidebar-toggle"
        type="button"
        :aria-label="collapsed ? 'Expand sidebar menu' : 'Collapse sidebar menu'"
        :aria-expanded="!collapsed"
        @click="toggleSidebar"
        @keydown.enter="toggleSidebar"
        @keydown.space.prevent="toggleSidebar"
      >
        <svg
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          :class="{ 'rotate-180': collapsed }"
        >
          <polyline points="15 18 9 12 15 6" />
        </svg>
        <span v-if="!collapsed" :class="{ 'collapsed-text': collapsed }">Hide Menu</span>
      </button>

      <!-- Terms text - only show when sidebar is expanded -->
      <div v-if="!collapsed" class="terms-text">
        By signing in you agree with our<br >
        <NuxtLink to="/terms">Terms of Service</NuxtLink>,
        <NuxtLink to="/privacy">Privacy Policy</NuxtLink>,
        <NuxtLink to="/complaints">Complaints Policy</NuxtLink>,
        <NuxtLink to="/content-removal">Content Removal Policy</NuxtLink>,
        <NuxtLink to="/record-keeping">18 U.S.C. 2257 Compliance</NuxtLink>,
        <NuxtLink to="/about">About Us</NuxtLink>,
        <NuxtLink to="/refund">Refund and Returns Policy</NuxtLink>.
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
// 定义props
interface Props {
  collapsed?: boolean
}

withDefaults(defineProps<Props>(), {
  collapsed: false
})

// 定义事件
const emit = defineEmits(['toggle-collapse', 'show-auth', 'show-checkin'])

// 运行时配置
const config = useRuntimeConfig()
const appName = config.public?.appName || 'Playshot'

// 用户状态管理
const userStore = useUserStore()

// 切换侧边栏状态
const toggleSidebar = () => {
  emit('toggle-collapse')
}

// 显示认证模态框
const showAuthModal = () => {
  emit('show-auth')
}

// 处理每日奖励点击
const handleDailyReward = () => {
  if (userStore.isLoggedIn && !userStore.isGuest) {
    // 如果已登录且不是游客，显示签到弹窗
    emit('show-checkin')
  } else {
    // 如果未登录或是游客，显示登录弹窗
    showAuthModal()
  }
}

// 处理Discord链接点击
const handleDiscord = () => {
  // Discord链接点击统计
  console.log('Discord link clicked')
}
</script>

<style lang="less" scoped>
.sidebar {
  width: 240px;
  height: 100%;
  background-color: var(--sidebar-bg);
  border-right: 1px solid var(--divider-color);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  overflow: hidden;

  &.collapsed {
    width: 70px;

    .nav-item {
      justify-content: center;
      margin: 0 8px;

      span {
        flex-shrink: 0;
      }

      svg {
        flex-shrink: 0;
      }
    }
  }

  .nav-menu {
    flex: 1;
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .nav-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: var(--pc-sidebar-item-padding);
      margin: 0 16px;
      border-radius: var(--pc-sidebar-item-radius);
      color: var(--pc-sidebar-text);
      cursor: pointer;
      transition: all 0.2s ease;
      white-space: nowrap;
      font-size: 14px;
      font-weight: 500;

      &:hover {
        background-color: var(--pc-sidebar-hover-bg);
        color: var(--text-primary);
      }

      &.active {
        background-color: var(--pc-sidebar-active-bg);
        color: var(--pc-sidebar-active-text);
      }

      span {
        transition: opacity 0.3s ease;
      }
    }
  }

  .sidebar-bottom {
    padding: 20px 16px 24px 16px;
    border-top: 1px solid var(--divider-color);
    display: flex;
    flex-direction: column;
    gap: 12px;

    .daily-reward-btn {
      display: flex;
      align-items: center;
      gap: 10px;
      width: 100%;
      padding: 8px 24px;
      border-radius: 20px;
      background-color: var(--accent-bg);
      color: var(--text-primary);
      border: none;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition:
        all 0.2s ease,
        padding 0.3s ease;
      position: relative;

      &:hover {
        background-color: rgba(202, 147, 242, 0.3);
      }

      .reward-text {
        flex: 1;
        text-align: left;
        color: var(--text-primary);
        font-family: 'Work Sans', sans-serif;
      }

      img,
      span:first-child {
        font-size: 24px;
        line-height: 1.5;
        min-width: 24px; // 确保图标不会被压缩
      }
    }

    .join-discord {
      .discord-btn {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 8px;
        padding: 12px 16px;
        border-radius: 20px;
        background: #5865f2;
        color: white;
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease;
        white-space: nowrap;
        min-height: 44px;

        &:hover {
          background: #4752c4;
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }

        .discord-icon {
          width: 20px;
          height: 20px;
          flex-shrink: 0;
        }

        .arrow-icon {
          width: 16px;
          height: 16px;
          flex-shrink: 0;
        }
      }
    }

    .terms-text {
      margin-top: 16px;
      font-size: 12px;
      text-align: center;
      color: var(--text-tertiary);
      line-height: 1.17;

      a {
        color: var(--text-tertiary);
        text-decoration: underline;

        &:hover {
          color: var(--text-secondary);
        }
      }
    }

    .sidebar-toggle {
      display: flex;
      align-items: center;
      // justify-content: center;
      gap: 8px;
      padding: 12px 16px;
      border-radius: 20px;
      background: transparent;
      color: var(--pc-sidebar-text);
      border: 1px solid var(--divider-color);
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s ease;
      white-space: nowrap;
      min-height: 44px;

      &:hover {
        background: var(--pc-sidebar-hover-bg);
        color: var(--text-primary);
      }

      &:active {
        transform: scale(0.98);
      }

      svg {
        width: 20px;
        height: 20px;
        transition: transform 0.3s ease;
        flex-shrink: 0;

        &.rotate-180 {
          transform: rotate(180deg);
        }
      }
    }
  }

  // 折叠状态下的底部按钮样式
  &.collapsed {
    .reward-text,
    .discord-btn span,
    .sidebar-toggle span {
      display: none;
    }

    .sidebar-bottom {
      padding: 12px;

      .daily-reward-btn,
      .discord-btn {
        padding: 12px;
        justify-content: center;
      }

      .sidebar-toggle {
        padding: 12px;
        justify-content: center;
      }
    }
  }
}

// 亮色主题适配
:global(.light-theme) .sidebar {
  background-color: var(--bg-secondary, rgba(0, 0, 0, 0.02));
  border-right-color: var(--border-color, rgba(0, 0, 0, 0.1));

  .nav-item {
    color: var(--text-secondary, rgba(0, 0, 0, 0.65));

    &:hover {
      background-color: var(--bg-hover, rgba(0, 0, 0, 0.05));
      color: var(--text-primary, rgba(0, 0, 0, 0.85));
    }

    &.active {
      background-color: var(--accent-color, #ca93f2);
      color: var(--text-on-accent, rgba(0, 0, 0, 0.85));
    }
  }

  .sidebar-footer {
    border-top-color: var(--border-color, rgba(0, 0, 0, 0.1));

    .footer-link {
      color: var(--text-tertiary, rgba(0, 0, 0, 0.45));

      &:hover {
        background-color: var(--bg-hover, rgba(0, 0, 0, 0.03));
        color: var(--text-secondary, rgba(0, 0, 0, 0.65));
      }
    }
  }

  .collapse-btn {
    background-color: var(--bg-secondary, rgba(0, 0, 0, 0.05));
    border-color: var(--border-color, rgba(0, 0, 0, 0.15));
    color: var(--text-secondary, rgba(0, 0, 0, 0.65));

    &:hover {
      background-color: var(--bg-hover, rgba(0, 0, 0, 0.1));
      color: var(--text-primary, rgba(0, 0, 0, 0.85));
    }
  }
}
</style>
