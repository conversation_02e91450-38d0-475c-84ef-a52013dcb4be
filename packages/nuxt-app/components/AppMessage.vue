<template>
  <Teleport to="body">
    <TransitionGroup name="message" tag="div" class="message-container">
      <div
        v-for="msg in messages"
        :key="msg.id"
        class="message"
        :class="msg.type"
        @click="removeMessage(msg.id)"
      >
        <div class="message-backdrop" />
        <div class="message-content">
          <div class="message-icon-wrapper">
            <div class="message-icon">
              <Icon v-if="msg.type === 'success'" name="lucide:check-circle" size="16" />
              <Icon v-if="msg.type === 'error'" name="lucide:x-circle" size="16" />
              <Icon v-if="msg.type === 'info'" name="lucide:info" size="16" />
              <Icon v-if="msg.type === 'warning'" name="lucide:alert-triangle" size="16" />
            </div>
          </div>
          <div class="message-body">
            <span class="message-text">{{ msg.content }}</span>
          </div>
          <button class="message-close" @click.stop="removeMessage(msg.id)">
            <Icon name="lucide:x" size="14" />
          </button>
        </div>
        <div class="message-progress" :style="{ animationDuration: msg.duration + 'ms' }" />
      </div>
    </TransitionGroup>
  </Teleport>
</template>

<script setup lang="ts">
export interface MessageItem {
  id: number
  content: string
  type: 'success' | 'error' | 'info' | 'warning'
  duration: number
  timeout?: NodeJS.Timeout
}

const messages = ref<MessageItem[]>([])
let messageId = 0

const addMessage = (content: string, type: MessageItem['type'], duration = 4000) => {
  const id = messageId++
  const message: MessageItem = {
    id,
    content,
    type,
    duration
  }

  messages.value.push(message)

  // 设置自动移除
  message.timeout = setTimeout(() => {
    removeMessage(id)
  }, duration)

  return id
}

const removeMessage = (id: number) => {
  const index = messages.value.findIndex((msg) => msg.id === id)
  if (index !== -1) {
    const message = messages.value[index]
    if (message.timeout) {
      clearTimeout(message.timeout)
    }
    messages.value.splice(index, 1)
  }
}

const success = (content: string, duration = 4000) => {
  return addMessage(content, 'success', duration)
}

const error = (content: string, duration = 5000) => {
  return addMessage(content, 'error', duration)
}

const info = (content: string, duration = 4000) => {
  return addMessage(content, 'info', duration)
}

const warning = (content: string, duration = 4000) => {
  return addMessage(content, 'warning', duration)
}

const clear = () => {
  messages.value.forEach((msg) => {
    if (msg.timeout) {
      clearTimeout(msg.timeout)
    }
  })
  messages.value = []
}

// 限制最大消息数量
watch(
  messages,
  (newMessages) => {
    if (newMessages.length > 5) {
      const excessive = newMessages.slice(0, newMessages.length - 5)
      excessive.forEach((msg) => {
        if (msg.timeout) {
          clearTimeout(msg.timeout)
        }
      })
      messages.value = newMessages.slice(-5)
    }
  },
  { deep: true }
)

// 组件挂载时注册到全局
onMounted(async () => {
  try {
    const { setMessageInstance } = await import('~/composables/useMessage')
    setMessageInstance({
      success,
      error,
      info,
      warning,
      clear
    })
  } catch (error) {
    console.error('Failed to register message instance:', error)
  }
})

// 组件卸载时清理定时器和全局实例
onBeforeUnmount(async () => {
  clear()
  // 清除全局实例引用
  try {
    const { clearMessageInstance } = await import('~/composables/useMessage')
    clearMessageInstance()
  } catch (error) {
    console.error('Failed to clear message instance:', error)
  }
})

defineExpose({
  success,
  error,
  info,
  warning,
  clear
})
</script>

<style lang="less" scoped>
.message-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  width: 320px;
  max-width: calc(100vw - 40px);
  pointer-events: none;
  display: flex;
  flex-direction: column;
  gap: 12px;

  @media (max-width: 768px) {
    width: calc(100vw - 32px);
    max-width: 280px;
  }
}

.message {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  pointer-events: auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
      0 32px 64px rgba(0, 0, 0, 0.15),
      0 16px 32px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }

  &.success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
    border-color: rgba(16, 185, 129, 0.3);

    .message-icon-wrapper {
      background: linear-gradient(135deg, #10b981, #059669);
    }

    .message-icon {
      color: white;
    }
  }

  &.error {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
    border-color: rgba(239, 68, 68, 0.3);

    .message-icon-wrapper {
      background: linear-gradient(135deg, #ef4444, #dc2626);
    }

    .message-icon {
      color: white;
    }
  }

  &.info {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.05) 100%);
    border-color: rgba(59, 130, 246, 0.3);

    .message-icon-wrapper {
      background: linear-gradient(135deg, #3b82f6, #2563eb);
    }

    .message-icon {
      color: white;
    }
  }

  &.warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%);
    border-color: rgba(245, 158, 11, 0.3);

    .message-icon-wrapper {
      background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    .message-icon {
      color: white;
    }
  }
}

.message-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  z-index: -1;
}

.message-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 18px;
  position: relative;
  z-index: 1;
}

.message-icon-wrapper {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 10px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    z-index: -1;
  }
}

.message-icon {
  flex-shrink: 0;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.message-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.message-text {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.3;
  color: rgba(0, 0, 0, 0.85);
  word-break: break-word;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.message-close {
  flex-shrink: 0;
  background: rgba(0, 0, 0, 0.05);
  border: none;
  color: rgba(0, 0, 0, 0.4);
  cursor: pointer;
  padding: 6px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);

  &:hover {
    background: rgba(0, 0, 0, 0.1);
    color: rgba(0, 0, 0, 0.7);
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }
}

.message-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 4px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%);
  width: 100%;
  transform-origin: left;
  animation: progressShrink linear;
  border-radius: 0 0 16px 16px;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
}

// Mobile optimizations
@media (max-width: 768px) {
  .message-content {
    padding: 14px 16px;
    gap: 10px;
  }

  .message-icon-wrapper {
    width: 28px;
    height: 28px;
    border-radius: 8px;
  }

  .message-text {
    font-size: 13px;
  }

  .message-close {
    padding: 5px;
  }
}

// Animations
.message-enter-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.message-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.message-enter-from {
  opacity: 0;
  transform: translateY(-20px) scale(0.9);
  filter: blur(4px);
}

.message-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(0.9);
  filter: blur(4px);
}

.message-move {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes progressShrink {
  from {
    transform: scaleX(1);
    opacity: 0.8;
  }
  to {
    transform: scaleX(0);
    opacity: 0.3;
  }
}

// Dark theme support
:global(.dark) {
  .message {
    background: rgba(30, 30, 30, 0.95);
    border-color: rgba(255, 255, 255, 0.1);

    .message-text {
      color: rgba(255, 255, 255, 0.9);
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }

    .message-close {
      background: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.6);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }

  .message-backdrop {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%);
  }
}
</style>
