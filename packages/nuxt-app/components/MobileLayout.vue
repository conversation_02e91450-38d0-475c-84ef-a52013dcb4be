<template>
  <div class="mobile-layout">
    <div
      class="page-container"
      :class="{ 'has-menu': showMenu }"
      :style="{ '--dynamic-menu-height': dynamicMenuHeight }"
    >
      <slot />
    </div>
    <div v-if="showMenu" ref="menuContainer" class="menu-container">
      <Menu />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import Menu from './AppMenu.vue'

const route = useRoute()
const menuContainer = ref<HTMLElement | null>(null)
const dynamicMenuHeight = ref('50px')

// 定义哪些页面显示底部菜单
const showMenu = computed(() => {
  // 默认显示菜单，除非路由明确设置不显示
  if (route.meta.showMenu === false) {
    return false
  }

  // 聊天相关页面不显示底部菜单
  if (
    route.path.startsWith('/chat') ||
    route.path.startsWith('/chat2') ||
    route.path.startsWith('/chat3') ||
    route.path.startsWith('/chat4')
  ) {
    return false
  }

  // 其他页面显示菜单
  return true
})

// 动态计算菜单高度
const updateMenuHeight = () => {
  if (menuContainer.value && showMenu.value) {
    nextTick(() => {
      const height = menuContainer.value?.offsetHeight || 50
      dynamicMenuHeight.value = `${height}px`
    })
  }
}

// 监听菜单显示状态变化
watch(
  showMenu,
  () => {
    if (showMenu.value) {
      nextTick(() => updateMenuHeight())
    }
  },
  { immediate: true }
)

onMounted(() => {
  updateMenuHeight()
  // 监听窗口大小变化
  window.addEventListener('resize', updateMenuHeight)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateMenuHeight)
})
</script>

<style lang="less" scoped>
.mobile-layout {
  height: calc(var(--vh, 1vh) * 100);
  background: linear-gradient(
    180deg,
    var(--mobile-bg-gradient-start, var(--bg-primary)) 0%,
    var(--mobile-bg-gradient-end, var(--bg-secondary)) 100%
  );
  position: relative;
  width: 100%;
  margin: 0 auto;
  transition: background 0.3s ease;
}

.page-container {
  position: relative;
  height: calc(var(--vh, 1vh) * 100);
  width: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;

  &.has-menu {
    height: calc(var(--vh, 1vh) * 100 - var(--dynamic-menu-height, 50px));
  }
}

.menu-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  min-height: 50px;
  height: auto;
  background: var(--mobile-menu-bg, var(--bg-secondary));
}
</style>
