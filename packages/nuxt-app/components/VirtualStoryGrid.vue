<template>
  <div
    ref="containerRef"
    class="virtual-story-grid"
    :class="{ 'is-pc': isPc, 'is-mobile': !isPc, 'page-scroll': usePageScroll }"
    :style="containerStyle"
    @scroll="usePageScroll ? undefined : handleScroll"
  >
    <!-- 虚拟滚动内容容器 -->
    <div class="virtual-content" :style="{ height: totalHeight + 'px', position: 'relative' }">
      <!-- 渲染可见的行 -->
      <div
        v-for="rowIndex in visibleRows"
        :key="`row-${rowIndex}`"
        class="story-row"
        :class="rowClass"
        :style="getRowStyle(rowIndex)"
      >
        <StoryCard
          v-for="(story, colIndex) in getRowStories(rowIndex)"
          :key="story?.id || `skeleton-${rowIndex}-${colIndex}`"
          :story="story"
          :is-pc="isPc"
          :loading="loading"
          :lazy-load="true"
          :in-viewport="isStoryInViewport(rowIndex)"
          class="story-card-item"
          @click="$emit('story-click', story)"
          @image-loaded="handleImageLoaded"
          @subscription-change="$emit('subscription-change', $event)"
          @need-login="$emit('need-login')"
          @need-email="$emit('need-email')"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Story {
  id: string
  title: string
  description?: string
  preview_url?: string
  carousel_image_url?: string[]
  badge?: string
  status?: 'normal' | 'preparing' | 'admin_only'
  is_subscribed?: boolean
  is_fav?: boolean
  author?: string
  tags?: string[]
  categories?: string[]
  // 兼容其他字段
  uuid?: string
  cover_url?: string
  image_url?: string
  play_count?: number
  rating?: number
  created_at?: string
  updated_at?: string
}

interface Props {
  stories: Story[]
  isPc?: boolean
  loading?: boolean
  itemHeight?: number
  columnsCount?: number
  gap?: number
  showPerformanceInfo?: boolean
  usePageScroll?: boolean // 新增：是否使用页面滚动而不是容器滚动
}

const props = withDefaults(defineProps<Props>(), {
  isPc: false,
  loading: false,
  itemHeight: 0, // 0 表示自动计算
  columnsCount: 0, // 0 表示自动计算
  gap: 16,
  showPerformanceInfo: false,
  usePageScroll: true // 默认使用页面滚动
})

const emit = defineEmits<{
  'story-click': [story: Story]
  'subscription-change': [story: Story]
  'need-login': []
  'need-email': []
  'image-loaded': []
}>()

// 响应式引用
const containerRef = ref<HTMLElement>()
const scrollTop = ref(0)
const containerHeight = ref(600)
const containerTop = ref(0) // 容器相对于页面顶部的位置
const isSSR = ref(true)
const isDev = ref(false)

// 响应式布局计算
const columns = computed(() => {
  if (props.columnsCount > 0) return props.columnsCount

  if (import.meta.client) {
    const width = window.innerWidth
    if (props.isPc) {
      // PC端自适应列数
      if (width >= 1800) return 6
      if (width >= 1400) return 5
      if (width >= 1200) return 4
      return 3
    } else {
      // 移动端自适应列数
      if (width >= 768) return 3
      if (width >= 480) return 2
      return 2
    }
  }

  // SSR 默认值
  return props.isPc ? 4 : 2
})

const itemHeight = computed(() => {
  if (props.itemHeight > 0) return props.itemHeight

  if (import.meta.client) {
    const width = window.innerWidth
    if (props.isPc) {
      // PC端高度 - 调整为9:16比例 (与移动端一致，更加修长美观)
      if (width >= 1800) return 480
      if (width >= 1400) return 450
      if (width >= 1200) return 420
      return 400
    } else {
      // 移动端高度 - 保持9:16比例
      if (width >= 768) return 320
      if (width >= 480) return 300
      return 280
    }
  }

  // SSR 默认值
  return props.isPc ? 450 : 300
})

// 计算总行数
const totalRows = computed(() => {
  return Math.ceil(props.stories.length / columns.value)
})

// 计算总高度
const totalHeight = computed(() => {
  return totalRows.value * (itemHeight.value + props.gap)
})

// 计算可见行范围
const visibleRows = computed(() => {
  if (isSSR.value) {
    // SSR 时渲染前几行
    return Array.from({ length: Math.min(3, totalRows.value) }, (_, i) => i)
  }

  let viewportTop, viewportBottom

  if (props.usePageScroll) {
    // 页面滚动模式：智能检测滚动容器
    const mainContent = document.querySelector('.main-content')
    const pageContainer = document.querySelector('.page-container')
    const layoutContainer = document.querySelector('.layout-container')

    let scrollContainer = null

    if (mainContent) {
      const style = getComputedStyle(mainContent)
      if (style.overflowY === 'auto' || style.overflow === 'auto' || style.overflowY === 'scroll') {
        scrollContainer = mainContent
      }
    }

    if (!scrollContainer && pageContainer) {
      const style = getComputedStyle(pageContainer)
      if (style.overflowY === 'auto' || style.overflow === 'auto' || style.overflowY === 'scroll') {
        scrollContainer = pageContainer
      }
    }

    if (!scrollContainer && layoutContainer) {
      const style = getComputedStyle(layoutContainer)
      if (style.overflowY === 'auto' || style.overflow === 'auto' || style.overflowY === 'scroll') {
        scrollContainer = layoutContainer
      }
    }

    if (scrollContainer && scrollContainer.scrollTop !== undefined) {
      // 实际是容器滚动，直接使用容器的滚动位置
      viewportTop = scrollTop.value
      viewportBottom = scrollTop.value + containerHeight.value
    } else {
      // 真正的页面滚动
      const pageScrollTop = scrollTop.value
      const viewportHeight = containerHeight.value
      const containerAbsoluteTop = containerTop.value

      viewportTop = pageScrollTop - containerAbsoluteTop
      viewportBottom = pageScrollTop + viewportHeight - containerAbsoluteTop
    }
  } else {
    // 容器滚动模式
    viewportTop = scrollTop.value
    viewportBottom = scrollTop.value + containerHeight.value
  }

  const startRow = Math.floor(viewportTop / (itemHeight.value + props.gap))
  const endRow = Math.min(
    totalRows.value - 1,
    Math.ceil(viewportBottom / (itemHeight.value + props.gap)) + 1
  )

  // 增加缓冲区，防止元素过早消失
  const bufferRows = 4 // 增加缓冲区
  const bufferedStart = Math.max(0, startRow - bufferRows)
  const bufferedEnd = Math.min(totalRows.value - 1, endRow + bufferRows)

  return Array.from({ length: bufferedEnd - bufferedStart + 1 }, (_, i) => bufferedStart + i)
})

// 获取行样式
const getRowStyle = (rowIndex: number) => ({
  position: 'absolute' as const,
  top: `${rowIndex * (itemHeight.value + props.gap)}px`,
  left: '0',
  right: '0',
  height: `${itemHeight.value}px`,
  display: 'grid',
  gridTemplateColumns: `repeat(${columns.value}, 1fr)`,
  gap: `${props.gap}px`,
  padding: props.isPc ? '0 20px' : '0 16px'
})

// 获取行中的故事数据
const getRowStories = (rowIndex: number) => {
  const startIndex = rowIndex * columns.value
  const endIndex = startIndex + columns.value
  const rowStories = props.stories.slice(startIndex, endIndex)

  // 如果是加载状态，填充空对象
  if (props.loading && rowStories.length < columns.value) {
    const emptyStories = Array(columns.value - rowStories.length).fill({
      id: `skeleton-${rowIndex}-${rowStories.length}`,
      title: '',
      status: 'normal'
    })
    return [...rowStories, ...emptyStories]
  }

  return rowStories
}

// 检查故事是否在视口中
const isStoryInViewport = (rowIndex: number) => {
  if (isSSR.value) return rowIndex < 2 // SSR 时前两行视为可见

  const rowTop = rowIndex * (itemHeight.value + props.gap)
  const rowBottom = rowTop + itemHeight.value

  let viewportTop, viewportBottom

  if (props.usePageScroll) {
    // 页面滚动模式：检测是否为容器滚动
    const scrollContainer =
      document.querySelector('.main-content') || document.querySelector('.layout-container')

    if (scrollContainer && scrollContainer.scrollTop !== undefined) {
      // 实际是容器滚动，直接使用容器的滚动位置
      viewportTop = scrollTop.value
      viewportBottom = scrollTop.value + containerHeight.value
    } else {
      // 真正的页面滚动
      const pageScrollTop = scrollTop.value
      const viewportHeight = containerHeight.value
      const containerAbsoluteTop = containerTop.value

      viewportTop = pageScrollTop - containerAbsoluteTop
      viewportBottom = pageScrollTop + viewportHeight - containerAbsoluteTop
    }
  } else {
    // 容器滚动模式
    viewportTop = scrollTop.value
    viewportBottom = scrollTop.value + containerHeight.value
  }

  // 增加缓冲区，提前加载即将进入视口的内容
  const buffer = itemHeight.value * 1.5 // 增加缓冲区
  const isVisible = rowBottom >= viewportTop - buffer && rowTop <= viewportBottom + buffer

  return isVisible
}

// 容器样式
const containerStyle = computed(() => {
  if (props.usePageScroll) {
    return {
      minHeight: `${totalHeight.value}px`,
      overflow: 'visible',
      position: 'relative' as const
    }
  } else {
    return {
      height: `${containerHeight.value}px`,
      overflow: 'auto',
      position: 'relative' as const
    }
  }
})

// 行样式类
const rowClass = computed(() => ({
  'story-row-pc': props.isPc,
  'story-row-mobile': !props.isPc
}))

// 滚动处理
let scrollTimer: number | null = null
const handleScroll = (event?: Event) => {
  if (props.usePageScroll) {
    // 页面滚动模式 - 检测实际的滚动容器
    if (typeof window !== 'undefined') {
      const target = event?.target as HTMLElement | Window

      if (target && target !== window && 'scrollTop' in target && target.scrollTop !== undefined) {
        // 容器滚动
        scrollTop.value = target.scrollTop
      } else {
        // 窗口滚动
        scrollTop.value = window.pageYOffset || document.documentElement.scrollTop
      }

      // 每次滚动时更新容器位置，确保视口检测准确
      updateContainerPosition()
    }
  } else {
    // 容器滚动模式
    const target = event?.target as HTMLElement
    if (target) {
      scrollTop.value = target.scrollTop
    }
  }

  // 滚动节流
  if (scrollTimer) {
    cancelAnimationFrame(scrollTimer)
  }
  scrollTimer = requestAnimationFrame(() => {
    // 可以在这里添加滚动后的处理逻辑
  })
}

// 更新容器位置
const updateContainerPosition = () => {
  if (containerRef.value && typeof window !== 'undefined') {
    const rect = containerRef.value.getBoundingClientRect()
    containerTop.value = rect.top + (window.pageYOffset || document.documentElement.scrollTop)
  }
}

// 图片加载处理
const handleImageLoaded = () => {
  emit('image-loaded')
}

// 响应式布局更新
let resizeObserver: ResizeObserver | null = null
const updateLayout = () => {
  if (props.usePageScroll) {
    // 页面滚动模式：检测实际的滚动容器
    if (typeof window !== 'undefined') {
      // 智能选择滚动容器：根据CSS属性选择
      const mainContent = document.querySelector('.main-content')
      const pageContainer = document.querySelector('.page-container')
      const layoutContainer = document.querySelector('.layout-container')

      let scrollContainer = null

      if (mainContent) {
        const style = getComputedStyle(mainContent)
        if (
          style.overflowY === 'auto' ||
          style.overflow === 'auto' ||
          style.overflowY === 'scroll'
        ) {
          scrollContainer = mainContent
        }
      }

      if (!scrollContainer && pageContainer) {
        const style = getComputedStyle(pageContainer)
        if (
          style.overflowY === 'auto' ||
          style.overflow === 'auto' ||
          style.overflowY === 'scroll'
        ) {
          scrollContainer = pageContainer
        }
      }

      if (!scrollContainer && layoutContainer) {
        const style = getComputedStyle(layoutContainer)
        if (
          style.overflowY === 'auto' ||
          style.overflow === 'auto' ||
          style.overflowY === 'scroll'
        ) {
          scrollContainer = layoutContainer
        }
      }

      if (scrollContainer) {
        // 使用滚动容器的高度
        containerHeight.value = scrollContainer.clientHeight || 600
      } else {
        // 使用视口高度
        containerHeight.value = window.innerHeight
      }

      updateContainerPosition()
    }
  } else {
    // 容器滚动模式：使用容器高度
    if (containerRef.value) {
      containerHeight.value = containerRef.value.clientHeight || 600
    }
  }
}

// 生命周期
onMounted(async () => {
  await nextTick()
  isSSR.value = false
  isDev.value = process.env.NODE_ENV === 'development'

  updateLayout()

  // 监听容器大小变化
  if (containerRef.value && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(updateLayout)
    resizeObserver.observe(containerRef.value)
  }

  // 监听窗口大小变化
  window.addEventListener('resize', updateLayout)

  // 监听滚动事件
  if (props.usePageScroll) {
    const scrollHandler = (e: Event) => {
      handleScroll(e)
    }

    // 尝试找到实际的滚动容器
    setTimeout(() => {
      // 智能选择滚动容器：优先根据布局特征选择，而不是依赖滚动能力
      const mainContent = document.querySelector('.main-content')
      const pageContainer = document.querySelector('.page-container') // 移动端滚动容器
      const layoutContainer = document.querySelector('.layout-container')

      let scrollContainer: Window | Element = window
      // 优先级：PC端的 .main-content > 移动端的 .page-container > 备用的 .layout-container
      if (mainContent) {
        const style = getComputedStyle(mainContent)
        if (
          style.overflowY === 'auto' ||
          style.overflow === 'auto' ||
          style.overflowY === 'scroll'
        ) {
          scrollContainer = mainContent
        }
      }

      if (scrollContainer === window && pageContainer) {
        const style = getComputedStyle(pageContainer)
        if (
          style.overflowY === 'auto' ||
          style.overflow === 'auto' ||
          style.overflowY === 'scroll'
        ) {
          scrollContainer = pageContainer
        }
      }

      if (scrollContainer === window && layoutContainer) {
        const style = getComputedStyle(layoutContainer)
        if (
          style.overflowY === 'auto' ||
          style.overflow === 'auto' ||
          style.overflowY === 'scroll'
        ) {
          scrollContainer = layoutContainer
        }
      }

      if (scrollContainer === window) {
        window.addEventListener('scroll', scrollHandler, { passive: true })
      } else {
        scrollContainer.addEventListener('scroll', scrollHandler, { passive: true })
      }

      updateContainerPosition()
      handleScroll()
    }, 200)
  }
})

onBeforeUnmount(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
  window.removeEventListener('resize', updateLayout)

  // 移除滚动监听器
  if (props.usePageScroll) {
    window.removeEventListener('scroll', handleScroll)
  }

  if (scrollTimer) {
    cancelAnimationFrame(scrollTimer)
  }
})

// 监听数据变化，重置滚动位置
watch(
  () => props.stories.length,
  () => {
    if (containerRef.value) {
      containerRef.value.scrollTop = 0
      scrollTop.value = 0
    }
  }
)
</script>

<style scoped>
.virtual-story-grid {
  width: 100%;
  position: relative;
  overflow-x: hidden;
  scroll-behavior: smooth;
  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
  will-change: scroll-position;
}

/* 容器滚动模式 */
.virtual-story-grid:not(.page-scroll) {
  overflow-y: auto;
}

/* 页面滚动模式 */
.virtual-story-grid.page-scroll {
  overflow-y: visible;
}

.virtual-content {
  width: 100%;
  min-height: 100%;
}

.story-row {
  width: 100%;
  box-sizing: border-box;
}

.story-card-item {
  width: 100%;
  height: 100%;
}

/* PC端样式 */
.virtual-story-grid.is-pc {
  background: transparent;
}

.virtual-story-grid.is-pc .story-row {
  padding: 0 20px;
}

/* 移动端样式 */
.virtual-story-grid.is-mobile {
  background: transparent;
}

.virtual-story-grid.is-mobile .story-row {
  padding: 0 16px;
}

/* 性能监控信息 */
.performance-info {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 9999;
  font-family: monospace;
}

.performance-info div {
  margin: 2px 0;
}

/* 滚动条样式 */
.virtual-story-grid::-webkit-scrollbar {
  width: 6px;
}

.virtual-story-grid::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.virtual-story-grid::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.virtual-story-grid::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
