<template>
  <Teleport to="body">
    <Transition name="fade">
      <div v-if="visible" class="character-select-modal" @click.self="$emit('close')">
        <div class="modal-content">
          <div class="modal-header">
            <h2>Select Character</h2>
            <button class="close-button" @click="$emit('close')">
              <Icon name="lucide:x" size="24" />
            </button>
          </div>

          <div class="characters-grid">
            <div
              v-for="actor in actors"
              :key="actor.id"
              class="character-card"
              @click="selectCharacter(actor)"
            >
              <div class="character-image">
                <NuxtImg :src="actor.preview_url" :alt="actor.name" loading="lazy" />
              </div>

              <div class="character-info">
                <h3 class="character-name">{{ actor.name }}</h3>

                <div class="character-cost">
                  <div v-if="!actor.is_purchased && actor.coins > 0" class="cost-badge">
                    <NuxtImg
                      :src="icons.diamond.value"
                      class="diamond-icon"
                      alt="Diamond icon"
                      loading="lazy"
                    />
                    <span>{{ actor.coins }}</span>
                  </div>
                  <div v-else-if="actor.is_purchased" class="unlocked-badge">Unlocked</div>
                  <div v-else class="free-badge">Free</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  actors: {
    type: Array,
    default: () => []
  },
  storyCoins: {
    type: Number,
    default: 0
  },
  storyIsPurchased: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'select'])

// 图标配置
const { icons } = useCdn()

const selectCharacter = (actor) => {
  emit('select', actor)
}
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.character-select-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: var(--bg-secondary, #1a1a1a);
  border-radius: 20px;
  padding: 24px;
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  animation: zoomIn 0.3s ease;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.modal-header h2 {
  color: var(--text-primary, #fff);
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: var(--text-secondary, #999);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-button:hover {
  color: var(--text-primary, #fff);
  background: var(--bg-hover, rgba(255, 255, 255, 0.1));
}

.characters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.character-card {
  background: var(--bg-tertiary, #2a2a2a);
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.character-card:hover {
  transform: translateY(-2px);
  border-color: #ca93f2;
  box-shadow: 0 4px 12px rgba(202, 147, 242, 0.3);
}

.character-image {
  width: 100%;
  aspect-ratio: 3/4;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
}

.character-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.character-info {
  text-align: center;
}

.character-name {
  color: var(--text-primary, #fff);
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.character-cost {
  display: flex;
  justify-content: center;
}

.cost-badge,
.unlocked-badge,
.free-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.cost-badge {
  background: rgba(202, 147, 242, 0.2);
  color: #ca93f2;
  border: 1px solid #ca93f2;
}

.unlocked-badge {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid #22c55e;
}

.free-badge {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  border: 1px solid #3b82f6;
}

.diamond-icon {
  width: 12px;
  height: 12px;
}

/* 亮色主题适配 */
body.light-theme .modal-content {
  background: var(--bg-primary);
  color: var(--text-primary);
}

body.light-theme .character-card {
  background: var(--bg-secondary);
}
</style>
