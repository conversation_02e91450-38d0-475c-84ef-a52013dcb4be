<template>
  <header class="top-header">
    <!-- 左侧Logo -->
    <div class="logo" @click="$router.push('/')">
      <NuxtImg :src="logoUrl" :alt="websiteTitle" loading="eager" width="142" height="31" />
    </div>

    <!-- 右侧用户操作区 -->
    <div class="user-actions">
      <!-- 主题切换 -->
      <button class="theme-toggle-pc" @click="toggleTheme">
        <Icon :name="isDarkTheme ? 'lucide:sun' : 'lucide:moon'" size="20" />
      </button>

      <!-- 社交链接 -->
      <a
        v-if="appName === 'ReelPlay'"
        href="https://x.com/Reelplay197835"
        target="_blank"
        rel="noopener noreferrer"
        class="x-btn"
      >
        <svg viewBox="0 0 24 24" fill="currentColor" width="18" height="18" class="x-icon">
          <path
            d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"
          />
        </svg>
        <span class="x-text">Follow</span>
      </a>

      <a
        v-if="appName === 'Playshot'"
        href="https://t.me/+bnRxPQGjVPM5MzM1"
        target="_blank"
        rel="noopener noreferrer"
        class="telegram-btn"
      >
        <Icon name="lucide:send" size="18" />
        <span class="telegram-text">Join</span>
      </a>

      <!-- 登录按钮或用户信息 -->
      <button
        v-if="!userStore.isLoggedIn || userStore.isGuest"
        class="sign-in-btn"
        @click="showAuthModal"
      >
        Sign in
      </button>
      <div v-else class="user-info-container">
        <!-- 钻石数量显示 -->
        <CreditDisplay
          :amount="userStore.userCoins"
          :show-add-button="true"
          @add="handleRecharge"
        />
        <div class="user-avatar-wrapper" @click="handleUserAvatarClick">
          <NuxtImg
            :src="userStore.userInfo?.avatar_url || icons.defaultAvatar.value"
            alt="User avatar"
            class="user-avatar"
            loading="eager"
          />
          <!-- 用户下拉菜单 -->
          <div v-if="showUserMenu" class="user-menu">
            <div class="menu-item" @click="handleProfile">
              <Icon name="lucide:user" :size="16" />
              Profile
            </div>
            <div class="menu-item" @click="handleSettings">
              <Icon name="lucide:settings" :size="16" />
              Settings
            </div>
            <div class="menu-item logout" @click="handleLogout">
              <Icon name="lucide:log-out" :size="16" />
              Logout
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
// 定义事件
const emit = defineEmits(['show-auth', 'theme-toggle'])

// 运行时配置
const config = useRuntimeConfig()
const { icons } = useCdn()
const logoUrl =
  config.public?.logoUrl || 'https://static.reelplay.ai/static/images/logo/reelplay_logo.png'
const websiteTitle = config.public?.websiteTitle || 'ReelPlay'
const appName = config.public?.appName || 'ReelPlay'

// 用户状态管理
const userStore = useUserStore()
const router = useRouter()

// 响应式数据
const isDarkTheme = ref(true)
const showUserMenu = ref(false)

// 立即初始化用户状态，避免闪现（与移动端保持一致）
if (import.meta.client) {
  userStore.initFromStorage()
}

// 组件挂载时初始化
onMounted(() => {
  // 主题初始化
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme) {
    isDarkTheme.value = savedTheme === 'dark'
  }

  // 监听点击外部事件
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 方法
const toggleTheme = () => {
  isDarkTheme.value = !isDarkTheme.value
  emit('theme-toggle', isDarkTheme.value)
}

const showAuthModal = () => {
  emit('show-auth')
}

const handleRecharge = () => {
  const rechargeStore = useRechargeStore()
  rechargeStore.showRechargeModal()
}

// 用户菜单相关方法
const handleUserAvatarClick = () => {
  showUserMenu.value = !showUserMenu.value
}

const handleProfile = () => {
  showUserMenu.value = false
  router.push('/user/profile')
}

const handleSettings = () => {
  showUserMenu.value = false
  router.push('/user/settings')
}

const handleLogout = () => {
  showUserMenu.value = false
  userStore.logout()
  router.push('/')
}

// 点击外部关闭用户菜单
const handleClickOutside = (event: MouseEvent) => {
  const userAvatarWrapper = document.querySelector('.user-avatar-wrapper')
  if (
    showUserMenu.value &&
    userAvatarWrapper &&
    !userAvatarWrapper.contains(event.target as Node)
  ) {
    showUserMenu.value = false
  }
}
</script>

<style lang="less" scoped>
// 顶部导航栏
.top-header {
  height: 72px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40px 0 20px;
  background-color: var(--pc-top-header-bg);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--divider-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  z-index: 10;

  .logo {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;

    img {
      width: 142px;
      height: 31px;
      object-fit: contain;
    }
  }

  .user-actions {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .theme-toggle-pc {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: var(--text-primary, rgba(255, 255, 255, 0.8));
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      color: var(--text-primary, rgba(255, 255, 255, 0.9));
    }
  }

  .x-btn,
  .telegram-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary, rgba(255, 255, 255, 0.8));
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      color: var(--text-primary, rgba(255, 255, 255, 0.9));
    }
  }

  .sign-in-btn {
    padding: 8px 20px;
    border-radius: 20px;
    background-color: var(--accent-color, #ca93f2);
    color: var(--bg-primary, #1f0038);
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--accent-hover, #b87de8);
    }
  }
}

/* 用户信息容器样式 */
.user-info-container {
  display: flex;
  align-items: center;
  gap: 16px;

  :deep(.credit-display) {
    height: 36px;
    padding: 0 32px;
    border-radius: 20px;
    border: 1px solid var(--accent-color, #ca93f2);

    .credit-icon {
      width: 30px;
      height: 30px;
      left: -8px;
    }

    .credit-amount {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-primary, rgba(255, 255, 255, 0.9));
    }

    .add-button {
      width: 22px;
      height: 24px;
      top: 48%;

      svg {
        width: 24px;
        height: 24px;
      }
    }
  }
}

/* 用户菜单样式 */
.user-avatar-wrapper {
  position: relative;
  cursor: pointer;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--accent-color, #ca93f2);
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.user-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 180px;
  background-color: var(--bg-secondary);
  border-radius: 12px;
  box-shadow: 0 4px 12px var(--shadow-color);
  overflow: hidden;
  z-index: 100;
  border: 1px solid var(--border-color);

  .menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: var(--text-primary);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--bg-hover);
    }

    &.logout {
      color: #ff6b6b;

      &:hover {
        background: rgba(255, 107, 107, 0.1);
        color: #ff6b6b;
      }
    }
  }
}
</style>
