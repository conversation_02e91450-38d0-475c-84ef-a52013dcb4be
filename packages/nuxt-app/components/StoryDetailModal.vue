<template>
  <Teleport to="body">
    <Transition name="fade">
      <div v-if="visible" class="story-detail-modal" @click.self="handleClose">
        <div class="modal-content">
          <button
            class="close-button"
            :title="isLoading ? 'Cancel operation (ESC)' : 'Close (ESC)'"
            @click="handleClose"
          >
            <Icon name="lucide:x" size="24" />
          </button>

          <div class="modal-header">
            <div class="header-content">
              <!-- 标题骨架屏 -->
              <div v-if="isLoading" class="skeleton skeleton-title" />
              <h2 v-else>{{ storyDetail?.title }}</h2>

              <div class="story-cost">
                <!-- 成本标签骨架屏 -->
                <div v-if="isLoading" class="skeleton skeleton-badge" />
                <template v-else>
                  <div v-if="!isStorePurchased && storyCost > 0" class="cost-badge">
                    <NuxtImg
                      :src="icons.diamond.value"
                      class="diamond-icon"
                      alt="Diamond icon"
                      loading="lazy"
                    />
                    <span>{{ storyCost }}</span>
                  </div>
                  <div v-else-if="isStorePurchased" class="unlocked-badge">Unlocked</div>
                  <div v-else class="free-badge">Free</div>
                </template>
              </div>
            </div>

            <div
              class="favorite-button"
              :class="{ active: isFavorited }"
              @click.stop="toggleFavorite"
            >
              <div ref="favoriteIconRef" class="heart-icon">
                <Icon v-if="isFavorited" name="lucide:heart" size="20" />
                <Icon v-else name="lucide:heart" size="20" />
              </div>
              <span class="favorite-text">{{ isFavorited ? 'Favorited' : 'Add favorite' }}</span>
            </div>
          </div>

          <div class="modal-body">
            <div class="story-info">
              <div class="description">
                <h3>Description</h3>
                <!-- 描述骨架屏 -->
                <div v-if="isLoading" class="skeleton skeleton-description" />
                <p v-else>{{ storyDetail?.description }}</p>
              </div>
            </div>

            <!-- 只有当故事版本不是3时才显示角色选择 -->
            <div v-if="storyDetail?.version !== '3'" class="character-section">
              <h3>Select Character</h3>
              <div class="character-carousel">
                <button
                  v-show="hasMultipleActors && !isLoading"
                  class="carousel-arrow prev"
                  :disabled="isScrollStart"
                  @click="scrollCharacters('prev')"
                >
                  <Icon name="lucide:chevron-left" size="20" />
                </button>

                <div ref="characterListRef" class="character-list">
                  <!-- 角色列表骨架屏 -->
                  <template v-if="isLoading">
                    <div v-for="i in 3" :key="`skeleton-${i}`" class="character-card skeleton-card">
                      <div class="card-content">
                        <div class="skeleton skeleton-image" />
                        <div class="character-info">
                          <div class="skeleton skeleton-name" />
                          <div class="skeleton skeleton-actor-badge" />
                        </div>
                      </div>
                    </div>
                  </template>
                  <!-- 实际角色列表 -->
                  <template v-else>
                    <div
                      v-for="actor in storyDetail?.actors"
                      :key="actor.id"
                      class="character-card"
                      :class="{ 'is-selected': selectedActor?.id === actor.id }"
                      @click="selectActor(actor)"
                    >
                      <div class="card-content">
                        <NuxtImg
                          :src="actor.preview_url"
                          :alt="actor.name"
                          class="preview-url"
                          loading="lazy"
                        />
                        <div class="character-info">
                          <div class="character-name">{{ actor.name }}</div>
                          <div v-if="!actor.is_purchased" class="actor-cost-badge">
                            <NuxtImg
                              :src="icons.diamond.value"
                              class="diamond-icon"
                              alt="Diamond icon"
                              loading="lazy"
                            />
                            {{ getActorCost(actor) }}
                          </div>
                          <div
                            v-else-if="actor.is_purchased && actor.coins > 0"
                            class="actor-unlocked-badge"
                          >
                            Unlocked
                          </div>
                          <div v-else class="actor-free-badge">Free</div>
                        </div>
                      </div>
                    </div>
                  </template>
                </div>

                <button
                  v-show="hasMultipleActors && !isLoading"
                  class="carousel-arrow next"
                  :disabled="isScrollEnd"
                  @click="scrollCharacters('next')"
                >
                  <Icon name="lucide:chevron-right" size="20" />
                </button>
              </div>
            </div>

            <!-- 只有当故事版本不是3时才显示角色属性 -->
            <template v-if="storyDetail?.version !== '3'">
              <!-- 角色属性骨架屏 -->
              <div v-if="isLoading" class="character-attributes skeleton-attributes">
                <div v-for="i in 4" :key="`attr-skeleton-${i}`" class="attribute-item">
                  <div class="skeleton skeleton-attr-label" />
                  <div class="skeleton skeleton-attr-value" />
                </div>
              </div>
              <!-- 实际角色属性 -->
              <div v-else-if="selectedActor" class="character-attributes">
                <div class="attribute-item">
                  <div class="attribute-label">Age</div>
                  <div class="attribute-value">{{ selectedActor.extra?.age || '18' }}</div>
                </div>
                <div class="attribute-item">
                  <div class="attribute-label">Relationship</div>
                  <div class="attribute-value">
                    {{ selectedActor.extra?.relationship || 'Unknown' }}
                  </div>
                </div>
                <div class="attribute-item">
                  <div class="attribute-label">Measurements</div>
                  <div class="attribute-value">
                    {{ selectedActor.extra?.measurements || 'Unknown' }}
                  </div>
                </div>
                <div class="attribute-item">
                  <div class="attribute-label">Personality</div>
                  <div class="attribute-value">
                    {{ selectedActor.extra?.personality || 'Friendly' }}
                  </div>
                </div>
              </div>
            </template>
          </div>

          <div class="modal-footer">
            <button class="cancel-button" @click="handleClose">Cancel</button>
            <button
              class="play-button"
              :disabled="(storyDetail?.version !== '3' && !selectedActor) || isLoading"
              @click="handlePlay"
            >
              <span v-if="isLoading" class="loading-spinner" />
              <span v-if="isLoading" class="loading-text">Loading...</span>
              <template v-else>
                Play
                <span v-if="selectedActor && !selectedActor.is_purchased" class="cost">
                  <NuxtImg
                    :src="icons.diamond.value"
                    class="diamond-icon"
                    alt="Diamond icon"
                    loading="lazy"
                  />
                  {{ getActorCost(selectedActor) }}
                </span>
              </template>
            </button>
          </div>
        </div>
      </div>
    </Transition>

    <!-- 聊天历史确认对话框 -->
    <ChatHistoryConfirmDialog
      :visible="showChatHistoryDialog"
      @continue="handleContinue"
      @restart="handleRestart"
      @close="handleChatHistoryDialogClose"
    />
  </Teleport>
</template>

<script setup>
// Props and Emits
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  storyId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['close', 'play', 'need-login'])

// Composables
const { getStoryDetail, toggleFavorite: toggleStoryFavorite } = useStories()
const { getUserChatHistory } = useApi()
const { icons } = useCdn()

// State
const isLoading = ref(false)
const storyDetail = ref(null)
const selectedActor = ref(null)
const isFavorited = ref(false)
const characterListRef = ref(null)
const favoriteIconRef = ref(null)
const isScrollStart = ref(true)
const isScrollEnd = ref(false)
const showChatHistoryDialog = ref(false)
const currentSelectedActor = ref(null) // 存储当前选择的角色

// Computed
const storyCost = computed(() => storyDetail.value?.coins || 0)
const isStorePurchased = computed(() => storyDetail.value?.is_purchased || false)
const hasMultipleActors = computed(() => (storyDetail.value?.actors?.length || 0) > 1)

// Methods
const handleClose = () => {
  emit('close')
}

const getActorCost = (actor) => {
  return actor.coins || 0
}

const selectActor = (actor) => {
  selectedActor.value = actor
}

const scrollCharacters = (direction) => {
  if (!characterListRef.value) return

  const scrollAmount = 200
  const currentScroll = characterListRef.value.scrollLeft

  if (direction === 'prev') {
    characterListRef.value.scrollTo({
      left: currentScroll - scrollAmount,
      behavior: 'smooth'
    })
  } else {
    characterListRef.value.scrollTo({
      left: currentScroll + scrollAmount,
      behavior: 'smooth'
    })
  }

  setTimeout(checkScrollPosition, 300)
}

const checkScrollPosition = () => {
  if (!characterListRef.value) return

  const { scrollLeft, scrollWidth, clientWidth } = characterListRef.value
  isScrollStart.value = scrollLeft <= 0
  isScrollEnd.value = scrollLeft >= scrollWidth - clientWidth - 1
}

const toggleFavorite = async () => {
  try {
    // 调用真实的API
    await toggleStoryFavorite(props.storyId, isFavorited.value)

    // 更新本地状态
    isFavorited.value = !isFavorited.value

    // 同时更新storyDetail中的收藏状态
    if (storyDetail.value) {
      storyDetail.value.is_fav = isFavorited.value
    }

    console.log('Toggle favorite success:', isFavorited.value)
  } catch (error) {
    console.error('Failed to toggle favorite:', error)
    // 可以显示错误提示
  }
}

const handlePlay = async () => {
  if (!selectedActor.value && storyDetail.value?.version !== '3') {
    return
  }

  const actorToUse = selectedActor.value || storyDetail.value?.actors?.[0]

  if (!actorToUse) {
    console.error('No actor selected')
    return
  }

  // 存储当前选择的角色
  currentSelectedActor.value = actorToUse

  // 检查是否需要聊天历史检查
  if (
    actorToUse?.version === '2' ||
    actorToUse?.version === '3' ||
    actorToUse?.version === '4' ||
    actorToUse?.version === '5' ||
    storyDetail.value?.version === '3'
  ) {
    // 检查聊天历史
    await checkChatHistory(actorToUse)

    // 如果显示了对话框，直接返回等待用户选择
    if (showChatHistoryDialog.value) {
      return
    }
  }

  // 没有历史记录或不需要检查，直接开始游戏
  await startGame(actorToUse, true)
}

// 检查聊天历史
const checkChatHistory = async (actor) => {
  try {
    const response = await getUserChatHistory(props.storyId, actor.id, storyDetail.value?.version)

    if (response.code === '0' && response.data?.history?.length > 0) {
      // 有历史记录，显示确认对话框
      showChatHistoryDialog.value = true
    }
  } catch (error) {
    console.error('Failed to check chat history:', error)
    // 出错时继续游戏
  }
}

// 处理继续游戏
const handleContinue = async () => {
  showChatHistoryDialog.value = false
  if (currentSelectedActor.value) {
    await startGame(currentSelectedActor.value, false)
  }
}

// 处理重新开始游戏
const handleRestart = async () => {
  showChatHistoryDialog.value = false
  if (currentSelectedActor.value) {
    await startGame(currentSelectedActor.value, true)
  }
}

// 处理对话框关闭（不选择继续或重新开始）
const handleChatHistoryDialogClose = () => {
  showChatHistoryDialog.value = false
  // 重置当前选择的角色
  currentSelectedActor.value = null
}

// 开始游戏
const startGame = async (actor, isRestart) => {
  // 根据故事和角色配置决定使用哪个chat版本
  const chatVersion = determineChatVersion(storyDetail.value, actor)

  // 构建主应用的chat路由（会通过iframe加载CSR应用）
  const chatRoute = buildChatRoute(chatVersion, props.storyId, actor.id)

  console.log('🎮 PC端导航到主应用chat页面:', {
    version: chatVersion,
    route: chatRoute,
    restart: isRestart
  })

  // 导航到主应用的chat页面（使用iframe）
  await navigateTo({
    path: chatRoute,
    query: {
      restart: isRestart ? '1' : '0'
    }
  })

  emit('play', actor)
  emit('close')
}

// 根据故事和角色配置决定chat版本
const determineChatVersion = (story, actor) => {
  if (!story) return 'chat'

  // 优先根据故事版本判断
  if (story.version === '3') {
    return 'chat3' // 故事版本3使用chat3 (Telepathy)
  }

  // 然后根据角色版本判断
  if (actor) {
    if (actor.version === '4' || actor.version === '5') {
      return 'chat4' // 角色版本4和5使用chat4 (Live)
    } else if (actor.version === '2') {
      return 'chat2' // 角色版本2使用chat2 (Enhanced)
    }
  }

  // 兼容旧的ID判断逻辑（作为备选方案）
  if (story.id === '1') return 'chat4'
  if (story.id === '2') return 'chat3'
  if (story.id === '3') return 'chat2'

  return 'chat' // 默认使用chat (Classic)
}

// 构建chat路由
const buildChatRoute = (version, storyId, actorId) => {
  switch (version) {
    case 'chat2':
      return `/chat2/${storyId}/${actorId}`
    case 'chat3':
      return `/chat3/${storyId}/${actorId}` // chat3使用标准参数顺序
    case 'chat4':
      return `/chat4/${storyId}/${actorId}`
    default:
      return `/chat/${storyId}/${actorId}`
  }
}

const loadStoryDetail = async () => {
  if (!props.storyId) return

  try {
    isLoading.value = true
    console.log('🎬 StoryDetailModal: 开始加载故事详情', props.storyId)
    storyDetail.value = await getStoryDetail(props.storyId)

    // 初始化角色选择
    if (storyDetail.value?.actors?.length > 0) {
      selectedActor.value = storyDetail.value.actors[0]
    }

    isFavorited.value = storyDetail.value?.is_fav || false

    // 检查滚动位置
    setTimeout(checkScrollPosition, 300)
  } catch (error) {
    console.error('Failed to load story details:', error)
  } finally {
    isLoading.value = false
  }
}

// Watchers
watch(
  [() => props.storyId, () => props.visible],
  ([newStoryId, newVisible], [oldStoryId, oldVisible]) => {
    // 只在以下情况下加载数据：
    // 1. Modal变为可见且有storyId
    // 2. storyId变化且Modal是可见的
    if (
      newVisible &&
      newStoryId &&
      (!oldVisible || // Modal刚打开
        newStoryId !== oldStoryId) // storyId变化了
    ) {
      loadStoryDetail()
    }
  },
  { immediate: false }
)

// ESC key handler
const handleKeydown = (event) => {
  if (event.key === 'Escape') {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)

  if (characterListRef.value) {
    characterListRef.value.addEventListener('scroll', checkScrollPosition)
  }
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.story-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  width: 800px;
  max-width: 90%;
  max-height: 90vh;
  background: linear-gradient(180deg, #2b1b3b 0%, #1a0f24 100%);
  border-radius: 24px;
  padding: 46px;
  animation: zoomIn 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 24px;
  position: relative;
  overflow: hidden;
  color: white;
  overflow-y: auto;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: all 0.2s ease;
  z-index: 1010;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.close-button:active {
  transform: scale(0.95);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 16px;
  min-height: 80px;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
  flex: 1;
  justify-content: center;
}

.header-content h2 {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
}

.story-cost {
  display: flex;
  align-items: center;
  gap: 8px;
}

.cost-badge,
.unlocked-badge,
.free-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 600;
}

.cost-badge {
  background: rgba(0, 0, 0, 0.2);
  color: #daff96;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 600;
}

.cost-badge .diamond-icon {
  width: 16px;
  height: 16px;
}

.unlocked-badge {
  background: rgba(218, 255, 150, 0.2);
  color: #daff96;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 600;
}

.free-badge {
  background: rgba(218, 255, 150, 0.2);
  color: #daff96;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 600;
}

.diamond-icon {
  width: 16px;
  height: 16px;
}

.favorite-button {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  padding: 8px 12px;
  border-radius: 20px;
  transition: background-color 0.2s ease;
}

.favorite-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.favorite-button.active {
  color: #ef4444;
}

.heart-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transform-origin: center;
  will-change: transform;
  color: rgba(255, 255, 255, 0.6);
  transition: color 0.2s ease;
}

.favorite-button:hover .heart-icon {
  color: white;
}

.favorite-button:active {
  transform: scale(0.98);
}

.favorite-text {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.2s ease;
}

.favorite-button:hover .favorite-text {
  color: white;
}

.modal-body {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.story-info h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #ca93f2;
}

.description p {
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.character-section h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: #ca93f2;
}

.character-carousel {
  position: relative;
  display: flex;
  align-items: center;
  gap: 16px;
}

.carousel-arrow {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.carousel-arrow:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
}

.carousel-arrow:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.character-list {
  display: flex;
  gap: 16px;
  overflow-x: auto;
  scroll-behavior: smooth;
  padding: 8px 0;
  flex: 1;
}

.character-list::-webkit-scrollbar {
  display: none;
}

.character-card {
  cursor: pointer;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 280px;
  flex: 0 0 180px; /* 固定宽度，不允许缩放 */
}

.character-card:hover {
  transform: translateY(-8px);
}

.character-card.is-selected {
  z-index: 1;
}

.character-card.is-selected .card-content {
  border-color: #ca93f2;
}

.character-card.is-selected .character-name {
  color: #ca93f2;
}

.card-content {
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.preview-url {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.character-info {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 16px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0));
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.character-name {
  font-size: 20px;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: color 0.3s ease;
  margin-bottom: 2px;
}

.actor-cost-badge,
.actor-unlocked-badge,
.actor-free-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  backdrop-filter: blur(4px);
}

.actor-cost-badge {
  background: rgba(0, 0, 0, 0.6);
  color: #daff96;
}

.actor-cost-badge .diamond-icon {
  width: 12px;
  height: 12px;
}

.actor-unlocked-badge {
  background: rgba(218, 255, 150, 0.2);
  color: #daff96;
}

.actor-free-badge {
  background: rgba(218, 255, 150, 0.2);
  color: #daff96;
}

.character-attributes {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
  margin-bottom: 16px;
}

.attribute-item {
  flex: 1;
  text-align: center;
  padding: 16px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  margin: 0 6px;
}

.attribute-item:first-child {
  margin-left: 0;
}

.attribute-item:last-child {
  margin-right: 0;
}

.attribute-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 8px;
}

.attribute-value {
  font-size: 16px;
  color: white;
  font-weight: 500;
}

.modal-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 16px;
}

.cancel-button {
  padding: 12px 24px;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
  min-width: 180px;
}

.cancel-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.play-button {
  padding: 12px 32px;
  border-radius: 24px;
  background: #ca93f2;
  border: none;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 180px;
}

.play-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.play-button:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(202, 147, 242, 0.3);
}

.play-button:not(:disabled):active {
  transform: translateY(0);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.cost {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

/* 骨架屏样式 */
.skeleton {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 25%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.skeleton-title {
  height: 32px;
  width: 60%;
  margin-bottom: 12px;
}

.skeleton-badge {
  height: 24px;
  width: 80px;
}

.skeleton-description {
  height: 60px;
  width: 100%;
}

.skeleton-card {
  opacity: 0.7;
}

.skeleton-image {
  height: 120px;
  width: 100%;
  border-radius: 8px;
}

.skeleton-name {
  height: 16px;
  width: 80%;
}

.skeleton-actor-badge {
  height: 20px;
  width: 60px;
}

.skeleton-attributes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.skeleton-attr-label {
  height: 12px;
  width: 60%;
  margin-bottom: 4px;
}

.skeleton-attr-value {
  height: 16px;
  width: 80%;
}

/* 亮色主题适配 */
body.light-theme .story-detail-modal {
  background: rgba(0, 0, 0, 0.5);
}

body.light-theme .modal-content {
  background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  box-shadow: 0 10px 30px var(--shadow-color);
}

body.light-theme .close-button {
  background: rgba(0, 0, 0, 0.1);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px var(--shadow-color);
}

body.light-theme .close-button:hover {
  background: rgba(0, 0, 0, 0.15);
}

body.light-theme .modal-header {
  border-bottom: 1px solid var(--border-color);
}

body.light-theme .header-content h2 {
  color: var(--text-primary);
}

body.light-theme .cost-badge {
  background: rgba(0, 0, 0, 0.05);
  color: var(--accent-color);
}

body.light-theme .unlocked-badge,
body.light-theme .free-badge {
  background: rgba(0, 0, 0, 0.05);
  color: var(--accent-color);
}

body.light-theme .favorite-button:hover {
  background-color: var(--bg-tertiary);
}

body.light-theme .favorite-button .heart-icon {
  color: var(--text-tertiary);
}

body.light-theme .favorite-button .favorite-text {
  color: var(--text-secondary);
}

body.light-theme .favorite-button:hover .heart-icon {
  color: var(--text-primary);
}

body.light-theme .favorite-button:hover .favorite-text {
  color: var(--text-primary);
}

body.light-theme .story-info .description h3 {
  color: var(--text-primary);
}

body.light-theme .story-info .description p {
  color: var(--text-secondary);
}

body.light-theme .carousel-arrow {
  background: rgba(0, 0, 0, 0.1);
  color: var(--text-primary);
}

body.light-theme .carousel-arrow:not(:disabled):hover {
  background: rgba(0, 0, 0, 0.2);
}

body.light-theme .character-card.is-selected .card-content {
  border-color: var(--accent-color);
}

body.light-theme .character-card.is-selected .character-name {
  color: var(--accent-color);
}

body.light-theme .card-content {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 8px var(--shadow-color);
}

body.light-theme .character-name {
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

body.light-theme .attribute-item {
  background: rgba(0, 0, 0, 0.03);
}

body.light-theme .attribute-label {
  color: #ca93f2;
}

body.light-theme .attribute-value {
  color: var(--text-primary);
}

body.light-theme .cancel-button {
  background: rgba(0, 0, 0, 0.05);
  color: var(--text-primary);
}

body.light-theme .cancel-button:hover {
  background: rgba(0, 0, 0, 0.1);
}

body.light-theme .skeleton {
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.05) 25%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.05) 75%
  );
  background-size: 200% 100%;
}
</style>
