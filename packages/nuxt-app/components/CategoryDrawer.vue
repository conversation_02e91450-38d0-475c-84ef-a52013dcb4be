<template>
  <div v-if="visible" class="category-drawer-overlay" @click="handleClose">
    <div class="category-drawer" @click.stop>
      <div class="drawer-header">
        <h3 class="drawer-title">{{ drawerTitle }}</h3>
        <button class="close-btn" @click="handleClose">
          <Icon name="lucide:x" :size="20" />
        </button>
      </div>

      <div class="drawer-content">
        <!-- Popular/Sort Options -->
        <template v-if="type === 'popular'">
          <div class="option-list">
            <div
              v-for="option in popularOptions"
              :key="option.value"
              class="option-item"
              :class="{ active: selectedValue === option.value }"
              @click="handleOptionSelect(option.value)"
            >
              <span class="option-label">{{ option.label }}</span>
              <Icon
                v-if="selectedValue === option.value"
                name="lucide:check"
                :size="16"
                class="check-icon"
              />
            </div>
          </div>
        </template>

        <!-- Gender Options -->
        <template v-if="type === 'gender'">
          <div class="option-list">
            <div
              class="option-item"
              :class="{ active: selectedValue === '' }"
              @click="handleOptionSelect('')"
            >
              <span class="option-label">All</span>
              <Icon v-if="selectedValue === ''" name="lucide:check" :size="16" class="check-icon" />
            </div>
            <div
              v-for="gender in genderOptions"
              :key="gender.id"
              class="option-item"
              :class="{ active: selectedValue === gender.id }"
              @click="handleOptionSelect(gender.id)"
            >
              <span class="option-label">{{ gender.name }}</span>
              <Icon
                v-if="selectedValue === gender.id"
                name="lucide:check"
                :size="16"
                class="check-icon"
              />
            </div>
          </div>
        </template>

        <!-- Tags Options -->
        <template v-if="type === 'tags'">
          <div class="tags-wrapper">
            <div class="tags-grid">
              <div
                v-for="tag in tagOptions"
                :key="tag.id"
                class="tag-item"
                :class="{ active: selectedTags.includes(tag.id) }"
                @click="handleTagToggle(tag.id)"
              >
                <span class="tag-label">{{ tag.name }}</span>
                <Icon
                  v-if="selectedTags.includes(tag.id)"
                  name="lucide:check"
                  :size="14"
                  class="check-icon"
                />
              </div>
            </div>
          </div>

          <div class="tags-actions">
            <button
              class="clear-btn"
              :disabled="selectedTags.length === 0"
              @click="handleClearTags"
            >
              Clear All
            </button>
            <button class="apply-btn" @click="handleApplyTags">
              Apply ({{ selectedTags.length }})
            </button>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  visible: boolean
  type: 'popular' | 'gender' | 'tags'
  title?: string
  initialPopular?: string
  initialGender?: string
  initialTags?: string[]
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'popular-change', value: string): void
  // eslint-disable-next-line @typescript-eslint/unified-signatures
  (e: 'gender-change', value: string): void
  (e: 'tags-change', value: string[]): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  initialPopular: 'popular',
  initialGender: '',
  initialTags: () => []
})

const emit = defineEmits<Emits>()

// 状态管理
const storyStore = useStoryStore()

// 本地状态
const selectedValue = ref(props.initialPopular || props.initialGender || '')
const selectedTags = ref<string[]>([...props.initialTags])

// 计算属性
const drawerTitle = computed(() => {
  if (props.title) return props.title
  switch (props.type) {
    case 'popular':
      return 'Sort by'
    case 'gender':
      return 'Gender'
    case 'tags':
      return 'Tags'
    default:
      return 'Filter'
  }
})

const popularOptions = [
  { label: 'Most popular', value: 'popular' },
  { label: 'Newest', value: 'newest' }
]

const genderOptions = computed(() => {
  const genderCategory = storyStore.storyCategories.find(
    (category) => category?.name?.toUpperCase() === 'GENDER'
  )
  return genderCategory?.subcategories || []
})

const tagOptions = computed(() => {
  const hobbyCategory = storyStore.storyCategories.find(
    (category) => category?.name?.toUpperCase() === 'HOBBY'
  )
  return hobbyCategory?.subcategories || []
})

// 监听props变化
watch(
  () => props.initialPopular,
  (newVal) => {
    if (newVal !== undefined) selectedValue.value = newVal
  }
)

watch(
  () => props.initialGender,
  (newVal) => {
    if (newVal !== undefined) selectedValue.value = newVal
  }
)

watch(
  () => props.initialTags,
  (newVal) => {
    if (newVal) selectedTags.value = [...newVal]
  }
)

// 方法
const handleClose = () => {
  emit('update:visible', false)
}

const handleOptionSelect = (value: string) => {
  selectedValue.value = value

  if (props.type === 'popular') {
    emit('popular-change', value)
  } else if (props.type === 'gender') {
    emit('gender-change', value)
  }

  handleClose()
}

const handleTagToggle = (tagId: string) => {
  const index = selectedTags.value.indexOf(tagId)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tagId)
  }
}

const handleClearTags = () => {
  selectedTags.value = []
}

const handleApplyTags = () => {
  emit('tags-change', [...selectedTags.value])
  handleClose()
}
</script>

<style scoped>
.category-drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.category-drawer {
  background-color: var(--mobile-bg-secondary);
  border-radius: 24px 24px 0 0;
  width: 100%;
  max-width: 500px;
  max-height: 70vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.2);
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.drawer-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 16px 0;
  width: 100%;
  border-bottom: 1px solid var(--border-color);
}

.drawer-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.close-btn {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  border: none;
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.3s ease;
}

.close-btn:hover {
  background: var(--bg-hover);
}

.drawer-content {
  padding: 16px 24px 24px;
  max-height: calc(70vh - 80px);
  overflow-y: auto;
}

.option-list {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.option-item {
  padding: 16px 0;
  text-align: center;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.option-item:hover {
  background: var(--bg-hover);
}

.option-item.active {
  color: var(--accent-color);
}

.option-label {
  font-size: 16px;
  font-weight: 500;
}

.check-icon {
  position: absolute;
  right: 16px;
  color: var(--accent-color);
}

.tags-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 16px 0;
}

.tags-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding: 0 16px 24px;
}

.tag-item {
  padding: 6px 10px;
  text-align: center;
  border-radius: 100px;
  background: var(--mobile-input-bg);
  border: 1px solid var(--mobile-input-border);
  color: var(--text-secondary);
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: fit-content;
  display: flex;
  align-items: center;
  gap: 6px;
}

.tag-item:hover {
  background: var(--bg-hover);
}

.tag-item.active {
  background: var(--accent-bg);
  color: var(--text-primary);
  border: 1px solid var(--accent-color);
}

.tags-actions {
  display: flex;
  padding: 16px;
  width: 100%;
}

.clear-btn,
.apply-btn {
  padding: 12px 0;
  border-radius: 24px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
  height: 42px;
  border: 1px solid var(--accent-color);
}

.clear-btn:active,
.apply-btn:active {
  transform: scale(0.98);
}

.clear-btn {
  background: transparent;
  margin-right: 12px;
  border-radius: 40px;
  color: var(--accent-color);
  width: 96px;
}

.clear-btn:hover:not(:disabled) {
  background: var(--accent-bg);
}

.clear-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.apply-btn {
  background: var(--accent-color);
  color: var(--bg-primary);
  border-radius: 40px;
  flex: 1;
}

.apply-btn:hover {
  background: var(--accent-hover);
}
</style>
