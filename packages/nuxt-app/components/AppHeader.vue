<template>
  <header class="app-header">
    <div class="logo">
      <NuxtLink to="/">
        <NuxtImg :src="logoUrl" :alt="websiteTitle" loading="eager" width="117" height="24" />
      </NuxtLink>
    </div>
    <div class="header-buttons">
      <!-- 主题切换按钮 -->
      <button class="theme-toggle" @click="toggleTheme">
        <div class="toggle-track" :class="{ active: !isDarkTheme }">
          <div class="toggle-thumb" :class="{ active: !isDarkTheme }">
            <Icon :name="isDarkTheme ? 'lucide:moon' : 'lucide:sun'" class="theme-icon" />
          </div>
        </div>
      </button>

      <button class="btn" @click="showCheckinModal">
        <span>🎁</span>
      </button>

      <a
        v-if="appName === 'ReelPlay'"
        href="https://x.com/Reelplay197835"
        target="_blank"
        rel="noopener noreferrer"
        class="btn"
      >
        <svg viewBox="0 0 24 24" fill="currentColor" width="16" height="16" class="x-icon">
          <path
            d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"
          />
        </svg>
        <span>Follow</span>
      </a>

      <a
        v-if="appName === 'Playshot'"
        href="https://t.me/+bnRxPQGjVPM5MzM1"
        target="_blank"
        rel="noopener noreferrer"
        class="btn"
      >
        <Icon name="lucide:send" size="16" />
        <span>Join</span>
      </a>

      <button class="btn btn-primary" @click="showAuthModal">Sign in</button>
    </div>

    <!-- 模态框 -->
    <AuthModal v-if="showAuth" @close="showAuth = false" />
  </header>
</template>

<script setup>
// 运行时配置
const config = useRuntimeConfig()

// 安全的配置访问
const logoUrl =
  config.public?.logoUrl || 'https://static.reelplay.ai/static/images/logo/reelplay_logo.png'
const websiteTitle = config.public?.websiteTitle || 'ReelPlay'
const appName = config.public?.appName || 'ReelPlay'

// 响应式数据
const isDarkTheme = ref(true)
const showAuth = ref(false)
const showCheckin = ref(false)

// 方法
const toggleTheme = () => {
  isDarkTheme.value = !isDarkTheme.value
  if (import.meta.client) {
    localStorage.setItem('theme', isDarkTheme.value ? 'dark' : 'light')
    document.body.classList.toggle('light-theme', !isDarkTheme.value)
  }
}

const showAuthModal = () => {
  showAuth.value = true
}

const showCheckinModal = () => {
  showCheckin.value = true
}

// 客户端初始化
onMounted(() => {
  // 初始化主题
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme) {
    isDarkTheme.value = savedTheme === 'dark'
  }
  document.body.classList.toggle('light-theme', !isDarkTheme.value)
})
</script>
