<template>
  <Teleport to="body">
    <Transition name="fade">
      <div v-if="visible" class="login-modal-overlay" @click.self="handleClose">
        <div class="login-modal">
          <!-- Close Button -->
          <button class="close-button" @click="handleClose">
            <Icon name="lucide:x" size="24" />
          </button>

          <div class="login-content">
            <!-- Logo Section -->
            <div class="logo-wrapper">
              <NuxtImg :src="logoUrl" alt="Magic Partner" loading="eager" />
              <h2>{{ title || 'Continue with' }}</h2>
            </div>

            <!-- Form Section -->
            <div class="form-wrapper">
              <!-- Email Input -->
              <div class="input-group">
                <input
                  v-model="form.email"
                  type="email"
                  placeholder="Enter your email"
                  :class="{ error: errors.email }"
                  @blur="validateEmail"
                >
                <span v-if="errors.email" class="error-text">{{ errors.email }}</span>
              </div>

              <!-- Verification Code Input -->
              <div class="input-group verification-group">
                <input
                  v-model="form.verificationCode"
                  type="text"
                  placeholder="Enter code"
                  :class="{ error: errors.verificationCode }"
                >
                <button
                  class="send-code-button"
                  :class="{ 'is-inactive': !isEmailValid }"
                  :disabled="loading || countdown > 0 || !isEmailValid"
                  @click="handleSendCode"
                >
                  {{ countdown > 0 ? `Resend (${countdown})` : 'Send' }}
                </button>
                <span v-if="errors.verificationCode" class="error-text">
                  {{ errors.verificationCode }}
                </span>
              </div>

              <!-- Login Button -->
              <button
                class="login-button"
                :class="{ 'is-inactive': !isEmailValid || !form.verificationCode }"
                :disabled="loading || !isEmailValid || !form.verificationCode"
                @click="handleLogin"
              >
                <span v-if="!loading">Log in</span>
                <Icon v-else name="lucide:loader-2" size="16" class="animate-spin" />
              </button>

              <!-- Social Login Divider -->
              <div class="divider">
                <span>Or continue with</span>
              </div>

              <!-- Social Login Buttons -->
              <div class="social-buttons">
                <div class="social-row">
                  <div class="social-item">
                    <button
                      class="social-button google"
                      :disabled="loading"
                      @click="handleSocialLogin('google')"
                    >
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path
                          d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                          fill="#4285F4"
                        />
                        <path
                          d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                          fill="#34A853"
                        />
                        <path
                          d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                          fill="#FBBC05"
                        />
                        <path
                          d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                          fill="#EA4335"
                        />
                      </svg>
                    </button>
                  </div>
                  <div class="social-item">
                    <button
                      class="social-button discord"
                      :disabled="loading"
                      @click="handleSocialLogin('discord')"
                    >
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path
                          d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"
                          fill="#ffffff"
                        />
                      </svg>
                    </button>
                  </div>
                  <div class="social-item">
                    <button
                      class="social-button facebook"
                      :disabled="loading"
                      @click="handleSocialLogin('facebook')"
                    >
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path
                          d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
                          fill="#ffffff"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Terms Text -->
          <div class="terms-text">
            By signing in you agree with our<br >
            <a
              href="/terms"
              :class="{ disabled: loading }"
              @click.prevent="handleTermsClick('terms')"
            >
              Terms of Service </a
            >,
            <a
              href="/privacy"
              :class="{ disabled: loading }"
              @click.prevent="handleTermsClick('privacy')"
            >
              Privacy Policy </a
            >,
            <a
              href="/complaints"
              :class="{ disabled: loading }"
              @click.prevent="handleTermsClick('complaints')"
            >
              Complaints Policy </a
            >,
            <a
              href="/content-removal"
              :class="{ disabled: loading }"
              @click.prevent="handleTermsClick('content-removal')"
            >
              Content Removal Policy </a
            >,
            <a
              href="/record-keeping"
              :class="{ disabled: loading }"
              @click.prevent="handleTermsClick('record-keeping')"
            >
              18 U.S.C. 2257 Compliance </a
            >.
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
// Props and Emits
defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'Continue with'
  }
})

const emit = defineEmits(['close', 'login'])

// 运行时配置
const config = useRuntimeConfig()
const logoUrl = computed(
  () => config.public?.logoUrl || 'https://cdn.magiclight.ai/assets/playshot/logo-v2.png'
)

// 用户状态管理
const userStore = useUserStore()

// 组件挂载时初始化用户状态
onMounted(() => {
  userStore.initFromStorage()
})

// 同步用户状态到其他应用
const syncUserStateToApps = async () => {
  try {
    // 同步认证状态
    const authData = {
      token: userStore.token,
      refreshToken: userStore.refreshToken,
      userId: userStore.userId,
      isAuthenticated: userStore.isAuthenticated,
      isGuest: userStore.isGuest
    }

    // 同步用户信息
    const userData = {
      user: userStore.userInfo,
      language: 'en', // 可以从用户设置中获取
      theme: 'dark' // 可以从用户设置中获取
    }

    // 如果在iframe环境中，通知父应用
    if (window.parent !== window) {
      window.parent.postMessage(
        {
          type: 'AUTH_STATE_SYNC',
          data: authData
        },
        '*'
      )

      window.parent.postMessage(
        {
          type: 'USER_STATE_SYNC',
          data: userData
        },
        '*'
      )
    }

    console.log('✅ 用户状态已同步到其他应用')
  } catch (error) {
    console.error('❌ 用户状态同步失败:', error)
  }
}

// Form state
const form = reactive({
  email: '',
  verificationCode: ''
})

const errors = reactive({
  email: '',
  verificationCode: ''
})

const loading = ref(false)
const countdown = ref(0)

// Computed
const isEmailValid = computed(() => {
  if (!form.email) return false
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(form.email)
})

// Methods
const handleClose = () => {
  emit('close')
}

const validateEmail = () => {
  if (!form.email) {
    errors.email = 'Email is required'
    return false
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(form.email)) {
    errors.email = 'Invalid email format'
    return false
  }

  errors.email = ''
  return true
}

const validateForm = () => {
  let isValid = true
  errors.verificationCode = ''

  if (!validateEmail()) {
    isValid = false
  }

  if (!form.verificationCode) {
    errors.verificationCode = 'Verification code is required'
    isValid = false
  }

  return isValid
}

const handleSendCode = async () => {
  if (!isEmailValid.value) {
    console.error(errors.email || 'Invalid email')
    return
  }

  try {
    loading.value = true
    errors.email = ''

    await userStore.sendVerificationCode(form.email, 'login')

    // Start countdown
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)

    console.log('Verification code sent successfully')
  } catch (error: any) {
    console.error('Failed to send verification code:', error)
    errors.email = error.message || 'Failed to send verification code'
  } finally {
    loading.value = false
  }
}

const handleLogin = async () => {
  if (!validateForm()) return

  try {
    loading.value = true
    errors.verificationCode = ''

    const isLogin = await userStore.loginWithCode({
      email: form.email,
      code: form.verificationCode,
      code_type: 'login',
      gender: 'male',
      user_id: userStore.userInfo?.uuid
    })

    if (isLogin) {
      console.log('Login successful')

      // 同步用户状态到其他应用
      await syncUserStateToApps()

      emit('login')
      emit('close')
    }
  } catch (error: any) {
    console.error('Login failed:', error)
    errors.verificationCode = error.message || 'Login failed'
  } finally {
    loading.value = false
  }
}

const handleSocialLogin = async (type: 'google' | 'discord' | 'facebook') => {
  if (loading.value) return

  try {
    loading.value = true
    console.log(`Social login with ${type}`)

    const origin = window.location.origin
    const redirect_url = `${origin}/user/social-callback?login_type=${type}`

    // 存储当前页面URL用于登录成功后重定向
    const app_redirect_url = window.location.href
    if (import.meta.client) {
      sessionStorage.setItem('login_redirect', app_redirect_url)
    }

    const config = useRuntimeConfig()
    const response = await $fetch(`${config.public.apiBase}/api/v1/social-login.get-url`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: {
        login_type: type,
        redirect_url
      }
    })

    if (response.code !== '0' || !response.data?.url) {
      throw new Error('Failed to get social login URL')
    }

    const loginUrl = response.data.url
    if (!loginUrl.startsWith('http')) {
      throw new Error('Invalid login URL')
    }

    // 跳转到社交登录页面
    window.location.href = loginUrl
  } catch (error: any) {
    console.error(`${type} login failed:`, error)
    loading.value = false
  }
}

const handleTermsClick = (type: string) => {
  if (loading.value) return
  console.log(`Navigate to ${type} page`)

  // 关闭模态框并导航到对应页面
  emit('close')
  navigateTo(`/${type}`)
}
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.login-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.login-modal {
  width: 460px;
  background: var(--bg-secondary, #1a1a1a);
  border-radius: 20px;
  position: relative;
  padding: 64px 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: zoomIn 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-secondary, #999);
  transition: color 0.2s ease;
  background: none;
  border: none;
}

.close-button:hover {
  color: var(--text-primary, #fff);
}

.login-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.logo-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.logo-wrapper img {
  height: 54px;
}

.logo-wrapper h2 {
  font-family: 'Work Sans', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: var(--text-secondary, #999);
  margin: 0;
}

.form-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.input-group {
  position: relative;
}

.input-group input {
  width: 100%;
  height: 40px;
  border-radius: 40px;
  border: 0.5px solid var(--accent-color, #ca93f2);
  background: var(--bg-tertiary, #2a2a2a);
  padding: 0 16px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary, #fff);
  box-sizing: border-box;
}

.input-group input::placeholder {
  color: var(--text-tertiary, #666);
}

.input-group input:focus {
  outline: none;
  border-color: var(--accent-color, #ca93f2);
}

.input-group input.error {
  border-color: #ec4551;
}

.error-text {
  color: #ec4551;
  font-size: 12px;
  margin-top: 4px;
  display: block;
  margin-left: 16px;
}

.verification-group {
  position: relative;
}

.send-code-button {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  background: #ca93f2;
  border: none;
  border-radius: 40px;
  padding: 8px 24px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  height: 30px;
}

.send-code-button.is-inactive {
  opacity: 0.5;
}

.send-code-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.login-button {
  width: 100%;
  height: 42px;
  border: none;
  border-radius: 54px;
  background: #ca93f2;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.login-button.is-inactive {
  opacity: 0.5;
}

.login-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.login-button:not(:disabled):hover {
  opacity: 0.9;
}

.login-button:not(:disabled):active {
  transform: scale(0.98);
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.divider {
  display: flex;
  align-items: center;
  margin: 0;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  height: 0.5px;
  background: var(--divider-color, #333);
}

.divider span {
  padding: 0 12px;
  font-size: 14px;
  color: var(--text-secondary, #999);
}

.social-buttons .social-row {
  display: flex;
  justify-content: space-between;
}

.social-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.social-button {
  width: 100px;
  height: 42px;
  border-radius: 31px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.5px solid var(--border-color, #333);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s;
}

.social-button:hover {
  transform: translateY(-2px);
}

.social-button:active {
  transform: translateY(1px);
}

.social-button.google {
  background: #fff;
  color: #333;
}

.social-button.discord {
  background: #6563ff;
  color: #fff;
}

.social-button.facebook {
  background: #0866ff;
  color: #fff;
}

.social-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.terms-text {
  margin-top: 24px;
  font-size: 12px;
  text-align: center;
  color: var(--text-tertiary, #666);
  line-height: 1.17;
}

.terms-text a {
  color: var(--text-secondary, #999);
  text-decoration: none;
  transition: color 0.2s ease;
}

.terms-text a:hover {
  color: var(--text-primary, #fff);
}

.terms-text a.disabled {
  pointer-events: none;
  opacity: 0.5;
}
</style>
