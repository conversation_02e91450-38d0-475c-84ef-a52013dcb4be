<template>
  <div class="credit-display" @click="handleClick">
    <NuxtImg :src="icons.diamond.value" class="credit-icon" alt="Diamond icon" loading="lazy" />
    <div class="credit-content">
      <span
        class="credit-amount"
        :class="{
          increase: isIncreasing,
          decrease: isDecreasing
        }"
      >
        {{ formattedAmount }}
        <span v-if="isIncreasing" class="change-indicator increase">+{{ changeAmount }}</span>
        <span v-if="isDecreasing" class="change-indicator decrease">-{{ changeAmount }}</span>
      </span>
    </div>
    <div v-if="showAddButton" class="add-button" @click.stop="handleAddClick">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path
          d="M12 5v14m-7-7h14"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
// 图标配置
const { icons } = useCdn()

const props = defineProps<{
  amount: number
  showAddButton?: boolean
}>()

const emit = defineEmits<{
  (e: 'add'): void
}>()

// 跟踪上一次的金额，用于判断增减
const previousAmount = ref(props.amount)
const isIncreasing = ref(false)
const isDecreasing = ref(false)
const changeAmount = ref(0)

// 格式化金额显示
const formattedAmount = computed(() => {
  const amount = Math.floor(props.amount)
  return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
})

// 监听金额变化
watch(
  () => props.amount,
  (newAmount, oldAmount) => {
    if (oldAmount !== undefined && newAmount !== oldAmount) {
      const diff = newAmount - oldAmount
      changeAmount.value = Math.abs(diff)

      if (diff > 0) {
        isIncreasing.value = true
        isDecreasing.value = false
      } else if (diff < 0) {
        isIncreasing.value = false
        isDecreasing.value = true
      }

      // 2秒后清除动画状态
      setTimeout(() => {
        isIncreasing.value = false
        isDecreasing.value = false
      }, 2000)
    }

    previousAmount.value = newAmount
  }
)

// 状态管理
const rechargeStore = useRechargeStore()

const handleClick = () => {
  if (props.showAddButton) {
    emit('add')
  } else {
    // 显示充值弹窗
    rechargeStore.showRechargeModal()
  }
}

const handleAddClick = () => {
  emit('add')
}
</script>

<style lang="less" scoped>
.credit-display {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 28px;
  background: #b48ded;
  border-radius: 20px;
  color: #fff;
  position: relative;
  height: 24px;
  cursor: pointer;

  .credit-icon {
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 28px;
    height: 28px;
    z-index: 1;
  }

  .credit-content {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1f0038;
    height: 100%;
    line-height: 1;
    position: relative;
    left: -2px;
  }

  .credit-amount {
    font-family: 'Work Sans', sans-serif;
    font-size: 11px;
    font-weight: 600;
    position: relative;
    transition: all 0.3s ease;

    &.increase {
      color: #4caf50;
      transform: scale(1.1);
    }

    &.decrease {
      color: #f44336;
      transform: scale(1.1);
    }
  }

  .change-indicator {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 10px;
    font-weight: 600;
    opacity: 0;
    animation: changeIndicator 2s ease-out forwards;

    &.increase {
      color: #4caf50;
    }

    &.decrease {
      color: #f44336;
    }
  }

  .add-button {
    position: absolute;
    right: -5px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background: #daff96;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1f0038;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-50%) scale(1.1);
      background: #c8ff7a;
    }
  }
}

@keyframes changeIndicator {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(0);
  }
  20% {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
  }
  80% {
    opacity: 1;
    transform: translateX(-50%) translateY(-10px);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-15px);
  }
}
</style>
