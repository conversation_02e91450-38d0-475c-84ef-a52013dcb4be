<template>
  <div class="menu-wrapper">
    <NuxtLink to="/" class="menu-item" active-class="active" @click="handleMenuClick('/')">
      <div class="icon-wrapper">
        <Icon
          :name="isHomeActive ? 'lucide:home' : 'lucide:home'"
          :class="{ active: isHomeActive }"
          size="24"
        />
      </div>
      <span>Home</span>
    </NuxtLink>
    <NuxtLink
      to="/user/profile"
      class="menu-item"
      active-class="active"
      @click="handleMenuClick('/user/profile')"
    >
      <div class="icon-wrapper">
        <Icon
          :name="isProfileActive ? 'lucide:user' : 'lucide:user'"
          :class="{ active: isProfileActive }"
          size="24"
        />
      </div>
      <span>Account</span>
    </NuxtLink>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const route = useRoute()

// 计算是否为首页激活状态
const isHomeActive = computed(() => route.path === '/' || route.path === '/index')
const isProfileActive = computed(() => route.path.startsWith('/user/profile'))

// 菜单点击事件处理
const handleMenuClick = (path: string) => {
  // 这里可以添加点击事件上报逻辑
  console.log('Menu clicked:', path)
}
</script>

<style lang="less" scoped>
.menu-wrapper {
  padding: 4px 0 calc(4px + env(safe-area-inset-bottom));
  background: var(--mobile-menu-bg, var(--bg-secondary));
  display: flex;
  justify-content: space-around;
  align-items: center;
  transition: background 0.3s ease;
  min-height: 46px;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  padding: 4px 0;
  flex: 1;
  color: var(--text-tertiary);
  text-decoration: none;
  transition: all 0.3s;
  font-size: 12px;

  &.active {
    color: var(--accent-color);
  }

  span {
    font-weight: 500;
    line-height: 1;
  }

  .icon-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;

    :deep(svg) {
      width: 24px;
      height: 24px;
      transition: color 0.3s ease;
    }

    :deep(.active) {
      color: var(--accent-color);
    }
  }
}

.red-dot {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background-color: #ff4d4f;
  border-radius: 50%;
}
</style>
