<template>
  <Teleport to="body">
    <Transition name="fade">
      <div
        v-if="visible"
        class="chat-history-confirm-dialog-overlay"
        @click.self="handleOverlayClick"
      >
        <div class="chat-history-confirm-dialog">
          <!-- PC端的关闭按钮 -->
          <button
            v-if="showCloseButton"
            class="close-button"
            :title="'Close (ESC)'"
            @click="handleClose"
          >
            <Icon name="lucide:x" size="20" />
          </button>

          <div class="dialog-content">
            <p class="dialog-message">
              Your previous progress has been safely saved, you can either continue or start over
            </p>
          </div>

          <div class="dialog-actions">
            <button class="dialog-button cancel-button" @click="handleRestart">Restart</button>
            <button class="dialog-button confirm-button" @click="handleContinue">Continue</button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue'

defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'continue', 'restart'])

// 检测是否为PC端，决定是否显示关闭按钮
const showCloseButton = computed(() => {
  if (typeof window === 'undefined') return false
  return window.innerWidth >= 768
})

const handleClose = () => {
  emit('close')
}

const handleContinue = () => {
  emit('continue')
}

const handleRestart = () => {
  emit('restart')
}

// 处理点击遮罩层
const handleOverlayClick = () => {
  // 只有PC端才允许点击遮罩层关闭
  if (showCloseButton.value) {
    handleClose()
  }
}

// ESC键处理（仅PC端）
const handleKeydown = (event) => {
  if (event.key === 'Escape' && showCloseButton.value) {
    handleClose()
  }
}

onMounted(() => {
  if (typeof window !== 'undefined') {
    document.addEventListener('keydown', handleKeydown)
  }
})

onUnmounted(() => {
  if (typeof window !== 'undefined') {
    document.removeEventListener('keydown', handleKeydown)
  }
})
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.chat-history-confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.chat-history-confirm-dialog {
  background: #1f0038;
  border-radius: 16px;
  padding: 24px;
  max-width: 320px;
  width: 100%;
  animation: zoomIn 0.3s ease;
  text-align: center;
  color: white;
  position: relative;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.close-button {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.2s ease;
  z-index: 1010;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: scale(1.1);
}

.close-button:active {
  transform: scale(0.95);
}

.dialog-content {
  margin-bottom: 24px;
}

.dialog-message {
  padding-top: 24px;
  font-size: 15px;
  font-weight: 700;
  line-height: 1.4;
  margin: 0;
  color: #fff;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.dialog-button {
  flex: 1;
  height: 48px;
  border-radius: 26px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid #1f0038;
}

.cancel-button {
  background: linear-gradient(180deg, #d6cafe 0%, #ca93f2 100%);
  color: #241d49;
  border-bottom: 6px solid #1f0038;
  border-top: 2px solid #1f0038;
  border-right: 2px solid #1f0038;
  border-left: 2px solid #1f0038;
  box-shadow: 0px 1.855px 11.13px 0px #b098ff;
}

.confirm-button {
  background: linear-gradient(180deg, #f5ffe2 0%, #daff96 100%);
  color: #241d49;
  border-bottom: 6px solid #1f0038;
  border-top: 2px solid #1f0038;
  border-right: 2px solid #1f0038;
  border-left: 2px solid #1f0038;
  box-shadow: 0px 1.721px 10.324px 0px #daff96;
}

.dialog-button:hover {
  transform: translateY(-1px);
}

.dialog-button:active {
  transform: translateY(1px);
  border-bottom-width: 2px;
}

/* PC端样式适配 */
@media (min-width: 768px) {
  .chat-history-confirm-dialog {
    background: linear-gradient(180deg, #2b1b3b 0%, #1a0f24 100%);
    border-radius: 16px;
    padding: 32px;
    max-width: 400px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  }

  .dialog-content {
    margin-bottom: 32px;
  }

  .dialog-message {
    font-size: 16px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
  }

  .dialog-actions {
    gap: 16px;
  }

  .dialog-button {
    height: 48px;
    border-radius: 24px;
    font-size: 16px;
    font-weight: 600;
  }

  .cancel-button {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: none;
  }

  .confirm-button {
    background: linear-gradient(90deg, #ca93f2 0%, #9b6cc8 100%);
    color: white;
    border: none;
    box-shadow: 0 4px 12px rgba(202, 147, 242, 0.3);
  }

  .dialog-button:active {
    border-bottom-width: 1px;
  }
}
</style>

