<template>
  <footer class="footer">
    <a :href="`mailto:${brandingConfig.supportEmail}`" class="support-email">
      Support Email: {{ brandingConfig.supportEmail }}
    </a>
  </footer>
</template>

<script setup lang="ts">
const { brandingConfig } = useBranding()
</script>

<style scoped>
.footer {
  display: flex;
  justify-content: center;
  padding: 20px 16px;
  position: relative;
  width: 100%;
  background-color: var(--mobile-bg-primary);
}

.support-email {
  color: var(--text-primary);
  text-align: center;
  font-size: 11px;
  font-weight: 500;
  text-decoration: none;
  opacity: 0.5;
}

.support-email:hover {
  opacity: 0.8;
}
</style>
