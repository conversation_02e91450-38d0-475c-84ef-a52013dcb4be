<template>
  <Teleport to="body">
    <Transition name="fade">
      <div v-if="visible" class="confirm-dialog-overlay" @click.self="$emit('close')">
        <div class="confirm-dialog">
          <div class="dialog-content">
            <h3 class="dialog-title">Continue Your Story</h3>
            <p class="dialog-message">{{ content }}</p>
          </div>

          <div class="dialog-actions">
            <button class="dialog-button cancel-button" @click="$emit('cancel')">
              {{ cancelText }}
            </button>
            <button class="dialog-button confirm-button" @click="$emit('confirm')">
              {{ confirmText }}
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  content: {
    type: String,
    default: 'Are you sure?'
  },
  confirmText: {
    type: String,
    default: 'Confirm'
  },
  cancelText: {
    type: String,
    default: 'Cancel'
  }
})

defineEmits(['close', 'confirm', 'cancel'])
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.confirm-dialog {
  background: linear-gradient(180deg, #2b1b3b 0%, #1a0f24 100%);
  border-radius: 20px;
  padding: 32px 24px 24px;
  max-width: 400px;
  width: 100%;
  animation: zoomIn 0.3s ease;
  text-align: center;
  color: white;
  border: 2px solid #1f0038;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.dialog-content {
  margin-bottom: 32px;
}

.dialog-title {
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: white;
}

.dialog-message {
  font-size: 15px;
  font-weight: 700;
  line-height: 1.4;
  margin: 0;
  color: white;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.dialog-button {
  padding: 12px 24px;
  border-radius: 26px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid #1f0038;
  min-width: 120px;
}

.cancel-button {
  background: linear-gradient(180deg, #d6cafe 0%, #ca93f2 100%);
  color: rgba(0, 0, 0, 0.85);
  border-bottom: 6px solid #1f0038;
  border-top: 2px solid #1f0038;
  border-right: 2px solid #1f0038;
  border-left: 2px solid #1f0038;
}

.confirm-button {
  background: linear-gradient(180deg, #f5ffe2 0%, #daff96 100%);
  color: rgba(0, 0, 0, 0.85);
  border-bottom: 6px solid #1f0038;
  border-top: 2px solid #1f0038;
  border-right: 2px solid #1f0038;
  border-left: 2px solid #1f0038;
  box-shadow: 0px 1.721px 10.324px 0px #daff96;
}

.dialog-button:hover {
  transform: translateY(-1px);
}

.dialog-button:active {
  transform: translateY(1px);
  border-bottom-width: 2px;
}

/* 亮色主题适配 */
body.light-theme .confirm-dialog {
  background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  color: var(--text-primary);
  border-color: var(--border-color);
}

body.light-theme .dialog-title,
body.light-theme .dialog-message {
  color: var(--text-primary);
}

body.light-theme .cancel-button {
  border-color: var(--border-color);
}

body.light-theme .confirm-button {
  border-color: var(--border-color);
}
</style>
