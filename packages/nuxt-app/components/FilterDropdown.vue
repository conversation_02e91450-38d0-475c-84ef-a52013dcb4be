<template>
  <div
    ref="containerRef"
    class="filter-dropdown"
    :class="{ 'has-selection': hasSelection, 'is-open': isOpen }"
  >
    <div
      ref="triggerRef"
      class="dropdown-trigger"
      tabindex="0"
      role="button"
      :aria-expanded="isOpen"
      :aria-haspopup="true"
      @click="toggleDropdown"
      @keydown.enter="toggleDropdown"
      @keydown.space.prevent="toggleDropdown"
      @keydown.escape="closeDropdown"
    >
      <slot name="trigger" />
    </div>

    <Teleport to="body">
      <Transition :name="isInitialized ? 'dropdown-fade' : ''">
        <div
          v-if="isOpen"
          class="dropdown-overlay"
          @click="closeDropdown"
          @keydown.escape="closeDropdown"
        />
      </Transition>

      <Transition :name="isInitialized ? 'dropdown-slide' : ''">
        <div
          v-if="isOpen"
          ref="dropdownRef"
          class="dropdown-content"
          :style="dropdownStyle"
          role="menu"
          @keydown.escape="closeDropdown"
        >
          <slot :close-dropdown="closeDropdown" />
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  onUnmounted,
  watch,
  nextTick,
  defineEmits,
  defineExpose,
  readonly
} from 'vue'

interface Props {
  hasSelection?: boolean
  disabled?: boolean
  placement?: 'bottom' | 'top' | 'auto'
  offset?: number
  closeOnSelect?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  hasSelection: false,
  disabled: false,
  placement: 'auto',
  offset: 8,
  closeOnSelect: true
})

const emit = defineEmits<{
  open: []
  close: []
  toggle: [isOpen: boolean]
}>()

// 响应式状态
const isOpen = ref(false)
const isInitialized = ref(false)
const containerRef = ref<HTMLElement>()
const triggerRef = ref<HTMLElement>()
const dropdownRef = ref<HTMLElement>()
const dropdownStyle = ref<Record<string, string>>({})

// 简化的位置计算函数
const calculatePosition = (
  trigger: HTMLElement,
  dropdown: HTMLElement,
  placement: string,
  offset: number
) => {
  const triggerRect = trigger.getBoundingClientRect()
  const dropdownRect = dropdown.getBoundingClientRect()
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  let top = 0
  let left = triggerRect.left

  // 简单的垂直位置计算
  if (placement === 'top') {
    top = triggerRect.top - dropdownRect.height - offset
  } else {
    // 默认向下，如果空间不够则向上
    const spaceBelow = viewportHeight - triggerRect.bottom
    if (spaceBelow >= dropdownRect.height + offset || placement === 'bottom') {
      top = triggerRect.bottom + offset
    } else {
      top = triggerRect.top - dropdownRect.height - offset
    }
  }

  // 简单的水平位置调整
  if (left + dropdownRect.width > viewportWidth) {
    left = viewportWidth - dropdownRect.width - 16
  }
  if (left < 16) {
    left = 16
  }

  return { top, left }
}

// 核心方法
const toggleDropdown = () => {
  if (props.disabled) return

  if (isOpen.value) {
    closeDropdown()
  } else {
    openDropdown()
  }
}

const openDropdown = async () => {
  if (props.disabled || isOpen.value) return

  // 先计算位置，再显示下拉框
  if (triggerRef.value) {
    const triggerRect = triggerRef.value.getBoundingClientRect()
    // 预设一个基本位置，避免从 (0,0) 开始动画
    dropdownStyle.value = {
      position: 'absolute',
      top: `${triggerRect.bottom + props.offset}px`,
      left: `${triggerRect.left}px`,
      zIndex: '1000'
    }
  }

  isOpen.value = true
  emit('open')
  emit('toggle', true)

  await nextTick()
  await updateDropdownPosition()
}

const closeDropdown = () => {
  if (!isOpen.value) return

  isOpen.value = false
  emit('close')
  emit('toggle', false)
}

const updateDropdownPosition = async (): Promise<void> => {
  if (!triggerRef.value || !dropdownRef.value) return

  return new Promise((resolve) => {
    // 立即计算正确位置，避免从左上角飞入的动画
    try {
      const { top, left } = calculatePosition(
        triggerRef.value!,
        dropdownRef.value!,
        props.placement,
        props.offset
      )

      // 直接设置正确位置，不需要先隐藏再显示
      dropdownStyle.value = {
        position: 'absolute',
        top: `${top}px`,
        left: `${left}px`,
        zIndex: '1000'
      }
    } catch (error) {
      console.warn('FilterDropdown: Position calculation failed', error)
      // 失败时使用触发器位置作为后备
      if (triggerRef.value) {
        const triggerRect = triggerRef.value.getBoundingClientRect()
        dropdownStyle.value = {
          position: 'absolute',
          top: `${triggerRect.bottom + props.offset}px`,
          left: `${triggerRect.left}px`,
          zIndex: '1000'
        }
      }
    }

    resolve()
  })
}

// 事件处理器
const handleClickOutside = (event: Event) => {
  if (!isOpen.value || !containerRef.value) return

  const target = event.target as Node
  if (!containerRef.value.contains(target)) {
    closeDropdown()
  }
}

const handleResize = () => {
  if (isOpen.value) {
    updateDropdownPosition()
  }
}

const handleScroll = () => {
  if (isOpen.value) {
    closeDropdown()
  }
}

// 生命周期
onMounted(() => {
  // 延迟一帧确保组件完全渲染
  requestAnimationFrame(() => {
    isInitialized.value = true
  })

  document.addEventListener('click', handleClickOutside)
  window.addEventListener('resize', handleResize)
  window.addEventListener('scroll', handleScroll, true)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('scroll', handleScroll, true)
})

// 监听器
watch(isOpen, (newValue) => {
  if (newValue) {
    nextTick(() => updateDropdownPosition())
  }
})

// 暴露方法
defineExpose({
  open: openDropdown,
  close: closeDropdown,
  toggle: toggleDropdown,
  isOpen: readonly(isOpen)
})
</script>

<style scoped>
/* 基础样式 */
.filter-dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 100px;
  background-color: var(--filter-dropdown-bg, rgba(255, 255, 255, 0.08));
  color: var(--filter-dropdown-text, rgba(255, 255, 255, 0.8));
  font-family: 'Work Sans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition:
    background-color 0.2s ease,
    border-color 0.2s ease,
    color 0.2s ease;
  border: 1px solid var(--filter-dropdown-border, rgba(255, 255, 255, 0.1));
  white-space: nowrap;
  user-select: none;
  outline: none;
  position: relative;
}

.dropdown-trigger:hover:not(:disabled) {
  background-color: var(--filter-dropdown-hover-bg, rgba(255, 255, 255, 0.12));
}

.dropdown-trigger:focus-visible {
  box-shadow: 0 0 0 2px var(--filter-dropdown-focus, rgba(202, 147, 242, 0.5));
}

.dropdown-trigger:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 选中状态 */
.filter-dropdown.has-selection .dropdown-trigger {
  background-color: var(--filter-dropdown-active-bg, rgba(202, 147, 242, 0.2));
  color: var(--filter-dropdown-active-text, #ca93f2);
  border-color: var(--filter-dropdown-active-border, rgba(202, 147, 242, 0.4));
}

.filter-dropdown.is-open .dropdown-trigger {
  background-color: var(--filter-dropdown-open-bg, rgba(255, 255, 255, 0.15));
}

/* 遮罩层 */
.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.1);
}

/* 下拉内容 */
.dropdown-content {
  background: var(--dropdown-bg, rgba(31, 0, 56, 0.95));
  backdrop-filter: blur(20px);
  border-radius: 12px;
  border: 1px solid var(--dropdown-border, rgba(255, 255, 255, 0.1));
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 4px 16px rgba(0, 0, 0, 0.2);
  min-width: 160px;
  max-width: 300px;
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  /* 确保初始位置不可见，避免闪烁 */
  transform-origin: top left;
}

/* 过渡动画 - 更温和的效果 */
.dropdown-fade-enter-active,
.dropdown-fade-leave-active {
  transition: opacity 0.12s ease-out;
}

.dropdown-fade-enter-from,
.dropdown-fade-leave-to {
  opacity: 0;
}

.dropdown-slide-enter-active {
  transition: all 0.15s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.dropdown-slide-leave-active {
  transition: all 0.1s cubic-bezier(0.4, 0, 1, 1);
}

.dropdown-slide-enter-from {
  opacity: 0;
  transform: scale(0.95);
}

.dropdown-slide-leave-to {
  opacity: 0;
  transform: scale(0.98);
}

/* 滚动条样式 */
.dropdown-content::-webkit-scrollbar {
  width: 6px;
}

.dropdown-content::-webkit-scrollbar-track {
  background: transparent;
}

.dropdown-content::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb, rgba(255, 255, 255, 0.2));
  border-radius: 3px;
}

.dropdown-content::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover, rgba(255, 255, 255, 0.3));
}

/* 亮色主题适配 */
body.light-theme .dropdown-trigger {
  background-color: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.8);
  border-color: rgba(0, 0, 0, 0.1);
}

body.light-theme .dropdown-trigger:hover:not(:disabled) {
  background-color: rgba(0, 0, 0, 0.08);
}

body.light-theme .dropdown-trigger:focus-visible {
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.5);
}

body.light-theme .filter-dropdown.has-selection .dropdown-trigger {
  background-color: rgba(202, 147, 242, 0.15);
  color: #8b5cf6;
  border-color: rgba(202, 147, 242, 0.3);
}

body.light-theme .filter-dropdown.is-open .dropdown-trigger {
  background-color: rgba(0, 0, 0, 0.1);
}

body.light-theme .dropdown-overlay {
  background-color: rgba(0, 0, 0, 0.05);
}

body.light-theme .dropdown-content {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(0, 0, 0, 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
}

body.light-theme .dropdown-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
}

body.light-theme .dropdown-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dropdown-content {
    max-width: calc(100vw - 32px);
    max-height: 60vh;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .dropdown-trigger {
    border-width: 2px;
  }

  .dropdown-content {
    border-width: 2px;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .dropdown-trigger,
  .dropdown-fade-enter-active,
  .dropdown-fade-leave-active,
  .dropdown-slide-enter-active,
  .dropdown-slide-leave-active {
    transition: none;
  }

  .dropdown-trigger:hover:not(:disabled) {
    transform: none;
  }
}
</style>
