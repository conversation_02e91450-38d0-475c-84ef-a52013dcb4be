<template>
  <div class="legal-page">
    <div class="page-header">
      <div class="back-button" @click="handleBack">
        <Icon name="lucide:arrow-left" :size="18" />
      </div>
      <h1>{{ title }}</h1>
    </div>

    <div class="content">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
}

defineProps<Props>()

const handleBack = () => {
  if (import.meta.client) {
    window.history.back()
  }
}
</script>

<style lang="less" scoped>
.legal-page {
  min-height: calc(var(--vh, 1vh) * 100);
  background: var(--mobile-bg-primary, #1f0038);
  color: var(--text-primary, rgba(255, 255, 255, 0.9));
  padding-bottom: 40px;
  transition: background-color 0.3s ease;
}

.page-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: var(--mobile-bg-primary, rgba(31, 0, 56, 0.95));
  backdrop-filter: blur(10px);
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  border-bottom: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));

  .back-button {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-secondary, rgba(255, 255, 255, 0.6));
    transition: all 0.3s;
    background: var(--bg-tertiary, rgba(255, 255, 255, 0.1));
    border-radius: 50%;
    border: none;

    &:hover {
      color: var(--text-primary, white);
      background: var(--bg-hover, rgba(255, 255, 255, 0.2));
    }
  }

  h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    flex: 1;
    color: var(--accent-color, #ca93f2);
  }
}

.content {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px 20px;

  :deep(.section) {
    margin-bottom: 32px;

    &.notice {
      background: var(--notice-bg, rgba(202, 147, 242, 0.1));
      border-radius: 12px;
      padding: 20px;
      border: 1px solid var(--notice-border, rgba(202, 147, 242, 0.2));
    }

    h2 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 16px;
      color: var(--accent-color, #ca93f2);
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 12px;
      color: var(--accent-color, #ca93f2);
    }

    p {
      margin: 0 0 16px;
      line-height: 1.6;
      color: var(--text-secondary, rgba(255, 255, 255, 0.8));
      font-size: 15px;

      &:last-child {
        margin-bottom: 0;
      }

      &.last-updated {
        color: var(--text-tertiary, rgba(255, 255, 255, 0.6));
        font-size: 14px;
        font-style: italic;
        margin-bottom: 24px;
      }
    }

    ul {
      margin: 12px 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        color: var(--text-secondary, rgba(255, 255, 255, 0.8));
        font-size: 15px;
        line-height: 1.6;

        &:last-child {
          margin-bottom: 0;
        }

        strong {
          color: var(--text-primary, rgba(255, 255, 255, 0.9));
          font-weight: 600;
        }
      }
    }

    ol {
      margin: 12px 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        color: var(--text-secondary, rgba(255, 255, 255, 0.8));
        font-size: 15px;
        line-height: 1.6;

        &:last-child {
          margin-bottom: 0;
        }

        strong {
          color: var(--text-primary, rgba(255, 255, 255, 0.9));
          font-weight: 600;
        }
      }
    }

    strong {
      color: var(--text-primary, rgba(255, 255, 255, 0.9));
      font-weight: 600;
    }

    a {
      color: var(--accent-color, #ca93f2);
      text-decoration: none;
      transition: all 0.2s ease;

      &:hover {
        text-decoration: underline;
        color: #d4a5f5;
      }
    }
  }
}

/* 亮色主题适配 */
body.light-theme {
  .legal-page {
    background: var(--mobile-bg-primary-light, #ffffff);
    color: var(--text-primary-light, #1a1a1a);

    .page-header {
      background: var(--mobile-bg-primary-light, rgba(255, 255, 255, 0.95));
      border-bottom-color: var(--border-color-light, rgba(0, 0, 0, 0.1));

      .back-button {
        background: var(--bg-tertiary-light, rgba(0, 0, 0, 0.1));
        color: var(--text-secondary-light, rgba(0, 0, 0, 0.6));

        &:hover {
          background: var(--bg-hover-light, rgba(0, 0, 0, 0.2));
          color: var(--text-primary-light, #1a1a1a);
        }
      }

      h1 {
        color: var(--accent-color, #ca93f2);
      }
    }

    .content {
      :deep(.section) {
        &.notice {
          background: var(--notice-bg-light, rgba(202, 147, 242, 0.05));
          border-color: var(--notice-border-light, rgba(202, 147, 242, 0.15));
        }

        h2,
        h3 {
          color: var(--accent-color, #ca93f2);
        }

        p {
          color: var(--text-secondary-light, rgba(0, 0, 0, 0.7));

          &.last-updated {
            color: var(--text-tertiary-light, rgba(0, 0, 0, 0.5));
          }
        }

        ul li,
        ol li {
          color: var(--text-secondary-light, rgba(0, 0, 0, 0.7));

          strong {
            color: var(--text-primary-light, #1a1a1a);
          }
        }

        strong {
          color: var(--text-primary-light, #1a1a1a);
        }

        a {
          color: var(--accent-color, #ca93f2);
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 16px 20px;

    h1 {
      font-size: 18px;
    }
  }

  .content {
    padding: 20px 16px;

    :deep(.section) {
      margin-bottom: 24px;

      h2 {
        font-size: 20px;
      }

      h3 {
        font-size: 16px;
      }

      p,
      ul li,
      ol li {
        font-size: 14px;
      }

      &.notice {
        padding: 16px;
      }
    }
  }
}

@media (min-width: 769px) {
  .page-header {
    padding: 20px 40px;

    h1 {
      font-size: 24px;
    }
  }

  .content {
    padding: 40px;
    max-width: 1000px;

    :deep(.section) {
      margin-bottom: 40px;

      h2 {
        font-size: 28px;
        margin-bottom: 20px;
      }

      h3 {
        font-size: 20px;
        margin-bottom: 16px;
      }

      p,
      ul li,
      ol li {
        font-size: 16px;
      }

      &.notice {
        padding: 24px;
      }
    }
  }
}
</style>
