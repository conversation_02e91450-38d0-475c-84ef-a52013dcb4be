<template>
  <div class="pc-layout">
    <!-- 顶部导航栏 -->
    <PCHeader @show-auth="showAuth = true" @theme-toggle="handleThemeToggle" />

    <!-- 主体内容区域 -->
    <div class="content-container">
      <!-- 左侧导航栏 -->
      <PCSidebar
        :collapsed="sidebarCollapsed"
        @toggle-collapse="sidebarCollapsed = !sidebarCollapsed"
        @show-auth="showAuth = true"
        @show-checkin="handleShowCheckin"
      />

      <!-- 主要内容 -->
      <main class="main-content">
        <slot />
      </main>
    </div>

    <!-- 认证模态框 -->
    <AuthModal v-if="showAuth" @close="showAuth = false" @login="handleLogin" />

    <!-- 签到弹窗 -->
    <CheckinModal />
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  layout: false
})

// 用户状态管理
const userStore = useUserStore()
const checkinStore = useCheckinStore()

// 响应式数据
const showAuth = ref(false)
const sidebarCollapsed = ref(false)

// 方法
const handleLogin = () => {
  showAuth.value = false
}

const handleShowCheckin = () => {
  // 确保用户已登录且不是游客
  if (!userStore.isLoggedIn || userStore.isGuest) {
    showAuth.value = true
    return
  }

  // 显示签到弹窗
  checkinStore.showModal()
}

const handleThemeToggle = (isDark: boolean) => {
  if (import.meta.client) {
    localStorage.setItem('theme', isDark ? 'dark' : 'light')
    document.body.classList.toggle('light-theme', !isDark)
  }
}

// 组件挂载时初始化
onMounted(() => {
  userStore.initFromStorage()

  // 主题初始化
  const savedTheme = localStorage.getItem('theme')
  const isDark = savedTheme ? savedTheme === 'dark' : true
  document.body.classList.toggle('light-theme', !isDark)
})
</script>

<style lang="less" scoped>
.pc-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

.content-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  background-color: var(--bg-primary);
}
</style>
