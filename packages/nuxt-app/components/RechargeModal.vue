<template>
  <!-- PC版充值弹窗 -->
  <PCModal
    v-if="shouldUsePCLayout"
    v-model="modalVisible"
    title="Purchase"
    width="600px"
    @close="handleClose"
  >
    <div class="recharge-content">
      <!-- 价格列表 -->
      <div class="price-list">
        <!-- 显示实际价格项 -->
        <template
          v-if="rechargeStore.priceList && rechargeStore.priceList.length > 0"
        >
          <div
            v-for="item in rechargeStore.priceList"
            :key="item.id"
            class="price-item"
            :class="{ 'is-selected': selectedPrice?.id === item.id }"
            @click="selectPrice(item)"
          >
            <NuxtImg
              v-if="item.extra?.background_url"
              :src="item.extra.background_url"
              :alt="item.name"
              class="background-image"
              loading="lazy"
            />
            <div class="price-info">
              <div class="info-content">
                <div class="amount">${{ (item.amount / 100).toFixed(2) }}</div>
                <div class="coins">
                  <NuxtImg
                    class="diamond-icon"
                    :src="icons.diamond.value"
                    alt="diamond"
                    loading="lazy"
                  />
                  {{ item.coins }}
                </div>
              </div>
              <div v-if="item.extra?.discount_percent" class="discount">
                {{ item.extra.discount_percent }}% OFF
              </div>
            </div>
          </div>
        </template>

        <!-- 显示骨架屏 -->
        <template v-else>
          <div
            v-for="i in 3"
            :key="`skeleton-${i}`"
            class="price-item skeleton-item"
          >
            <div class="skeleton-image" />
            <div class="price-info">
              <div class="info-content">
                <div class="amount skeleton-text" />
                <div class="coins skeleton-text" />
              </div>
              <div v-if="i === 1" class="discount skeleton-discount" />
            </div>
          </div>
        </template>
      </div>

      <!-- 购买按钮 -->
      <button
        class="purchase-button"
        :disabled="!selectedPrice || rechargeStore.paymentLoading"
        @click="handlePayment(selectedPrice!)"
      >
        <template v-if="rechargeStore.paymentLoading">
          <Icon name="lucide:loader-2" :size="18" class="animate-spin" />
          Processing...
        </template>
        <template v-else> Purchase Now </template>
      </button>
    </div>
  </PCModal>

  <!-- 移动版充值弹窗 -->
  <div
    v-if="rechargeStore.visible && !shouldUsePCLayout"
    class="recharge-drawer"
    @click.self="handleClose"
  >
    <div class="drawer-content">
      <div class="drawer-header">
        <h2>Purchase</h2>
        <button class="close-button" @click="handleClose">
          <Icon name="lucide:x" :size="20" />
        </button>
      </div>

      <div class="drawer-body">
        <div class="price-list">
          <!-- 显示实际价格项 -->
          <template
            v-if="rechargeStore.priceList && rechargeStore.priceList.length > 0"
          >
            <div
              v-for="item in rechargeStore.priceList"
              :key="item.id"
              class="price-item"
              :class="{ 'is-selected': selectedPrice?.id === item.id }"
              @click="selectPrice(item)"
            >
              <NuxtImg
                v-if="item.extra?.background_url"
                :src="item.extra.background_url"
                :alt="item.name"
                class="background-image"
                loading="lazy"
              />
              <div class="price-info">
                <div class="info-content">
                  <div class="amount"
                    >${{ (item.amount / 100).toFixed(2) }}</div
                  >
                  <div class="coins">
                    <NuxtImg
                      class="diamond-icon"
                      :src="icons.diamond.value"
                      alt="diamond"
                      loading="lazy"
                    />
                    {{ item.coins }}
                  </div>
                </div>
                <div v-if="item.extra?.discount_percent" class="discount">
                  {{ item.extra.discount_percent }}% OFF
                </div>
              </div>
            </div>
          </template>

          <!-- 显示骨架屏 -->
          <template v-else>
            <div
              v-for="i in 3"
              :key="`skeleton-${i}`"
              class="price-item skeleton-item"
            >
              <div class="skeleton-image" />
              <div class="price-info">
                <div class="info-content">
                  <div class="amount skeleton-text" />
                  <div class="coins skeleton-text" />
                </div>
                <div v-if="i === 1" class="discount skeleton-discount" />
              </div>
            </div>
          </template>
        </div>
      </div>

      <div class="drawer-footer">
        <button
          class="purchase-button mobile-purchase-button"
          :disabled="!selectedPrice || rechargeStore.paymentLoading"
          @click="handlePayment(selectedPrice!)"
        >
          <template v-if="rechargeStore.paymentLoading">
            <Icon name="lucide:loader-2" :size="18" class="animate-spin" />
            Processing...
          </template>
          <template v-else> Purchase Now </template>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PriceItem } from 'shared-payment'

// 图标配置
const { icons } = useCdn()

// 状态管理
const router = useRouter()
const userStore = useUserStore()
const rechargeStore = useRechargeStore()

// 本地状态
const selectedPrice = ref<PriceItem | null>(null)

// 计算属性
const shouldUsePCLayout = computed(() => {
  if (import.meta.client) {
    return window.innerWidth >= 768
  }
  // SSR时默认为移动端，避免hydration不匹配
  return false
})

const modalVisible = computed({
  get: () => rechargeStore.visible,
  set: (value: boolean) => {
    if (!value) {
      rechargeStore.hideRechargeModal()
    }
  },
})

// 方法
const selectPrice = (price: PriceItem) => {
  selectedPrice.value = price
  console.log('Price selected:', price)
}

const handlePayment = async (price: PriceItem) => {
  if (rechargeStore.paymentLoading || !price) return

  if (userStore.isGuest) {
    rechargeStore.hideRechargeModal()
    // 跳转到登录页面或显示登录弹窗
    await router.push('/auth/login')
    return
  }

  try {
    // 使用共享支付系统，内部已处理所有错误逻辑
    await rechargeStore.createAndRedirectToPayment(price.id)
    // 成功时会自动重定向，不需要额外处理
  } catch (err: any) {
    // 只处理用户反馈，错误信息已经是用户友好的
    const { error } = useMessage()
    await error(err.message || 'Payment failed, please try again later')
  }
}

const handleClose = () => {
  selectedPrice.value = null
  rechargeStore.hideRechargeModal()
}

// 监听弹窗显示状态
watch(
  () => rechargeStore.visible,
  (visible) => {
    if (visible) {
      selectedPrice.value = null
      // 获取价格列表
      rechargeStore.fetchPriceList()
    }
  },
)

onMounted(() => {
  selectedPrice.value = null
})
</script>

<style lang="less" scoped>
// PC版样式
.recharge-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.price-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 16px;
}

.price-item {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  background: var(--bg-tertiary, rgba(255, 255, 255, 0.05));

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(202, 147, 242, 0.3);
  }

  &.is-selected {
    border-color: var(--accent-color, #ca93f2);
    box-shadow: 0 0 20px rgba(202, 147, 242, 0.5);
  }

  .background-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
  }

  .price-info {
    padding: 16px;
    position: relative;

    .info-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .amount {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary, rgba(255, 255, 255, 0.9));
      }

      .coins {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 16px;
        font-weight: 500;
        color: var(--text-secondary, rgba(255, 255, 255, 0.7));

        .diamond-icon {
          width: 16px;
          height: 16px;
        }
      }
    }

    .discount {
      position: absolute;
      top: -8px;
      right: 8px;
      background: linear-gradient(135deg, #ff6b6b, #ee5a24);
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }
  }
}

.purchase-button {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, #ca93f2, #b87de8);
  color: white;
  border: none;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #b87de8, #a66bd9);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(202, 147, 242, 0.4);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

// 骨架屏样式
.skeleton-item {
  .skeleton-image {
    width: 100%;
    height: 120px;
    background: linear-gradient(
      90deg,
      var(--bg-tertiary) 25%,
      var(--bg-hover) 50%,
      var(--bg-tertiary) 75%
    );
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
  }

  .skeleton-text {
    height: 20px;
    background: linear-gradient(
      90deg,
      var(--bg-tertiary) 25%,
      var(--bg-hover) 50%,
      var(--bg-tertiary) 75%
    );
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;

    &:first-child {
      width: 60%;
    }

    &:last-child {
      width: 40%;
    }
  }

  .skeleton-discount {
    width: 50px;
    height: 16px;
    background: linear-gradient(
      90deg,
      var(--bg-tertiary) 25%,
      var(--bg-hover) 50%,
      var(--bg-tertiary) 75%
    );
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 8px;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 移动版样式
.recharge-drawer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  animation: fadeIn 0.3s ease;

  .drawer-content {
    width: 100%;
    background: var(--bg-secondary, #1a1a1a);
    border-radius: 20px 20px 0 0;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    animation: slideUp 0.3s ease;
  }

  .drawer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));

    h2 {
      font-size: 20px;
      font-weight: 600;
      color: var(--text-primary, rgba(255, 255, 255, 0.9));
      margin: 0;
    }

    .close-button {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: transparent;
      border: none;
      color: var(--text-secondary, rgba(255, 255, 255, 0.6));
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background: var(--bg-hover, rgba(255, 255, 255, 0.1));
        color: var(--text-primary, rgba(255, 255, 255, 0.9));
      }
    }
  }

  .drawer-body {
    flex: 1;
    overflow-y: auto;
    padding: 24px;

    .price-list {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .price-item {
        display: flex;
        align-items: center;
        padding: 16px;
        border-radius: 12px;
        background: var(--bg-tertiary, rgba(255, 255, 255, 0.05));
        border: 2px solid transparent;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: var(--bg-hover, rgba(255, 255, 255, 0.1));
        }

        &.is-selected {
          border-color: var(--accent-color, #ca93f2);
          background: rgba(202, 147, 242, 0.1);
        }

        .background-image {
          width: 60px;
          height: 60px;
          border-radius: 8px;
          object-fit: cover;
          margin-right: 16px;
        }

        .price-info {
          flex: 1;
          position: relative;

          .info-content {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .amount {
              font-size: 18px;
              font-weight: 600;
              color: var(--text-primary, rgba(255, 255, 255, 0.9));
            }

            .coins {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 16px;
              font-weight: 500;
              color: var(--text-secondary, rgba(255, 255, 255, 0.7));

              .diamond-icon {
                width: 16px;
                height: 16px;
              }
            }
          }

          .discount {
            position: absolute;
            top: -8px;
            right: 0;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
          }
        }
      }
    }
  }

  .drawer-footer {
    flex-shrink: 0;
    padding: 16px 24px 24px;
    border-top: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
    background: var(--bg-secondary, #1a1a1a);
  }

  // 移动端充值按钮样式
  .mobile-purchase-button {
    width: 100%;
    height: 50px;
    border-radius: 25px;
    border: none;
    background: linear-gradient(
      90deg,
      var(--accent-color) 0%,
      var(--accent-hover) 100%
    );
    color: var(--bg-primary);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &:not(:disabled):hover {
      opacity: 0.9;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(202, 147, 242, 0.4);
    }

    &:not(:disabled):active {
      opacity: 0.8;
      transform: translateY(0);
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
</style>
