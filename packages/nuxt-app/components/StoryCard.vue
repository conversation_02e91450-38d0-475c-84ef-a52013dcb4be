<template>
  <div
    class="story-card"
    :class="{ 'is-pc': isPc, 'is-loading': loading }"
    :data-story-id="story?.id"
    @click="handleCardClick"
  >
    <!-- 骨架屏状态 -->
    <template v-if="loading">
      <div class="story-image skeleton-mode">
        <div class="image-placeholder skeleton-shimmer" />

        <!-- 模拟 badge -->
        <div class="story-badge skeleton-badge" />

        <div class="story-overlay skeleton-overlay">
          <div class="story-info">
            <div class="story-name skeleton-text" />
            <div class="story-count skeleton-text" />
            <div class="story-button-placeholder skeleton-element" />
          </div>
        </div>

        <!-- 模拟订阅按钮 -->
        <div class="subscription-status skeleton-subscription">
          <div class="subscription-button-skeleton skeleton-element" />
        </div>
      </div>
    </template>

    <!-- 正常内容状态 -->
    <template v-else>
      <div class="story-image" :class="{ 'image-loaded': imageLoaded }">
        <!-- 图片占位符 -->
        <div class="image-placeholder" :style="generatePlaceholderColor()" />

        <!-- 故事媒体（图片或视频） - 优化渲染性能 -->
        <template v-if="shouldLoadContent && carouselUrls.length > 0">
          <component
            :is="getMediaType(url)"
            v-for="(url, index) in carouselUrls"
            :key="`media-${index}`"
            :src="url"
            :alt="story?.title || 'Story'"
            v-bind="getMediaProps(url, index)"
            class="story-media"
            :class="getMediaClasses(index)"
            :style="getMediaStyles(index)"
            @load="index === 0 ? onImageLoad() : null"
            @loadeddata="index === 0 ? onImageLoad() : null"
            @error="index === 0 ? onImageError() : null"
          />
        </template>

        <!-- 收藏按钮 - 右上角 -->
        <div
          class="story-favorite-button"
          :class="{ active: isFavorited, loading: isFavoriteLoading }"
          :style="{ opacity: imageLoaded ? 1 : 0 }"
          @click.stop="toggleFavorite"
        >
          <span v-if="isFavoriteLoading" class="loading-spinner" />
          <span v-else class="heart-icon">
            <Icon v-if="isFavorited" name="lucide:heart" class="favorited" />
            <Icon v-else name="lucide:heart" class="favorite" />
          </span>
        </div>

        <!-- 徽章标签 - 左上角 -->
        <div v-if="story.badge" class="story-badge" :style="{ opacity: imageLoaded ? 1 : 0 }">
          {{ story.badge }}
        </div>

        <!-- Coming Soon 标签 -->
        <div
          v-if="story.status === 'preparing'"
          class="story-badge coming-soon"
          :style="{ opacity: imageLoaded ? 1 : 0 }"
        >
          Coming Soon
        </div>

        <!-- 管理员专属标签 -->
        <div
          v-if="story.status && !['normal', 'preparing'].includes(story.status) && isAdmin"
          class="story-badge admin-only"
          :style="{ opacity: imageLoaded ? 1 : 0 }"
        >
          <span class="admin-icon">👑</span>
          <span>Admin Only</span>
        </div>

        <!-- 故事信息覆盖层 -->
        <div class="story-overlay" :style="{ opacity: imageLoaded ? 1 : 0 }">
          <div class="story-content">
            <!-- 标题区域 -->
            <div class="story-title-section">
              <div class="story-name">{{ story?.title || 'Untitled' }}</div>
              <div v-if="showDescription && story.description" class="story-description">
                {{ story.description }}
              </div>
            </div>

            <!-- 标签区域 -->
            <div v-if="showTags" class="story-tags-section" :class="tagsDisplayClass">
              <span
                v-for="(tag, index) in allTags"
                :key="index"
                class="story-tag"
                :class="getTagClass(tag)"
              >
                {{ tag }}
              </span>
            </div>
          </div>

          <!-- 作者信息 - 按设计稿要求显示 -->
          <div v-if="story.author" class="story-author">@{{ story.author }}</div>
        </div>

        <!-- 订阅状态 -->
        <div
          v-if="story.status === 'preparing'"
          class="subscription-status"
          :style="{ opacity: imageLoaded ? 1 : 0 }"
        >
          <button
            class="subscription-button"
            :class="{ subscribed: story.is_subscribed, loading: isSubscribing }"
            :disabled="isSubscribing"
            @click.stop="handleSubscriptionToggle"
          >
            <div class="subscription-button-icon">
              <div v-if="isSubscribing" class="loading-spinner" />
              <template v-else>
                <Icon v-if="story.is_subscribed" name="lucide:check" />
                <Icon v-else name="lucide:bell" />
              </template>
            </div>
            <div class="subscription-button-text">
              {{ isSubscribing ? 'Loading...' : story.is_subscribed ? 'Subscribed' : 'Subscribe' }}
            </div>
          </button>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'

// 定义Story类型
interface Story {
  id: string
  title: string
  description?: string
  preview_url?: string
  carousel_image_url?: string[]
  badge?: string
  status?: 'normal' | 'preparing' | 'admin_only'
  is_subscribed?: boolean
  is_fav?: boolean
  author?: string
  tags?: string[]
  categories?: string[]
  uuid?: string
  cover_url?: string
  image_url?: string
}

const props = defineProps<{
  story: Story
  isPc?: boolean
  loading?: boolean
  lazyLoad?: boolean
  inViewport?: boolean
}>()

const emit = defineEmits<{
  click: [story: Story]
  'image-loaded': []
  'image-error': []
  'subscription-change': [story: Story]
  'need-login': []
  'need-email': []
}>()

// 响应式状态
const imageLoaded = ref(false)
const hasError = ref(false)
const isSubscribing = ref(false)
const isComponentMounted = ref(false)

// 收藏相关状态
const isFavorited = ref(false)
const isFavoriteLoading = ref(false)

// 轮播相关状态
const currentCarouselIndex = ref(0)
const nextCarouselIndex = ref(0)
const carouselTimer = ref<ReturnType<typeof setInterval> | null>(null)
const isTransitioning = ref(false)
const preloadedImages = ref<Set<string>>(new Set())
const isVisible = ref(true)
const intersectionObserver = ref<IntersectionObserver | null>(null)

// 懒加载状态
const shouldLoadImages = ref(false)
const hasStartedLoading = ref(false)

// CDN 工具函数
const { getBrandImageUrl } = useCdn()

// 用户状态管理
const userStore = useUserStore()

// 消息提示
const { success, error } = useMessage()

// 模拟主题状态
const isDarkTheme = ref(true)

// 从用户状态获取管理员状态和认证状态
const isAdmin = computed(() => userStore.isAdmin)
const isAuthenticated = computed(() => userStore.isLoggedIn) // 使用 isLoggedIn 而不是 isAuthenticated
const userEmail = computed(() => userStore.userInfo?.email || '')

// 显示控制
const showDescription = ref(true) // 简化版本，直接显示描述
const showTags = ref(true) // 简化版本，直接显示标签
const tagsDisplayClass = ref('tags-two-lines') // 默认两行显示

// 懒加载控制
const shouldLoadContent = computed(() => {
  if (!props.lazyLoad) return true
  if (props.inViewport !== undefined) {
    return props.inViewport
  }
  return shouldLoadImages.value
})

// 轮播图片/视频数组
const carouselUrls = computed(() => {
  if (!shouldLoadContent.value) {
    return [] // 懒加载时返回空数组
  }

  if (props.story?.carousel_image_url && props.story.carousel_image_url.length > 0) {
    return props.story.carousel_image_url
  }
  return props.story?.preview_url
    ? [props.story.preview_url]
    : [getBrandImageUrl('story/default-preview.png')]
})

// 性能优化：缓存是否需要轮播
const shouldShowCarousel = computed(() => shouldLoadContent.value && carouselUrls.value.length > 1)

// 媒体类型检测
const mediaTypeCache = new Map<string, string>()
const getMediaType = (url: string): string => {
  if (mediaTypeCache.has(url)) {
    return mediaTypeCache.get(url)!
  }
  const type = /\.(mp4|webm|ogg|mov|avi)(\?.*)?$/i.test(url) ? 'video' : 'img'
  mediaTypeCache.set(url, type)
  return type
}

// 媒体属性
const getMediaProps = (url: string, index: number) => {
  const isVideo = getMediaType(url) === 'video'
  const isFirst = index === 0

  if (isVideo) {
    return {
      muted: true,
      autoplay: true,
      loop: true,
      playsinline: true,
      preload: 'metadata'
    }
  } else {
    return {
      loading: 'lazy',
      decoding: 'async',
      fetchpriority: isFirst ? 'high' : 'low'
    }
  }
}

// 媒体样式类
const getMediaClasses = (index: number) => ({
  current: index === currentCarouselIndex.value,
  next: index === nextCarouselIndex.value && shouldShowCarousel.value,
  transitioning: isTransitioning.value
})

// 媒体内联样式
const getMediaStyles = (index: number) => ({
  opacity: index === currentCarouselIndex.value ? 1 : 0,
  display:
    index === currentCarouselIndex.value ||
    (index === nextCarouselIndex.value && isTransitioning.value)
      ? 'block'
      : 'none'
})

// 合并所有标签
const allTags = computed(() => {
  const tags = props.story?.tags || []
  const categories = props.story?.categories || []
  return [...tags, ...categories]
})

// 标签类型判断
const getTagClass = (tag: string) => {
  const tags = props.story?.tags || []
  return tags.includes(tag) ? 'tags-tag' : 'category-tag'
}

// 生成占位符颜色
const generatePlaceholderColor = () => {
  if (isDarkTheme.value) {
    return {
      background: '#140a29',
      backgroundImage: 'linear-gradient(234deg, #1f0038 0%, #140a29 100%)',
      animation: !imageLoaded.value ? 'placeholderShimmer 2s infinite linear' : 'none'
    }
  } else {
    return {
      background: '#f5f5f5',
      backgroundImage: 'linear-gradient(234deg, #e0e0e0 0%, #f5f5f5 100%)',
      animation: !imageLoaded.value ? 'placeholderShimmer 2s infinite linear' : 'none'
    }
  }
}

// 图片加载处理
const onImageLoad = () => {
  if (!isComponentMounted.value) return

  requestAnimationFrame(() => {
    if (!isComponentMounted.value) return
    imageLoaded.value = true
    emit('image-loaded')
  })
}

const onImageError = () => {
  if (!isComponentMounted.value) return
  hasError.value = true
  emit('image-error')
}

// 卡片点击处理
const handleCardClick = (event: MouseEvent) => {
  if (!props.story) return

  const subscriptionButton = (event.target as HTMLElement)?.closest('.subscription-button')
  if (props.story.status === 'preparing') {
    if (subscriptionButton) {
      return
    } else {
      // 简化版本：直接显示消息
      console.log('The Story is coming soon, stay tuned')
    }
  } else {
    emit('click', props.story)
  }
}

// 收藏切换
const toggleFavorite = async () => {
  if (isFavoriteLoading.value || !props.story) return

  if (!isAuthenticated.value) {
    emit('need-login')
    return
  }

  isFavoriteLoading.value = true

  try {
    const api = useApi()
    const previousState = isFavorited.value

    // 乐观更新：立即更新本地状态
    isFavorited.value = !previousState

    // 调用真实的API
    const response = previousState
      ? await api.removeFavorite(props.story.id)
      : await api.addFavorite(props.story.id)

    if (response.code === '0') {
      // API调用成功，发出事件通知父组件更新数据
      emit('subscription-change', { ...props.story, is_fav: !previousState })
      success(`${!previousState ? 'Added to' : 'Removed from'} favorites`)
    } else {
      // API调用失败，恢复之前的状态
      isFavorited.value = previousState
      error('Failed to update favorite status')
    }
  } catch (err) {
    // 发生错误时，恢复之前的状态
    isFavorited.value = !isFavorited.value
    error('Failed to toggle favorite')
    console.error('Failed to toggle favorite:', err)
  } finally {
    isFavoriteLoading.value = false
  }
}

// 订阅切换
const handleSubscriptionToggle = async () => {
  if (isSubscribing.value || !props.story) return

  if (!isAuthenticated.value) {
    emit('need-login')
    return
  }

  if (!userEmail.value) {
    emit('need-email')
    return
  }

  isSubscribing.value = true

  try {
    const api = useApi()
    const startTime = Date.now()

    // 调用真实的API
    if (props.story.is_subscribed) {
      await api.unsubscribeStory(props.story.id)
      success('Unsubscribed successfully')
    } else {
      await api.subscribeStory(props.story.id, userEmail.value)
      success("We'll email you when it's live")
    }

    // 确保最小loading时间
    const loadingTime = Date.now() - startTime
    if (loadingTime < 300) {
      await new Promise((resolve) => setTimeout(resolve, 300 - loadingTime))
    }

    const newSubscriptionStatus = !props.story.is_subscribed
    emit('subscription-change', {
      ...props.story,
      is_subscribed: newSubscriptionStatus
    })
  } catch (err) {
    error('Failed to update subscription')
    console.error('Failed to toggle subscription:', err)
  } finally {
    isSubscribing.value = false
  }
}

// 轮播控制 - 优化：只在可见且有内容时启动
const startCarousel = () => {
  if (!shouldLoadContent.value || carouselUrls.value.length <= 1 || !isVisible.value) return

  stopCarousel()

  const interval = 5000 // 固定间隔，简化逻辑

  carouselTimer.value = setInterval(() => {
    performCarouselTransition()
  }, interval)
}

const stopCarousel = () => {
  if (carouselTimer.value) {
    clearInterval(carouselTimer.value)
    carouselTimer.value = null
  }
}

const performCarouselTransition = () => {
  if (isTransitioning.value || carouselUrls.value.length <= 1 || !isComponentMounted.value) {
    return
  }

  isTransitioning.value = true

  const nextIndex = (currentCarouselIndex.value + 1) % carouselUrls.value.length
  nextCarouselIndex.value = nextIndex

  // 简化的切换动画
  setTimeout(() => {
    currentCarouselIndex.value = nextIndex
    isTransitioning.value = false
  }, 800)
}

// 初始化懒加载
const initializeLazyLoading = () => {
  if (!props.lazyLoad || props.inViewport !== undefined) {
    shouldLoadImages.value = true
    hasStartedLoading.value = true
    return
  }

  // 使用 Intersection Observer 实现懒加载
  if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
    const cardElement = document.querySelector(`[data-story-id="${props.story?.id}"]`)
    if (cardElement) {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting && !hasStartedLoading.value) {
              shouldLoadImages.value = true
              hasStartedLoading.value = true
              observer.unobserve(entry.target)
            }
          })
        },
        {
          rootMargin: '50px', // 提前50px开始加载
          threshold: 0.1
        }
      )
      observer.observe(cardElement)
      intersectionObserver.value = observer
    }
  } else {
    // 降级方案：直接加载
    shouldLoadImages.value = true
    hasStartedLoading.value = true
  }
}

// 生命周期
onMounted(() => {
  isComponentMounted.value = true
  isFavorited.value = props.story?.is_fav || false

  // 初始化懒加载
  initializeLazyLoading()

  // 启动轮播 - 延迟到内容加载后
  nextTick(() => {
    if (shouldShowCarousel.value) {
      nextCarouselIndex.value = 1
      setTimeout(() => {
        startCarousel()
      }, 1000)
    }
  })
})

onBeforeUnmount(() => {
  isComponentMounted.value = false
  stopCarousel()

  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect()
    intersectionObserver.value = null
  }

  mediaTypeCache.clear()
  preloadedImages.value.clear()
})

// 监听收藏状态变化
watch(
  () => props.story?.is_fav,
  (newIsFav) => {
    isFavorited.value = newIsFav || false
  },
  { immediate: true }
)

// 监听视口状态变化（用于虚拟滚动）
watch(
  () => props.inViewport,
  (inViewport) => {
    if (props.lazyLoad && inViewport !== undefined) {
      isVisible.value = inViewport

      if (inViewport && !hasStartedLoading.value) {
        shouldLoadImages.value = true
        hasStartedLoading.value = true
      }

      // 控制轮播
      if (inViewport && shouldShowCarousel.value) {
        startCarousel()
      } else {
        stopCarousel()
      }
    }
  },
  { immediate: true }
)
</script>

<style scoped>
@keyframes placeholderShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 优化动画性能 */
@media (prefers-reduced-motion: reduce) {
  .story-card {
    transition: none;
  }

  .story-card:hover {
    transform: none;
  }

  @keyframes placeholderShimmer {
    0%,
    100% {
      background-position: 0 0;
    }
  }
}

@keyframes skeletonPulse {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 0.3;
  }
}

@keyframes skeletonShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes breathing {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.breathing-animation {
  animation: breathing 2s ease-in-out infinite;
}

.story-card {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  background: #1f0038;
  width: 100%;
  aspect-ratio: 9/16; /* 手机端默认9:16比例 */
  transform: translateZ(0); /* 强制硬件加速 */
  cursor: pointer;
  /* 优化过渡效果，减少重排重绘 */
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  /* 性能优化 */
  will-change: transform; /* 提示浏览器优化 */
  contain: layout style paint; /* 限制重绘范围 */
}

/* 只在非加载状态下启用 hover 效果 */
.story-card:hover:not(.is-loading) {
  transform: translateZ(0) translateY(-2px); /* 减少位移距离 */

  /* 使用 CSS 变量控制子元素动画，避免重复计算 */
  --hover-scale: 1.02; /* 减少缩放比例 */
}

.story-card:hover:not(.is-loading) .story-button img {
  transform: scale(var(--hover-scale, 1));
}

.story-card:hover:not(.is-loading) .story-image .story-media.current {
  transform: scale(var(--hover-scale, 1));
}

/* 骨架屏状态样式 */
.story-card.is-loading {
  cursor: wait;
  pointer-events: none;
}

.story-card.is-loading .skeleton-mode .skeleton-shimmer {
  /* 暗色主题骨架屏 */
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  background-size: 200% 100%;
  animation: skeletonShimmer 1.5s infinite;
}

/* 亮色主题骨架屏 */
body.light-theme .story-card.is-loading .skeleton-mode .skeleton-shimmer {
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.05) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.05) 100%
  );
}

.story-card.is-loading .skeleton-mode .skeleton-element {
  background: rgba(255, 255, 255, 0.1);
  animation: skeletonPulse 1.5s infinite ease-in-out;
}

/* 亮色主题适配 */
body.light-theme .story-card.is-loading .skeleton-mode .skeleton-element {
  background: rgba(0, 0, 0, 0.08);
}

.story-card.is-loading .skeleton-mode .skeleton-text {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 4px;
  animation: skeletonPulse 1.5s infinite ease-in-out;
}

/* 亮色主题适配 */
body.light-theme .story-card.is-loading .skeleton-mode .skeleton-text {
  background: rgba(0, 0, 0, 0.1);
}

.story-card.is-loading .skeleton-mode .skeleton-badge {
  background: rgba(202, 147, 242, 0.3);
  animation: skeletonPulse 1.5s infinite ease-in-out;
  animation-delay: 0.2s;
}

/* 亮色主题适配 */
body.light-theme .story-card.is-loading .skeleton-mode .skeleton-badge {
  background: rgba(202, 147, 242, 0.2);
}

/* PC端样式 */
.story-card.is-pc {
  aspect-ratio: 9/16;
  border-radius: 20px;
  background: var(--bg-card, rgba(255, 255, 255, 0.08));
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.story-card.is-pc:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3);
}

.story-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.story-image.loading .story-badge,
.story-image.loading .story-overlay {
  opacity: 0;
  visibility: hidden;
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.3s ease;
  background-size: 200% 100%;
  z-index: 1;
  pointer-events: none;
}

.story-media {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  z-index: 2; /* 保持在较低层级，不覆盖UI元素 */
}

/* 当前显示的媒体 */
.story-media.current {
  z-index: 2;
}

/* 下一张媒体（用于过渡） */
.story-media.next {
  z-index: 1; /* 在当前图片下方 */
}

/* 过渡状态下的样式 */
.story-media.transitioning {
  transition: none; /* 禁用CSS过渡，使用JS动画 */
}

.story-image.image-loaded .image-placeholder {
  opacity: 0;
}

.story-image.image-loaded .story-badge,
.story-image.image-loaded .story-overlay {
  opacity: 1;
  visibility: visible;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;
}

/* 右上角收藏按钮 */
.story-favorite-button {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 10; /* 提高z-index确保在媒体元素之上 */
  transition: all 0.3s ease;
  will-change: opacity, transform;
  cursor: pointer;
  border-radius: 50%;
}

.story-favorite-button:hover:not(.loading) {
  transform: scale(1.1);
}

.story-favorite-button.loading {
  cursor: not-allowed;
  opacity: 0.7;
}

.story-favorite-button.loading:hover {
  transform: none;
}

.story-favorite-button .heart-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s ease;
}

.story-favorite-button .heart-icon svg {
  width: 26px;
  height: 26px;
}

.story-favorite-button .loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin: 0;
  padding: 0;
  display: block;
  flex-shrink: 0;
}

.story-badge {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 0px 0px 10px 0px;
  background: #ca93f2;
  display: inline-flex;
  padding: 2px 10px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  color: #fff;
  font-size: 11px;
  font-weight: 600;
  z-index: 10; /* 提高z-index确保在媒体元素之上 */
  transition: opacity 0.5s ease;
  will-change: opacity;
}

.story-badge.coming-soon {
  background: #daff96;
  color: #1f0038;
}

.story-badge.admin-only {
  left: auto;
  right: 0;
  top: 0;
  background: #ff6b6b;
  border-radius: 0px 0px 0px 10px;
  padding: 2px 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
}

.story-badge.admin-only .admin-icon {
  font-size: 12px;
}

/* 公共的story-overlay样式 */
.story-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-end; /* 改为flex-end，让内容贴底部 */
  gap: 8px; /* 减少间距 */
  padding: 16px 12px 6px; /* 减少上边距，让覆盖层更小 */
  background: linear-gradient(180deg, rgba(31, 0, 56, 0) 0%, rgba(31, 0, 56, 0.7) 42.54%);
  color: white;
  z-index: 10; /* 提高z-index确保在媒体元素之上 */
  transition: opacity 0.5s ease;
  will-change: opacity;
}

.story-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.story-title-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.story-name {
  font-size: 12px; /* 移动端默认大小 */
  font-weight: 600;
  line-height: 1.173;
  color: #ffffff;
  text-shadow: 0px 1px 3px rgba(0, 0, 0, 0.2);
  /* 单行显示，超出部分显示省略号 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.story-description {
  font-size: 10px; /* 移动端默认大小 */
  font-weight: 400;
  line-height: 1.173;
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 0px 1px 3px rgba(0, 0, 0, 0.2);
  /* 最多显示两行，超出部分显示省略号 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.story-tags-section {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  overflow: hidden;
}

/* A组：超过两行省略 */
.story-tags-section.tags-two-lines {
  max-height: calc(2 * (10px + 4px + 4px)); /* 2行 * (字体大小 + padding上下 + 行间距) */
}

/* B组：仅显示一行 */
.story-tags-section.tags-single-line {
  max-height: calc(1 * (10px + 4px + 4px)); /* 1行 * (字体大小 + padding上下 + 行间距) */
}

.story-tag {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2px 4px; /* 移动端默认padding */
  border-radius: 27px;
  font-family: 'Work Sans', sans-serif;
  font-size: 10px; /* 移动端默认大小 */
  font-weight: 400;
  line-height: 1.173;
  color: #ffffff;
  text-shadow: 0px 1px 3px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
  flex-shrink: 0;
}

/* tags标签样式 - 按设计稿使用#D8A2FF背景 */
.story-tag.tags-tag {
  background: #d8a2ff;
}

/* categories标签样式 - 按设计稿使用半透明背景 */
.story-tag.category-tag {
  background: rgba(224, 224, 224, 0.2);
}

.story-author {
  font-family: 'Work Sans', sans-serif;
  font-size: 12px; /* 移动端默认大小 */
  font-weight: 400;
  line-height: 1.173;
  color: rgba(255, 255, 255, 0.5);
  text-shadow: 0px 1px 3px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.subscription-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  width: 80%;
  gap: 12px;
  z-index: 15; /* 最高z-index确保订阅按钮在最上层 */
  pointer-events: auto;
  transition: opacity 0.5s ease;
  will-change: opacity;
}

.subscription-button {
  width: fit-content;
  min-width: 120px;
  height: 30px;
  border-radius: 18px;
  font-weight: 600;
  cursor: pointer;
  color: #1f0038;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 1px solid #daff96;
  font-size: 12px;
  background: #daff96;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px 12px;
  -webkit-tap-highlight-color: transparent;
}

.subscription-button.subscribed {
  background: rgba(0, 0, 0, 0.5);
  border-color: transparent;
  color: rgba(255, 255, 255, 0.8);
}

.subscription-button.loading {
  cursor: not-allowed;
  opacity: 0.7;
}

.subscription-button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.subscription-button-icon :deep(svg) {
  width: 14px;
  height: 14px;
}

.subscription-button-text {
  flex: 1;
  line-height: 1;
  text-align: left;
  padding-right: 8px;
  white-space: nowrap;
}

.subscription-button .loading-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

/* PC端详细样式适配 */
.story-card.is-pc .story-name {
  font-size: 16px;
}

.story-card.is-pc .story-description {
  font-size: 12px;
}

.story-card.is-pc .story-image {
  border-radius: 20px;
  overflow: hidden;
}

.story-card.is-pc .story-image .story-media {
  transition: transform 0.5s ease;
}

.story-card.is-pc .story-image .story-media:not(.transitioning) {
  transition: transform 0.5s ease;
}

.story-card.is-pc .story-image.image-loaded .story-media.current {
  transform: scale(1.02); /* 默认就有轻微放大效果 */
}

.story-card.is-pc .story-favorite-button {
  width: 40px;
  height: 40px;
}

.story-card.is-pc .story-favorite-button .heart-icon svg {
  width: 22px;
  height: 22px;
}

.story-card.is-pc .story-badge {
  padding: 6px 14px;
  font-size: 13px;
  font-weight: 600;
  border-radius: 0 0 12px 0;
}

.story-card.is-pc .story-badge.coming-soon {
  background: #daff96;
  color: #1f0038;
}

.story-card.is-pc .story-badge.admin-only {
  border-radius: 0 0 0 12px;
  padding: 6px 14px;
}

.story-card.is-pc .subscription-button {
  min-width: 160px;
  height: 40px;
  font-size: 15px;
  border-radius: 20px;
  padding: 0 20px;
}

.story-card.is-pc .subscription-button-icon {
  width: 24px;
  height: 24px;
}

.story-card.is-pc .subscription-button-icon :deep(svg) {
  width: 18px;
  height: 18px;
}

/* 骨架屏详细样式 */
.skeleton-mode .skeleton-overlay .story-info .story-name {
  height: 20px;
  width: 70%;
  margin-bottom: 8px;
}

.skeleton-mode .skeleton-overlay .story-info .story-count {
  height: 16px;
  width: 60px;
  margin-bottom: 12px;
  animation-delay: 0.3s;
}

.skeleton-mode .skeleton-overlay .story-info .story-button-placeholder {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-top: 10px;
  animation-delay: 0.5s;
}

.skeleton-subscription .subscription-button-skeleton {
  width: 120px;
  height: 30px;
  border-radius: 16px;
  animation-delay: 0.4s;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
