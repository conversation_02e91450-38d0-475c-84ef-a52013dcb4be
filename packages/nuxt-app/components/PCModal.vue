<template>
  <Teleport to="body">
    <Transition name="fade">
      <div v-if="modelValue" class="pc-modal-overlay" @click.self="handleClose">
        <div class="pc-modal" :style="modalStyle">
          <div v-if="!hideHeader" class="modal-header">
            <h2 class="title">{{ title }}</h2>
            <div class="close-button" @click="handleClose">
              <Icon name="lucide:x" :size="20" />
            </div>
          </div>

          <div class="modal-content">
            <slot/>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
const props = defineProps<{
  modelValue: boolean
  title?: string
  width?: string | number
  maxHeight?: string | number
  hideHeader?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'close'): void
}>()

const modalStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.width) {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width
  }

  if (props.maxHeight) {
    style.maxHeight = typeof props.maxHeight === 'number' ? `${props.maxHeight}px` : props.maxHeight
  }

  return style
})

const handleClose = () => {
  emit('update:modelValue', false)
  emit('close')
}
</script>

<style lang="less" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.pc-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.pc-modal {
  width: 600px;
  max-height: 90vh;
  background: var(--bg-secondary, #1a1a1a);
  border-radius: 20px;
  position: relative;
  overflow: hidden;
  animation: zoomIn 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
  border-bottom: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
  flex-shrink: 0;

  .title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary, rgba(255, 255, 255, 0.9));
    margin: 0;
  }

  .close-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: transparent;
    border: none;
    color: var(--text-secondary, rgba(255, 255, 255, 0.6));
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      background: var(--bg-hover, rgba(255, 255, 255, 0.1));
      color: var(--text-primary, rgba(255, 255, 255, 0.9));
    }
  }
}

.modal-content {
  padding: 32px;
  overflow-y: auto;
  flex: 1;
}

/* 亮色主题适配 */
:global(.light-theme) {
  .pc-modal {
    background: var(--bg-secondary, white);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  .modal-header {
    border-bottom-color: var(--border-color, rgba(0, 0, 0, 0.1));

    .title {
      color: var(--text-primary, rgba(0, 0, 0, 0.85));
    }

    .close-button {
      color: var(--text-secondary, rgba(0, 0, 0, 0.45));

      &:hover {
        background: var(--bg-hover, rgba(0, 0, 0, 0.05));
        color: var(--text-primary, rgba(0, 0, 0, 0.85));
      }
    }
  }
}
</style>
