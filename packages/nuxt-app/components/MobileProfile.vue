<template>
  <div class="profile-view">
    <div ref="container" class="profile-container">
      <!-- 上半部分：用户信息 -->
      <div
        class="upper-section"
        :style="{
          height: `${upperSectionHeight}px`
        }"
      >
        <div class="mobile-header">
          <h1>My Profile</h1>
          <div class="settings-button" @click="handleSettings">
            <Icon size="22" name="lucide:settings" />
          </div>
        </div>

        <div class="user-section">
          <div class="user-info">
            <div class="left-section">
              <div class="avatar">
                <NuxtImg
                  :src="userStore.userInfo?.avatar_url || icons.defaultAvatar.value"
                  alt="avatar"
                  loading="eager"
                />
              </div>
              <div class="info-column">
                <div class="info">
                  <h2 class="username">{{ userStore.userInfo?.name }}</h2>
                  <p class="uid">UID {{ userStore.userInfo?.uuid?.slice(0, 7) }}</p>
                </div>
                <CreditDisplay
                  :amount="userStore.userInfo?.coins || 0"
                  class="credit-display"
                  @add="handleUpgrade('creditButton')"
                />
              </div>
            </div>
            <div class="action-buttons">
              <button class="tasks-button" @click="handleDailyTasks">Daily Tasks</button>
              <button class="purchase-button" @click="handleUpgrade('purchaseButton')">
                Purchase
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 下半部分：历史记录 -->
      <div
        ref="bottomSheet"
        class="bottom-sheet"
        :class="{ expanded, initializing: isInitializing }"
        :style="{
          '--sheet-height': `${sheetHeight}px`,
          '--translate-y': `${translateY}px`
        }"
        @pointerdown="onPointerDown"
        @pointermove="onPointerMove"
        @pointerup="onPointerUp"
        @pointerleave="onPointerUp"
      >
        <div class="sheet-handle">
          <div class="handle-line" />
        </div>

        <div ref="sheetContent" class="sheet-content">
          <div class="section-header">
            <div class="tabs">
              <button
                class="tab"
                :class="{ active: activeTab === 'history' }"
                @click="handleTabClick('history')"
              >
                History
              </button>
              <button
                class="tab"
                :class="{ active: activeTab === 'like' }"
                @click="handleTabClick('like')"
              >
                Like
              </button>
            </div>
          </div>

          <div v-if="activeTab === 'history'" class="history-container">
            <p v-if="userStore.userPlayedStories.length === 0" class="empty-text">
              No browsing history available yet.
            </p>
            <StoryCard
              v-for="story in userStore.userPlayedStories"
              :key="story.id"
              :story="story"
              :is-pc="false"
              @click="handleStoryClick(story)"
            />
          </div>
          <div v-if="activeTab === 'like'" class="history-container">
            <p v-if="userStore.userLikedStories.length === 0" class="empty-text">
              No Liked history yet.
            </p>
            <StoryCard
              v-for="story in userStore.userLikedStories"
              :key="story.id"
              :story="story"
              :is-pc="false"
              @click="handleStoryClick(story)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'

// 图标配置
const { icons } = useCdn()

// 定义Story类型，与StoryCard组件兼容
interface Story {
  id: string
  title: string
  description?: string
  preview_url?: string
  carousel_image_url?: string[]
  badge?: string
  status?: 'normal' | 'preparing' | 'admin_only'
  is_subscribed?: boolean
  is_fav?: boolean
  author?: string
  tags?: string[]
  categories?: string[]
  // 兼容用户故事数据的字段
  uuid?: string
  cover_url?: string
  image_url?: string
}

// 用户状态管理
const userStore = useUserStore()

// 状态
const activeTab = ref('history')

// 底部面板状态
const container = ref<HTMLElement | null>(null)
const bottomSheet = ref<HTMLElement | null>(null)
const expanded = ref(false)
const dragging = ref(false)
const startY = ref(0)
const currentY = ref(0)
const progress = ref(0)

// 高度计算
const upperSectionHeight = ref(0)
const sheetHeight = ref(0)
const minHeight = ref(300)
const maxHeight = ref(0)

const translateY = computed(() => {
  const totalHeight = maxHeight.value - minHeight.value
  if (!dragging.value) {
    return expanded.value ? 0 : totalHeight
  }
  const delta = currentY.value - startY.value
  return Math.max(0, Math.min(delta + (expanded.value ? 0 : totalHeight), totalHeight))
})

const isInitializing = ref(true)
const sheetContent = ref<HTMLElement | null>(null)

// 初始化尺寸
const initializeSizes = () => {
  if (!container.value) return

  // 移动端保持响应式计算
  const containerHeight = container.value.clientHeight
  upperSectionHeight.value = containerHeight * 0.25
  maxHeight.value = containerHeight * 0.7
  minHeight.value = containerHeight * 0.4
  sheetHeight.value = maxHeight.value

  // 使用 nextTick 确保在 DOM 更新后移除初始化状态
  nextTick(() => {
    setTimeout(() => {
      isInitializing.value = false
    }, 0)
  })
}

// 手势处理
const onPointerDown = (e: PointerEvent) => {
  // 如果点击的是 handle，或者内容在顶部时，允许拖拽
  const isHandleDrag = (e.target as HTMLElement).closest('.sheet-handle')

  if (!isHandleDrag) {
    return
  }

  dragging.value = true
  startY.value = e.clientY
  currentY.value = e.clientY
  bottomSheet.value?.setPointerCapture(e.pointerId)
}

const onPointerMove = (e: PointerEvent) => {
  if (!dragging.value) return

  const delta = e.clientY - startY.value
  const totalHeight = maxHeight.value - minHeight.value

  if (expanded.value) {
    if (delta > 0) {
      currentY.value = e.clientY
      progress.value = Math.min(1, Math.max(0, 1 - delta / totalHeight))
      e.preventDefault()
    } else {
      currentY.value = e.clientY
      progress.value = Math.min(1, Math.max(0, 1 - delta / totalHeight))
    }
  } else {
    currentY.value = e.clientY
    progress.value = Math.min(1, Math.max(0, -delta / totalHeight))
  }
}

const onPointerUp = (e: PointerEvent) => {
  if (!dragging.value) return
  dragging.value = false

  const velocity = e.clientY - currentY.value
  const shouldExpand = expanded.value ? progress.value > 0.5 : progress.value > 0.5 || velocity < -5

  expanded.value = shouldExpand
  progress.value = shouldExpand ? 1 : 0

  // 保存状态到 localStorage
  if (import.meta.client) {
    localStorage.setItem('bottom_sheet_expanded', shouldExpand.toString())
  }

  bottomSheet.value?.releasePointerCapture(e.pointerId)
}

// 处理标签点击
const handleTabClick = async (tab: string) => {
  activeTab.value = tab

  // 重置滚动位置
  if (sheetContent.value) {
    sheetContent.value.scrollTop = 0
  }
}

// 处理故事点击
const handleStoryClick = (story: Story) => {
  console.log('Story clicked:', story)
  navigateTo(`/story/${story.id}`)
}

// 处理设置按钮点击
const handleSettings = () => {
  navigateTo('/user/settings')
}

// 处理升级/充值
const handleUpgrade = (type: string) => {
  const rechargeStore = useRechargeStore()
  rechargeStore.showRechargeModal()
  console.log('Upgrade clicked:', type)
}

// 处理每日任务
const handleDailyTasks = () => {
  navigateTo('/daily-tasks')
}

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    initializeSizes()
    if (import.meta.client) {
      window.addEventListener('resize', initializeSizes)

      // 在所有初始化完成后，设置展开状态
      setTimeout(() => {
        const savedState = localStorage.getItem('bottom_sheet_expanded')
        expanded.value = savedState !== 'false'
        progress.value = expanded.value ? 1 : 0
      }, 100)
    }
  })
})

onUnmounted(() => {
  if (import.meta.client) {
    window.removeEventListener('resize', initializeSizes)
  }
})

// 监听activeTab变化
watch(activeTab, () => {
  // 重置滚动位置
  if (sheetContent.value) {
    sheetContent.value.scrollTop = 0
  }
})
</script>

<style lang="less" scoped>
/* 移动端样式 - 完全复制CSR应用的样式 */
.profile-view {
  height: 100%;
  width: 100%;
  position: relative;
  background: var(--mobile-bg-gradient-start);
  contain: paint;
  transition: background 0.3s ease;
}

.profile-container {
  height: 100%;
  position: relative;
  overflow: hidden;
  contain: paint;
}

.upper-section {
  position: relative;
  z-index: 1;
  padding: 20px 16px;
  min-height: 140px;
  transition: opacity 0.3s ease;
  touch-action: auto;
}

.bottom-sheet {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--sheet-height);
  background: var(--mobile-app-bg, var(--bg-secondary));
  border-radius: 20px 20px 0 0;
  z-index: 2;
  will-change: transform;
  contain: paint;
  transform: translateY(var(--translate-y, 0));
  transition: background 0.3s ease;

  &.expanded {
    --translate-y: 0;
  }

  // 添加初始化状态类
  &.initializing {
    transition: none;
  }

  &:not(.initializing) {
    transition: transform 0.3s cubic-bezier(0.4, 0.22, 0, 1);
  }

  .sheet-handle {
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: inherit;
    border-radius: 20px 20px 0 0;
    position: relative;
    z-index: 1;
    cursor: grab;
    touch-action: none;

    &:active {
      cursor: grabbing;
    }

    .handle-line {
      width: 32px;
      height: 4px;
      background: var(--border-color);
      border-radius: 2px;
      pointer-events: none;
    }
  }

  .sheet-content {
    height: calc(100% - 32px);
    padding: 0;
    background: inherit;
    -webkit-overflow-scrolling: touch;
    position: relative;
    overflow-y: auto;
  }

  .section-header {
    position: sticky;
    top: 0;
    background: inherit;
    z-index: 1000;
    padding: 0 16px 16px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    touch-action: auto;

    .tabs {
      display: flex;
      gap: 40px;
      touch-action: auto;

      .tab {
        background: none;
        border: none;
        color: var(--text-tertiary);
        font-size: 14px;
        font-weight: 500;
        padding: 0;
        cursor: pointer;
        position: relative;
        touch-action: auto;
        transition: color 0.3s ease;

        &.active {
          color: var(--text-primary);

          &::after {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--accent-color);
            border-radius: 1px;
          }
        }
      }
    }
  }
}

.mobile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
  }

  .settings-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-primary);
    background: var(--bg-tertiary);
    border-radius: 50%;
    transition: all 0.3s ease;

    &:hover {
      background: var(--bg-hover);
    }

    svg {
      width: 24px;
      height: 24px;
    }
  }
}

.user-section {
  .user-info {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    gap: 12px;
    margin-top: 34px;

    .left-section {
      display: flex;
      gap: 15px;
      flex: 1;
      min-width: 0;
    }

    .avatar {
      width: 90px;
      height: 90px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .info-column {
      display: flex;
      flex-direction: column;
      gap: 8px;
      flex: 1;
      min-width: 0;
      justify-content: space-between;
    }

    .info {
      .username {
        font-size: 20px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 6px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .uid {
        font-size: 14px;
        color: var(--text-tertiary);
        margin: 0;
      }
    }

    .credit-display {
      flex-shrink: 0;
      width: fit-content;
      padding-right: 16px;
    }

    .action-buttons {
      display: flex;
      flex-direction: column;
      gap: 8px;
      flex-shrink: 0;
    }

    .tasks-button {
      background: var(--accent-color);
      border: none;
      border-radius: 20px;
      color: var(--bg-primary);
      font-size: 14px;
      font-weight: 600;
      padding: 4px 16px;
      height: 28px;
      cursor: pointer;
      white-space: nowrap;
      flex-shrink: 0;
      transition: all 0.3s ease;

      &:hover {
        background: var(--accent-hover);
      }

      &:active {
        opacity: 0.9;
        transform: scale(0.98);
      }
    }

    .purchase-button {
      background: var(--accent-color);
      border: none;
      border-radius: 20px;
      color: var(--bg-primary);
      font-size: 14px;
      font-weight: 600;
      padding: 4px 16px;
      height: 28px;
      cursor: pointer;
      white-space: nowrap;
      flex-shrink: 0;
      transition: all 0.3s ease;

      &:hover {
        background: var(--accent-hover);
      }

      &:active {
        opacity: 0.9;
        transform: scale(0.98);
      }
    }
  }
}

.history-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 16px;
  padding-bottom: 100px;
  touch-action: pan-y;

  :deep(.story-card) {
    width: 100%;
    aspect-ratio: 167/294;
    margin: 0;
    touch-action: auto;

    .story-image {
      width: 100%;
      height: 100%;
      position: relative;
    }
  }

  .empty-text {
    grid-column: 1 / -1;
    text-align: center;
    color: var(--text-tertiary);
    font-size: 14px;
    padding: 32px 0;
  }

  .bottom-sheet:not(.expanded) & {
    overflow: hidden;
    touch-action: none;
  }
}
</style>
