<template>
  <div class="pc-layout">
    <!-- 顶部导航栏 -->
    <PCHeader @show-auth="showAuthModal" @theme-toggle="handleThemeToggle" />

    <!-- 主体内容区域 -->
    <div class="content-container">
      <!-- 左侧导航栏 -->
      <PCSidebar
        :collapsed="sidebarCollapsed"
        @toggle-collapse="toggleSidebar"
        @show-auth="showAuthModal"
        @show-checkin="showCheckinModal"
      />

      <!-- SEO 隐藏标题 - 上部区域 -->
      <h1 class="seo-hidden">Free Best Pc & Mobile Otome Games, Romance Visual Novels</h1>

      <!-- 主要内容 -->
      <main class="main-content">
        <!-- Stories Section -->
        <section class="stories-section">
          <div class="pc-container">
            <div class="section-header">
              <div class="section-title">
                <!-- <Icon name="lucide:flame" size="24" /> -->
                🔥 Hottest
              </div>
            </div>

            <div class="filter-container">
              <!-- 第一行：下拉菜单 -->
              <div class="dropdown-row">
                <FilterDropdown :has-selection="selectedPopular !== 'popular'">
                  <template #trigger>
                    {{ selectedPopularOptionLabel() }}
                    <Icon name="lucide:chevron-down" size="16" />
                  </template>
                  <template #default="{ closeDropdown }">
                    <div class="dropdown-options">
                      <div
                        v-for="option in popularOptions"
                        :key="option.value"
                        class="dropdown-option"
                        :class="{ active: selectedPopular === option.value }"
                        @click="
                          () => {
                            handleSortChange(option.value)
                            closeDropdown()
                          }
                        "
                      >
                        {{ option.label }}
                      </div>
                    </div>
                  </template>
                </FilterDropdown>
              </div>

              <!-- 第二行：标签直接展示 -->
              <div class="tags-row">
                <div
                  v-for="tag in availableTags"
                  :key="tag.id"
                  class="tag-pill"
                  :class="{ active: selectedTags.includes(tag.id) }"
                  @click="toggleTag(tag.id)"
                >
                  {{ tag.name }}
                </div>
              </div>
            </div>

            <!-- SEO 隐藏标题 - 中部区域 -->
            <h2 class="seo-hidden">Play anime dating sim, Virtual Date Games Starting at $0</h2>

            <div class="story-grid-container">
              <!-- 使用虚拟滚动组件 -->
              <VirtualStoryGrid
                :stories="
                  isLoading
                    ? Array(12).fill({ id: 'skeleton', title: '', status: 'normal' })
                    : displayStories
                "
                :is-pc="true"
                :loading="isLoading"
                :show-performance-info="false"
                :use-page-scroll="true"
                class="virtual-grid-pc"
                :class="{ 'loading-blur': isLoading }"
                @story-click="handleStoryClick"
                @need-login="showAuthModal"
                @need-email="showAuthModal"
                @subscription-change="handleSubscriptionChange"
              />

              <!-- 空状态：没有加载且没有数据 -->
              <div v-if="shouldShowEmptyState" class="no-data">
                <p>No characters available under current conditions.</p>
              </div>
            </div>

            <!-- SEO 隐藏标题 - 中部区域 -->
            <h2 class="seo-hidden">choose your own romance & online Romance Stories</h2>
          </div>
        </section>

        <!-- SEO 隐藏标题 - 底部区域 -->
        <h3 class="seo-hidden">interactive story-driven romance games</h3>
        <h3 class="seo-hidden">Premium otome games English patch & anime art style</h3>
      </main>

      <!-- 模态框 -->
      <AuthModal :visible="showAuth" @close="showAuth = false" @login="handleLoginSuccess" />

      <!-- 签到弹窗 -->
      <CheckinModal />

      <!-- 故事详情模态框 -->
      <StoryDetailModal
        :visible="showStoryDetail"
        :story-id="selectedStoryId"
        @close="showStoryDetail = false"
        @play="handlePlayStory"
        @need-login="showAuthModal"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
// Nuxt 3 自动导入，无需手动导入 ref 和 computed

// 定义Story类型
interface Story {
  id: string
  title: string
  description?: string
  preview_url?: string
  carousel_image_url?: string[]
  badge?: string
  status: 'normal' | 'preparing' | 'admin_only'
  is_subscribed?: boolean
  is_fav?: boolean
  author?: string
  tags?: string[]
  categories?: string[]
}

// 使用和移动端相同的标签筛选逻辑
const {
  selectedPopular,
  selectedTags,
  handlePopularChange,
  handleTagsChange,
  initFromUrlParams,
  isLoading
} = useTagsFilter()

// 用户状态管理
const userStore = useUserStore()
const storyStore = useStoryStore()

// 响应式数据
const showAuth = ref(false)
const sidebarCollapsed = ref(false) // 侧边栏收起状态
const showStoryDetail = ref(false) // 故事详情模态框状态
const selectedStoryId = ref('') // 选中的故事ID

// 下拉菜单选项（与CSR项目保持一致）
const popularOptions = [
  { label: 'Most popular', value: 'popular' },
  { label: 'Newest', value: 'newest' }
]

// 直接使用useStoriesV2的数据
const displayStories = computed(() => {
  const filtered = storyStore.stories.filter(
    (story: any) =>
      story &&
      story.id &&
      story.title &&
      typeof story.title === 'string' &&
      story.title.trim() !== ''
  )

  // 确保每个故事都有必需的字段
  return filtered.map((story: any) => ({
    ...story,
    status: story.status || 'normal' // 提供默认值
  }))
})

// 性能优化：缓存故事数量
const storiesCount = computed(() => displayStories.value.length)

// 初始化状态
const isInitialized = ref(false)

// 性能优化：是否显示空状态
const shouldShowEmptyState = computed(
  () => isInitialized.value && !isLoading.value && !storyStore.loading && storiesCount.value === 0
)

// 计算可用标签（过滤掉"all"标签）
const availableTags = computed(() => {
  const tags: { id: string; name: string }[] = []

  storyStore.storyCategories.forEach((category) => {
    if (category.subcategories) {
      category.subcategories.forEach((sub) => {
        // 过滤掉名称为"all"的标签
        if (sub.name.toLowerCase() !== 'all') {
          tags.push({
            id: sub.id,
            name: sub.name
          })
        }
      })
    } else {
      // 过滤掉名称为"all"的标签
      if (category.name.toLowerCase() !== 'all') {
        tags.push({
          id: category.id,
          name: category.name
        })
      }
    }
  })

  return tags
})
const selectedPopularOptionLabel = () => {
  const option = popularOptions.find((opt) => opt.value === selectedPopular.value)
  return option ? option.label : 'Most popular'
}

const handleSortChange = async (value: string) => {
  await handlePopularChange(value)
}

const toggleTag = async (tagId: string) => {
  const currentTags = [...selectedTags.value]
  const index = currentTags.indexOf(tagId)

  if (index === -1) {
    currentTags.push(tagId)
  } else {
    currentTags.splice(index, 1)
  }

  await handleTagsChange(currentTags)
}

// 方法
const handleThemeToggle = (isDark: boolean) => {
  if (import.meta.client) {
    localStorage.setItem('theme', isDark ? 'dark' : 'light')
    document.body.classList.toggle('light-theme', !isDark)
  }
}

const showAuthModal = () => {
  showAuth.value = true
}

const showCheckinModal = () => {
  const checkinStore = useCheckinStore()

  // 确保用户已登录且不是游客
  if (!userStore.isLoggedIn || userStore.isGuest) {
    showAuthModal()
    return
  }

  // 显示签到弹窗
  checkinStore.showModal()
}

const handleLoginSuccess = () => {
  showAuth.value = false
}

// 处理故事点击
const handleStoryClick = (story: Story) => {
  selectedStoryId.value = story.id
  showStoryDetail.value = true
}

// 处理开始游戏
const handlePlayStory = () => {
  showStoryDetail.value = false
}

const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
  if (import.meta.client) {
    localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString())
  }
}

// StoryCard相关事件处理
const handleSubscriptionChange = (_story: Story) => {
  // 这里可以触发数据重新获取或者通知父组件更新
}

// 主题初始化已在 app.vue 中处理

// 立即初始化用户状态，避免闪现（与 HomePageMobile 保持一致）
if (import.meta.client) {
  userStore.initFromStorage()

  // 延迟一帧后标记为已初始化，确保用户状态已经同步到 UI
  requestAnimationFrame(() => {
    isInitialized.value = true
  })
}

// 客户端初始化
onMounted(async () => {
  try {
    // 用户状态初始化由 smart-auth.client.ts 插件处理，这里不重复调用

    // 只有在没有数据时才初始化，避免重复请求
    // 因为 pages/index.vue 已经处理了数据加载
    if (storyStore.stories.length === 0 && storyStore.storyCategories.length === 0) {
      await initFromUrlParams()
    }
  } catch (error) {
    console.error('Failed to initialize PC home page:', error)
  } finally {
    // 标记为已初始化，无论成功还是失败
    isInitialized.value = true
  }
})
</script>

<style scoped>
/* 性能优化：使用 contain 属性 */
.pc-layout {
  contain: layout style;
}

.stories-grid-pc {
  contain: layout;
  /* 使用 GPU 加速 */
  transform: translateZ(0);
  will-change: auto;
}

/* 虚拟滚动网格样式 */
.virtual-grid-pc {
  margin-bottom: 40px;
  border-radius: 12px;
  background: transparent;
  min-height: 400px; /* 最小高度确保有内容显示 */
}

/* 故事网格容器 */
.story-grid-container {
  position: relative;
}

/* 加载遮罩层 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--loading-overlay-bg, rgba(0, 0, 0, 0.3));
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 12px;
  transition: opacity 0.3s ease;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 24px;
  background: var(--loading-content-bg, rgba(255, 255, 255, 0.1));
  border-radius: 12px;
  border: 1px solid var(--loading-content-border, rgba(255, 255, 255, 0.2));
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--loading-spinner-bg, rgba(255, 255, 255, 0.2));
  border-top: 3px solid var(--loading-spinner-color, #ca93f2);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: var(--loading-text-color, rgba(255, 255, 255, 0.9));
  font-family: 'Work Sans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  margin: 0;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 加载时的模糊效果 */
.loading-blur {
  filter: blur(2px);
  opacity: 0.6;
  transition:
    filter 0.3s ease,
    opacity 0.3s ease;
}

/* 筛选器容器样式 */
.filter-container {
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 第一行：下拉菜单 */
.dropdown-row {
  display: flex;
  gap: 12px;
  align-items: center;
}

.dropdown-row svg {
  stroke: var(--filter-dropdown-text, rgba(255, 255, 255, 0.8)) !important;
}

/* 第二行：标签展示 */
.tags-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 4px;
}

/* 标签样式 */
.tag-pill {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border-radius: 100px;
  background-color: var(--tag-bg, rgba(255, 255, 255, 0.08));
  color: var(--tag-text, rgba(255, 255, 255, 0.8));
  font-family: 'Work Sans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--tag-border, rgba(255, 255, 255, 0.1));
  white-space: nowrap;
}

.tag-pill:hover {
  background-color: var(--tag-hover-bg, rgba(255, 255, 255, 0.12));
}

.tag-pill.active {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: var(--tag-active-bg, rgba(202, 147, 242, 0.2));
  color: var(--tag-active-text, #ca93f2);
  border: 1px solid var(--tag-active-border, rgba(202, 147, 242, 0.4));
  font-weight: 600;
}

.tag-pill.active::before {
  content: '';
  display: inline-block;
  width: 12px;
  height: 8px;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDMuNUw0LjUgN0wxMSAxIiBzdHJva2U9IiNjYTkzZjIiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+')
    no-repeat center center;
  background-size: contain;
}

/* 下拉菜单选项 */
.dropdown-options {
  padding: 10px 0;
}

.dropdown-option {
  padding: 10px 16px;
  color: var(--filter-dropdown-text, rgba(255, 255, 255, 0.8));
  font-family: 'Work Sans', sans-serif;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dropdown-option:hover {
  background-color: var(--filter-option-hover-bg, rgba(255, 255, 255, 0.1));
}

.dropdown-option.active {
  color: var(--filter-option-active-text, #ca93f2);
  background-color: var(--filter-option-active-bg, rgba(202, 147, 242, 0.2));
  font-weight: 700;
}

.no-data {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
  color: var(--text-secondary, rgba(255, 255, 255, 0.6));
  font-size: 16px;
}

/* 亮色主题适配 */
body.light-theme .tag-pill {
  background-color: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.8);
  border-color: rgba(0, 0, 0, 0.1);
}

body.light-theme .tag-pill:hover {
  background-color: rgba(0, 0, 0, 0.08);
}

body.light-theme .tag-pill.active {
  background-color: rgba(202, 147, 242, 0.15);
  color: #8b5cf6;
  border-color: rgba(202, 147, 242, 0.3);
}

body.light-theme .dropdown-option {
  color: rgba(0, 0, 0, 0.8);
}

body.light-theme .dropdown-option:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

body.light-theme .dropdown-option.active {
  color: #8b5cf6;
  background-color: rgba(202, 147, 242, 0.15);
}

body.light-theme .no-data {
  color: rgba(0, 0, 0, 0.6);
}

/* 亮色主题加载遮罩层 */
body.light-theme .loading-overlay {
  background: rgba(255, 255, 255, 0.3);
}

body.light-theme .loading-content {
  background: rgba(0, 0, 0, 0.05);
  border-color: rgba(0, 0, 0, 0.1);
}

body.light-theme .loading-spinner {
  border-color: rgba(0, 0, 0, 0.1);
  border-top-color: #8b5cf6;
}

body.light-theme .loading-text {
  color: rgba(0, 0, 0, 0.8);
}

/* SEO 隐藏标题 - 对用户不可见但对搜索引擎可见 */
.seo-hidden {
  position: absolute;
  left: -10000px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
  margin: 0;
  padding: 0;
  border: 0;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
}
</style>
