<template>
  <div v-if="visible" class="invite-code-modal">
    <div class="modal-overlay" @click="handleClose" />
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">Your Invite Link</h3>
        <button class="close-button" @click="handleClose">×</button>
      </div>
      <div class="modal-body">
        <p class="invite-description">
          Share this link with your friends and earn rewards when they sign up!
        </p>

        <div class="invite-code-container">
          <div class="invite-link" @click="copyCode">
            {{ getInviteLink() }}
            <div class="copy-hint">Tap to copy link</div>
          </div>
        </div>

        <div class="share-options">
          <button class="share-button" @click="handleShare">Share</button>
          <button class="copy-button" @click="copyCode">Copy</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  visible: boolean
  inviteCode: string
}

interface Emits {
  (e: 'close'): void
  // eslint-disable-next-line @typescript-eslint/unified-signatures
  (e: 'copy'): void
  // eslint-disable-next-line @typescript-eslint/unified-signatures
  (e: 'share'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleClose = () => {
  emit('close')
}

const getInviteLink = () => {
  if (!props.inviteCode) return ''
  // 创建带有邀请码参数的完整链接
  const baseUrl = window.location.origin
  return `${baseUrl}?invite=${props.inviteCode}`
}

const copyCode = async () => {
  if (!props.inviteCode) return

  try {
    // 复制完整的邀请链接而不仅仅是邀请码
    const inviteLink = getInviteLink()
    await navigator.clipboard.writeText(inviteLink)
    console.log('Invite link copied to clipboard!')
    emit('copy')
  } catch (err) {
    console.error('Failed to copy invite link:', err)
  }
}

const handleShare = async () => {
  if (!props.inviteCode) return

  // 获取完整的邀请链接
  const inviteLink = getInviteLink()

  // 如果支持网页分享API
  if (navigator.share) {
    try {
      await navigator.share({
        title: 'Join me on Magic Partner!',
        text: `Use my invite link to sign up and get rewards!`,
        url: inviteLink
      })
      emit('share')
    } catch (err: any) {
      console.error('Failed to share:', err)
      // 如果用户取消分享，不显示错误
      if (err.name !== 'AbortError') {
        console.error('Failed to share')
      }
    }
  } else {
    // 如果不支持分享API，则复制到剪贴板
    copyCode()
  }
}
</script>

<style scoped>
.invite-code-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  background: var(--bg-secondary);
  border-radius: 16px;
  width: 100%;
  max-width: 400px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--border-color);
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.close-button {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.close-button:hover {
  background: var(--bg-hover);
}

.modal-body {
  padding: 24px;
}

.invite-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 20px;
  text-align: center;
  line-height: 1.5;
}

.invite-code-container {
  margin-bottom: 24px;
}

.invite-link {
  background: rgba(202, 147, 242, 0.15);
  border: 1px dashed var(--accent-color);
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  font-size: 14px;
  color: var(--accent-color);
  position: relative;
  cursor: pointer;
  word-break: break-all;
  overflow: hidden;
  transition: background 0.3s ease;
}

.invite-link:hover {
  background: rgba(202, 147, 242, 0.25);
}

.copy-hint {
  position: absolute;
  bottom: 4px;
  right: 8px;
  font-size: 10px;
  color: var(--text-tertiary);
  font-weight: normal;
}

.share-options {
  display: flex;
  gap: 12px;
}

.share-button,
.copy-button {
  flex: 1;
  height: 44px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-button {
  background: var(--accent-color);
  color: var(--bg-primary);
}

.share-button:hover {
  background: var(--accent-hover);
}

.copy-button {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.copy-button:hover {
  background: var(--bg-hover);
}
</style>
