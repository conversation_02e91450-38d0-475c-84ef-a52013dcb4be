<template>
  <LegalPageLayout title="18 U.S.C. 2257 Compliance">
    <div class="section">
      <h2>18 U.S.C. 2257 Record-Keeping Requirements Compliance Statement</h2>
      <p class="last-updated">Last Updated: June 3, 2025</p>
    </div>

    <div class="section notice">
      <h3>Exemption Statement</h3>
      <p>
        All visual content displayed on {{ appName.toLowerCase() }}.ai is exempt from the
        record-keeping requirements under 18 U.S.C. §2257, 2257A, and 28 C.F.R. 75 for the following
        reasons: our website does not depict actual human beings engaged in real sexually explicit
        conduct.
      </p>
    </div>

    <div class="section">
      <h3>AI-Generated Content Declaration</h3>
      <ul>
        <li>
          All content created from our website is exclusively generated by artificial intelligence
          (AI) and created entirely through algorithmic processes.
        </li>
        <li>
          No real human models, actors, actresses, or other individuals appear in any AI-generated
          images on this website.
        </li>
        <li>
          Our website does not permit users from uploading images containing real human models,
          actors, actresses, or other individuals.
        </li>
      </ul>
    </div>

    <div class="section">
      <h3>Compliance Commitment</h3>
      <p>
        {{ appName.toLowerCase() }}.ai is committed to adhering to all applicable laws and
        regulations and only provides AI-generated content that meets strict ethical and compliance
        standards. The content created from this website contains only text generated based on AI
        and does not contain images or videos.
      </p>
    </div>

    <div class="section">
      <h3>Contact Information</h3>
      <p>
        For any inquiries or clarifications regarding our compliance with 2257 record-keeping
        requirements, please contact {{ brandingConfig.supportEmail }}.
      </p>
    </div>
  </LegalPageLayout>
</template>

<script setup lang="ts">
// 使用品牌配置
const { brandingConfig } = useBranding()
const appName = computed(() => brandingConfig.value.appName)

// SEO 配置
usePageSeo({
  pageTitle: '18 U.S.C. 2257 Compliance',
  customDescription: `All visual content displayed on ${appName.value.toLowerCase()}.ai is exempt from the record-keeping requirements under 18 U.S.C. §2257, 2257A, and 28 C.F.R. 75 for the following reasons: our website does not depict actual human beings engaged in real sexually explicit conduct.`
})
</script>
