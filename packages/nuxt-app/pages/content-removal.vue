<template>
  <LegalPageLayout title="Content Removal Policy">
    <div class="section">
      <h2>Content Removal Policy</h2>
      <p>
        At {{ appName }} AI, we are committed to maintaining a respectful and safe environment for
        all users. We understand that certain content may not adhere to our guidelines or may be
        deemed inappropriate. This Content Removal Policy outlines the procedures and circumstances
        under which content may be removed from our platform.
      </p>
    </div>

    <div class="section notice">
      <h3>Types of Removable Content</h3>
      <ul>
        <li>Content that violates our Community Guidelines.</li>
        <li>Content that infringes upon intellectual property rights.</li>
        <li>Content that is considered illegal, harmful, or abusive.</li>
        <li>Content that involves impersonation or unauthorized use of personal information.</li>
      </ul>
    </div>

    <div class="section">
      <h3>Reporting Mechanism</h3>
      <ul>
        <li>
          Users can report inappropriate content through our reporting system available on each
          content page.
        </li>
        <li>
          Reports can also be sent directly to our support team at support@{{
            appName.toLowerCase()
          }}.ai.
        </li>
      </ul>
    </div>

    <div class="section">
      <h3>Review Process</h3>
      <ul>
        <li>All reported content is reviewed by our moderation team within 24 hours.</li>
        <li>
          During the review, content may be temporarily removed or restricted until a final decision
          is made.
        </li>
      </ul>
    </div>

    <div class="section">
      <h3>Consequences of Violation</h3>
      <p>
        Repeated violations of our Content Removal Policy may result in account suspension or
        termination.
      </p>
    </div>

    <div class="section">
      <h3>Contact Information</h3>
      <p>
        For any questions or concerns regarding this policy, please contact us at
        {{ brandingConfig.supportEmail }}.
      </p>
    </div>
  </LegalPageLayout>
</template>

<script setup lang="ts">
// 使用品牌配置
const { brandingConfig } = useBranding()
const appName = computed(() => brandingConfig.value.appName)

// SEO 配置
usePageSeo({
  pageTitle: 'Content Removal Policy',
  customDescription: `At ${appName.value} AI, we are committed to maintaining a respectful and safe environment for all users. This Content Removal Policy outlines the procedures and circumstances under which content may be removed from our platform.`
})
</script>
