<template>
  <div class="chat-page">
    <!-- 故事详情内容 - 用于 SEO 和 view-source -->
    <div v-if="storyDetail" class="story-content-for-seo">
      <h1>{{ storyDetail.title }}</h1>
      <p>{{ storyDetail.description }}</p>

      <!-- SEO H1 标题 - 分散放置 -->
      <h1 class="seo-title">"{{ storyDetail.title }}" – Play Now for Free</h1>

      <div v-if="selectedActor" class="current-character">
        <h2>Current Character:</h2>
        <div class="character-info">
          <strong>{{ selectedActor.name }}</strong>
          <span v-if="selectedActor.description"> - {{ selectedActor.description }}</span>
        </div>
      </div>

      <!-- SEO H2 标题 - 分散放置 -->
      <h2 class="seo-pricing">
        Pricing & Features – From ${{
          storyDetail.coins ? (storyDetail.coins * 0.01).toFixed(2) : '3.99'
        }}
      </h2>

      <div v-if="storyDetail.tags && storyDetail.tags.length > 0" class="tags">
        <span>Tags: </span>
        <span v-for="tag in storyDetail.tags" :key="tag" class="tag">{{ tag }}</span>
      </div>

      <!-- SEO H2 标题 - 分散放置 -->
      <h2 class="seo-packs">
        Exclusive "{{ storyDetail.title }}" visual novels Packs Starting at $0
      </h2>

      <!-- SEO H3 标题 - 分散放置 -->
      <h3 class="seo-otome">Best Otome Games "{{ storyDetail.title }}" – Free Trial Available</h3>
    </div>

    <!-- 聊天应用 iframe -->
    <RemoteChatLoader :chat-type="chatType" :character-id="characterId" :story-id="storyId" />
  </div>
</template>

<script setup lang="ts">
// 定义页面元数据
definePageMeta({
  layout: 'mobile'
})

const route = useRoute()

// 解析路由参数
const params = computed(() => (route.params.params as string[]) || [])

const chatType = computed((): 'chat2' => 'chat2')

const storyId = computed(() => {
  const p = params.value
  if (!p || p.length < 2) return undefined
  // chat2: /chat2/:storyId/:actorId 格式
  return p[0]
})

const characterId = computed(() => {
  const p = params.value
  if (!p || p.length < 2) return undefined
  // chat2: /chat2/:storyId/:actorId 格式
  return p[1]
})

// 获取品牌配置
const { brandingConfig } = useBranding()

// 获取故事详情数据用于 SEO
const { getStoryDetail } = useStories()

// 在服务端预加载故事数据
const { data: storyDetail } = await useLazyAsyncData(
  `story-${storyId.value}`,
  async () => {
    if (!storyId.value) return null
    try {
      const story = await getStoryDetail(String(storyId.value))
      return story
    } catch (error) {
      console.error('Failed to load story detail:', error)
      return null
    }
  },
  {
    server: true, // 在服务端执行
    default: () => null
  }
)

// 当前选择的角色
const selectedActor = computed(() => {
  if (!storyDetail.value?.actors || !characterId.value) return null
  return storyDetail.value.actors.find((actor) => actor.id === characterId.value) || null
})

// 生成并设置游戏结构化数据
const { generateGameStructuredData, setGameStructuredData } = useGameStructuredData()
const gameStructuredData = computed(() =>
  generateGameStructuredData(
    storyDetail.value,
    selectedActor.value,
    'chat2',
    storyId.value || '',
    characterId.value || ''
  )
)

// 监听数据变化，更新结构化数据
watch(
  gameStructuredData,
  (newData) => {
    setGameStructuredData(newData)
  },
  { immediate: true }
)

// SEO设置 - 使用故事详情优化 SEO
useSeoMeta({
  title: computed(() => {
    if (storyDetail.value?.title && selectedActor.value?.name) {
      return `${storyDetail.value.title} - Chat with ${selectedActor.value.name} - ${brandingConfig.value.websiteTitle}`
    } else if (storyDetail.value?.title) {
      return `${storyDetail.value.title} - Chat - ${brandingConfig.value.websiteTitle}`
    }
    return `Chat - ${brandingConfig.value.websiteTitle}`
  }),
  description: computed(() => {
    if (storyDetail.value?.description && selectedActor.value?.name) {
      return `Interactive chat experience with ${selectedActor.value.name} from ${storyDetail.value.title}: ${storyDetail.value.description}`
    } else if (storyDetail.value?.description) {
      return `Interactive chat experience with ${storyDetail.value.title}: ${storyDetail.value.description}`
    }
    return 'Interactive chat experience'
  }),
  keywords: computed(() => {
    const tags = storyDetail.value?.tags || []
    const baseKeywords = 'AI chat, interactive story, virtual dating, otome game'
    const characterKeyword = selectedActor.value?.name ? `, ${selectedActor.value.name}` : ''
    return tags.length > 0
      ? `${baseKeywords}${characterKeyword}, ${tags.join(', ')}`
      : `${baseKeywords}${characterKeyword}`
  }),
  ogTitle: computed(() => {
    if (storyDetail.value?.title && selectedActor.value?.name) {
      return `${storyDetail.value.title} - Chat with ${selectedActor.value.name} - ${brandingConfig.value.websiteTitle}`
    } else if (storyDetail.value?.title) {
      return `${storyDetail.value.title} - Chat - ${brandingConfig.value.websiteTitle}`
    }
    return `Chat - ${brandingConfig.value.websiteTitle}`
  }),
  ogDescription: computed(() => {
    if (storyDetail.value?.description && selectedActor.value?.name) {
      return `Interactive chat experience with ${selectedActor.value.name} from ${storyDetail.value.title}: ${storyDetail.value.description}`
    } else if (storyDetail.value?.description) {
      return `Interactive chat experience with ${storyDetail.value.title}: ${storyDetail.value.description}`
    }
    return 'Interactive chat experience'
  }),
  ogImage: computed(
    () =>
      selectedActor.value?.avatar ||
      storyDetail.value?.preview_url ||
      brandingConfig.value.ogImageUrl
  ),
  ogType: 'website',
  ogUrl: computed(
    () => `${brandingConfig.value.siteUrl}/chat2/${storyId.value}/${characterId.value}`
  ),
  twitterCard: 'summary_large_image',
  twitterTitle: computed(() => {
    if (storyDetail.value?.title && selectedActor.value?.name) {
      return `${storyDetail.value.title} - Chat with ${selectedActor.value.name} - ${brandingConfig.value.websiteTitle}`
    } else if (storyDetail.value?.title) {
      return `${storyDetail.value.title} - Chat - ${brandingConfig.value.websiteTitle}`
    }
    return `Chat - ${brandingConfig.value.websiteTitle}`
  }),
  twitterDescription: computed(() => {
    if (storyDetail.value?.description && selectedActor.value?.name) {
      return `Interactive chat experience with ${selectedActor.value.name} from ${storyDetail.value.title}: ${storyDetail.value.description}`
    } else if (storyDetail.value?.description) {
      return `Interactive chat experience with ${storyDetail.value.title}: ${storyDetail.value.description}`
    }
    return 'Interactive chat experience'
  }),
  twitterImage: computed(
    () =>
      selectedActor.value?.avatar ||
      storyDetail.value?.preview_url ||
      brandingConfig.value.ogImageUrl
  ),
  robots: 'noindex, nofollow'
})

// Canonical 链接
useHead({
  link: [
    {
      rel: 'canonical',
      href: computed(() =>
        storyId.value
          ? `${brandingConfig.value.siteUrl}/story/${storyId.value}`
          : brandingConfig.value.siteUrl
      )
    }
  ]
})
</script>

<style scoped>
.chat-page {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* 故事详情内容 - 隐藏但用于 SEO */
.story-content-for-seo {
  position: absolute;
  left: -9999px;
  top: -9999px;
  width: 1px;
  height: 1px;
  overflow: hidden;
  opacity: 0;
  pointer-events: none;
}

.story-content-for-seo h1 {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 16px;
}

.story-content-for-seo p {
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.story-content-for-seo .current-character {
  margin-bottom: 16px;
}

.story-content-for-seo .current-character h2 {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
}

.story-content-for-seo .character-info {
  font-size: 16px;
  line-height: 1.5;
}

.story-content-for-seo .character-info strong {
  color: #333;
}

.story-content-for-seo .actors-list {
  margin-bottom: 16px;
}

.story-content-for-seo .actors-list h2 {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
}

.story-content-for-seo .actors-list ul {
  list-style: none;
  padding: 0;
}

.story-content-for-seo .actors-list li {
  margin-bottom: 8px;
}

.story-content-for-seo .tags {
  margin-bottom: 16px;
}

.story-content-for-seo .tag {
  display: inline-block;
  background: #f0f0f0;
  padding: 4px 8px;
  margin-right: 8px;
  border-radius: 4px;
  font-size: 14px;
}

/* SEO 标题样式 - 分散放置 */
.story-content-for-seo .seo-title {
  font-size: 20px;
  font-weight: bold;
  margin: 16px 0;
  color: #000;
}

.story-content-for-seo .seo-pricing {
  font-size: 18px;
  font-weight: bold;
  margin: 14px 0;
  color: #333;
}

.story-content-for-seo .seo-packs {
  font-size: 18px;
  font-weight: bold;
  margin: 14px 0;
  color: #333;
}

.story-content-for-seo .seo-otome {
  font-size: 16px;
  font-weight: bold;
  margin: 12px 0;
  color: #555;
}
</style>
