<template>
  <AdaptiveLayout>
    <!-- PC端布局 -->
    <template #pc>
      <PCLayout>
        <PCProfile />
      </PCLayout>
    </template>

    <!-- 移动端布局 -->
    <template #mobile>
      <MobileLayout>
        <MobileProfile />
      </MobileLayout>
    </template>
  </AdaptiveLayout>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  layout: false
})

// 用户状态管理
const userStore = useUserStore()

// 生命周期钩子 - 预加载用户数据
onMounted(async () => {
  try {
    // 只加载用户信息，故事数据在切换标签时按需加载
    await userStore.getUserInfo()

    // 预加载历史记录（默认标签页）
    if (userStore.userPlayedStories.length === 0) {
      await userStore.getUserPlayedStories()
    }
  } catch (error) {
    console.error('Failed to load user data:', error)
  }
})

// 使用品牌配置
const { brandingConfig } = useBranding()

// SEO设置
useHead({
  title: computed(() => `Profile - ${brandingConfig.value.websiteTitle}`),
  meta: [
    {
      name: 'description',
      content: computed(
        () => `Manage your ${brandingConfig.value.websiteTitle} profile and account settings.`
      )
    }
  ]
})
</script>
