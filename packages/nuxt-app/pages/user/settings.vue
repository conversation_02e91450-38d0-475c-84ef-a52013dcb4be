<template>
  <AdaptiveLayout>
    <!-- PC端布局 -->
    <template #pc>
      <PCLayout>
        <div class="pc-settings-view">
          <div class="settings-container">
            <!-- 顶部图标 -->
            <div class="header-icon">
              <NuxtImg
                :src="userStore.userInfo?.avatar_url || icons.defaultAvatar.value"
                alt="User avatar"
                loading="eager"
              />
            </div>

            <!-- 设置内容 -->
            <div class="settings-content">
              <div class="settings-form">
                <!-- 第一行设置项 -->
                <div class="settings-row">
                  <!-- 用户名设置 -->
                  <div class="settings-item">
                    <div class="item-label">Username</div>
                    <div class="item-input" @click="showUsernameModal = true">
                      <span>{{ userStore.userInfo?.name }}</span>
                      <Icon name="lucide:chevron-right" :size="16" class="arrow-icon" />
                    </div>
                  </div>

                  <!-- 性别设置 -->
                  <div class="settings-item">
                    <div class="item-label">Gender</div>
                    <div class="gender-dropdown">
                      <div class="item-input" @click="showGenderDropdown = !showGenderDropdown">
                        <span>{{ userStore.userInfo?.gender || 'Not set' }}</span>
                        <Icon name="lucide:chevron-down" :size="16" class="arrow-icon" />
                      </div>
                      <div v-if="showGenderDropdown" class="gender-options">
                        <div
                          class="gender-option"
                          :class="{ active: userStore.userInfo?.gender === 'male' }"
                          @click="handleGenderSelect('male')"
                        >
                          Male
                        </div>
                        <div
                          class="gender-option"
                          :class="{ active: userStore.userInfo?.gender === 'female' }"
                          @click="handleGenderSelect('female')"
                        >
                          Female
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 第三行设置项 -->
                <div class="settings-row">
                  <!-- UID设置 -->
                  <div class="settings-item">
                    <div class="item-label">UID (cannot be modified)</div>
                    <div class="item-input non-editable">
                      <span>{{ userStore.userInfo?.uuid?.slice(0, 7) }}</span>
                    </div>
                  </div>

                  <!-- 账号设置 -->
                  <div class="settings-item">
                    <div class="item-label">Account</div>
                    <div class="item-input non-editable">
                      <span>{{ userStore.userInfo?.email }}</span>
                    </div>
                  </div>
                </div>

                <!-- 确认按钮 -->
                <button class="confirm-button" @click="handleLogout">Log out</button>
              </div>
            </div>
          </div>

          <!-- 用户名修改弹窗 -->
          <div v-if="showUsernameModal" class="modal-overlay" @click="handleCancelUsername">
            <div class="modal-content" @click.stop>
              <div class="modal-header">
                <h3>Change Username</h3>
                <button class="close-btn" @click="handleCancelUsername">
                  <Icon name="lucide:x" :size="20" />
                </button>
              </div>
              <div class="modal-body">
                <input
                  v-model="newUsername"
                  type="text"
                  placeholder="Enter new username"
                  maxlength="20"
                  class="modal-input"
                />
                <div class="modal-buttons">
                  <button class="cancel-button" @click="handleCancelUsername">Cancel</button>
                  <button class="confirm-button" :disabled="updating" @click="handleUpdateUsername">
                    <span v-if="!updating">Confirm</span>
                    <span v-else class="loading-dots">
                      <span>.</span><span>.</span><span>.</span>
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </PCLayout>
    </template>

    <!-- 移动端布局 -->
    <template #mobile>
      <MobileLayout>
        <MobileSettings />
      </MobileLayout>
    </template>
  </AdaptiveLayout>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  layout: false
})

// 用户状态管理
const userStore = useUserStore()
const { icons } = useCdn()

// 响应式数据
const showUsernameModal = ref(false)
const showGenderDropdown = ref(false)
const newUsername = ref('')
const updating = ref(false)

// 处理用户名更新
const handleUpdateUsername = async () => {
  if (!newUsername.value.trim()) {
    console.error('Please enter a username')
    return
  }

  try {
    updating.value = true
    await userStore.updateUserInfo({ name: newUsername.value.trim() })
    console.log('Username updated successfully')
    showUsernameModal.value = false
  } catch (error: unknown) {
    console.error(error instanceof Error ? error.message : 'Failed to update username')
  } finally {
    updating.value = false
  }
}

// 取消用户名修改
const handleCancelUsername = () => {
  showUsernameModal.value = false
  newUsername.value = userStore.userInfo?.name || ''
}

// 处理性别选择
const handleGenderSelect = async (gender: string) => {
  try {
    await userStore.updateUserInfo({ gender })
    showGenderDropdown.value = false
    console.log('Gender updated successfully')
  } catch (error: unknown) {
    console.error(error instanceof Error ? error.message : 'Failed to update gender')
  }
}

// 处理退出登录
const handleLogout = () => {
  userStore.logout()
  navigateTo('/')
}

// 组件挂载时初始化
onMounted(() => {
  userStore.initFromStorage()

  // 初始化用户名
  newUsername.value = userStore.userInfo?.name || ''
})

// 使用品牌配置
const { brandingConfig } = useBranding()

// SEO设置
useHead({
  title: computed(() => `Settings - ${brandingConfig.value.websiteTitle}`),
  meta: [
    {
      name: 'description',
      content: computed(
        () => `Manage your ${brandingConfig.value.websiteTitle} account settings and preferences.`
      )
    }
  ]
})
</script>

<style lang="less" scoped>
.pc-layout {
  height: 100vh;
  background-color: var(--bg-primary, #1f0038);
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 主体内容区域
.content-container {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
}

// 主要内容
.main-content {
  flex: 1;
  overflow-y: auto;
  background-color: var(--content-bg, var(--bg-primary, #1f0038));
}

.pc-settings-view {
  width: 100%;
  min-height: 100%;
  padding: 40px 24px;
}

.settings-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
  max-width: 684px;
  margin: 0 auto;
}

.header-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;

  img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--accent-color, #ca93f2);
  }
}

.settings-content {
  width: 100%;
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
}

.settings-row {
  display: flex;
  gap: 20px;
  width: 100%;
}

.settings-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.item-label {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-secondary, rgba(0, 0, 0, 0.65));
}

.item-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border: 0.5px solid var(--text-secondary, rgba(0, 0, 0, 0.45));
  border-radius: 40px;
  font-size: 14px;
  color: var(--text-primary, rgba(0, 0, 0, 0.85));
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--accent-color, #ca93f2);
  }

  &.non-editable {
    background-color: rgba(0, 0, 0, 0.03);
    cursor: default;
    color: var(--text-tertiary, rgba(0, 0, 0, 0.45));
    border-color: var(--text-tertiary, rgba(0, 0, 0, 0.25));

    &:hover {
      border-color: var(--text-tertiary, rgba(0, 0, 0, 0.25));
    }
  }
}

.arrow-icon {
  display: flex;
  align-items: center;
  color: var(--text-secondary, rgba(0, 0, 0, 0.45));
}

.gender-dropdown {
  position: relative;
  width: 100%;

  .gender-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-secondary, white);
    border: 0.5px solid var(--border-color, #e0e0e0);
    border-radius: 20px;
    box-shadow: 0px 2px 5px 0px rgba(91, 91, 91, 0.12);
    overflow: hidden;
    z-index: 10;
    margin-top: 8px;

    .gender-option {
      padding: 12px 20px;
      font-size: 14px;
      color: var(--text-primary, rgba(0, 0, 0, 0.85));
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: var(--bg-hover, rgba(202, 147, 242, 0.1));
      }

      &.active {
        color: var(--accent-color, #ca93f2);
        background-color: var(--accent-bg, rgba(202, 147, 242, 0.2));
        font-weight: 600;
      }
    }
  }
}

.confirm-button {
  width: 100%;
  height: 42px;
  background: var(--accent-color, #ca93f2);
  border: none;
  border-radius: 54px;
  color: var(--text-on-accent, rgba(0, 0, 0, 0.85));
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 16px;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.9;
  }
}

// 模态框样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--bg-secondary, white);
  border-radius: 16px;
  width: 90%;
  max-width: 400px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary, rgba(0, 0, 0, 0.85));
    margin: 0;
  }

  .close-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: transparent;
    border: none;
    color: var(--text-secondary, rgba(0, 0, 0, 0.45));
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:hover {
      background: var(--bg-hover, rgba(0, 0, 0, 0.05));
      color: var(--text-primary, rgba(0, 0, 0, 0.85));
    }
  }
}

.modal-body {
  padding: 20px 24px 24px;

  .modal-input {
    width: 100%;
    height: 48px;
    background: var(--bg-tertiary, rgba(0, 0, 0, 0.03));
    border: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
    border-radius: 12px;
    padding: 0 16px;
    color: var(--text-primary, rgba(0, 0, 0, 0.85));
    font-size: 15px;
    margin-bottom: 20px;
    transition: all 0.3s ease;

    &::placeholder {
      color: var(--text-tertiary, rgba(0, 0, 0, 0.3));
    }

    &:focus {
      outline: none;
      border-color: var(--accent-color, #ca93f2);
    }
  }

  .modal-buttons {
    display: flex;
    gap: 12px;

    .cancel-button {
      flex: 1;
      height: 42px;
      background: transparent;
      border: 1px solid var(--border-color, rgba(0, 0, 0, 0.2));
      border-radius: 54px;
      color: var(--text-secondary, rgba(0, 0, 0, 0.65));
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: var(--bg-hover, rgba(0, 0, 0, 0.05));
      }
    }

    .confirm-button {
      flex: 1;
      height: 42px;
      background: var(--accent-color, #ca93f2);
      border: none;
      border-radius: 54px;
      color: var(--text-on-accent, rgba(0, 0, 0, 0.85));
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        opacity: 0.9;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      .loading-dots {
        display: inline-flex;
        gap: 2px;

        span {
          animation: loading-dot 1.4s infinite ease-in-out;

          &:nth-child(1) {
            animation-delay: -0.32s;
          }
          &:nth-child(2) {
            animation-delay: -0.16s;
          }
          &:nth-child(3) {
            animation-delay: 0s;
          }
        }
      }
    }
  }
}

@keyframes loading-dot {
  0%,
  80%,
  100% {
    opacity: 0.3;
  }
  40% {
    opacity: 1;
  }
}
</style>
