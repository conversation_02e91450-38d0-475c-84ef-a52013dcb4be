<template>
  <div class="coins-page">
    <div class="page-header">
      <div class="back-button" @click="router.back()">
        <Icon name="lucide:arrow-left" />
      </div>
      <h1>Coins Details</h1>
    </div>

    <div class="coins-display">
      <NuxtImg :src="icons.diamond.value" class="diamond-icon" alt="diamond" />
      <span class="amount">{{ userStore.userInfo?.coins || 0 }}</span>
    </div>

    <div class="history-list">
      <div v-for="item in historyList" :key="item.action_time" class="history-item">
        <div class="left">
          <div class="type">{{ formatActionType(item.action_type) }}</div>
          <div class="time">{{ formatTime(item.action_time) }}</div>
        </div>
        <div
          class="amount"
          :class="{
            positive: item.coin_delta > 0,
            neutral: item.coin_delta === 0,
            negative: item.coin_delta < 0
          }"
        >
          {{ item.coin_delta > 0 ? '+' : '' }}{{ item.coin_delta }}
        </div>
      </div>
      <div v-if="loading" class="loading">
        <div class="loading-spinner" />
      </div>
      <div v-if="!loading && historyList.length === 0" class="empty">
        No coin history available yet.
      </div>
      <div v-if="!loading && historyList.length > 0" class="no-more">No more history</div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  layout: false
})

interface CoinHistoryItem {
  action_time: string
  action_type: string
  coin_delta: number
  metadata: Record<string, any>
}

const router = useRouter()
const userStore = useUserStore()
const { apiRequest } = useApi()
const { icons } = useCdn()

const historyList = ref<CoinHistoryItem[]>([])
const loading = ref(false)

const loadHistory = async () => {
  try {
    loading.value = true
    const response = await apiRequest<CoinHistoryItem[]>('/api/v1/coin-history.list', {
      method: 'GET'
    })
    if (response.code === '0') {
      historyList.value = response.data || []
    }
  } catch (error) {
    console.error('Failed to load coin history:', error)
  } finally {
    loading.value = false
  }
}

const formatActionType = (type: string) => {
  const typeMap: Record<string, string> = {
    checkin: 'Daily Check-in',
    purchase: 'Purchase',
    consume: 'Consumption',
    reward: 'Reward',
    refund: 'Refund',
    task: 'Task Completion'
  }
  return typeMap[type] || type
}

const formatTime = (time: string) => {
  const date = new Date(time)
  return (
    date.toLocaleDateString() +
    ' ' +
    date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  )
}

// 生命周期
onMounted(async () => {
  try {
    await userStore.getUserInfo()
    await loadHistory()
  } catch (error) {
    console.error('Failed to load data:', error)
  }
})

// 使用品牌配置
const { brandingConfig } = useBranding()

// SEO设置
useHead({
  title: computed(() => `Coins Details - ${brandingConfig.value.websiteTitle}`),
  meta: [
    {
      name: 'description',
      content: 'View your coin balance and transaction history.'
    }
  ]
})
</script>

<style lang="less" scoped>
.coins-page {
  height: calc(var(--vh, 1vh) * 100);
  background: var(--mobile-app-bg);
  color: var(--text-primary);
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition:
    background 0.3s ease,
    color 0.3s ease;
}

.page-header {
  padding: 20px 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  margin-bottom: 0;

  .back-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-tertiary);
    border-radius: 50%;
    margin-right: 16px;
    cursor: pointer;
    transition: background 0.3s ease;

    &:active {
      background: var(--bg-hover);
    }
  }

  h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    color: var(--text-primary);
  }
}

.coins-display {
  padding: 32px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  background: var(--bg-tertiary);
  margin: 0 16px 24px;
  border-radius: 16px;

  .diamond-icon {
    width: 32px;
    height: 32px;
  }

  .amount {
    font-size: 32px;
    font-weight: 700;
    color: var(--accent-color);
  }
}

.history-list {
  flex: 1;
  padding: 0 16px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: var(--bg-tertiary);
  border-radius: 12px;
  margin-bottom: 8px;
  transition: background 0.3s ease;

  .left {
    .type {
      font-size: 16px;
      font-weight: 500;
      color: var(--text-primary);
      margin-bottom: 4px;
    }

    .time {
      font-size: 14px;
      color: var(--text-tertiary);
    }
  }

  .amount {
    font-size: 18px;
    font-weight: 600;

    &.positive {
      color: #52c41a;
    }

    &.negative {
      color: #ff4d4f;
    }

    &.neutral {
      color: var(--text-tertiary);
    }
  }
}

.loading {
  display: flex;
  justify-content: center;
  padding: 32px;

  .loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

.empty,
.no-more {
  text-align: center;
  padding: 32px;
  color: var(--text-tertiary);
  font-size: 14px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
