<template>
  <LegalPageLayout title="Complaints Policy">
    <div class="section">
      <h2>Complaints Policy</h2>
      <p>
        At {{ appName }} AI, we value user feedback and strive to address any concerns or complaints
        promptly and effectively. This Complaints Policy outlines the procedures for users to report
        and resolve issues related to our services.
      </p>
    </div>

    <div class="section notice">
      <h3>Submitting a Complaint</h3>
      <ul>
        <li>Complaints can be submitted through our Discord available on the Contact Us page.</li>
        <li>Complaints can also be sent via email to {{ brandingConfig.supportEmail }}.</li>
      </ul>
    </div>

    <div class="section">
      <h3>Information Required</h3>
      <ul>
        <li>Detailed description of the complaint.</li>
        <li>Any supporting evidence or documentation.</li>
        <li>Contact information for follow-up.</li>
      </ul>
    </div>

    <div class="section">
      <h3>Acknowledgment</h3>
      <p>Upon receipt of a complaint, we will acknowledge the complaint within 24 hours.</p>
    </div>

    <div class="section">
      <h3>Confidentiality</h3>
      <p>All complaints and related information will be handled with strict confidentiality.</p>
    </div>

    <div class="section">
      <h3>Resolution</h3>
      <p>All reported complaints will be reviewed and resolved within 7 business days.</p>
    </div>

    <div class="section">
      <h3>Contact Information</h3>
      <p>
        For any questions or concerns regarding this policy, please contact us at
        {{ brandingConfig.supportEmail }}.
      </p>
    </div>
  </LegalPageLayout>
</template>

<script setup lang="ts">
// 使用品牌配置
const { brandingConfig } = useBranding()
const appName = computed(() => brandingConfig.value.appName)

// SEO 配置
usePageSeo({
  pageTitle: 'Complaints Policy',
  customDescription: `At ${appName.value} AI, we value user feedback and strive to address any concerns or complaints promptly and effectively. This Complaints Policy outlines the procedures for users to report and resolve issues related to our services.`
})
</script>
