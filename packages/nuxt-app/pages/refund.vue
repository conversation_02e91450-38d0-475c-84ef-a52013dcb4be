<template>
  <LegalPageLayout title="Refund and Returns Policy">
    <div class="section">
      <h2>{{ appName }} Refund and Returns Policy</h2>
      <p>
        <strong>Last updated: July 1st, 2025</strong>
      </p>
    </div>

    <div class="section notice">
      <h3>Important Notice</h3>
      <p>
        By using our services and making purchases, you acknowledge that you have read, understood,
        and agree to be bound by this Refund and Returns Policy.
      </p>
    </div>

    <div class="section">
      <h3>Non-Refundable Fees</h3>
      <p>
        Fees are non-refundable once the Login details have been used on Portal. Non-use of a
        Membership or inability of User to access the Website through no fault of {{ appName }}.AI
        shall not be grounds for a refund of Fees. Company does not provide refunds or credits for
        any partial-month Membership periods.
      </p>
    </div>

    <div class="section">
      <h3>Refund Process</h3>
      <p>
        In the event a refund is issued, ALL refunds will be made by {{ appName }}.AI who will
        credit the Payment Method used to make the original purchase. NO refunds will be made by
        cash or paper check. ALL refunds will be issued within ten (10) days of communication
        between the User and the Company's Client Relations department.
      </p>
    </div>

    <div class="section">
      <h3>Pricing Errors</h3>
      <p>
        In the event a product and/or service is listed at an incorrect price or with incorrect
        information due to typographical error, we shall have the right to refuse or cancel any
        orders placed for the product and/or service listed at the incorrect price. We shall have
        the right to refuse or cancel any such order whether or not the order has been confirmed and
        your credit card charged. If your credit card has already been charged for the purchase and
        your order is canceled, we shall immediately issue a credit to your credit card account or
        other payment account in the amount of the charge.
      </p>
    </div>

    <div class="section">
      <h3>Payment Method Limitations</h3>
      <p>
        This refund policy does not apply when the payment method You used to purchase a membership
        does not offer the possibility to issue such a refund. This includes, but is not limited to,
        payment in cryptocurrency and/or through payment provider OnlyPay.
      </p>
    </div>

    <div class="section">
      <h3>Third-Party Payment Providers</h3>
      <p>
        Refunds through some payment providers, are processed by the payment provider and
        {{ appName }}.AI doesn't have complete control on the finality and therefore can't guarantee
        how the refund will be issued.
      </p>
    </div>
  </LegalPageLayout>
</template>

<script setup lang="ts">
// 使用品牌配置
const { brandingConfig } = useBranding()
const appName = computed(() => brandingConfig.value.appName)

// SEO 配置
usePageSeo({
  pageTitle: 'Refund and Returns Policy',
  customDescription: `Fees are non-refundable once the Login details have been used on ${appName.value} Portal. Non-use of a Membership or inability of User to access the Website through no fault of ${appName.value}.AI shall not be grounds for a refund of Fees.`
})
</script>
