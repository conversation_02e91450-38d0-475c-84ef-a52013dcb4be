<template>
  <div class="daily-tasks-page">
    <div class="page-header">
      <div class="back-button" @click="router.back()">
        <Icon name="lucide:arrow-left" :size="20" />
      </div>
      <h1 class="page-title">Daily Tasks</h1>
    </div>

    <!-- 邀请码模态框 -->
    <InviteCodeModal
      :visible="showInviteModal"
      :invite-code="tasksStore.inviteCode || ''"
      @close="showInviteModal = false"
    />

    <div class="content">
      <!-- Daily Check-in Section -->
      <div class="section checkin-section">
        <h2 class="section-title">Daily check-in</h2>

        <div class="checkin-grid">
          <div
            v-for="[key, reward] in Object.entries(sysConfigStore.checkInCoinsPerDay || {})"
            :key="key"
            class="checkin-item"
            :class="{
              'is-today': checkinStore.getDayNumber(key) === checkinStore.currentDay,
              'is-claimed':
                checkinStore.getDayNumber(key) < checkinStore.currentDay ||
                (checkinStore.getDayNumber(key) === checkinStore.currentDay &&
                  checkinStore.todayClaimed)
            }"
          >
            <div class="diamond-container">
              <div class="diamond-icon">
                <NuxtImg
                  class="credit-icon"
                  :src="icons.diamond.value"
                  alt="diamond"
                  width="14"
                  height="14"
                />
                <div
                  v-if="
                    checkinStore.getDayNumber(key) < checkinStore.currentDay ||
                    (checkinStore.getDayNumber(key) === checkinStore.currentDay &&
                      checkinStore.todayClaimed)
                  "
                  class="check-mark"
                >
                  ✓
                </div>
                <div v-else class="reward-amount">
                  {{ reward }}
                </div>
              </div>
            </div>
            <div class="day-label">Day {{ checkinStore.getDayNumber(key) }}</div>
          </div>
        </div>

        <button
          class="claim-button"
          :class="{ 'is-claimed': checkinStore.todayClaimed }"
          :disabled="checkinStore.loading || checkinStore.todayClaimed"
          @click="handleClaim"
        >
          <template v-if="checkinStore.loading">
            <Icon name="lucide:loader-2" :size="18" class="animate-spin" />
          </template>
          <template v-else-if="checkinStore.todayClaimed"> Back tomorrow for more! </template>
          <template v-else> Get Today's Reward 🎁 </template>
        </button>
      </div>

      <!-- Tasks Section -->
      <div class="section tasks-section">
        <h2 class="section-title">Task</h2>

        <div class="task-list">
          <!-- 动态渲染任务列表 -->
          <template v-if="!tasksStore.loading">
            <div
              v-for="task in tasksStore.tasks"
              :key="task.id"
              class="task-item"
              :class="{ 'is-completed': task.is_done }"
            >
              <div class="task-icon">
                <NuxtImg
                  class="credit-icon"
                  :src="icons.diamond.value"
                  alt="diamond"
                  width="14"
                  height="14"
                />
                <div class="reward-amount">{{ task.reward }}</div>
              </div>
              <div class="task-info">
                <div class="task-title">{{ task.name }}</div>
                <div class="task-description">{{ task.description }}</div>
                <div v-if="task.count > 0 && !task.is_done" class="task-progress">
                  {{ task.count }}/{{ task.limit }}
                </div>
              </div>
              <button
                class="task-button"
                :class="{
                  'check-button': task.type === 'play_game',
                  'ok-button': task.type !== 'play_game',
                  'completed-button': task.is_done
                }"
                :disabled="task.is_done || tasksStore.loading"
                @click="handleTaskAction(task)"
              >
                <template v-if="tasksStore.loading && currentTaskId === task.id">
                  <Icon name="lucide:loader-2" :size="18" class="animate-spin" />
                </template>
                <template v-else-if="task.is_done">Completed</template>
                <template v-else-if="task.type === 'play_game'">Check</template>
                <template v-else>Ok</template>
              </button>
            </div>
          </template>

          <!-- 加载中显示 - 骨架屏 -->
          <template v-else>
            <div v-for="i in 3" :key="`skeleton-${i}`" class="task-item skeleton-item">
              <div class="task-icon skeleton">
                <div class="skeleton-circle" />
              </div>
              <div class="task-info">
                <div class="task-title skeleton" />
                <div class="task-description skeleton" />
              </div>
              <div class="task-button skeleton" />
            </div>
          </template>

          <!-- 无任务时显示 -->
          <div
            v-if="!tasksStore.loading && tasksStore.tasks.length === 0 && !initialLoading"
            class="no-tasks"
          >
            <p>No tasks available at the moment.</p>
          </div>

          <!-- 全部完成显示 -->
          <div
            v-if="!tasksStore.loading && !initialLoading && tasksStore.allTasksCompleted"
            class="all-completed"
          >
            <div class="completed-icon">✓</div>
            <p>All tasks completed for today!</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { TaskItem } from '~/composables/useApi'

// 图标配置
const { icons } = useCdn()

// 使用新的SEO规则：针对日常任务页面的专门优化
usePageSeo({
  pageTitle: 'Daily Tasks',
  customDescription:
    'Complete daily tasks and check-in to earn diamonds on ReelPlay AI. Free rewards every day for active users.',
  useAutoDescription: false
})

// 页面元数据
definePageMeta({
  layout: 'mobile'
})

// 状态管理
const router = useRouter()
const sysConfigStore = useSysConfigStore()
const checkinStore = useCheckinStore()
const tasksStore = useTasksStore()

// 消息提示
const { success } = useMessage()

// 本地状态
const currentTaskId = ref<string | null>(null)
const showInviteModal = ref(false)
const initialLoading = ref(true)

// 页面初始化
onMounted(async () => {
  // 获取系统配置
  if (!sysConfigStore.checkInCoinsPerDay) {
    await sysConfigStore.fetchConfigs()
  }

  // 获取签到信息
  await checkinStore.fetchCheckinInfo()

  // 获取任务列表
  await tasksStore.fetchTasks()

  initialLoading.value = false
})

// 处理签到
const handleClaim = async () => {
  if (checkinStore.todayClaimed) return

  const entry =
    Object.entries(sysConfigStore.checkInCoinsPerDay || {}).find(
      ([key]) => checkinStore.getDayNumber(key) === checkinStore.currentDay
    ) || []
  const key = entry[0]
  const reward = entry[1]

  if (!key) return

  const claimSuccess = await checkinStore.claimDailyReward()
  if (claimSuccess) {
    success(`You've claimed ${reward} diamonds!`)
  }
}

// 处理任务操作
const handleTaskAction = async (task: TaskItem) => {
  if (task.is_done || tasksStore.loading) return

  currentTaskId.value = task.id

  try {
    switch (task.type) {
      case 'play_game': {
        await router.push('/')
        break
      }
      case 'share': {
        await router.push('/')
        break
      }
      case 'invite': {
        const inviteCode = await tasksStore.fetchInviteCode()
        if (inviteCode) {
          showInviteModal.value = true
        }
        break
      }
    }
  } catch (error) {
    console.error('Failed to complete task:', error)
  } finally {
    currentTaskId.value = null
  }
}
</script>

<style scoped>
.daily-tasks-page {
  min-height: 100vh;
  background-color: var(--mobile-bg-primary);
  color: var(--text-primary);
  padding-bottom: 20px;
  transition: all 0.3s ease;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 16px;
  position: relative;
}

.back-button {
  position: absolute;
  left: 16px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  border-radius: 50%;
  cursor: pointer;
  transition: background 0.3s ease;
}

.back-button:hover {
  background: var(--bg-hover);
}

.page-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.content {
  padding: 0 16px;
  margin-top: 24px;
}

.section {
  background: var(--mobile-bg-secondary);
  border-radius: 16px;
  padding: 16px;
  margin-bottom: 16px;
  transition: background 0.3s ease;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px;
  color: var(--text-primary);
}

.checkin-section {
  background: var(--mobile-bg-secondary);
  padding: 16px 12px 20px;
  border-radius: 20px;
  box-shadow: 0 4px 12px var(--shadow-color);
}

.checkin-grid {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 0 10px;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  overflow: visible;
  align-items: center;
}

.checkin-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 42px;
  text-align: center;
  position: relative;
  flex: 1;
}

.checkin-item.is-today {
  opacity: 1;
}

.diamond-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 4px;
}

.diamond-icon {
  position: relative;
  width: 40px;
  height: 40px;
  background: var(--bg-tertiary);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 2px;
  margin-bottom: 2px;
  box-shadow: 0 2px 4px var(--shadow-color);
  overflow: hidden;
  padding: 4px;
  z-index: 10;
  transition: all 0.3s ease;
}

.diamond-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 12px;
  border: 2px solid transparent;
}

.credit-icon {
  width: 14px;
  height: 14px;
  z-index: 1;
}

.reward-amount {
  bottom: 2px;
  font-size: 12px;
  font-weight: 600;
  color: var(--accent-color);
  z-index: 2;
}

.check-mark {
  color: var(--text-primary);
  font-size: 10px;
  font-weight: bold;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  z-index: 2;
}

.checkin-item.is-claimed .diamond-icon {
  background: var(--bg-tertiary);
}

.checkin-item.is-claimed .credit-icon {
  opacity: 0.7;
}

.checkin-item.is-today:not(.is-claimed) .diamond-icon {
  background: var(--accent-color);
  border: 1px solid var(--bg-primary);
}

.checkin-item.is-today.is-claimed .diamond-icon {
  background: var(--bg-tertiary);
}

.day-label {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
  margin-top: 2px;
}

.claim-button {
  width: 100%;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  background: linear-gradient(180deg, var(--accent-hover) 0%, var(--accent-color) 100%);
  color: var(--bg-primary);
  box-shadow: 0 4px 8px var(--accent-shadow);
  position: relative;
  overflow: hidden;
  border-radius: 26px;
  border-top: 2px solid var(--bg-primary);
  border-right: 2px solid var(--bg-primary);
  border-bottom: 6px solid var(--bg-primary);
  border-left: 2px solid var(--bg-primary);
  transition: all 0.3s ease;
}

.claim-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 12px var(--accent-shadow);
}

.claim-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.claim-button.is-claimed {
  background: var(--accent-bg);
  color: var(--text-secondary);
  box-shadow: none;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 200px;
}

.no-tasks,
.all-completed {
  text-align: center;
  padding: 20px 0;
  color: var(--text-secondary);
}

.no-tasks p,
.all-completed p {
  margin: 8px 0 0;
}

.all-completed .completed-icon {
  width: 40px;
  height: 40px;
  background: var(--accent-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: var(--bg-primary);
  font-size: 20px;
  font-weight: bold;
}

.all-completed p {
  color: var(--accent-color);
  font-weight: 500;
}

/* 骨架屏动画 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton {
  background: linear-gradient(
    90deg,
    var(--bg-tertiary) 25%,
    var(--bg-hover) 37%,
    var(--bg-tertiary) 63%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.skeleton-item {
  background: var(--bg-tertiary);
  transition: all 0.3s ease;
}

.skeleton-item .task-title.skeleton {
  height: 16px;
  width: 70%;
  margin-bottom: 8px;
}

.skeleton-item .task-description.skeleton {
  height: 12px;
  width: 90%;
}

.skeleton-item .task-button.skeleton {
  min-width: 70px;
  height: 32px;
  border-radius: 16px;
}

.skeleton-item .skeleton-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--bg-hover);
}

.task-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 12px;
  transition: opacity 0.3s ease;
}

.task-item.is-completed {
  opacity: 0.7;
}

.task-icon {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  background: var(--accent-bg);
  transition: background 0.3s ease;
}

.task-icon .credit-icon {
  width: 14px;
  height: 14px;
}

.task-icon .reward-amount {
  bottom: -4px;
  right: -4px;
  padding: 2px 4px;
  font-size: 10px;
  font-weight: 600;
  color: var(--accent-color);
}

.task-info {
  flex: 1;
}

.task-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--text-primary);
}

.task-description {
  font-size: 12px;
  color: var(--text-secondary);
}

.task-progress {
  font-size: 11px;
  color: var(--accent-color);
  margin-top: 4px;
  font-weight: 500;
}

.task-button {
  min-width: 70px;
  height: 32px;
  border-radius: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
}

.task-button.check-button {
  background: var(--accent-color);
  color: var(--bg-primary);
}

.task-button.check-button:hover {
  background: var(--accent-hover);
}

.task-button.ok-button {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.task-button.ok-button:hover {
  background: var(--bg-hover);
}

.task-button.completed-button {
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
  cursor: not-allowed;
  opacity: 0.6;
}
</style>
