#!/bin/bash

# AWS Amplify 构建检查脚本
# 用于本地验证 Amplify 构建流程

echo "🚀 开始 Amplify 构建检查..."

# 检查 Node.js 版本
echo "📋 检查 Node.js 版本..."
node --version

# 确保使用 Node.js 20.9.0+ (解决 oxc-parser 问题)
REQUIRED_NODE_VERSION="20.9.0"
CURRENT_NODE_VERSION=$(node --version | sed 's/v//')

echo "📋 当前 Node.js 版本: $CURRENT_NODE_VERSION"
echo "📋 要求 Node.js 版本: $REQUIRED_NODE_VERSION+"

# 简单的版本检查
if [[ "$(printf '%s\n' "$REQUIRED_NODE_VERSION" "$CURRENT_NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_NODE_VERSION" ]]; then
    echo "❌ Node.js 版本过低，请升级到 $REQUIRED_NODE_VERSION 或更高版本"
    echo "💡 建议运行: nvm use 20.9.0 或 nvm install 20.9.0"
    exit 1
fi

echo "✅ Node.js 版本符合要求"

# 检查 pnpm 版本
echo "📋 检查 pnpm 版本..."
if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm 未安装，正在安装..."
    npm install -g pnpm@8.11.0
fi
pnpm --version

# 设置内存限制
export NODE_OPTIONS="--max-old-space-size=4096"
echo "📋 设置 Node.js 内存限制: $NODE_OPTIONS"

# 模拟 Amplify 环境变量
export AWS_APP_ID="test-app-id"
echo "📋 模拟 Amplify 环境: AWS_APP_ID=$AWS_APP_ID"

# 清理缓存
echo "🧹 清理缓存..."
rm -rf node_modules/.cache
rm -rf packages/*/node_modules/.cache
rm -rf packages/nuxt-app/.nuxt
rm -rf packages/nuxt-app/.output

# 安装依赖
echo "📦 安装依赖..."
pnpm install --frozen-lockfile

# 构建项目
echo "🔨 构建 shared-payment..."
cd packages/shared-payment
pnpm run build

echo "🔨 构建 nuxt-app..."
cd ../nuxt-app
pnpm run build:production

# 检查输出
echo "📁 检查构建输出..."

# 优先检查 Amplify SSR 输出目录
if [ -d ".amplify-hosting" ]; then
    echo "✅ Amplify SSR 构建成功！"
    echo "📊 完整输出大小:"
    du -sh .amplify-hosting

    # 检查服务端代码
    if [ -d ".amplify-hosting/compute" ]; then
        echo "✅ 找到服务端代码目录 (compute)"
        ls -la .amplify-hosting/compute/
    else
        echo "❌ 未找到服务端代码目录"
    fi

    # 检查静态文件
    if [ -d ".amplify-hosting/static" ]; then
        echo "✅ 找到静态文件目录"
        STATIC_COUNT=$(find .amplify-hosting/static -type f | wc -l)
        echo "📊 静态文件数量: $STATIC_COUNT"
    else
        echo "❌ 未找到静态文件目录"
    fi

    # 检查部署清单
    if [ -f ".amplify-hosting/deploy-manifest.json" ]; then
        echo "✅ 找到部署清单"
        echo "📄 部署清单内容:"
        cat .amplify-hosting/deploy-manifest.json
    else
        echo "❌ 未找到部署清单"
    fi

elif [ -d ".output/public" ]; then
    echo "✅ 标准 SSR 构建成功！输出目录: .output/public"
    echo "📊 输出文件大小:"
    du -sh .output/public
    echo "📄 输出文件列表:"
    ls -la .output/public

    # 检查关键文件
    if [ -f ".output/public/index.html" ]; then
        echo "✅ 找到 index.html"
    else
        echo "⚠️  未找到 index.html"
    fi

else
    echo "❌ 构建失败！未找到输出目录"
    echo "🔍 查找所有目录:"
    ls -la
    echo "🔍 查找 .output 目录:"
    ls -la .output/ || echo "未找到 .output 目录"
    echo "🔍 查找 .amplify-hosting 目录:"
    ls -la .amplify-hosting/ || echo "未找到 .amplify-hosting 目录"
    exit 1
fi

echo "🎉 Amplify 构建检查完成！"
