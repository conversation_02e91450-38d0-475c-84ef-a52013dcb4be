version: 1
backend:
  phases:
    build:
      commands:
        - '# No backend build required for CSR app'
frontend:
  phases:
    preBuild:
      commands:
        # 安装 pnpm
        - npm install -g pnpm@8.11.0
        # 设置 Node.js 内存限制为 4GB (Amplify 8GB 实例)
        - export NODE_OPTIONS="--max-old-space-size=4096"
        # 安装依赖
        - pnpm install --frozen-lockfile
    build:
      commands:
        # 先构建 shared-payment 依赖
        - cd packages/shared-payment
        - pnpm run build
        # 构建 csr-app (PlayShot 正式环境)
        - cd ../csr-app
        - echo "Building CSR app for PlayShot production..."
        - pnpm run build:prod:playshot
        # 验证构建输出
        - echo "Verifying PlayShot production build output..."
        - ls -la dist/ || echo "No dist directory"
        - echo "Static files count:"
        - find dist -type f | wc -l || echo "0"
        - echo "Checking for index.html:"
        - ls -la dist/index.html || echo "No index.html found"
        - echo "Checking app configuration:"
        - grep -r "PlayShot\|playshot" dist/ || echo "No PlayShot branding found"
    postBuild:
      commands:
        - echo "PlayShot production build completed successfully"
  artifacts:
    # CSR 应用的静态输出目录
    baseDirectory: packages/csr-app/dist
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
      - packages/*/node_modules/**/*
      - packages/csr-app/node_modules/.vite/**/*
