#!/bin/bash

# 股票代码数组
STOCKS=("RDDT" "TSLA" "APP" "HOOD")

# 上一次查询的价格记忆
declare -A LAST_PRICE

# ANSI颜色
RESET="\033[0m"
RED="\033[31m"
GREEN="\033[32m"
YELLOW="\033[33;1m"   # 高亮黄
BOLD="\033[1m"
BOLD_RED="\033[31;1m"
BOLD_YELLOW="\033[33;1m"

# 大波动阈值（比如 5%）
THRESHOLD=5

# 获取 sleep 时长（秒）
get_sleep_duration() {
  current_time=$(TZ=America/New_York date +%H:%M)

  if [[ "$current_time" < "04:00" || "$current_time" > "16:00" ]]; then
    echo 3600  # 非交易时段，每小时获取一次
  elif [[ "$current_time" < "06:30" ]]; then
    echo 1800  # 盘前前半段，每30分钟
  elif [[ "$current_time" < "09:15" ]]; then
    echo 600   # 盘前主力活跃段，每10分钟
  elif [[ "$current_time" < "09:30" ]]; then
    echo 300   # 开盘前15分钟，每5分钟
  elif [[ "$current_time" < "10:30" ]]; then
    echo 300   # 盘中早盘，每5分钟
  elif [[ "$current_time" < "14:00" ]]; then
    echo 900   # 盘中中段，每15分钟
  else
    echo 600   # 盘中尾盘，每10分钟
  fi
}

# 主循环
while true; do
  echo ""
  echo -e "🕒 更新时间: $(date '+%F %T')  (ET: $(TZ=America/New_York date '+%H:%M'))"
  echo "==================================================================================="
  printf "📌 股票\t⏰ 时间\t\t💵 价格\t📉 涨跌\t📈 涨跌%%\t🟢 买价\t🔴 卖价\t📊 成交量\n"
  echo "-----------------------------------------------------------------------------------"

  for SYMBOL in "${STOCKS[@]}"; do
    RESPONSE=$(curl -s "https://api.nasdaq.com/api/quote/$SYMBOL/info?assetclass=stocks&lang=zh" \
      -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)' \
      -H 'Accept: application/json, text/plain, */*' \
      -H 'Accept-Language: zh-CN,zh;q=0.9' \
      -H 'Origin: https://www.nasdaq.com' \
      -H 'Referer: https://www.nasdaq.com/market-activity/stocks/'"$SYMBOL" \
      -H 'Connection: keep-alive' \
      --compressed)

    echo "$RESPONSE" | jq -r --arg SYMBOL "$SYMBOL" '
      .data.primaryData as $p | 
      [ $SYMBOL, $p.lastTradeTimestamp, $p.lastSalePrice, $p.netChange, $p.percentageChange, $p.bidPrice, $p.askPrice, $p.volume ] | @tsv
    '
  done | while IFS=$'\t' read -r symbol time price change percent bid ask volume; do
    numeric_price=$(echo "$price" | tr -d '$,')

    prev_price=${LAST_PRICE[$symbol]}
    if [[ -z "$prev_price" || "$prev_price" == "0" || "$numeric_price" == "0" ]]; then
      price_display="$price"
    else
      price_diff=$(echo "$numeric_price - $prev_price" | bc)
      percent_change=$(echo "scale=2; ($price_diff / $prev_price) * 100" | bc)

      diff=$(echo "$numeric_price > $prev_price" | bc)
      if [[ $diff -eq 1 ]]; then
        price_display="${YELLOW}${BOLD}$price${RESET}"
      else
        diff_down=$(echo "$numeric_price < $prev_price" | bc)
        if [[ $diff_down -eq 1 ]]; then
          price_display="${RED}$price${RESET}"
        else
          price_display="$price"
        fi
      fi
    fi

    LAST_PRICE[$symbol]=$numeric_price
    echo -e "$symbol\t$time\t$price_display\t$change\t$percent\t$bid\t$ask\t$volume"
  done

  echo "==================================================================================="

  # 动态 sleep
SLEEP_SECONDS=$(get_sleep_duration)
NEXT_TIME=$(TZ=America/New_York date -v+${SLEEP_SECONDS}S '+%H:%M')
echo "⏳ 下次更新时间：$NEXT_TIME ET （sleep $SLEEP_SECONDS 秒）"
sleep "$SLEEP_SECONDS"
done