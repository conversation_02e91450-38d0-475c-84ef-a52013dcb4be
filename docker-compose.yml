version: '3.8'

services:
  nuxt-app:
    build:
      context: .
      dockerfile: packages/nuxt-app/Dockerfile
      args:
        NODE_ENV: ${NODE_ENV:-production}
        DEPLOYMENT_ENV: ${DEPLOYMENT_ENV:-production}
    container_name: nuxt-app-${DEPLOYMENT_ENV:-production}
    restart: unless-stopped
    ports:
      - "${NUXT_PORT:-3000}:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - DEPLOYMENT_ENV=${DEPLOYMENT_ENV:-production}
      - NITRO_PORT=3000
      - NITRO_HOST=0.0.0.0
    env_file:
      - .env.${DEPLOYMENT_ENV:-production}
    networks:
      - magic-partner-network

  # CSR应用 (可选)
  csr-app:
    build:
      context: .
      dockerfile: packages/csr-app/Dockerfile
      args:
        NODE_ENV: ${NODE_ENV:-production}
        DEPLOYMENT_ENV: ${DEPLOYMENT_ENV:-production}
    container_name: csr-app-${DEPLOYMENT_ENV:-production}
    restart: unless-stopped
    ports:
      - "${CSR_PORT:-5173}:5173"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - DEPLOYMENT_ENV=${DEPLOYMENT_ENV:-production}
    env_file:
      - .env.${DEPLOYMENT_ENV:-production}
    networks:
      - magic-partner-network
    profiles:
      - full-stack

networks:
  magic-partner-network:
    driver: bridge
