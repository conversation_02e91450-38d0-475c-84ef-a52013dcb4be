#!/bin/bash

# Turbo Monorepo 部署脚本
# 使用方法: ./deploy-turbo.sh [staging|production] [nuxt|csr|all]

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 1 ]; then
    log_error "缺少环境参数"
    echo "使用方法: $0 [staging|production] [nuxt|csr|all]"
    echo "示例:"
    echo "  $0 staging nuxt     # 部署 staging 环境的 nuxt 应用"
    echo "  $0 production all   # 部署 production 环境的所有应用"
    exit 1
fi

ENVIRONMENT=$1
SERVICE=${2:-nuxt}

# 验证环境参数
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    log_error "无效的环境参数: $ENVIRONMENT"
    echo "使用方法: $0 [staging|production] [nuxt|csr|all]"
    exit 1
fi

# 验证服务参数
if [[ "$SERVICE" != "nuxt" && "$SERVICE" != "csr" && "$SERVICE" != "all" ]]; then
    log_error "无效的服务参数: $SERVICE"
    echo "使用方法: $0 [staging|production] [nuxt|csr|all]"
    exit 1
fi

log_info "🚀 开始部署 Turbo Monorepo..."
echo "   环境: $ENVIRONMENT"
echo "   服务: $SERVICE"

# 检查必要工具
if ! command -v docker &> /dev/null; then
    log_error "Docker 未安装"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    log_error "Docker Compose 未安装"
    exit 1
fi

if ! command -v turbo &> /dev/null; then
    log_error "Turbo 未安装，请运行: pnpm install -g turbo"
    exit 1
fi

# 检查环境文件
ENV_FILE=".env.$ENVIRONMENT"
if [ ! -f "$ENV_FILE" ]; then
    log_warning "⚠️  环境文件 $ENV_FILE 不存在"
    log_info "创建默认环境文件..."
    
    cat > "$ENV_FILE" << EOF
NODE_ENV=$ENVIRONMENT
DEPLOYMENT_ENV=$ENVIRONMENT
NUXT_PORT=3000
CSR_PORT=5173
EOF
    
    log_success "已创建默认环境文件: $ENV_FILE"
    log_warning "请根据需要修改环境变量"
fi

# 设置环境变量
export NODE_ENV=$ENVIRONMENT
export DEPLOYMENT_ENV=$ENVIRONMENT

# 停止现有服务
log_info "停止现有服务..."
if [ "$SERVICE" = "all" ]; then
    docker-compose --env-file "$ENV_FILE" down || true
elif [ "$SERVICE" = "nuxt" ]; then
    docker-compose --env-file "$ENV_FILE" stop nuxt-app || true
    docker-compose --env-file "$ENV_FILE" rm -f nuxt-app || true
elif [ "$SERVICE" = "csr" ]; then
    docker-compose --env-file "$ENV_FILE" stop csr-app || true
    docker-compose --env-file "$ENV_FILE" rm -f csr-app || true
fi

# 清理旧镜像
log_info "清理旧镜像..."
docker image prune -f || true

# 构建和启动服务
log_info "构建和启动服务..."
if [ "$SERVICE" = "all" ]; then
    docker-compose --env-file "$ENV_FILE" --profile full-stack up -d --build
elif [ "$SERVICE" = "nuxt" ]; then
    docker-compose --env-file "$ENV_FILE" up -d --build nuxt-app
elif [ "$SERVICE" = "csr" ]; then
    docker-compose --env-file "$ENV_FILE" --profile full-stack up -d --build csr-app
fi

# 等待服务启动
log_info "等待服务启动..."
sleep 20

# 健康检查
log_info "执行健康检查..."
HEALTH_CHECK_PASSED=true

if [ "$SERVICE" = "nuxt" ] || [ "$SERVICE" = "all" ]; then
    NUXT_PORT=$(grep NUXT_PORT "$ENV_FILE" | cut -d'=' -f2 || echo "3000")
    if curl -f "http://localhost:$NUXT_PORT/" > /dev/null 2>&1; then
        log_success "✅ Nuxt 应用健康检查通过"
    else
        log_error "❌ Nuxt 应用健康检查失败"
        HEALTH_CHECK_PASSED=false
    fi
fi

if [ "$SERVICE" = "csr" ] || [ "$SERVICE" = "all" ]; then
    CSR_PORT=$(grep CSR_PORT "$ENV_FILE" | cut -d'=' -f2 || echo "5173")
    if curl -f "http://localhost:$CSR_PORT/" > /dev/null 2>&1; then
        log_success "✅ CSR 应用健康检查通过"
    else
        log_error "❌ CSR 应用健康检查失败"
        HEALTH_CHECK_PASSED=false
    fi
fi

if [ "$HEALTH_CHECK_PASSED" = true ]; then
    log_success "🎉 部署成功!"
    echo ""
    echo "🌐 访问地址:"
    if [ "$SERVICE" = "nuxt" ] || [ "$SERVICE" = "all" ]; then
        NUXT_PORT=$(grep NUXT_PORT "$ENV_FILE" | cut -d'=' -f2 || echo "3000")
        echo "   Nuxt App: http://localhost:$NUXT_PORT"
    fi
    if [ "$SERVICE" = "csr" ] || [ "$SERVICE" = "all" ]; then
        CSR_PORT=$(grep CSR_PORT "$ENV_FILE" | cut -d'=' -f2 || echo "5173")
        echo "   CSR App:  http://localhost:$CSR_PORT"
    fi
    echo ""
    echo "📊 容器状态:"
    docker-compose --env-file "$ENV_FILE" ps
else
    log_error "❌ 部署失败"
    echo ""
    echo "📋 查看日志:"
    docker-compose --env-file "$ENV_FILE" logs --tail=20
    exit 1
fi

log_success "🎉 Turbo Monorepo 部署完成!"
