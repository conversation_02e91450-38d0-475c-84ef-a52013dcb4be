# Magic Partner Monorepo

一个基于 Nuxt 3 和 Vue 3 的 AI 角色聊天平台，采用微前端架构。

## 🏗️ 项目架构

本项目采用 **Monorepo** 架构，包含两个独立的应用：

```
magic-partner/
├── packages/
│   ├── nuxt-app/          # Nuxt 3 SSR 应用 (首页 + iframe 容器)
│   └── csr-app/           # Vue 3 CSR 应用 (聊天功能)
├── package.json           # 根级别依赖管理
├── pnpm-workspace.yaml    # pnpm workspace 配置
└── .husky/                # Git hooks 配置
```

### 🎯 应用职责分工

#### Nuxt App (SSR)

- **首页展示**: 故事列表、用户认证、SEO 优化
- **路由容器**: 通过 iframe 嵌入 CSR 应用的聊天功能
- **用户认证**: 登录、注册、用户状态管理
- **SEO 友好**: 服务端渲染，搜索引擎优化

#### CSR App (客户端渲染)

- **聊天功能**: 4种聊天模式的完整实现
- **实时交互**: WebSocket、音频、视频处理
- **游戏逻辑**: 复杂的聊天状态机和事件处理
- **性能优化**: 客户端渲染，快速交互响应

## 🚀 快速开始

### 环境要求

- Node.js >= 18
- pnpm >= 8.11.0

### 安装依赖

```bash
# 安装所有依赖
pnpm install
```

### 开发模式

```bash
# 启动 Nuxt 应用 (端口 3000)
pnpm dev:nuxt

# 启动 CSR 应用 (端口 5173)
pnpm dev:csr

# 同时启动两个应用
pnpm dev
```

### 构建部署

```bash
# 构建所有应用
pnpm build

# 构建特定应用
pnpm build:nuxt
pnpm build:csr

# 生产环境构建
pnpm build:prod.southeastAsia
```

## 🛠️ 开发工具

### 代码质量

```bash
# 类型检查
pnpm type:check

# 代码格式化
pnpm lint:fix

# 运行测试
pnpm test
```

### Git Hooks

项目配置了 Husky Git hooks：

- **pre-commit**: 自动运行类型检查和代码格式化
- **commit-msg**: 验证提交信息格式 (conventional commits)

## 📁 项目结构

### Nuxt App 结构

```
packages/nuxt-app/
├── components/           # Vue 组件
│   ├── HomePagePC.vue   # PC 端首页
│   ├── HomePageMobile.vue # 移动端首页
│   ├── StoryCard.vue    # 故事卡片
│   └── ...
├── pages/               # 路由页面
│   ├── index.vue        # 首页
│   └── chat/            # 聊天页面 (iframe 容器)
├── composables/         # 组合式函数
├── stores/              # Pinia 状态管理
└── nuxt.config.ts       # Nuxt 配置
```

### CSR App 结构

```
packages/csr-app/
├── src/
│   ├── components/      # Vue 组件
│   ├── views/           # 页面视图
│   ├── stores/          # Pinia 状态管理
│   ├── composables/     # 组合式函数
│   └── utils/           # 工具函数
├── public/              # 静态资源
└── vite.config.ts       # Vite 配置
```

## 🔧 配置说明

### 环境配置

项目支持多环境配置：

- `dev` - 开发环境
- `prod` - 生产环境
- `prod.southeastAsia` - 东南亚生产环境

### 微前端通信

Nuxt 应用通过 iframe 嵌入 CSR 应用，使用 postMessage 进行通信：

```javascript
// Nuxt 应用发送消息
iframe.contentWindow.postMessage(
  {
    type: 'USER_INFO',
    data: userInfo
  },
  'https://dev.playshot.ai/'
)

// CSR 应用接收消息
window.addEventListener('message', (event) => {
  if (event.origin !== 'https://nuxt.yourdomain.com') return
  // 处理消息
})
```

## 📦 依赖管理

使用 pnpm workspace 管理依赖：

- **根级别**: 共享的开发工具 (husky, commitlint, lint-staged)
- **子应用**: 各自独立的运行时依赖

## 🚀 部署

### Docker 部署

```bash
# 构建 Docker 镜像
docker build -t magic-partner .

# 运行容器
docker run -p 3000:3000 magic-partner
```

### AWS Amplify 部署

项目配置了 AWS Amplify 自动部署：

- **构建命令**: `pnpm run build:prod.southeastAsia`
- **输出目录**: `packages/nuxt-app/.output/public`
- **Node.js 内存**: 4GB

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'feat: add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 提交 Pull Request

### 提交信息规范

使用 [Conventional Commits](https://conventionalcommits.org/) 规范：

- `feat:` 新功能
- `fix:` 修复 bug
- `docs:` 文档更新
- `style:` 代码格式化
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建工具或辅助工具的变动

## 📄 许可证

[MIT License](LICENSE)

## 🔧 故障排除

### 常见问题

#### 1. 依赖安装失败

```bash
# 清理缓存重新安装
rm -rf node_modules packages/*/node_modules
pnpm store prune
pnpm install
```

#### 2. 类型检查错误

```bash
# 重新生成类型文件
pnpm --filter nuxt-app prepare
pnpm type:check
```

#### 3. 端口冲突

- Nuxt 应用默认端口: 3000
- CSR 应用默认端口: 5173
- 可在各自的配置文件中修改端口

#### 4. iframe 通信问题

检查 CSR 应用的 URL 配置是否与 Nuxt 应用中的 iframe src 一致。

### 性能优化建议

1. **开发环境**: 使用 `pnpm dev` 同时启动两个应用
2. **生产环境**: 分别部署两个应用到不同域名
3. **缓存策略**: 配置适当的静态资源缓存
4. **CDN**: 使用 CDN 加速静态资源加载

## 🔗 相关链接

- [Nuxt 3 文档](https://nuxt.com/)
- [Vue 3 文档](https://vuejs.org/)
- [Pinia 文档](https://pinia.vuejs.org/)
- [pnpm 文档](https://pnpm.io/)
- [Conventional Commits](https://conventionalcommits.org/)
- [Husky 文档](https://typicode.github.io/husky/)
