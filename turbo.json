{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*", "!.env.local", "!.env*.local"], "outputs": [".nuxt/**", ".output/**", "dist/**"]}, "build:staging": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env.staging", ".env*"], "outputs": [".nuxt/**", ".output/**", "dist/**"], "env": ["NODE_ENV", "DEPLOYMENT_ENV"]}, "build:production": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env.production", ".env*"], "outputs": [".nuxt/**", ".output/**", "dist/**"], "env": ["NODE_ENV", "DEPLOYMENT_ENV"]}, "build:prod.southeastAsia": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".nuxt/**", ".output/**", "dist/**"], "env": ["NODE_ENV", "VITE_DEPLOYMENT_ENV"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"], "inputs": ["$TURBO_DEFAULT$", ".eslintrc*", "eslint.config.*"]}, "lint:fix": {"dependsOn": ["^lint:fix"], "inputs": ["$TURBO_DEFAULT$", ".eslintrc*", "eslint.config.*"]}, "type:check": {"dependsOn": ["^type:check"], "inputs": ["$TURBO_DEFAULT$", "tsconfig.json", "**/*.ts", "**/*.tsx", "**/*.vue"]}, "test": {"dependsOn": ["^test"], "inputs": ["$TURBO_DEFAULT$", "**/*.test.*", "**/*.spec.*"]}, "clean": {"cache": false}, "deploy": {"dependsOn": ["build"], "cache": false}, "upload:staging": {"dependsOn": ["build:staging"], "cache": false}, "upload:production": {"dependsOn": ["build:production"], "cache": false}}, "globalDependencies": ["package.json", "pnpm-lock.yaml", "pnpm-workspace.yaml", ".pnpmrc"]}