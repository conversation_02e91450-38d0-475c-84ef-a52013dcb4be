# customHttp.yml（amplify custom headers）
# 临时禁用
headers:
  # 压缩的JS文件 - 带版本控制的长期缓存
  - pattern: '**/assets-v*/**/*.js.br'
    headers:
      - key: 'Content-Encoding'
        value: 'br'
      - key: 'Content-Type'
        value: 'application/javascript'
      - key: 'Cache-Control'
        value: 'public, max-age=31536000, immutable'
      - key: 'ETag'
        value: 'W/"{{request_id}}"'

  - pattern: '**/assets-v*/**/*.js.gz'
    headers:
      - key: 'Content-Encoding'
        value: 'gzip'
      - key: 'Content-Type'
        value: 'application/javascript'
      - key: 'Cache-Control'
        value: 'public, max-age=31536000, immutable'
      - key: 'ETag'
        value: 'W/"{{request_id}}"'

  # 压缩的CSS文件 - 带版本控制的长期缓存
  - pattern: '**/assets-v*/**/*.css.br'
    headers:
      - key: 'Content-Encoding'
        value: 'br'
      - key: 'Content-Type'
        value: 'text/css'
      - key: 'Cache-Control'
        value: 'public, max-age=31536000, immutable'
      - key: 'ETag'
        value: 'W/"{{request_id}}"'

  - pattern: '**/assets-v*/**/*.css.gz'
    headers:
      - key: 'Content-Encoding'
        value: 'gzip'
      - key: 'Content-Type'
        value: 'text/css'
      - key: 'Cache-Control'
        value: 'public, max-age=31536000, immutable'
      - key: 'ETag'
        value: 'W/"{{request_id}}"'

  # 带版本控制的JS文件 - 长期缓存
  - pattern: '**/assets-v*/**/*.js'
    headers:
      - key: 'Content-Type'
        value: 'application/javascript'
      - key: 'Cache-Control'
        value: 'public, max-age=31536000, immutable'
      - key: 'ETag'
        value: 'W/"{{request_id}}"'

  # 带版本控制的CSS文件 - 长期缓存
  - pattern: '**/assets-v*/**/*.css'
    headers:
      - key: 'Content-Type'
        value: 'text/css'
      - key: 'Cache-Control'
        value: 'public, max-age=31536000, immutable'
      - key: 'ETag'
        value: 'W/"{{request_id}}"'

  # 根目录下的JS/CSS文件 - 短期缓存，防止版本冲突
  - pattern: '/*.js'
    headers:
      - key: 'Content-Type'
        value: 'application/javascript'
      - key: 'Cache-Control'
        value: 'public, max-age=3600, must-revalidate'
      - key: 'ETag'
        value: 'W/"{{request_id}}"'

  - pattern: '/*.css'
    headers:
      - key: 'Content-Type'
        value: 'text/css'
      - key: 'Cache-Control'
        value: 'public, max-age=3600, must-revalidate'
      - key: 'ETag'
        value: 'W/"{{request_id}}"'

  # HTML文件 - 永不缓存，确保总是获取最新版本
  - pattern: '**/*.html'
    headers:
      - key: 'Cache-Control'
        value: 'no-cache, no-store, must-revalidate'
      - key: 'Pragma'
        value: 'no-cache'
      - key: 'Expires'
        value: '0'
      - key: 'Content-Security-Policy'
        value: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.magiclight.ai https://js.stripe.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https: wss:; media-src 'self' https:;"

  # Service Worker - 永不缓存，确保立即更新
  - pattern: '/sw.js'
    headers:
      - key: 'Cache-Control'
        value: 'no-cache, no-store, must-revalidate'
      - key: 'Pragma'
        value: 'no-cache'
      - key: 'Expires'
        value: '0'
      - key: 'Service-Worker-Allowed'
        value: '/'

  # 图片资源 - 带版本控制的长期缓存
  - pattern: '**/assets-v*/**/*.{png,jpg,jpeg,gif,webp,svg,ico}'
    headers:
      - key: 'Cache-Control'
        value: 'public, max-age=31536000, immutable'
      - key: 'ETag'
        value: 'W/"{{request_id}}"'

  # 根目录图片 - 中期缓存
  - pattern: '/*.{png,jpg,jpeg,gif,webp,svg,ico}'
    headers:
      - key: 'Cache-Control'
        value: 'public, max-age=86400, must-revalidate'
      - key: 'ETag'
        value: 'W/"{{request_id}}"'

  # 字体文件 - 带版本控制的长期缓存
  - pattern: '**/assets-v*/**/*.{woff,woff2,ttf,eot}'
    headers:
      - key: 'Cache-Control'
        value: 'public, max-age=31536000, immutable'
      - key: 'Access-Control-Allow-Origin'
        value: '*'
      - key: 'ETag'
        value: 'W/"{{request_id}}"'

  # 根目录字体 - 中期缓存
  - pattern: '/*.{woff,woff2,ttf,eot}'
    headers:
      - key: 'Cache-Control'
        value: 'public, max-age=86400, must-revalidate'
      - key: 'Access-Control-Allow-Origin'
        value: '*'
      - key: 'ETag'
        value: 'W/"{{request_id}}"'

  # Manifest文件 - 短期缓存，支持PWA更新
  - pattern: '/manifest.json'
    headers:
      - key: 'Content-Type'
        value: 'application/manifest+json'
      - key: 'Cache-Control'
        value: 'public, max-age=3600, must-revalidate'
      - key: 'ETag'
        value: 'W/"{{request_id}}"'

  # 版本信息文件 - 永不缓存，确保版本检查准确
  - pattern: '/version.json'
    headers:
      - key: 'Content-Type'
        value: 'application/json'
      - key: 'Cache-Control'
        value: 'no-cache, no-store, must-revalidate'
      - key: 'Pragma'
        value: 'no-cache'
      - key: 'Expires'
        value: '0'
      - key: 'ETag'
        value: 'W/"{{request_id}}"'

  # 通用静态资源回退规则 - 短期缓存
  - pattern: '**/*'
    headers:
      - key: 'Cache-Control'
        value: 'public, max-age=3600, must-revalidate'
      - key: 'ETag'
        value: 'W/"{{request_id}}"'
