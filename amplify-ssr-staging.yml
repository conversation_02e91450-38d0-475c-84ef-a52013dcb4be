version: 1
backend:
  phases:
    build:
      commands:
        - '# No backend build required'
frontend:
  phases:
    preBuild:
      commands:
        # 确保使用 Node.js 20.9.0+ (解决 oxc-parser 问题)
        # 安装 pnpm
        - npm install -g pnpm@8.11.0
        # 设置 Node.js 内存限制为 4GB (Amplify 8GB 实例)
        - export NODE_OPTIONS="--max-old-space-size=4096"
        # 安装依赖
        - pnpm install --frozen-lockfile
    build:
      commands:
        # 先构建 shared-payment 依赖
        - cd packages/shared-payment
        - pnpm run build
        # 构建 nuxt-app (SSR 模式)
        - cd ../nuxt-app
        - echo "Building Nuxt app in SSR mode..."
        - pnpm run build:staging
        # 验证构建输出
        - echo "Verifying SSR build output..."
        - ls -la .amplify-hosting/ || echo "No .amplify-hosting directory"
        - echo "Checking compute directory (server code):"
        - ls -la .amplify-hosting/compute/ || echo "No compute directory"
        - echo "Checking static directory:"
        - ls -la .amplify-hosting/static/ || echo "No static directory"
        - echo "Checking for deploy manifest:"
        - cat .amplify-hosting/deploy-manifest.json || echo "No deploy manifest"
    postBuild:
      commands:
        - echo "Build completed successfully"
  artifacts:
    # Nuxt 3 SSR 应用的完整输出目录 (包含服务端代码)
    baseDirectory: packages/nuxt-app/.amplify-hosting
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
      - packages/*/node_modules/**/*
      - packages/nuxt-app/.nuxt/**/*
