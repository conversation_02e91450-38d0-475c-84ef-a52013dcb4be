# Dependencies
node_modules/
**/node_modules/

# Build outputs
dist/
dist-ssr/
.output/
.nuxt/
.nitro/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.local

# Package manager files
yarn-error.log
yarn.lock
package-lock.json
pnpm-debug.log*

# IDE files
.idea/
.vscode/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm/

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache/
.parcel-cache/

# Next.js build output
.next/

# Nuxt.js build / generate output
.nuxt/
dist/

# Gatsby files
.cache/
public/

# Storybook build outputs
.out/
.storybook-out/

# Temporary folders
tmp/
temp/

# Editor directories and files
.history/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Local development
.local/

# Test files
test-results/
playwright-report/
test-results.xml

# Cypress
cypress/videos/
cypress/screenshots/

# Vitest
coverage/

# CSR App specific
packages/csr-app/dist/
packages/csr-app/.output/

# Nuxt App specific
packages/nuxt-app/.output/
packages/nuxt-app/.nuxt/
packages/nuxt-app/.nitro/
packages/nuxt-app/dist/

# Deployment files
*.tar.gz
*.zip

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# Monorepo specific
# Prevent individual package lock files
packages/*/package-lock.json
packages/*/yarn.lock
packages/*/pnpm-lock.yaml

# Prevent individual node_modules
packages/*/node_modules/

# Turborepo cache
.turbo/
**/.turbo/

# Husky
.husky/_

# Git hooks backup
*.sample

# macOS specific
.AppleDouble
.LSOverride

# Windows specific
desktop.ini

# Linux specific
*~

# Temporary files
.tmp/
.temp/

# IDE specific
.idea/
.vscode/
*.sublime-*
*.code-workspace

# Runtime files
.pid
.seed
.pid.lock

# Optional cache directories
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# Deployment files
deploy.sh
*.deploy.js

# Documentation files (except README.md)
docs/
*.md
*.MD
!README.md
!**/README.md

# Documentation build
docs/dist/
docs/.vitepress/cache/
docs/.vitepress/dist/
