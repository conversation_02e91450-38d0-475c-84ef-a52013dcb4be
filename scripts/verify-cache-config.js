#!/usr/bin/env node

/**
 * 缓存配置验证脚本
 * 用于验证部署后的缓存头配置是否正确
 */

const https = require('https')
const http = require('http')

class CacheConfigVerifier {
  constructor(baseUrl) {
    this.baseUrl = baseUrl.replace(/\/$/, '') // 移除末尾斜杠
    this.results = []
  }

  /**
   * 发起HTTP请求并检查响应头
   */
  async checkUrl(path, expectedHeaders = {}) {
    return new Promise((resolve, reject) => {
      const url = this.baseUrl + path
      const isHttps = url.startsWith('https')
      const client = isHttps ? https : http
      
      console.log(`🔍 检查: ${url}`)
      
      const req = client.request(url, { method: 'HEAD' }, (res) => {
        const headers = res.headers
        const result = {
          url,
          status: res.statusCode,
          headers: {},
          issues: []
        }
        
        // 检查关键缓存头
        const cacheControl = headers['cache-control']
        const etag = headers['etag']
        const expires = headers['expires']
        const pragma = headers['pragma']
        
        result.headers = {
          'cache-control': cacheControl,
          'etag': etag,
          'expires': expires,
          'pragma': pragma,
          'content-type': headers['content-type'],
          'content-encoding': headers['content-encoding']
        }
        
        // 验证预期的头部
        for (const [key, expectedValue] of Object.entries(expectedHeaders)) {
          const actualValue = headers[key.toLowerCase()]
          if (!actualValue) {
            result.issues.push(`缺少头部: ${key}`)
          } else if (expectedValue && !actualValue.includes(expectedValue)) {
            result.issues.push(`头部值不匹配: ${key} (期望包含: ${expectedValue}, 实际: ${actualValue})`)
          }
        }
        
        // 检查常见问题
        if (path.includes('.js') || path.includes('.css')) {
          if (!cacheControl) {
            result.issues.push('静态资源缺少 Cache-Control 头')
          } else if (path.includes('/assets-v') && !cacheControl.includes('immutable')) {
            result.issues.push('版本化资源应该使用 immutable 缓存')
          }
        }
        
        if (path === '/' || path.includes('.html')) {
          if (cacheControl && !cacheControl.includes('no-cache')) {
            result.issues.push('HTML 文件应该使用 no-cache')
          }
        }
        
        if (path === '/sw.js') {
          if (cacheControl && !cacheControl.includes('no-cache')) {
            result.issues.push('Service Worker 应该使用 no-cache')
          }
        }
        
        this.results.push(result)
        resolve(result)
      })
      
      req.on('error', (error) => {
        console.error(`❌ 请求失败: ${url}`, error.message)
        reject(error)
      })
      
      req.setTimeout(10000, () => {
        req.destroy()
        reject(new Error('请求超时'))
      })
      
      req.end()
    })
  }

  /**
   * 运行完整的缓存配置验证
   */
  async verify() {
    console.log(`🚀 开始验证缓存配置: ${this.baseUrl}`)
    console.log('=' * 50)
    
    const testCases = [
      // HTML 文件
      { path: '/', expectedHeaders: { 'cache-control': 'no-cache' } },
      
      // Service Worker
      { path: '/sw.js', expectedHeaders: { 'cache-control': 'no-cache' } },
      
      // 版本信息
      { path: '/version.json', expectedHeaders: { 'cache-control': 'no-cache' } },
      
      // Manifest
      { path: '/manifest.json', expectedHeaders: { 'cache-control': 'max-age' } },
      
      // 模拟版本化资源（需要根据实际构建结果调整）
      { path: '/assets-v1-3-0/index.js', expectedHeaders: { 'cache-control': 'immutable' } },
      { path: '/assets-v1-3-0/index.css', expectedHeaders: { 'cache-control': 'immutable' } },
      
      // 根目录资源
      { path: '/favicon.ico', expectedHeaders: {} },
    ]
    
    const results = []
    
    for (const testCase of testCases) {
      try {
        const result = await this.checkUrl(testCase.path, testCase.expectedHeaders)
        results.push(result)
        
        if (result.issues.length === 0) {
          console.log(`✅ ${testCase.path}`)
        } else {
          console.log(`⚠️  ${testCase.path}`)
          result.issues.forEach(issue => console.log(`   - ${issue}`))
        }
      } catch (error) {
        console.log(`❌ ${testCase.path} - ${error.message}`)
        results.push({
          url: this.baseUrl + testCase.path,
          status: 'ERROR',
          error: error.message,
          issues: ['请求失败']
        })
      }
    }
    
    console.log('\n' + '=' * 50)
    this.printSummary(results)
    
    return results
  }
  
  /**
   * 打印验证结果摘要
   */
  printSummary(results) {
    const total = results.length
    const passed = results.filter(r => r.issues && r.issues.length === 0).length
    const failed = total - passed
    
    console.log('📊 验证结果摘要:')
    console.log(`   总计: ${total}`)
    console.log(`   通过: ${passed} ✅`)
    console.log(`   失败: ${failed} ❌`)
    
    if (failed > 0) {
      console.log('\n🔧 需要修复的问题:')
      results.forEach(result => {
        if (result.issues && result.issues.length > 0) {
          console.log(`\n📄 ${result.url}:`)
          result.issues.forEach(issue => console.log(`   - ${issue}`))
          
          if (result.headers) {
            console.log('   当前头部:')
            Object.entries(result.headers).forEach(([key, value]) => {
              if (value) {
                console.log(`     ${key}: ${value}`)
              }
            })
          }
        }
      })
    }
    
    console.log('\n💡 建议:')
    console.log('1. 确保 customHttp.yml 配置正确')
    console.log('2. 检查 CDN 或代理服务器配置')
    console.log('3. 验证构建输出的资源路径')
    console.log('4. 测试版本更新流程')
  }
}

// 命令行使用
if (require.main === module) {
  const args = process.argv.slice(2)
  const baseUrl = args[0] || 'http://localhost:3000'
  
  console.log('🔧 缓存配置验证工具')
  console.log(`📍 目标地址: ${baseUrl}`)
  console.log('')
  
  const verifier = new CacheConfigVerifier(baseUrl)
  verifier.verify()
    .then(() => {
      console.log('\n✅ 验证完成')
    })
    .catch((error) => {
      console.error('\n❌ 验证失败:', error)
      process.exit(1)
    })
}

module.exports = CacheConfigVerifier
