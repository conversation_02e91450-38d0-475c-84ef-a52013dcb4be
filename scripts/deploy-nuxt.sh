#!/bin/bash

# Nuxt应用部署脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 开始部署Nuxt应用...${NC}"

# 检查参数
if [ $# -eq 0 ]; then
    echo -e "${RED}❌ 请指定环境: staging 或 production${NC}"
    echo "用法: ./scripts/deploy-nuxt.sh [staging|production]"
    exit 1
fi

ENVIRONMENT=$1

# 验证环境参数
if [ "$ENVIRONMENT" != "staging" ] && [ "$ENVIRONMENT" != "production" ]; then
    echo -e "${RED}❌ 无效的环境: $ENVIRONMENT${NC}"
    echo "支持的环境: staging, production"
    exit 1
fi

echo -e "${YELLOW}📦 环境: $ENVIRONMENT${NC}"

# 进入nuxt-app目录
cd nuxt-app

# 设置环境变量
export NODE_ENV=$ENVIRONMENT
export DEPLOYMENT_ENV=$ENVIRONMENT

# 安装依赖
echo -e "${YELLOW}📦 安装依赖...${NC}"
pnpm install --frozen-lockfile

# 构建应用
echo -e "${YELLOW}🔨 构建应用...${NC}"
pnpm build

# 部署到不同环境
case $ENVIRONMENT in
    "staging")
        echo -e "${YELLOW}🚀 部署到测试环境...${NC}"
        # 这里添加你的测试环境部署命令
        echo "rsync -avz --delete .output/ user@staging-server:/var/www/app-staging/"
        ;;
    "production")
        echo -e "${YELLOW}🚀 部署到生产环境...${NC}"
        # 这里添加你的生产环境部署命令
        echo "rsync -avz --delete .output/ user@prod-server:/var/www/app/"
        ;;
esac

# 返回根目录
cd ..

echo -e "${GREEN}✅ Nuxt应用部署完成!${NC}"

# 健康检查
echo -e "${YELLOW}🔍 执行健康检查...${NC}"
case $ENVIRONMENT in
    "staging")
        curl -f https://staging-app.yourdomain.com/ || echo -e "${RED}❌ 健康检查失败${NC}"
        ;;
    "production")
        curl -f https://app.yourdomain.com/ || echo -e "${RED}❌ 健康检查失败${NC}"
        ;;
esac

echo -e "${GREEN}🎉 部署流程完成!${NC}"
