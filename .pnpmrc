# pnpm 配置文件 - 优化构建性能

# 存储配置
store-dir=.pnpm-store
shared-workspace-lockfile=true

# 网络配置
network-timeout=300000
fetch-retries=5
fetch-retry-factor=2
fetch-retry-mintimeout=10000
fetch-retry-maxtimeout=60000

# 安装配置
prefer-offline=true
strict-peer-dependencies=false
auto-install-peers=true
dedupe-peer-dependents=true

# 性能优化
side-effects-cache=true
side-effects-cache-readonly=false

# 日志配置
reporter=append-only
loglevel=warn

# 解决方案配置 (对应 package.json 中的 resolutions)
# 这些配置会被 package.json 中的 resolutions 覆盖
