#!/bin/bash

# Nuxt应用独立部署脚本 (Turbo Monorepo)
# 使用方法: ./deploy-nuxt-only.sh [staging|production]

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -eq 0 ]; then
    log_error "缺少环境参数"
    echo "使用方法: $0 [staging|production]"
    exit 1
fi

ENVIRONMENT=$1

# 验证环境参数
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    log_error "无效的环境参数: $ENVIRONMENT"
    echo "使用方法: $0 [staging|production]"
    exit 1
fi

log_info "🚀 部署 Nuxt 应用到 $ENVIRONMENT 环境..."

# 检查必要工具
if ! command -v docker &> /dev/null; then
    log_error "Docker 未安装"
    exit 1
fi

if ! command -v turbo &> /dev/null; then
    log_warning "Turbo 未安装，尝试使用 pnpm 直接构建"
fi

# 设置环境变量
export NODE_ENV=$ENVIRONMENT
export DEPLOYMENT_ENV=$ENVIRONMENT

# 检查环境文件
ENV_FILE=".env.$ENVIRONMENT"
if [ ! -f "$ENV_FILE" ]; then
    log_warning "⚠️  环境文件 $ENV_FILE 不存在，使用默认配置"
fi

# 停止现有容器
log_info "停止现有容器..."
docker stop "nuxt-app-$ENVIRONMENT" > /dev/null 2>&1 || true
docker rm "nuxt-app-$ENVIRONMENT" > /dev/null 2>&1 || true

# 清理旧镜像
log_info "清理旧镜像..."
docker rmi "nuxt-app:$ENVIRONMENT" > /dev/null 2>&1 || true

# 构建 Docker 镜像
log_info "构建 Docker 镜像..."
docker build -f packages/nuxt-app/Dockerfile -t "nuxt-app:$ENVIRONMENT" \
    --build-arg NODE_ENV=$ENVIRONMENT \
    --build-arg DEPLOYMENT_ENV=$ENVIRONMENT \
    .

# 启动容器
log_info "启动容器..."
DOCKER_CMD="docker run -d \
  --name nuxt-app-$ENVIRONMENT \
  --restart unless-stopped \
  -p 3000:3000 \
  -e NODE_ENV=$ENVIRONMENT \
  -e DEPLOYMENT_ENV=$ENVIRONMENT \
  -e NITRO_PORT=3000 \
  -e NITRO_HOST=0.0.0.0"

# 如果环境文件存在，添加环境文件参数
if [ -f "$ENV_FILE" ]; then
    DOCKER_CMD="$DOCKER_CMD --env-file $ENV_FILE"
fi

DOCKER_CMD="$DOCKER_CMD nuxt-app:$ENVIRONMENT"

eval $DOCKER_CMD

# 等待服务启动
log_info "等待服务启动..."
sleep 15

# 健康检查
log_info "执行健康检查..."
if curl -f http://localhost:3000/ > /dev/null 2>&1; then
    log_success "✅ 部署成功!"
    echo ""
    echo "🌐 访问地址: http://localhost:3000"
    echo ""
    echo "📊 容器状态:"
    docker ps --filter "name=nuxt-app-$ENVIRONMENT"
    echo ""
    echo "📋 部署信息:"
    echo "   环境: $ENVIRONMENT"
    echo "   镜像: nuxt-app:$ENVIRONMENT"
    echo "   容器: nuxt-app-$ENVIRONMENT"
else
    log_error "❌ 部署失败 - 服务未响应"
    echo ""
    echo "📋 查看日志:"
    docker logs --tail=20 "nuxt-app-$ENVIRONMENT"
    exit 1
fi

log_success "🎉 Nuxt 应用部署完成!"
